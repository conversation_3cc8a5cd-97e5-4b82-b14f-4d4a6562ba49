from services.report_manager import ReportManager
from services.data_handler import DataManager
from services.reasoning_chronobid import ChronobidReasoning
from services.update_backend_status import BackendStatusUpdater
from models import Requirement,Project
import os, json, re

class GenerateReportCBP:
    def __init__(self, requirement_id, is_test=0):
        """
        Initialize the GenerateReportCBP class with the requirement ID and a report manager.

        Args:
            report_manager (object): Instance of the ReportManager class to handle report loading.
            requirement_id (str): The ID of the requirement for which the report is being generated.
        """
        print('initialized cbp report class...')
        # Get the path to the parent directory
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # Load environment variables from env.json file in the parent directory
        env_file_path = os.path.join(parent_dir, 'env.json')
        with open(env_file_path, 'r') as f:
            self.env_data = json.load(f)

        self.is_test = is_test
        self.requirement = Requirement.get_single(requirement_id)
        self.project = Project.get_single(self.requirement["project_id"])
        self.requirement_id = requirement_id
        print('1/3')
        self.criterion = json.loads(self.requirement["criteria"])
        self.tender_title = self.requirement["name"]
        self.tender_number = self.requirement["id"]
        print('1/2')
        self.bidder_name = '[BIDDER NAME]'
        self.bidder_date = '[BIDDER DATE]'
        self.report_manager = ReportManager(self.env_data.get('CHRONOBID_DIR'), self.requirement["project_id"], self.requirement_id)
        self.log = self.load_log()
        self.chronobid_reasoning = ChronobidReasoning()
        self.data_manager = DataManager()
        self.backend_status_updater = BackendStatusUpdater()
        print('i intializex successfully.')

    async def generate_detailed_evaluation(self, count, indx):
        chunk_to_pass = self.log['chunk_to_pass'][indx]
        await self.chronobid_reasoning.get_detailed_evaluation(chunk_to_pass, self.criteria, count, self.log)

    async def process_introduction_section(self):
        await self.chronobid_reasoning.get_introduction_section(
            self.project['name'],
            await self.get_project_scope(), 
            self.criterion,
            self.log
        )

    async def process_cover_page_section(self):
        await self.chronobid_reasoning.get_cover_page_section(
            self.project['name'],
            self.tender_title,
            self.tender_number,
            self.bidder_name,
            self.bidder_date,
            self.log
        )
       
    async def generate_recommendation(self):
        await self.chronobid_reasoning.get_recommendation(
            self.log['evaluation_report_section']['strength_chunk'],
            self.log['evaluation_report_section']['weak_chunk'],
            self.log['evaluation_report_section']['risk_chunk'],
            self.remove_extension(self.requirement['name']), self.project['name'],
            self.criterion,
            self.log
        )

    async def get_project_scope(self):
        project_scope = ""
        criteria = {"name": "Project Scope", "description": f"Project Scope of the {self.project['name']} project"}
        result = await self.data_manager.extract_data_chronobid(self.requirement['project_id'], criteria)
        for document in result['metadatas']:
            project_scope += f"\n{document['documents']}"
        
        print(f"project scope done...")
        return project_scope
        
    def remove_extension(self, file_name):
        base_name = os.path.splitext(file_name)[0]
        pattern = r'_(\d{6})_merged$'        
        modified_name = re.sub(pattern, '', base_name)
        
        return modified_name
    
    def update_log(self):
        self.report_manager.update_log(self.log)

    def load_log(self):
        log = self.report_manager.load_report()
        # print(log)
        return log
    
    async def _generate_report(self):
        count = 0
        indx = 0

        if not self.requirement["is_report_generated"] or self.is_test:
            print('starting to generate cbp report....')
            for idx, criteria in enumerate(self.criterion, start=0):
                self.criteria = criteria
                count += 1
                self.log['criteria'] += f"Criteria {count}: Name: {criteria['name']} Description: {criteria['description']} ({criteria['weight']}%)<br/>"
                self.log['evaluation_report_section']['evaluation_result'] += f"\n• Criterion {count}: {criteria['name']} {criteria['description']} — {self.chronobid_reasoning.extract_scores(self.log['evaluate_summary_chunk'][idx])}\n"

                await self.generate_detailed_evaluation(count, indx)
                print('generated detailed evaluation....')
                indx += 1
            
           
            self.log['evaluation_report_section']['evaluation_result'] = self.chronobid_reasoning.replace_evaluation_summary_placeholder(self.log['evaluation_report_section']['evaluation_result'], float(self.log['evaluate_summary_score']))
            
            
            await self.process_introduction_section()
            print('introduction section done....')
            await self.process_cover_page_section()
            print('coverpage done.......')
            await self.generate_recommendation()
            print('recommendation done........')

            self.update_log()
            Requirement.update(self.requirement_id, is_report_generated=1)

            print('is report: ', self.is_test)
            if not self.is_test:
                is_report_done = 1
                print('updating cbp reports now.....')
                self.backend_status_updater.update_report_status(self.requirement_id, is_report_done)
            

if __name__ == "__main__":
    report_generator = GenerateReportCBP('0ac4f209-5bf0-453d-92db-c29841016da4')
    count, indx = 0
    for criteria in report_generator.criterion:
        
        count += 1
        report_generator.generate_detailed_evaluation(count, indx)
        report_generator.process_introduction_section()
        report_generator.process_cover_page_section()
        report_generator.generate_recommendation()
        indx += 1
