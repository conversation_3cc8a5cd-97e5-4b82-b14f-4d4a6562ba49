#!/bin/bash

# Configuration
TARGET_DIR="dev-ds-oil-and-gas"
REPO_URL="http://23.29.118.76:3000/mkdlabs/ds_oil_and_gas"
BRANCH="dev-deploy"
USER="umarphp"
PASSWORD="l11w&3*CMSXt"

# Check if the target directory exists
if [ ! -d "$TARGET_DIR" ]; then
  echo "Target directory does not exist: $TARGET_DIR"
  exit 1
fi

# Navigate to the target directory
cd "$TARGET_DIR" || exit

# Initialize a new git repository if one doesn't exist
if [ ! -d ".git" ]; then
  git init
  git remote add origin $REPO_URL
fi

# Fetch the latest changes from the remote repository
git fetch origin

# Stash any local changes
git stash

# Checkout the specified branch
git checkout $BRANCH

git fetch origin $BRANCH
git reset --hard origin/$BRANCH

# Pull the latest changes from the specified branch
git pull origin $BRANCH

# Remove untracked files, excluding specified directories
git clean -fd -e chronobid -e data

echo "Repository has been updated successfully."

echo "giving necessary permissions to files"
chmod +x /home/<USER>/dev-ds-oil-and-gas/scripts/setup.sh
chmod +x /home/<USER>/dev-ds-oil-and-gas/scripts/restart_server.sh
chmod +x /home/<USER>/dev-ds-oil-and-gas/*.json
sudo chown -R azureuser:azureuser /home/<USER>/dev-ds-oil-and-gas









[92mCLAUDE USAGE : Usage(cache_creation_input_tokens=0, cache_read_input_tokens=0, input_tokens=3950, output_tokens=665, 