#!/bin/bash

# Define the paths
BASE_PATH="/home/<USER>/dev-ds-oil-and-gas/"
VENV_PATH="$BASE_PATH/venv/bin/activate"
REQUIREMENTS_PATH="$BASE_PATH/requirements.txt"
LOGS_DIR="$BASE_PATH/logs"
GUNICORN_CMD="gunicorn --bind=0.0.0.0:5120 app:app --timeout=3000 --workers=8 --access-logfile /home/<USER>/dev-ds-oil-and-gas/logs/server.log --error-logfile /home/<USER>/dev-ds-oil-and-gas/logs/server.log --capture-output --log-level debug --max-requests 300 --max-requests-jitter 30 --daemon"
UWSGI_CMD="uwsgi --http-socket 0.0.0.0:5120 --wsgi-file app.py --callable app --processes 8 --threads 1 --ha<PERSON><PERSON> 3000 --max-requests 300 --logto /home/<USER>/dev-ds-oil-and-gas/logs/server.log --log-master --daemonize /home/<USER>/dev-ds-oil-and-gas/logs/server.log"
CRONJOB_CMD="$BASE_PATH/scripts/template.cron"

# Activate the virtual environment
echo "Activating virtual environment..."
source "$VENV_PATH"

# Install requirements
echo "Installing requirements..."
pip install torch --no-cache-dir
pip install -r "$REQUIREMENTS_PATH" || { echo "Failed to install requirements"; exit 1; }

# Function to kill gunicorn processes running on port 5120
sudo pkill -f gunicorn || true;
sleep 2;

# Change directory to the base path
cd "$BASE_PATH" || { echo "Failed to change directory to $BASE_PATH"; exit 1; }

# Start gunicorn
echo "Starting gunicorn..."
$UWSGI_CMD &
if [ $? -ne 0 ]; then
  echo "Failed to start gunicorn"
  exit 1
fi

# Start cron service
echo "Starting cron service..."
crontab $CRONJOB_CMD || { echo "Failed to start cron service"; exit 1; }

# Tail logs
# 
echo "Tailing logs..."
# tail -f "$LOGS_DIR"/*.log