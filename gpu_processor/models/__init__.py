from extensions import db
import uuid
from sqlalchemy import or_,Index
import json
from datetime import datetime

# # Create tables if they do not exist
# with app.app_context():
#     db.create_all()


class Project(db.Model):
    __tablename__ = 'project'
    id = db.Column(db.String(36), primary_key=True)  # Assuming UUIDs are used, hence the length 36
    name = db.Column(db.String(255), nullable=False, index=True)
    entity_type = db.Column(db.String(255), nullable=True, index=True)
    assistant_id = db.Column(db.String(255), nullable=True)
    has_deleted_file = db.Column(db.<PERSON>, nullable=False, default=False)
    discipline_code = db.Column(db.String(255), nullable=True)  # Adjusted to 255
    files = db.relationship('File', back_populates='project', cascade='all, delete-orphan')
    merged_files = db.relationship('Merged_file', back_populates='project', cascade='all, delete-orphan')
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)

    @property
    def serialize(self):
        return {
            "id": self.id,
            "name": self.name,
            "entity_type": self.entity_type,
            "files": [file.serialize for file in self.files],
            "discipline_code": self.discipline_code,
            "merged_files": [merged_file.serialize for merged_file in self.merged_files],
            "assistant_id": self.assistant_id,
            "has_deleted_file": self.has_deleted_file,
            "created_at": self.created_at.isoformat() if self.created_at else None,
           "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize
    
    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [project.serialize for project in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []
    
    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def get_one_by(cls, **kwargs):
        try:
            return db.session.query(cls).filter_by(**kwargs).first()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def create(cls, **kwargs):
        try:
            project = cls(**kwargs)
            db.session.add(project)
            db.session.commit()
            return project.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def update(cls, id, **kwargs):
        try:
            project = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(project, key, value)
            db.session.commit()
            return project.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

class DocumentNumberingTemplate(db.Model):
    __tablename__ = 'document_numbering_template'
    id = db.Column(db.String(36), primary_key=True)
    client = db.Column(db.String(255), nullable=False, index=True)
    template = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)

    @property
    def serialize(self):
        return {
            "id": self.id,
            "client": self.client,
            "template": self.template,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize
    
    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [project.serialize for project in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []
    
    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def get_one_by(cls, **kwargs):
        try:
            return db.session.query(cls).filter_by(**kwargs).first()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def create(cls, **kwargs):
        try:
            project = cls(**kwargs)
            db.session.add(project)
            db.session.commit()
            return project.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def update(cls, id, **kwargs):
        try:
            project = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(project, key, value)
            db.session.commit()
            return project.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

class ReportTemplate(db.Model):
    __tablename__ = 'report_template'
    id = db.Column(db.String(36), primary_key=True)
    client = db.Column(db.String(255), nullable=False, index=True)
    intro_template = db.Column(db.Text, nullable=True)
    body_template = db.Column(db.Text, nullable=True)
    conclusion_template = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)

    @property
    def serialize(self):
        return {
            "id": self.id,
            "client": self.client,
            "intro_template": self.intro_template,
            "body_template": self.body_template,
            "conclusion_template": self.conclusion_template,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize
    
    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [project.serialize for project in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []
    
    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def get_one_by(cls, **kwargs):
        try:
            return db.session.query(cls).filter_by(**kwargs).first()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def create(cls, **kwargs):
        try:
            project = cls(**kwargs)
            db.session.add(project)
            db.session.commit()
            return project.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def update(cls, id, **kwargs):
        try:
            project = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(project, key, value)
            db.session.commit()
            return project.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

class Users(db.Model):
    __tablename__ = 'users'
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(255), nullable=False, index=True)
    status = db.Column(db.String(255), nullable=False, default='active', index=True)
    role = db.Column(db.String(255), nullable=False, default='customer')  # manager, customer, admin
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)

    @property
    def serialize(self):
        return {
            "id": self.id,
            "name": self.name,
            "status": self.status,
            "role": self.role,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

    @classmethod
    def get_single(cls, id):
        try:
            return db.session.query(cls).filter_by(id=id).first().serialize
        except Exception as e:
            print(e)
            return None

    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [user.serialize for user in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []

    @classmethod
    def get_one_by(cls, **kwargs):
        try:
            return db.session.query(cls).filter_by(**kwargs).first()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def create(cls, **kwargs):
        try:
            user = cls(**kwargs)
            db.session.add(user)
            db.session.commit()
            return user.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def update(cls, id, **kwargs):
        try:
            user = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(user, key, value)
            db.session.commit()
            return user.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None

class Bids(db.Model):
    __tablename__ = 'bids'
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(255), nullable=False, index=True)
    user_id = db.Column(db.String(36), db.ForeignKey('users.id', ondelete='CASCADE'), nullable=False, index=True)
    status = db.Column(db.String(255), nullable=False, default='idle', index=True)
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)

    user = db.relationship('Users', backref=db.backref('bids', lazy=True, cascade='all, delete-orphan'))

    @property
    def serialize(self):
        return {
            "id": self.id,
            "name": self.name,
            "status": self.status,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

    @classmethod
    def get_single(cls, id):
        try:
            return db.session.query(cls).filter_by(id=id).first().serialize
        except Exception as e:
            print(e)
            return None

    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [bid.serialize for bid in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []

    @classmethod
    def get_one_by(cls, **kwargs):
        try:
            return db.session.query(cls).filter_by(**kwargs).first()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def create(cls, **kwargs):
        try:
            bid = cls(**kwargs)
            db.session.add(bid)
            db.session.commit()
            return bid.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def update(cls, id, **kwargs):
        try:
            bid = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(bid, key, value)
            db.session.commit()
            return bid.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None

class BidFile(db.Model):
    __tablename__ = 'bid_files'
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    bid_id = db.Column(db.String(36), db.ForeignKey('bids.id', ondelete='CASCADE'), nullable=False, index=True)
    name = db.Column(db.String(255), nullable=False)
    status = db.Column(db.String(255), nullable=False, default='idle', index=True)
    file_type = db.Column(db.String(50), nullable=False)
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationship with Bids
    bid = db.relationship('Bids', backref=db.backref('files', lazy=True, cascade='all, delete-orphan'))

    @property
    def serialize(self):
        return {
            "id": self.id,
            "bid_id": self.bid_id,
            "name": self.name,
            "file_type": self.file_type,
            "status": self.status,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

    @classmethod
    def get_single(cls, id):
        try:
            return db.session.query(cls).filter_by(id=id).first().serialize
        except Exception as e:
            print(e)
            return None

    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [file.serialize for file in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []

    @classmethod
    def get_one_by(cls, **kwargs):
        try:
            return db.session.query(cls).filter_by(**kwargs).first()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def create(cls, **kwargs):
        try:
            bid_file = cls(**kwargs)
            db.session.add(bid_file)
            db.session.commit()
            return bid_file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def update(cls, id, **kwargs):
        try:
            bid_file = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(bid_file, key, value)
            db.session.commit()
            return bid_file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None


class BidFileMetadata(db.Model):
    __tablename__ = 'bid_file_metadata'
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    bid_id = db.Column(db.String(36), db.ForeignKey('bids.id', ondelete='CASCADE'), nullable=False, index=True)
    bid_metadata = db.Column(db.Text, nullable=False)
    status = db.Column(db.String(255), nullable=False, default='idle', index=True)
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)

    bid = db.relationship('Bids', backref=db.backref('metadata_records', lazy=True, cascade='all, delete-orphan'))

    @property
    def serialize(self):
        return {
            "id": self.id,
            "bid_id": self.bid_id,
            "bid_metadata": self.bid_metadata,
            "status": self.status,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

    @classmethod
    def get_single(cls, id):
        try:
            return db.session.query(cls).filter_by(id=id).first().serialize
        except Exception as e:
            print(e)
            return None

    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [bid.serialize for bid in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []

    @classmethod
    def get_one_by(cls, **kwargs):
        try:
            return db.session.query(cls).filter_by(**kwargs).first()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def create(cls, **kwargs):
        try:
            bid = cls(**kwargs)
            db.session.add(bid)
            db.session.commit()
            return bid.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def update(cls, id, **kwargs):
        try:
            bid = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(bid, key, value)
            db.session.commit()
            return bid.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None
class File_merged_file_association(db.Model):
    __tablename__ = 'file_merged_file_association' 
    id = db.Column(db.String(36), primary_key=True)  # Assuming UUIDs are used, hence the length 36
    file_id = db.Column(db.String(36), db.ForeignKey('file.id'), nullable=False)  # Assuming UUIDs are used, hence the length 36
    merged_file_id = db.Column(db.String(36), db.ForeignKey('merged_file.id'), nullable=False)  # Assuming UUIDs are used, hence the length 36
    association_type = db.Column(db.String(255), nullable=False)  # Adjusted to 255
    status = db.Column(db.String(255), nullable=False)  # Adjusted to 255

    # Define relationships with File and Merged_file tables if necessary
    # file = db.relationship('File', back_populates='file_associations')
    # merged_file = db.relationship('Merged_file', back_populates='merged_file_associations')

    @property
    def serialize(self):
        return {
            "id": self.id,
            "file_id": self.file_id,
            "merged_file_id": self.merged_file_id,
            "merge_start_index": self.merge_start_index,
            "merge_end_index": self.merge_end_index
        }

    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [x.serialize for x in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []

    @classmethod
    def create(cls, file_id, merged_file_id):
        try:
            id = str(uuid.uuid4())
            association = cls(id=id, file_id=file_id, merged_file_id=merged_file_id)
            db.session.add(association)
            db.session.commit()
            return association
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def update(cls, file_id, merged_file_id, **kwargs):
        try:
            association = cls.query.filter_by(file_id=file_id, merged_file_id=merged_file_id).first()
            if association:
                for key, value in kwargs.items():
                    setattr(association, key, value)
                db.session.commit()
                return association
            else:
                return None
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def delete_by(cls, **kwargs):
        try:
            association = cls.query.filter_by(**kwargs).first()
            if association:
                db.session.delete(association)
                db.session.commit()
                return True
            else:
                return False
        except Exception as e:
            print(e)
            db.session.rollback()
            return False

    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [file.serialize for file in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []

class Merged_file(db.Model):
    id = db.Column(db.String(36), primary_key=True)  # Assuming UUIDs are used, hence the length 36
    project_id = db.Column(db.String(36), db.ForeignKey('project.id'), nullable=False)  # Assuming UUIDs are used, hence the length 36
    assistant_file_id = db.Column(db.String(255))
    status = db.Column(db.String(255))  # Adjusted to 255
    
    project = db.relationship('Project', back_populates='merged_files')
    files = db.relationship('File', secondary='file_merged_file_association', back_populates='merged_files')

    @property
    def serialize(self):
        return {
            "id": self.id,
            "project_id": self.project_id,
            "assistant_file_id": self.assistant_file_id,
            "status": self.status,
            "files": [file.serialize_basic for file in self.files]
        }
    @property
    def serialize_basic(self):
        return {
            "id": self.id,
            "project_id": self.project_id,
            "assistant_file_id": self.assistant_file_id,
            "status": self.status,
        }

    @classmethod
    def get_single(cls, id):
        try:
            return db.session.query(cls).filter_by(id=id).first().serialize
        except Exception as e:
            print(e)
            return None

    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [merged_file.serialize for merged_file in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []
    
    @classmethod
    def get_by_prefix(cls, prefix):
        try:
            records = db.session.query(cls).filter(cls.id.like(f'{prefix}%')).all()
            return [record.serialize for record in records]
        except Exception as e:
            print(e)
            return []

    @classmethod
    def delete_all(cls):
        try:
            db.session.query(cls).delete()
            db.session.commit()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def get_one_by(cls, **kwargs):
        try:
            return db.session.query(cls).filter_by(**kwargs).first()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def create(cls, **kwargs):
        try:
            merged_file = cls(**kwargs)
            db.session.add(merged_file)
            db.session.commit()
            return merged_file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def update(cls, id, **kwargs):
        try:
            merged_file = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(merged_file, key, value)
            db.session.commit()
            return merged_file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

class File(db.Model):
    id = db.Column(db.String(36), primary_key=True)  # Assuming UUIDs are used, hence the length 36
    name = db.Column(db.String(255), nullable=False, index=True)
    project_id = db.Column(db.String(36), db.ForeignKey('project.id'), nullable=False, index=True)  
    file_type = db.Column(db.String(255), nullable=False, index=True)  # Adjusted to 255
    status = db.Column(db.String(255), nullable=True, default='idle', index=True)  # Adjusted to 255
    merge_status = db.Column(db.String(255), default='not_merged')  # Adjusted to 255
    tried = db.Column(db.Integer, default=0, index=True)
    category = db.Column(db.String(255), nullable=True)  # Adjusted to 255
    file_dirtry = db.Column(db.String(255), nullable=False, default='qmp', index=True)  # Adjusted to 255
    pdf_conversion_status = db.Column(db.String(36), nullable=False, default='idle', index=True)
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)

    project = db.relationship('Project', back_populates='files')
    # file_associations = db.relationship('File_merged_file_association', back_populates='file')
    merged_files = db.relationship('Merged_file', secondary='file_merged_file_association', back_populates ='files')
    
    @property
    def serialize(self):
        return {
           "id": self.id,
           "name": self.name,
           "project_id": self.project_id,
           "file_type": self.file_type,
           "file_dirtry": self.file_dirtry,
           "category": self.category,
           "status": self.status,
           "pdf_conversion_status": self.pdf_conversion_status,
           "merged_files": [merged_file.serialize_basic for merged_file in self.merged_files],
           "tried": self.tried,
           "merge_status": self.merge_status,
           "created_at": self.created_at.isoformat() if self.created_at else None,
           "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
    @property
    def serialize_basic(self):
        return {
           "id": self.id,
           "name": self.name,
           "project_id": self.project_id,
           "file_type": self.file_type,
           "file_dirtry": self.file_dirtry,
           "category": self.category,
           "status": self.status,
           "pdf_conversion_status": self.pdf_conversion_status,
           "tried": self.tried,
           "merge_status": self.merge_status,
           "created_at": self.created_at.isoformat() if self.created_at else None,
           "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize
    
    @classmethod
    def get_by(cls, **kwargs):
        try:
            # print(kwargs)
            return [file.serialize for file in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []
    
    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def create(cls, **kwargs):
        try:
            file = cls(**kwargs)
            db.session.add(file)
            db.session.commit()
            return file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def update(cls, id, **kwargs):
        try:
            file = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(file, key, value)
            db.session.commit()
            return file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

class Prompt(db.Model):
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))  # UUID as a string
    name = db.Column(db.String(255), nullable=False)
    value = db.Column(db.Text, nullable=False) 
    type = db.Column(db.String(255), nullable=False)
    date_created = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    date_updated = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    @property
    def serialize(self):
        return {
           "id": self.id,
           "name": self.name,
           "value": self.value,
           "type": self.type,
           "date_created": self.date_created.isoformat(),
           "date_updated": self.date_updated.isoformat()
        }
    
    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize
    
    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [prompt.serialize for prompt in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []

    @classmethod
    def create(cls, **kwargs):
        try:
            prompt = cls(**kwargs)
            db.session.add(prompt)
            db.session.commit()
            return prompt.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def update(cls, id, **kwargs):
        try:
            prompt = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(prompt, key, value)
            db.session.commit()
            return prompt.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None
        
class Discipline(db.Model):
    __tablename__ = 'disciplines'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    shortcode = db.Column(db.String(2), unique=True, nullable=True)
    name = db.Column(db.String(255), nullable=False)
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)

    categories = db.relationship('Category', back_populates='discipline')

    @property
    def serialize(self):
        return {
           "id": self.id,
           "shortcode": self.shortcode,
           "name": self.name,
           "created_at": self.created_at.isoformat() if self.created_at else None,
           "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize
    
    @classmethod
    def get_by(cls, **kwargs):
        try:
            # print(kwargs)
            return [file.serialize for file in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []
    
    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def create(cls, **kwargs):
        try:
            file = cls(**kwargs)
            db.session.add(file)
            db.session.commit()
            return file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def update(cls, id, **kwargs):
        try:
            file = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(file, key, value)
            db.session.commit()
            return file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

class Category(db.Model):
    __tablename__ = 'categories'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    name = db.Column(db.String(255), nullable=False)
    feed = db.Column(db.Boolean, default=False)
    detail = db.Column(db.Boolean, default=False)
    vendor = db.Column(db.Boolean, default=False)
    similar = db.Column(db.Text, nullable=True)
    criteria = db.Column(db.Text, nullable=True)
    discipline_id = db.Column(db.Integer, db.ForeignKey('disciplines.id'), nullable=False)
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)

    discipline = db.relationship('Discipline', back_populates='categories')

    @property
    def serialize(self):
        return {
           "id": self.id,
           "name": self.name,
           "feed": self.feed,
           "detail": self.detail,
           "vendor": self.vendor,
           "discipline_id": self.discipline_id,
           "similar": json.loads(self.similar) if self.similar else [],
           "criteria": json.loads(self.criteria) if self.criteria else [],
           "created_at": self.created_at.isoformat() if self.created_at else None,
           "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize
    
    @classmethod
    def get_by(cls, **kwargs):
        try:
            # print(kwargs)
            return [file.serialize for file in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []
    
    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def create(cls, **kwargs):
        try:
            file = cls(**kwargs)
            db.session.add(file)
            db.session.commit()
            return file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def update(cls, id, **kwargs):
        try:
            file = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(file, key, value)
            db.session.commit()
            return file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None


class Chunks(db.Model):
    id = db.Column(db.String(36), primary_key=True)  
    title = db.Column(db.String(255), nullable=False, index=True)
    content = db.Column(db.Text, nullable=False)
    source = db.Column(db.String(255))
    summary = db.Column(db.Text)
    project_id = db.Column(db.String(36), nullable=False, index=True)  
    file_id = db.Column(db.String(36), nullable=False, index=True)
    page_number = db.Column(db.String(36), nullable=True)
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)

    
    tags = db.relationship('Tags', back_populates='chunks')

    @property
    def serialize(self):
        return {
            "id": self.id,
            "title": self.title,
            "content": self.content,
            "source": self.source,
            "summary": self.summary,
            "tags": [tag.serialize for tag in self.tags],
            "project_id": self.project_id,
            "file_id": self.file_id,
            "page_number": self.page_number,
            "created_at": self.created_at.isoformat() if self.created_at else None,
           "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize

    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [chunk.serialize for chunk in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []


    @classmethod
    def create(cls, **kwargs):
        try:
            chunk = cls(**kwargs)
            db.session.add(chunk)
            db.session.commit()
            return chunk.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def update(cls, id, **kwargs):
        try:
            chunk = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(chunk, key, value)
            db.session.commit()
            return chunk.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None

class Tags(db.Model):
    id = db.Column(db.String(36), primary_key=True)  # Assuming UUIDs are used, hence the length 36
    name = db.Column(db.String(255), nullable=False)
    chunk_id = db.Column(db.String(36), db.ForeignKey('chunks.id'), nullable=False) 

    chunks = db.relationship('Chunks', back_populates='tags')

    def __init__(self, name, chunk_id):
        self.name = name
        self.chunk_id = chunk_id

    @property
    def serialize(self):
        return {
            "id": self.id,
            "name": self.name,
            "chunk_id": self.chunk_id
        }

    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize

    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [tag.serialize for tag in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []

    @classmethod
    def create(cls, **kwargs):
        try:
            tag = cls(**kwargs)
            db.session.add(tag)
            db.session.commit()
            return tag.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def update(cls, id, **kwargs):
        try:
            tag = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(tag, key, value)
            db.session.commit()
            return tag.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

class Requirement(db.Model):
    __tablename__ = 'requirement'
    
    id = db.Column(db.String(36), primary_key=True)  # Assuming UUIDs are used, hence the length 36
    name = db.Column(db.Text, nullable=False, index=True)
    project_id = db.Column(db.String(36), db.ForeignKey('project.id'), nullable=False)  # Assuming UUIDs are used, hence the length 36
    file_type = db.Column(db.String(255), nullable=False)  # Adjusted to 255
    category = db.Column(db.String(255), nullable=True)  # Adjusted to 255
    discipline = db.Column(db.String(255), nullable=True)  # Adjusted to 255
    status = db.Column(db.String(255), nullable=True, default='idle', index=True)  # Adjusted to 255
    tried = db.Column(db.Integer, default=0, index=True)
    is_test = db.Column(db.Integer, default=0)
    exist_bid = db.Column(db.Integer, default=0)
    score = db.Column(db.Integer, nullable=True)
    criteria = db.Column(db.String(4096), nullable=True)  # Adjusted to 255
    type = db.Column(db.String(255), nullable=True, default='scp', index=True)  # Added type field with default value
    is_report_generated = db.Column(db.Integer, default=0, nullable=False)
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (
        Index('ix_requirement_name', 'name', mysql_length=191),
    )
    
    @property
    def serialize(self):
        return {
           "id": self.id,
           "name": self.name,
           "project_id": self.project_id,
           "file_type": self.file_type,
           "discipline": self.discipline,
           "category": self.category,
           "is_test": self.is_test,
           "exist_bid": self.exist_bid,
           "status": self.status,
           "tried": self.tried,
           "score": self.score,
           "type": self.type,
           "criteria": self.criteria,
           "is_report_generated": self.is_report_generated,
           "created_at": self.created_at.isoformat() if self.created_at else None,
           "updated_at": self.updated_at.isoformat() if self.updated_at else None
       }

    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize
    
    @classmethod
    def get_by(cls, **kwargs):
        try:
            # print(kwargs)
            return [file.serialize for file in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []
    
    @classmethod
    def get_scp_by(cls, **kwargs):
        try:
            query = db.session.query(cls).filter_by(**kwargs)
            # Add an additional filter to exclude records with an empty category field
            query = query.filter(or_(cls.criteria.is_(None), cls.criteria == ''))
            return [file.serialize for file in query.all()]
        except Exception as e:
            print(e)
            return []
    
    @classmethod
    def get_by_not_null_criteria(cls, **kwargs):
        try:
            query = db.session.query(cls)
    
            # Apply filter criteria where 'criteria' is not null or empty
            query = query.filter(or_(cls.criteria != None, cls.criteria != ''))
    
            # Apply additional filter criteria provided in kwargs
            if kwargs:
                query = query.filter_by(**kwargs)
    
            # Retrieve records and return serialized objects
            return [file.serialize for file in query.all()]
    
        except Exception as e:
            print(e)
            return []

    @classmethod
    def get_by_null_criteria(cls, **kwargs):
        try:
            # Query all records where 'criteria' is null or empty
            return [file.serialize for file in db.session.query(cls).filter(or_(cls.criteria == None, cls.criteria == '')).all()]
        
        except Exception as e:
            print(e)
            return []

    @classmethod
    def create(cls, **kwargs):
        try:
            file = cls(**kwargs)
            db.session.add(file)
            db.session.commit()
            return file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def update(cls, id, **kwargs):
        try:
            file = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(file, key, value)
            db.session.commit()
            return file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None

class Tender(db.Model):
    id = db.Column(db.String(36), primary_key=True)  # Assuming UUIDs are used, hence the length 36
    name = db.Column(db.String(255), nullable=False)
    project_id = db.Column(db.String(36), nullable=False, index=True)  # Assuming UUIDs are used, hence the length 36
    file_type = db.Column(db.String(255), nullable=False)  # Adjusted to 255
    status = db.Column(db.String(255), nullable=True, default='idle', index=True)  # Adjusted to 255
    tried = db.Column(db.Integer, default=0)
    score = db.Column(db.Integer, nullable=True)
    criteria = db.Column(db.Text, nullable=True)
    
    
    @property
    def serialize(self):
       return {
           "id": self.id,
           "name": self.name,
           "project_id": self.project_id,
           "file_type": self.file_type,
           "status": self.status,
           "tried": self.tried,
           "score": self.score,
           "criteria": self.criteria
       }
    
    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize
    
    @classmethod
    def get_by(cls, **kwargs):
        try:
            # print(kwargs)
            return [file.serialize for file in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []

    @classmethod
    def create(cls, **kwargs):
        try:
            file = cls(**kwargs)
            db.session.add(file)
            db.session.commit()
            return file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def update(cls, id, **kwargs):
        try:
            file = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(file, key, value)
            db.session.commit()
            return file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None

