import json
import asyncio
from services.question_answer import QuestionAnswerer

class QuestionAnswerTester:
    def __init__(self, data, output_file):
        self.data = data
        self.output_file = output_file
        self.qa_service = QuestionAnswerer()

    async def fetch_answer(self, project_id, question):
        # Simulate the answer_question_v1_1 method call
        response = self.qa_service.answer_question_v1_1(project_id, question)
        return {
            "project_id": project_id,
            "question": question,
            "response": response
        }

    async def process_questions(self, data):

        tasks = []
        for item in data:
            project_id = item.get('project_id')
            question = item.get('question')
            tasks.append(self.fetch_answer(project_id, question))

        # Run all tasks asynchronously
        responses = await asyncio.gather(*tasks)
        
        # Write the results to the output file
        with open(self.output_file, 'w') as file:
            json.dump(responses, file, indent=4)

    def run(self):
        asyncio.run(self.process_questions(self.data))


# Usage
# if __name__ == "__main__":
    # tester = QuestionAnswerTester("data.json", "responses.json")
    # tester.run()
