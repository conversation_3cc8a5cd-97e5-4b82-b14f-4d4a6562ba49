
from .bid_evaluation.technical_bid.utils import construct_queries
from services.pinecone_vector_db import CanopyA<PERSON>
from services.cohere_embedding import CohereService
from typing import Dict, List, Any, Optional, Union
from services.claude_ai_service import ClaudeService
from services.prompt_loader import Prompt<PERSON>oader
from services.faiss_embedding import FaissEmbedding
from services.reasoning_chronobid import ChronobidReasoning
from models import Chunks

class TechnicalCriteriaEvaluator:
    """
    Class for evaluating technical criteria of equipment based on bid and tender documents.
    """
    
    def __init__(self, socket_manager=None, requirement_id=None):
        """
        Initialize the evaluator with required services.
        
        Args:
            socket_manager: Socket manager for emitting progress events
            requirement_id: ID of the requirement for socket events
        """
        self.eb = FaissEmbedding()
        self.claude_client = ClaudeService(claude_keys=['CLAUDE_API_CBP', 'CLAUDE_API_CBP'],default_key="CLAUDE_API_CBP")
        self.prompt_loader = PromptLoader()
        self.socket_manager = socket_manager
        self.requirement_id = requirement_id
    
    
    def add_event(self, requirement_id, event_name, data):
        """
        Emit socket event to client for progress tracking.
        
        Args:
            requirement_id: ID of the requirement
            event_name: Name of the event
            data: Data to be sent with the event
        """
        if self.socket_manager:
            self.socket_manager.emit_to_client(requirement_id, event_name, data)
            print(f"Emitted {event_name} event to room {requirement_id}")
    
    def search_equipment_on_tender(self, equipment_dict, tender_id, use_test_id=False):
        """
        Search for equipment information in tender documents.
        
        Args:
            equipment_dict: Dictionary containing equipment details
            tender_id: ID of the tender document
            use_test_id: If True, use test_id instead of tender_id
            
        Returns:
            Tender document text relevant to the equipment
        """
        q1, q2 = construct_queries(equipment_dict)
        
        vd = CanopyAI()
        
        test_chunks_content = """
        |  |  |  |  |  |  |  | Detailed Engineering ― ArcelorMittal Las Truchas |  |  |  |  |  |  |  |  | <br/> |  |  |  |  |  |  |  | P3803-500-PROC-DS-002_0 |  |  |  |  |  |  |  |  | <br/> |  |  |  |  |  |  |  | Project #: | P3803 | Rev | Date | Description |  |  |  | Prepared | Checked | Approved | Client<br/>Title | L1 | L2 | L3 | # |  | Package #: | M006 | 0 | 2021-09-27 00:00:00 | Issued for Bid |  |  |  | Iavor Boev, P.Eng | Ewald Pengel,
        P. Eng. | Tim Fletcher, 
        """
        test_title = "TITLE_1_P3803"
        test_page = 1
        
        # Define batch size
        batch_size = 30
        satisfying_data = {"ids": [], "metadatas": []}
        
        # Use test_id if specified, otherwise use provided tender_id
        search_id = "ffa934b3-5f21-434f-b176-a2eef9630311" if use_test_id else tender_id
        
        # Process queries in batches
        for i in range(0, len(q2), batch_size):
            batch = q2[i:i+batch_size]
            queries_str = ", ".join(batch)
            
            batch_result = vd.get_data(search_id, [queries_str])
            
            # Process results
            if any(batch_result["ids"]) and any(batch_result["metadatas"]):
                for ids, metadatas in zip(batch_result["ids"], batch_result["metadatas"]):
                    satisfying_data["ids"].append(ids)
                    for metadata in metadatas:
                        # In production, we use the database query to get chunk details
                        chunk = Chunks.get_by(id=metadata["section_id"])
                        if len(chunk) > 0:
                            metadata['detailed_chunk'] = chunk[0]["content"]
                            metadata['title'] = chunk[0]["title"]
                            metadata['page_number'] = chunk[0]["page_number"]
                            metadata['source'] = chunk[0]["source"]
                        
                        # For testing, use test values
                        #metadata['detailed_chunk'] = test_chunks_content
                        #metadata['title'] = test_title
                        #metadata['page_number'] = test_page
                        satisfying_data["metadatas"].append(metadata)
        
        pinecone_data = []
        chunk_detailed_content = []
        source_document_text = ""
        for resp in range(len(satisfying_data['metadatas'])):
            metadata = satisfying_data['metadatas'][resp]

            pinecone_data.append(metadata.get("documents", ""))
            detailed_chunk = metadata.get("detailed_chunk", "")
            if detailed_chunk:
                chunk_detailed_content.append(detailed_chunk)
            else:
                source_document_text += metadata.get("documents", "") + "\n"
                source_document_text += f"\n\n==== SOURCE_DOCUMENT_NAME: {metadata.get('source', '')} ====\n"
        
        if source_document_text != "":
            return source_document_text
        
        cohere_embedding = CohereService()
        # Use all queries combined for reranking
        all_queries = ", ".join(q2)
        reranked_texts = cohere_embedding.rerank_texts(all_queries, chunk_detailed_content)
        
        return reranked_texts
    
    def evaluate(self, equipment_list, tender_id, bid_id, technical_criteria_weight, log, criteria_index,equipment_name_weights):
        """
        Evaluate technical criteria for a list of equipment.
        
        Args:
            equipment_list: List of equipment dictionaries
            tender_id: ID of the tender document
            bid_id: ID of the bid document
            technical_criteria_weight: Weight of the technical criteria
            log: Log dictionary to store evaluation results
            criteria_index: Index of the criteria being evaluated
        
        Returns:
            Dictionary with evaluation results
        """
        # Notify start of processing
        if self.socket_manager and self.requirement_id:
            self.add_event(self.requirement_id, 'progress_message', {
                'event_type': 'CBP',
                'request_id': self.requirement_id,
                'data': {'status': 'extracting_sources', 'message': f"Welcome to Technical Bid Evaluation Assistant..."}
            })
        
        results = []
        
        # Process each equipment
        for idx, equipment in enumerate(equipment_list):
            equipment_name = equipment["equipment_name"]
            print(f"\nProcessing equipment {idx+1}/{len(equipment_list)}: {equipment_name}")
            
            # Notify equipment processing start
            if self.socket_manager and self.requirement_id:
                self.add_event(self.requirement_id, 'progress_message', {
                    'event_type': 'CBP',
                    'request_id': self.requirement_id,
                    'data': {'status': 'extracting_sources', 'message': f"Starting analysis for Equipment {idx+1}: {equipment_name}. Extracting relevant tender sources.", 'query_idx': idx+1}
                })
            
            # Extract items from the equipment categories
            equipment_items = []
            if "categories" in equipment:
                if equipment["categories"]:
                  for category in equipment["categories"]:
                    if "items" in category:
                        if category["items"]:
                          for item in category["items"]:
                             equipment_items.append(item["name"])
            
            # Check if equipment has at least one item
            if not equipment_items:
                print(f"No items available for equipment: {equipment_name}")
                # Add results with default message
                results.append({
                    "equipment_name": equipment_name,
                    "items": [],
                    "tender_analysis": "No items analysis available for this equipment",
                    "bid_analysis": "No items analysis available for this equipment"
                })
                continue
            
            # Format items as a numbered list for display
            formatted_items = "Technical Specification \n".join([f"{i+1}. {item}" for i, item in enumerate(equipment_items)])
            
            # Search for tender documents
            tender_documents = self.search_equipment_on_tender(equipment, tender_id, use_test_id=(tender_id is None))
            
            # Load and prepare tender analysis prompt
            tender_prompt = self.prompt_loader.get_prompt('cbp_analyse_equipment_items_using_tender')
            tender_messages = tender_prompt.copy()
            tender_messages[0]['content'] = tender_messages[0]['content'].format(
                equipment_name=equipment_name,
                formatted_items=formatted_items,
                tender_document=tender_documents
            )
    
            # Notify tender analysis start
            if self.socket_manager and self.requirement_id:
                self.add_event(self.requirement_id, 'progress_message', {
                    'event_type': 'CBP',
                    'request_id': self.requirement_id,
                    'data': {'status': 'extracting_sources', 'message': f"Continuing analysis for Equipment {idx+1}: {equipment_name}. Analyzing tender specifications.", 'query_idx': idx+1}
                })
            
            # Call Claude for tender analysis
            try:
                tender_completion = self.claude_client.generate_message_agent_sonnet_new_sync(
                    messages=tender_messages,
                    temperature=0.001
                )
                tender_analysis = tender_completion.content[0].text if (
                    tender_completion and hasattr(tender_completion.content[0], 'text')
                ) else "Analysis failed"
            except Exception as e:
                print(f"Error in tender analysis: {e}")
                tender_analysis = f"Error: {str(e)}"
            
            # Generate queries and search for bid documents
            q1, q2 = construct_queries(equipment)
            query = ",Technical specification ".join(q2)
            
            # Notify bid document search
            if self.socket_manager and self.requirement_id:
                self.add_event(self.requirement_id, 'progress_message', {
                    'event_type': 'CBP',
                    'request_id': self.requirement_id,
                    'data': {'status': 'extracting_sources', 'message': f"Continuing analysis for Equipment {idx+1}: {equipment_name}. Extracting relevant bid information.", 'query_idx': idx+1}
                })
            
            bid_documents = self.eb.search_sync([query], bid_id, 40)

            def format_uplaod_documents(res):
                formatted_results = []
                for item in res[0]:
                    text_content = item[0]
                    source_doc = item[2] if len(item) > 2 else "Unknown Source"
                    
                    formatted_results.append({
                        "text_content": text_content,
                        "UPLOADDED_DOCUMENT_NAME": source_doc
                    })
                return formatted_results
            
            bid_documents = format_uplaod_documents(bid_documents)
          
            print(  'bid documents: ', bid_documents)

            # Load and prepare bid analysis prompt
            bid_prompt = self.prompt_loader.get_prompt('cbp_analyse_equipment_items_using_bid')
            bid_messages = bid_prompt.copy()
            bid_messages[0]['content'] = bid_messages[0]['content'].format(
                equipment_name=equipment_name,
                formatted_items=formatted_items,
                bid_documents=bid_documents
            )
            
            # Notify bid analysis start
            if self.socket_manager and self.requirement_id:
                self.add_event(self.requirement_id, 'progress_message', {
                    'event_type': 'CBP',
                    'request_id': self.requirement_id,
                    'data': {'status': 'extracting_sources', 'message': f"Insights for Equipment {idx+1}: {equipment_name} extracted. Preparing to analyze document chunks.", 'query_idx': idx+1}
                })
            
            # Call Claude for bid analysis
            try:
                bid_completion = self.claude_client.generate_message_agent_sonnet_new_sync(
                    messages=bid_messages,
                    temperature=0.001
                )
                bid_analysis = bid_completion.content[0].text if (
                    bid_completion and hasattr(bid_completion.content[0], 'text')
                ) else "Analysis failed"
            except Exception as e:
                print(f"Error in bid analysis: {e}")
                bid_analysis = f"Error: {str(e)}"
            
            # Add results to the list
            results.append({
                "equipment_name": equipment_name,
                "items": equipment_items,
                "tender_analysis": tender_analysis,
                "bid_analysis": bid_analysis
            })
            
            print(f"Completed processing for {equipment_name}")
        
        # Notify technical evaluation start
        if self.socket_manager and self.requirement_id:
            self.add_event(self.requirement_id, 'progress_message', {
                'event_type': 'CBP',
                'request_id': self.requirement_id,
                'data': {'status': 'extracting_sources', 'message': f"Compiling the technical evaluation summary. Finalizing results."}
            })
        
        # Perform technical evaluation on all equipment
        print(f"\033[92mEQUPMENT NAMES AND WEIGHTS : {equipment_name_weights}\033[0m")

        technical_evaluation = self.evaluate_technical_equipment(results, technical_criteria_weight,criteria_index,equipment_name_weights)
        
      
        

        if criteria_index > len(log['evaluate_summary_chunk']):
            # Extend the list if needed
            log['evaluate_summary_chunk'].extend([""] * (criteria_index - len(log['evaluate_summary_chunk'])))
            print(f"Extended list to length: {len(log['evaluate_summary_chunk'])}")
        
        # More debug prints
        print(f"Final evaluate_summary_chunk length: {len(log['evaluate_summary_chunk'])}")
        print(f"Trying to access index: {criteria_index-1}")
        
        # Store the response in the log (using 0-based indexing)
        
        log['evaluate_summary_chunk'][criteria_index-1] = technical_evaluation
        print("Evaluate summary chunk: ", log['evaluate_summary_chunk'])
        
        # Update the full summary - ensure all items in the list are strings before joining
        summary_chunks = []
        for chunk in log['evaluate_summary_chunk']:
            if chunk is not None:
                summary_chunks.append(chunk)
            else:
                summary_chunks.append('')
                
        log['evaluate_summary'] = "<div class='container'>" + log.get('top_text', '') + ''.join(summary_chunks) + "</div>"
        
        # Calculate and update the weighted score
        cr = ChronobidReasoning()
        weighted_score = cr.manual_calculate_total_weighted_score_2_sync(log['evaluate_summary'])
        log['evaluate_summary_score'] = weighted_score

        print(f"Criteria {criteria_index} summary completed with score: {weighted_score}")
        
        # Replace placeholders with the actual score
        log['evaluate_summary'] = cr.replace_evaluation_summary_placeholder(log['evaluate_summary'], weighted_score)
        log['evaluate_summary_intro'] = cr.replace_evaluation_summary_placeholder(log.get('evaluate_summary_intro', ''), weighted_score)
        
        print(f"Criteria {criteria_index} summary completed with score: {weighted_score}")

        print("Final evaluate summary: ", log['evaluate_summary_chunk'])
        # Notify completion with in_progress_event
        
        #return results
    
    def evaluate_technical_equipment(self, equipment_analysis_list, technical_criteria_weight,criteria_index,equipment_name_weights):
        """
        Evaluate technical equipment based on bid and tender analysis
        
        Args:
            equipment_analysis_list: List of equipment analyses with bid and tender analysis
            technical_criteria_weight: Weight of the technical criteria
            
        Returns:
            Technical evaluation results
        """
        
        # Format the complete equipment analysis
        complete_equipment_analysis = ""
        for equipment in equipment_analysis_list:
            complete_equipment_analysis += f"\nEQUIPMENT: {equipment['equipment_name']}\n"
            complete_equipment_analysis += f"BID ANALYSIS:\n{equipment['bid_analysis']}\n"
            complete_equipment_analysis += f"TENDER ANALYSIS:\n{equipment['tender_analysis']}\n\n"
        
        # Load and prepare technical evaluation prompt
        tech_eval_prompt = self.prompt_loader.get_prompt('cbp_technical_equipment_evaluation')
        tech_eval_messages = tech_eval_prompt.copy()
        tech_eval_messages[0]['content'] = tech_eval_messages[0]['content'].format(
            complete_equipment_analysis=complete_equipment_analysis,
            technical_criteria_weight=technical_criteria_weight,
            equipment_name_weights = equipment_name_weights,
            criteria_index=criteria_index
        )
        
        # Call Claude for technical evaluation
        try:
            tech_eval_completion = self.claude_client.generate_message_agent_sonnet_new_sync(
                messages=tech_eval_messages,
                temperature=0.001
            )
            technical_evaluation = tech_eval_completion.content[0].text if (
                tech_eval_completion and hasattr(tech_eval_completion.content[0], 'text')
            ) else "Evaluation failed"
        except Exception as e:
            print(f"Error in technical evaluation: {e}")
            technical_evaluation = f"Error occured while processing technical evaluation."
        
        return technical_evaluation

# Example usage
if __name__ == "__main__":
    # Initialize the evaluator
    evaluator = TechnicalCriteriaEvaluator()
    sample_equipment_list = [{
    "equipment_name": "BALL MILL",
    'id': "eq001",
    "categories": [
        {
        "name": "GENERAL",
        "items": [
            {"name": "Mill Type", "unit": "", "discipline": "MECHANICAL"},
            {"name": "Mill ID", "unit": "", "discipline": "MECHANICAL"},
            {"name": "Mill Length (Effective Grinding Length)", "unit": "", "discipline": "MECHANICAL"},
            {"name": "Mill Speed", "unit": "rpm", "discipline": "MECHANICAL"},
            {"name": "N.C.", "unit": "", "discipline": "MECHANICAL"},
            {"name": "Power Consumption", "unit": "kw (hp)", "discipline": "MECHANICAL"},
            {"name": "Pinion power draw at:", "unit": "", "discipline": "MECHANICAL"},
            {"name": "New Liners", "unit": "kw (hp)", "discipline": "MECHANICAL"},
            {"name": "Worn Liners", "unit": "kw (hp)", "discipline": "MECHANICAL"}
        ]
        },
        {
        "name": "MILL SHELL",
        "items": [
            {"name": "Drilling", "unit": "", "discipline": "MECHANICAL"},
            {"name": "Drilling Method", "unit": "", "discipline": "MECHANICAL"},
            {"name": "Shell Material", "unit": "", "discipline": "MECHANICAL"},
            {"name": "Flange Material", "unit": "", "discipline": "MECHANICAL"},
            {"name": "Shell Thickness", "unit": "mm", "discipline": "MECHANICAL"},
            {"name": "Flange Thickness", "unit": "mm", "discipline": "MECHANICAL"},
            {"name": "Number of Manholes", "unit": "", "discipline": "MECHANICAL"}
        ]
        },
        {
        "name": "MILL HEADS",
        "items": [
            {"name": "MILL HEADS", "unit": "", "discipline": "MECHANICAL"},
            {"name": "Material", "unit": "", "discipline": "MECHANICAL"},
            {"name": "Thickness at Trunnion Bearing", "unit": "mm", "discipline": "MECHANICAL"},
            {"name": "Head Mass (KG)", "unit": "kg", "discipline": "MECHANICAL"},
            {"name": "Trunnion Mass (KG)", "unit": "kg", "discipline": "MECHANICAL"}
        ]
        },
        {
        "name": "GIRTH GEAR",
        "items": [
            {"name": "Country of Origin", "unit": "", "discipline": "ELECTRICAL"},
            {"name": "Supplier", "unit": "", "discipline": "ELECTRICAL"},
            {"name": "Diameter", "unit": "mm", "discipline": "ELECTRICAL"},
            {"name": "Face Width", "unit": "mm", "discipline": "ELECTRICAL"},
            {"name": "Number of Segments", "unit": "", "discipline": "ELECTRICAL"},
            {"name": "Gear Type", "unit": "", "discipline": "ELECTRICAL"},
            {"name": "No. of Teeth", "unit": "", "discipline": "ELECTRICAL"},
            {"name": "Material", "unit": "", "discipline": "ELECTRICAL"},
            {"name": "AGMA", "unit": "", "discipline": "ELECTRICAL"},
            {"name": "Surface Finish", "unit": "µm", "discipline": "ELECTRICAL"},
            {"name": "Service Factor: Durability", "unit": "", "discipline": "ELECTRICAL"},
            {"name": "Service Factor: Strength", "unit": "", "discipline": "ELECTRICAL"},
            {"name": "Hardness", "unit": "HB", "discipline": "ELECTRICAL"},
            {"name": "Reversible", "unit": "yes/no", "discipline": "ELECTRICAL"},
            {"name": "Mass (KG)", "unit": "kg", "discipline": "ELECTRICAL"},
            {"name": "Module", "unit": "mm", "discipline": "ELECTRICAL"},
            {"name": "Guard supplier", "unit": "", "discipline": "ELECTRICAL"}
        ]
        }
    ]
    }]

    sample = sample_equipment_list[0]

    log = {
    'requirement_id': 'req-789',
    'evaluate_summary_chunk': [None, None, None],  # For criteria 1, 2, 3
    'evaluation_report_section': {
        'strength_chunk': [[], [], []],  # For criteria 1, 2, 3
        'weak_chunk': [[], [], []],      # For criteria 1, 2, 3
        'risk_chunk': [[], [], []]       # For criteria 1, 2, 3
    }
}
    
    # Define test IDs
    test_tender_id = "ffa934b3-5f21-434f-b176-a2eef9630311"
    test_bid_id = "62b432fe-72a1-4a13-9baa-799253d554df"
    
    # Set technical criteria weight
    technical_criteria_weight = 40
    
    # Evaluate the sample equipment list
    results = evaluator.evaluate(
        equipment_list=sample_equipment_list,
        tender_id=test_tender_id,
        bid_id=test_bid_id,
        criteria_index = 1,
        log = log,
        technical_criteria_weight=technical_criteria_weight
    )
    
    # Print summary
    print("\nEvaluation Summary:")
    for result in results:
        print(f"- Equipment: {result['equipment_name']}")
        print(f"  Items: {len(result['items'])}")
        print(f"  Tender Analysis Length: {len(result['tender_analysis'])} characters")
        print(f"  Bid Analysis Length: {len(result['bid_analysis'])} characters")
        print()
    
    # Print the technical evaluation
    print("Technical Evaluation:")
    print(results[0]["technical_evaluation"])
    
    print("Evaluation complete. Results available in the 'results' variable.")
