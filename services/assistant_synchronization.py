import os
from models import Project, Merged_file
from services.open_ai_service import Assistant<PERSON>anager

class AssistantSyncManager:
    def __init__(self):
        self.merged_file_base_path = os.getenv("WORKSPACE_DIR")
        self.assistant_manager = AssistantManager()

    def create_project_assistant(self, project_id):
        project = Project.get_by(id=project_id)[0]
        if not project:
            return {"error": True, "message": "Project not found!"}
        
        assistant_id = project["assistant_id"]
        if not assistant_id:
            assistant_id = self.assistant_manager.create_assistant(project['name'] + " | " + project["id"])
            Project.update(project["id"], assistant_id=assistant_id)
        
        return assistant_id

    def synchronize_assistant(self, merged_file):
        Merged_file.update(merged_file["id"], status="syncing")
        
        project = Project.get_by(id=merged_file["project_id"])[0]
        if not project:
            return {"error": True, "message": "Project not found!"}
        
        assistant_id = self.create_project_assistant(project["id"])
        if not assistant_id:
            return {"error": True, "message": "Assistant not found!"}
        
        account_no = assistant_id.split('/')[1]
        assistant_id = assistant_id.split('/')[0]
        
        file_path = os.path.join(self.merged_file_base_path, merged_file["project_id"], merged_file["id"] + "_merged.docx")
        
        if merged_file["assistant_file_id"]:
            try:
                self.assistant_manager.delete_assistant_file(assistant_id, int(account_no), merged_file["assistant_file_id"])
            except Exception as e:
                print(e)
        
        new_file_id = self.assistant_manager.create_assistant_file(assistant_id, int(account_no), file_path).id

        if not new_file_id:
            return {"error": True, "message": "Assistant file not created!"}
        
        Merged_file.update(merged_file["id"], assistant_file_id=new_file_id, status="synced")

        return

    def sync_queue(self):
        try:
            files = Merged_file.get_by(status='modified')
            print(files[0])
            if len(files) == 0:
                print("No files to sync")
                return
            
            self.synchronize_assistant(files[0])
        except Exception as e:
            Merged_file.update(files[0]["id"], status="modified")
            print(e)

    def delete_queue(self):
        files = Merged_file.get_by(status='deleted')
        if len(files) == 0:
            print("No files to delete")
            return

        try:
            assistant_id = self.create_project_assistant(files[0]["project_id"])
            if not assistant_id:
                return {"error": True, "message": "Assistant not found!"}
            
            account_no = assistant_id.split('/')[1]
            assistant_id = assistant_id.split('/')[0]
            
            if files[0]["assistant_file_id"] is not None:
                self.assistant_manager.delete_assistant_file(assistant_id, int(account_no), files[0]["assistant_file_id"])
            
            Merged_file.delete_by(id=files[0]["id"])
        except Exception as e:
            print(e)


# if __name__ == "__main__":
#     assistant_sync_manager = AssistantSyncManager()
#     assistant_sync_manager.sync_queue()
#     assistant_sync_manager.delete_queue()
