import asyncio
from services.data_handler import DataManager
from services.synonym_expansion import SynonymGenerator
import json
import re
from itertools import chain


class QMPSourceExtractor:
    def __init__(self):
        self.data_manager = DataManager()
        self.synonym_service = SynonymGenerator()

    async def extract_sources(self, project_ids, sub_question):
        self.project_ids = project_ids
        data = []
        # if len(project_ids) < 3:
            # Generate similar questions to improve search
        synonyms = await self.synonym_service.generate_synonym(f"{sub_question}\n Description: {sub_question}")
        data = [f"{name.strip()} - {description.strip()}" 
            for name, description in re.findall(r"Name:\s*(.+?)\n\s*Description:\s*(.+?)(?:\n|</SYNONYMS>)", synonyms, re.DOTALL)]
        
        # print("Questions generated successfully...")
        # print('questions generated by expansion agent: ', data)
        print('checkpoint QMP QMPSourceExtractor 19')
        print('length is: ', len(data))

        """Extract sources for multiple project IDs asynchronously."""
        tasks = [
            self.data_manager.extract_data_v2(project_id, [sub_question], data, 5)
            for project_id in project_ids
        ]
        results = await asyncio.gather(*tasks)
        self.sources = results
        self.Question = sub_question
        print('done extracting sources....')
        # return results
    
    def flatten_sources(self, sources):
        """Flatten array of arrays containing dictionaries into single array."""
        return list(chain(*sources))
    
    def get_combined_sources(self, sources):
        """Combine source_map and source_list from all sources into a single structure."""
        combined_source_map = {}
        combined_source_list = []
        combined_questions_list = []

        for source in sources:
            # Combine source_map
            if 'source_map' in source:
                combined_source_map.update(source['source_map'])
            
            # Combine source_list
            if 'source_list' in source:
                combined_source_list.extend(source['source_list'])
            
            if 'questions' in source:
                combined_questions_list.extend(source['questions'])

        return combined_source_map, combined_source_list, combined_questions_list


    def extract_matching_sources(self):
        """Extract and sort the top 5 sources across all source lists."""
        all_sources = self.flatten_sources(self.sources)
        # Filter sources with matching questions
        matching_sources = []
        synonym_sources = []
        for source in all_sources:
            if 'questions' in source and isinstance(source['questions'], list):
                if source['questions'][0].lower() == self.Question.lower():
                    matching_sources.append(source)
                else:
                    synonym_sources.append(source)
                    print('i am appending to synonym_sources')

        return matching_sources, synonym_sources
    
    def get_top_5_question_sources(self, matching_sources, limit=5):
        combined_source_map, combined_source_list, combined_questions_list = self.get_combined_sources(matching_sources)
        
        # Filter sources with score 50% and above
        filtered_sources = [source for source in combined_source_list if source.get('score', 0) >= 0.5]

        # Sort the source_list by score and get the top 3
        top_limit_matching = sorted(
            filtered_sources,
            key=lambda x: x.get('score', 0),
            reverse=True
        )[:limit]

        
        # Extract the corresponding source_map keys for the top 3 items
        top_limit_source_map_keys = {item['section_id']: combined_source_map[item['section_id']] for item in top_limit_matching if item['section_id'] in combined_source_map}

        # Prepare the final output structure
        top_limit_matching = [{
            'source_map': top_limit_source_map_keys,
            'source_list': top_limit_matching
        }]

        return top_limit_matching

    def get_top_5_synonym_sources(self, synonym_sources, limit=5):
        combined_source_map, combined_source_list, combined_questions_list = self.get_combined_sources(synonym_sources)
        
        # Filter sources with score 50% and above
        filtered_sources = [source for source in combined_source_list if source.get('score', 0) >= 0.5]


        # Sort the source_list by score and get the top 3
        top_limit_matching = sorted(
            filtered_sources,
            key=lambda x: x.get('score', 0),
            reverse=True
        )[:limit]

        
        # Extract the corresponding source_map keys for the top 3 items
        top_limit_source_map_keys = {item['section_id']: combined_source_map[item['section_id']] for item in top_limit_matching if item['section_id'] in combined_source_map}

        # Prepare the final output structure
        top_limit_matching = [{
            'source_map': top_limit_source_map_keys,
            'source_list': top_limit_matching
        }]

        return top_limit_matching

    def get_response(self):
        matching_result = []
        synonym_result = []
        matching_sources, synonym_sources = self.extract_matching_sources()
        matching_result.append(self.get_top_5_question_sources(matching_sources, 5))
        print('extracting top for synonyms...: ', len(synonym_sources))
        synonym_result.append(self.get_top_5_synonym_sources(synonym_sources, 3))
        
        final_result = matching_result + synonym_result
        flattened_data = self.flatten_sources(final_result)
        # return final_result
        combined_source_map, combined_source_list, questions = self.get_combined_sources(flattened_data)
        return [{
            'source_map': combined_source_map,
            'source_list': combined_source_list
        }]





# Usage example (to be placed in the appropriate method):
if __name__ == "__main__":
    import time

    project_ids = [
        '8cae6c9c-cf8f-4eb2-bb93-a0bb3d5c1e72',
        '1a0e8f66-5d87-41f9-bb93-1cb655f4a0f9',
        '1c31374d-4a4a-4a39-a0ee-372d99575ceb'
    ]

    sub_question_1 = '2010 Report on Regulators’ Use of Standards' #api international
    sub_question_2 = 'Deep learning with keras?' #keras
    sub_question_3 = 'VLCC VESSELS Conversion' #scope of work

    qmp_source_extractor = QMPSourceExtractor()

    # Start timing
    start_time = time.time()

    sources = asyncio.run(qmp_source_extractor.extract_sources(project_ids, sub_question_2))
    final_result = qmp_source_extractor.get_response()

    # End timing
    end_time = time.time()
    elapsed_time = end_time - start_time

    # Print the elapsed time
    print(f"Time taken to get the response: {elapsed_time:.2f} seconds")

    with open('lab.json', 'w', encoding='utf-8') as f:
        json.dump(final_result, f, indent=4)
