import json, os
from models import Category, Discipline


# Get the path to the parent directory
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
print('parent_dir:', parent_dir)

# Load environment variables from env.json file in the parent directory
env_file_path = os.path.join(parent_dir, 'env.json')
print('env_file_path:', env_file_path)
# Load environment variables from env.json file
with open(env_file_path, 'r') as f:
    env_data = json.load(f)

specs_dir_path = os.path.join(parent_dir, env_data.get("SPECS_DIR", ""))
engr_doc_keywords_path = os.path.join(specs_dir_path, env_data.get("ENGR_DOCS", ""))

with open(engr_doc_keywords_path, 'r') as f:
    engr_doc_data = json.load(f)

# Discipline codes
discipline_codes = {
    "AR": "Architectural",
    "CV": "Civil & Structural",
    "EL": "Electrical",
    "CI": "Control & Instrumentation",
    "ME": "Mechanical & Piping",
    "MT": "Material & Corrosion",
    "MD": "Multi-discipline",
    "OR": "Operations Readiness & Assurance",
    "PR": "Process",
    "HS": "HSE",
    "RE": "Reservoir Simulation",
    "GM": "Geo-Modeling",
    "WD": "Well Delivery",
    "GT": "Geo-Mechanics and Testing",
    "SV": "Surveying / Topography",
    "QC": "Quality Assurance and Control",
    "PM": "Project Management"
}

# Assuming you have a Discipline model with methods like `get_by` and `create`

for shortcode, name in discipline_codes.items():
    # Check if the discipline already exists in the database
    discipline = Discipline.get_by(name=name, shortcode=shortcode)
    if not discipline:
        # If discipline doesn't exist, create a new one
        discipline = Discipline.create(name=name, shortcode=shortcode)

# Insert data
for discipline_name, categories in engr_doc_data.items():
    get_discipline = Discipline.get_by(name=discipline_name)
    if len(get_discipline) > 0:
        discipline_id = get_discipline[0]['id']
        for category_name, category_data in categories.items():
            # Create category
            category = Category.create(
                name=category_name,
                feed=category_data.get("FEED", "false") == "true",
                detail=category_data.get("DETAIL", "false") == "true",
                vendor=category_data.get("VENDOR", "false") == "true",
                discipline_id=discipline_id,
                similar=json.dumps(category_data.get("similar", [])),
                criteria=json.dumps(category_data.get("criteria", []))
            )
