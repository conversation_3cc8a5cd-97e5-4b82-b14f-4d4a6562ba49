from extensions import celery
#from services.file_uploader import Fileuploader
from services.file_uploader_v2 import Fileuploader
from flask import current_app
from cbp.pipelines.bid_evaluation.technical_bid import TechnicalBidProcessor
from models import CbpRequirementTechnicalReport, EquipmentPackage
from socket_instance import socket_instance
import asyncio
import json

@celery.task(bind=True, max_retries=3, name="task_celery.cbp_report.cbp_report")
def cbp_report(self, request_id):
    try:
        socket_manager = socket_instance.get_instance()
        socket_manager.emit_to_client(
            request_id,
            'test_event',
            {'message': 'Test from CBP Celery'},
            namespace='/cbp_report'
        )
        ##process technical report
        data = CbpRequirementTechnicalReport.get_single(request_id)
        bids_ids = json.loads(data["report_bid_ids"])
        equipment_ids = json.loads(data["report_equipment_technical"])
        user_id = data["user_id"]
        equipment_list = EquipmentPackage.get_equipment_with_specs(user_id=user_id, equipment_package_ids=equipment_ids)
        processor = TechnicalBidProcessor(socket_manager=socket_manager, requirement_id=request_id)
        technical_report = asyncio.run(processor.process_multiple_bids(bid_ids=bids_ids, equipment_list=equipment_list))

        def add_event(requirement_id, event_name, data):
            """Emit socket event to client for progress tracking"""
            print(f"Adding event {event_name} to room {requirement_id}")
            if socket_manager:  # Use socket_manager instead of self.socket_manager
                socket_manager.emit_to_client(requirement_id, event_name, data)

        event_name = "completed_event"
        data = {
            "request_id": request_id,
            "event_type": "cbp_final_report",
            "data": [{"technical_report": technical_report}]
        }
        add_event(request_id, event_name, data)  # Pass the required parameters to add_event
        print(f"Final report: {technical_report}")
    except Exception as e:
        print(f"Error during file upload: {e}")
        return str(e)