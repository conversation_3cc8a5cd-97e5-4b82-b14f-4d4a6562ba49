"""
GRAPH RAG QUERY


Step 1. Extract relevant entity from the query
Step 2. Extract relevant relations from the query
Step 3. Query the graph database with the extracted entity and relations
Step 4. Pass subgraph to LLM to answer the query
Step 5. Return the answer

"""

from typing import List, Dict, Any, Optional, Tuple
from pydantic import BaseModel, Field
import json
from langchain_groq import ChatGroq
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import PromptTemplate
from dgraph import DgraphService


class EntityRelation(BaseModel):
    """Entity and relation extracted from a query."""
    entities: List[str] = Field(description="List of entities extracted from the query")
    relations: List[str] = Field(description="List of relations extracted from the query")
    entity_types: List[str] = Field(description="Types of entities to look for in the graph")


class KnowledgeGraphQuery:
    """
    Class for querying a knowledge graph with natural language.
    """
    
    def __init__(self, llm, dgraph_service: DgraphService):
        """
        Initialize the Knowledge Graph Query.
        
        Args:
            llm: Language model for extracting entities and answering queries
            dgraph_service: DgraphService instance for querying the graph
        """
        self.llm = llm
        self.dgraph_service = dgraph_service
        
        # Initialize the entity-relation extraction parser
        self.entity_relation_parser = PydanticOutputParser(pydantic_object=EntityRelation)
        
        # Create the entity-relation extraction prompt
        self.entity_relation_prompt = PromptTemplate(
            template="""
            You are an expert in extracting entities and relations from natural language queries.
            
            Given the following query, extract:
            1. The main entities mentioned
            2. The relations or connections being asked about
            3. The types of entities to look for in the graph
            
            Query: {query}
            
            {format_instructions}
            """,
            input_variables=["query"],
            partial_variables={"format_instructions": self.entity_relation_parser.get_format_instructions()}
        )
        
    def extract_entities_relations(self, query: str) -> EntityRelation:
        """
        Extract entities and relations from the query.
        
        Args:
            query: Natural language query
            
        Returns:
            EntityRelation object containing extracted entities and relations
        """
        # Format the prompt with the query
        prompt = self.entity_relation_prompt.format(query=query)
        
        # Get the response from the LLM
        response = self.llm.invoke(prompt)
        
        # Parse the response into an EntityRelation object
        try:
            return self.entity_relation_parser.parse(response.content)
        except Exception as e:
            print(f"Error parsing entity-relation extraction response: {e}")
            # Fallback to empty extraction
            return EntityRelation(entities=[], relations=[], entity_types=[])
    
    def build_graph_query(self, entity_relation: EntityRelation) -> str:
        """
        Build a DQL query based on extracted entities and relations.
        
        Args:
            entity_relation: EntityRelation object containing extracted entities and relations
            
        Returns:
            DQL query string
        """
        # Build the type filter for entities
        type_filters = []
        for entity_type in entity_relation.entity_types:
            type_filters.append(f'type({entity_type})')
        
        # Combine type filters with OR
        type_filter_clause = ' OR '.join(type_filters) if type_filters else "has(dgraph.type)"
        
        # Build the main query without relying on text search
        main_query = f"""
        {{
          # Get all document containers (limited to 50)
          documents(func: type(DocumentContainer), first: 50) {{
            uid
            dgraph.type
            content
            metadata
            page
            section
          }}
          
          # Get entities by type
          entities(func: has(dgraph.type), first: 50) @filter({type_filter_clause}) {{
            uid
            dgraph.type
            name
            content
            metadata
            
            # Get document containers
            source_extracted_from {{
              uid
              dgraph.type
              content
              metadata
              page
              section
            }}
            
            # Get other connected nodes
            expand(_all_) {{
              uid
              dgraph.type
              name
              content
              metadata
            }}
          }}
        }}
        """
        
        return main_query
    
    def query_graph(self, dql_query: str) -> Dict[str, Any]:
        """
        Query the graph database with the DQL query.
        
        Args:
            dql_query: DQL query string
            
        Returns:
            Query results
        """
        try:
            return self.dgraph_service.execute_query(dql_query)
        except Exception as e:
            print(f"Error querying graph: {e}")
            return {}
    
    def format_subgraph_for_llm(self, subgraph: Dict[str, Any]) -> str:
        """
        Format the subgraph for the LLM.
        
        Args:
            subgraph: Subgraph data from the query
            
        Returns:
            Formatted subgraph as a string
        """
        return json.dumps(subgraph, indent=2)
    
    def generate_answer(self, query: str, subgraph: str) -> str:
        """
        Generate an answer to the query based on the subgraph.
        
        Args:
            query: Original natural language query
            subgraph: Formatted subgraph data
            
        Returns:
            Generated answer
        """
        prompt = f"""
        You are an expert in analyzing graph data and answering questions.
        
        Given the following knowledge graph data and a query, provide a detailed answer based only on the information in the graph.
        
        Query: {query}
        
        Knowledge Graph Data:
        {subgraph}
        
        Important instructions:
        1. Focus on the document content found in DocumentContainer nodes, which contain the original text.
        2. Pay attention to relationships between entities and documents (source_extracted_from).
        3. Provide specific citations from the documents when possible, including page numbers if available.
        4. If the data doesn't contain enough information to answer the query, state that clearly.
        
        Answer the query based on the knowledge graph data.
        """
        
        response = self.llm.invoke(prompt)
        return response.content
    
    
    def query(self, query: str) -> str:
        """
        Process a natural language query against the knowledge graph.
        
        Args:
            query: Natural language query
            
        Returns:
            Answer to the query
        """
        # Step 1 & 2: Extract entities and relations from the query
        entity_relation = self.extract_entities_relations(query)
        
        # Step 3: Build and execute the graph query
        dql_query = self.build_graph_query(entity_relation)
        subgraph = self.query_graph(dql_query)
        
        # Step 4: Format the subgraph for the LLM
        formatted_subgraph = self.format_subgraph_for_llm(subgraph)
        
        # Step 5: Generate and return the answer
        return self.generate_answer(query, formatted_subgraph)
    
    


# Example usage
if __name__ == "__main__":
    import os
    import json
    
    # Load environment variables
    parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    env_file_path = os.path.join(parent_dir, 'env.json')
    with open(env_file_path, 'r') as f:
        env_data = json.load(f)
    os.environ["GROQ_API_KEY"] = env_data.get("GROQ_API_KEY", "")
    
    # Initialize LLM
    llm = ChatGroq(
        temperature=0, 
        model_name="llama-3.1-8b-instant", 
        api_key=os.environ["GROQ_API_KEY"]
    )
    
    # Initialize DgraphService
    dgraph_service = DgraphService(hosts=["localhost"], port=9080)
    
    # Initialize KnowledgeGraphQuery
    kg_query = KnowledgeGraphQuery(llm, dgraph_service)
    
    # Example query
    query = "The delivery date for mill reline equipment"
    
    try:
        # Process the query
        entity_relation = kg_query.extract_entities_relations(query)
        print(f"Entity Relation: {entity_relation}")
        
        dql_query = kg_query.build_graph_query(entity_relation)
        print(f"DQL Query: {dql_query}")
        
        subgraph = kg_query.query_graph(dql_query)
        print(f"Subgraph: {subgraph}")
        
        formatted_subgraph = kg_query.format_subgraph_for_llm(subgraph)
        print(f"Formatted Subgraph: {formatted_subgraph}")
        
        answer = kg_query.generate_answer(query, formatted_subgraph)
        
        
        
        print(f"Query: {query}")
        print(f"Answer: {answer}")
    except Exception as e:
        print(f"Error processing query: {e}")
    finally:
        # Clean up resources
        dgraph_service.close()