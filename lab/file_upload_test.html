<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Upload Socket Test</title>
    <script src="https://cdn.socket.io/4.5.4/socket.io.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        input, button {
            padding: 8px 12px;
            border-radius: 4px;
        }
        input {
            border: 1px solid #ccc;
            flex-grow: 1;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        #messages {
            max-height: 400px;
            overflow-y: auto;
        }
        .message {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .info {
            background-color: #e7f3fe;
            border-left: 4px solid #2196F3;
        }
        .success {
            background-color: #ddffdd;
            border-left: 4px solid #4CAF50;
        }
        .error {
            background-color: #ffdddd;
            border-left: 4px solid #f44336;
        }
        .progress-container {
            width: 100%;
            background-color: #f1f1f1;
            border-radius: 4px;
            margin-top: 10px;
        }
        .progress-bar {
            height: 20px;
            background-color: #4CAF50;
            border-radius: 4px;
            text-align: center;
            color: white;
            line-height: 20px;
        }
        .timestamp {
            color: #666;
            font-size: 0.8em;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>File Upload Socket Test</h1>
            <p>Enter a request ID to connect to the file upload socket and monitor progress messages.</p>
            
            <div class="controls">
                <input type="text" id="serverUrl" placeholder="Server URL" value="http://localhost:5130">
                <input type="text" id="requestId" placeholder="Enter Request ID">
                <button id="connectBtn">Connect</button>
                <button id="disconnectBtn" disabled>Disconnect</button>
            </div>
        </div>
        
        <div class="card">
            <h2>Connection Status</h2>
            <div id="status" class="message info">Not connected</div>
            
            <h2>Overall Progress</h2>
            <div class="progress-container">
                <div id="progressBar" class="progress-bar" style="width:0%">0%</div>
            </div>
        </div>
        
        <div class="card">
            <h2>Messages</h2>
            <button id="clearBtn">Clear Messages</button>
            <div id="messages"></div>
        </div>
    </div>

    <script>
        // DOM elements
        const serverUrlInput = document.getElementById('serverUrl');
        const requestIdInput = document.getElementById('requestId');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const clearBtn = document.getElementById('clearBtn');
        const statusDiv = document.getElementById('status');
        const messagesDiv = document.getElementById('messages');
        const progressBar = document.getElementById('progressBar');
        
        // Socket variables
        let socket = null;
        let isConnected = false;
        
        // Connect to socket
        connectBtn.addEventListener('click', () => {
            const serverUrl = serverUrlInput.value.trim();
            const requestId = requestIdInput.value.trim();
            
            if (!serverUrl) {
                addMessage('Please enter a server URL', 'error');
                return;
            }
            
            if (!requestId) {
                addMessage('Please enter a request ID', 'error');
                return;
            }
            
            // Disconnect existing socket if any
            if (socket) {
                socket.disconnect();
            }
            
            // Update UI
            statusDiv.textContent = 'Connecting...';
            statusDiv.className = 'message info';
            connectBtn.disabled = true;
            
            // Connect to socket
            try {
                socket = io(`${serverUrl}/cbp`, {
                    transports: ['websocket', 'polling'],
                    reconnection: true,
                    reconnectionAttempts: 5,
                    reconnectionDelay: 1000
                });
                
                // Socket event handlers
                socket.on('connect', () => {
                    isConnected = true;
                    statusDiv.textContent = 'Connected to server';
                    statusDiv.className = 'message success';
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                    
                    addMessage('Connected to server', 'info');
                    
                    // Join room with request ID
                    socket.emit('join', { request_id: requestId });
                    addMessage(`Joining room with request ID: ${requestId}`, 'info');
                });
                
                socket.on('disconnect', () => {
                    isConnected = false;
                    statusDiv.textContent = 'Disconnected from server';
                    statusDiv.className = 'message error';
                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                    
                    addMessage('Disconnected from server', 'error');
                });
                
                socket.on('connect_error', (error) => {
                    statusDiv.textContent = `Connection error: ${error.message}`;
                    statusDiv.className = 'message error';
                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                    
                    addMessage(`Connection error: ${error.message}`, 'error');
                });
                
                // Listen for progress messages
                socket.on('progress_message', (data) => {
                    console.log('Received progress message:', data);
                    
                    let messageType = 'info';
                    if (data.data && data.data.status) {
                        if (data.data.status === 'success') messageType = 'success';
                        if (data.data.status === 'error') messageType = 'error';
                    }
                    
                    // Extract message
                    let message = '';
                    if (data.data && data.data.message) {
                        message = data.data.message;
                    } else if (data.message) {
                        message = data.message;
                    } else {
                        message = JSON.stringify(data);
                    }
                    
                    addMessage(message, messageType);
                    
                    // Update progress bar if progress information is available
                    if (data.data && data.data.progress !== undefined) {
                        updateProgressBar(data.data.progress);
                    }
                });
                
                // Listen for completed event
                socket.on('completed_event', (data) => {
                    console.log('Received completed event:', data);
                    
                    let messageType = 'success';
                    if (data.data && data.data.status) {
                        if (data.data.status === 'error') messageType = 'error';
                        if (data.data.status === 'partial') messageType = 'info';
                    }
                    
                    // Extract message
                    let message = '';
                    if (data.data && data.data.message) {
                        message = data.data.message;
                    } else if (data.message) {
                        message = data.message;
                    } else {
                        message = JSON.stringify(data);
                    }
                    
                    addMessage(`✅ COMPLETED: ${message}`, messageType);
                    
                    // Show failed files if any
                    if (data.data && data.data.failed_files_messages && data.data.failed_files_messages.length > 0) {
                        addMessage('Failed files:', 'error');
                        data.data.failed_files_messages.forEach(failedMsg => {
                            addMessage(failedMsg, 'error');
                        });
                    }
                    
                    // Update progress bar to 100% on completion
                    updateProgressBar(100);
                });
                
                // Listen for error event
                socket.on('error_event', (data) => {
                    console.log('Received error event:', data);
                    
                    let message = '';
                    if (data.data && data.data.message) {
                        message = data.data.message;
                    } else if (data.message) {
                        message = data.message;
                    } else {
                        message = JSON.stringify(data);
                    }
                    
                    addMessage(`❌ ERROR: ${message}`, 'error');
                });
                
                // Listen for test event
                socket.on('test_event', (data) => {
                    console.log('Received test event:', data);
                    
                    let message = '';
                    if (data.message) {
                        message = data.message;
                    } else {
                        message = JSON.stringify(data);
                    }
                    
                    addMessage(`Test event: ${message}`, 'info');
                });
                
            } catch (error) {
                statusDiv.textContent = `Error: ${error.message}`;
                statusDiv.className = 'message error';
                connectBtn.disabled = false;
                
                addMessage(`Error: ${error.message}`, 'error');
            }
        });
        
        // Disconnect from socket
        disconnectBtn.addEventListener('click', () => {
            if (socket) {
                socket.disconnect();
                socket = null;
                
                statusDiv.textContent = 'Disconnected';
                statusDiv.className = 'message info';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                
                addMessage('Manually disconnected from server', 'info');
            }
        });
        
        // Clear messages
        clearBtn.addEventListener('click', () => {
            messagesDiv.innerHTML = '';
            updateProgressBar(0);
        });
        
        // Helper function to add a message to the messages div
        function addMessage(message, type = 'info') {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const messageContent = document.createElement('div');
            messageContent.textContent = message;
            messageDiv.appendChild(messageContent);
            
            const timestamp = document.createElement('div');
            timestamp.className = 'timestamp';
            timestamp.textContent = new Date().toLocaleTimeString();
            messageDiv.appendChild(timestamp);
            
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        // Helper function to update the progress bar
        function updateProgressBar(progress) {
            const percentage = Math.min(Math.max(progress, 0), 100);
            progressBar.style.width = `${percentage}%`;
            progressBar.textContent = `${Math.round(percentage)}%`;
        }
    </script>
</body>
</html>