from services.faiss_embedding import FaissEmbedding
from services.fast_apis_service import FastAPIs
import asyncio
import re

class OrderOfPrecedence:
    def __init__(self, requirement_id, project_id):
        self.faiss_processor = FaissEmbedding()
        self.fast_apis = FastAPIs()
        self.log = []
        self.requirement_id = requirement_id
        self.project_id = project_id

    async def meth_1(self):
        query = """
            Order of Precedence, 
            Hierarchy of Standards, 
            Priority of Codes, 
            Standards Ranking, 
            Regulation Hierarchy, 
            Code Precedence, 
            Standard Order, 
            Compliance Priority, 
            Regulatory Sequence, 
            Precedence Rules, 
            Standards Preference, 
            Policy Hierarchy, 
            Legislation Sequence, 
            Order of Priority, 
            Compliance Order, 
            Standards Hierarchy.
        """
        cosine_similar_result = await self.faiss_processor.search([query], self.project_id)
        tasks = [
            self.review_order_of_precedence(data) 
            for data in cosine_similar_result
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)

        for result in results:
            if result:
                await self.extract_valid_oop(result, self.log)

    async def review_order_of_precedence(self, data):
        # Validate data structure
        if not isinstance(data, dict) or 'content' not in data:
            print(f"Invalid data structure for order of precedence document :")
            return None
        
        if not data['content']:
            print(f"Empty content for order of precedence document")
            return None
                    
        prompt = '''
            <ENGR_DOCUMENT>
                {content}
            </ENGR_DOCUMENT>
            <TASK>
                You are an expert **EPC (Engineering, Procurement, and Construction) Project Engineer** assistant. Your task is to analyze the given document content and determine if it discusses **Order of Precedence**.  
                **Order of Precedence** refers to the ranking, hierarchy, or priority of standards, codes, regulations, policies, or compliance frameworks in engineering or regulatory contexts.

                Evaluate the document for direct or strongly implied references to:
                - **Hierarchy of Standards**  
                - **Priority of Codes or Regulations**  
                - **Compliance Order and Ranking**  
                - **Legislation and Regulatory Sequence**  
                - **Standards Preference and Precedence Rules**  
            </TASK>
            <IMPORTANT>
                - If the document **explicitly** discusses order of precedence, return **"<STATUS>YES</STATUS>"**.
                - If the document **implicitly** suggests order of precedence, return **"<STATUS>MAYBE</STATUS>"**.
                - If the document does **not** discuss order of precedence, return **"<STATUS>NO</STATUS>"**.
                - Always return the original document content in the **<CONTENT>** tag.
                - Strictly ensure the result follows the output format requested.
            </IMPORTANT>
            <OUTPUT_FORMAT>
                Return only the following structured output:
                <STATUS>[YES/NO/MAYBE]</STATUS>
                <CONTENT>[original document content]</CONTENT>
            </OUTPUT_FORMAT>
        '''
        formatted_prompt = prompt.format(content=data['content'])
        # print('this is formatted response: ', formatted_prompt)
        try:
            completion = await self.fast_apis.generate_completion(
                [{"role": "user", "content": formatted_prompt}],
                'groq',
                'llama3-70b-8192'
            )
            if completion:
                return completion

        except Exception as e:
            print(e)
            return None
    
    async def extract_valid_oop(self, document_string, log): #extract order of precedence info
        oop_status_match = re.search(r'<STATUS>\s*(.*?)\s*</STATUS>', document_string, re.DOTALL)

        order_of_precedence_status = oop_status_match.group(1).strip() if oop_status_match else None
        if order_of_precedence_status and order_of_precedence_status != 'NO':
            log['order_of_precedence'].append(order_of_precedence_status)