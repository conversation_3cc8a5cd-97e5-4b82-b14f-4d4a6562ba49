import gevent
from gevent import monkey
monkey.patch_all()

import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor
import json
# Assuming these imports are correct based on your environment
from cbp.services.embeddings.vectore_database_service import VectorDatabaseService
from cbp.services.llm.claude import AnthropicService # You used ClaudeService later, ensure consistency
from services.claude_ai_service import ClaudeService # This was also in your original code
from services.faiss_embedding import FaissEmbedding
# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Vector Search Service Layer
class VectorSearchLayer:
    def __init__(self):
        self.vector_service = FaissEmbedding()
    
    async def search(self, query: str, request_id: str, num_results: int = 50) -> Dict[str, Any]:
        """Execute vector search for a query."""
        try:
            results = await self.vector_service._execute_gpu_search(
                search_queries=[query], 
                request_id=request_id, 
                num_results=num_results
            )
            return {"success": True, "results": results}
        except Exception as e:
            logger.error(f"Vector search failed: {str(e)}")
            return {"success": False, "results": ""}
        
    def filter_top_k_sources_by_median(self,sources, k=5, median_threshold=0.9):
        import statistics
        """
        Filters sources based on 90% of the median score and returns top-k by score.

        Args:
            sources (list): List of dicts with a 'score' key.
            k (int): Max number of sources to return.

        Returns:
            list: Top-k filtered and sorted sources.
        """
        valid_scores = [s.get('score', 0) for s in sources if s.get('score') is not None]

        if not valid_scores:
            return []

        median_score = statistics.median(valid_scores)
        threshold = median_threshold * median_score

        filtered = [s for s in sources if s.get('score', 0) >= threshold]

        return sorted(filtered, key=lambda s: s.get('score', 0), reverse=True)[:k]
    
    def search_sync(self, query: str, request_id: str, num_results: int = 40) -> Dict[str, Any]:
        """Execute vector search for a query."""
        try:
            sources = self.vector_service.search_sync(
                query=[query], 
                request_id=request_id, 
                k=num_results
            )

            final_results = [{
                        "source_map": {},
                        "source_list": [],
                        "questions": [query]
                    }]
            sources_list = []
            source_map = {}
            for i, source in enumerate(sources[0]):
                source_map[i] = source[0]
                sources_list.append({
                    "id": i,
                    "content": source[0],
                    "score": source[1],
                    "section_id": i,
                    "source": source[2],
                    "page_number": source[3]
                })
            final_results[0]['source_map'] = source_map
            final_results[0]['source_list'] = sources_list
            sources = final_results
            filtered = self.filter_top_k_sources_by_median(sources[0]['source_list'], k=30, median_threshold=0.65)
            return {"success": True, "results": filtered}
        except Exception as e:
            logger.error(f"Vector search failed: {str(e)}")
            return {"success": False, "results": ""}

# AI Processing Service Layer
class AIProcessingLayer:
    def __init__(self):
        # Ensure correct ClaudeService is used
        self.llm_service =  ClaudeService(claude_keys=['CLAUDE_API_CBP', 'CLAUDE_API_CBP'],default_key="CLAUDE_API_CBP")
    
    async def extract_prices(self, document: str, equipment_types: List[str]) -> Dict[str, List[Dict]]:
        """Extract equipment prices using the AI service."""
        prompt = self._construct_prompt(document, equipment_types)
        
        try:
            response = self.llm_service.generate_message_agent_sonnet_new_sync(
                messages=[{"role": "user", "content": prompt}],
                temperature=0.00001,
                max_tokens=4096
            )
           
            if not response or not response.content:
                logger.error("Failed to generate response from Claude")
                return {eq_type: [] for eq_type in equipment_types}
            
            completion = response.content[0].text
            extracted_data = json.loads(completion)
            
            # Validate all equipment types are present
            for eq_type in equipment_types:
                if eq_type not in extracted_data:
                    extracted_data[eq_type] = []
            
            return extracted_data
            
        except Exception as e:
            logger.error(f"Error extracting prices: {str(e)}")
            return {eq_type: [] for eq_type in equipment_types}
    
    def extract_prices_sync(self, document: str, equipment_types: List[str]) -> Dict[str, Dict]:
        """Extract equipment prices using the AI service synchronously."""
        prompt = self._construct_prompt(document, equipment_types)
        
        try:
            response = self.llm_service.generate_message_agent_sonnet_new_sync(
                messages=[{"role": "user", "content": prompt}],
                temperature=0.00001,
                max_tokens=4096
            )
            
            if not response or not response.content:
                logger.error("Failed to generate response from Claude")
                return {eq_type: {"main_price": None, "items": []} for eq_type in equipment_types}
        
            completion = response.content[0].text
            extracted_data = json.loads(completion)
            
            # Validate all equipment types are present and have correct structure
            for eq_type in equipment_types:
                if eq_type not in extracted_data:
                    extracted_data[eq_type] = {"main_price": None, "items": []}
                elif "main_price" not in extracted_data[eq_type]:
                    extracted_data[eq_type]["main_price"] = None
                elif "items" not in extracted_data[eq_type]:
                    extracted_data[eq_type]["items"] = []
            
            return extracted_data
            
        except Exception as e:
            logger.error(f"Error extracting prices: {str(e)}")
            return {eq_type: {"main_price": None, "items": []} for eq_type in equipment_types}

    def _construct_prompt(self, document: str, equipment_types: List[str]) -> str:
        """Construct the prompt for price extraction."""
        return f"""
        Extract ALL equipment items and their corresponding prices from the following text.
        Consider the following equipment types: {", ".join(equipment_types)}.
        
        For each equipment type, analyze the document and extract:
        1. The main equipment price (if available)
        2. ANY relevant items and prices, even if they are not explicitly labeled.
        
        Return ONLY a valid JSON object where:
        - Keys are the equipment types (exactly as listed above)
        - Values are objects with these keys:
          - 'main_price': Object containing:
            - 'price': Main equipment price value (must include currency if specified)
            - 'currency': Currency unit (USD, EUR, GBP, JPY etc.)
            - 'comment': Source context or pricing status
          - 'items': Array of objects with these keys:
            - 'item': Name/description of the item (extract ALL items found)
            - 'unit_price': Unit Price value (must include currency if specified)
            - 'currency': Currency unit (USD, EUR, GBP, JPY etc.)
            - 'comment': Source context of the price or pricing status
        
        If main price is not available, set main_price to null.
        For prices not available, use "N/A", "TBA", "Contact for Quote" etc.
        
        RETURN RESPONSE IN JSON FORMAT, NOTHING BEFORE OR AFTER DO NOT ADD ```json BEFORE OR AFTER
        Here are the documents to analyze:
        {document}
        """


# Report Generation Layer
class EquipmentReportGenerator:
    def __init__(self):
        self.llm_service = ClaudeService(claude_keys=['CLAUDE_API_CBP', 'CLAUDE_API_CBP'],default_key="CLAUDE_API_CBP")
    
    def generate_consolidated_report(self, bid_results: Dict[str, Dict[str, List[Dict[str, str]]]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Generate a consolidated report of equipment prices across multiple bids
        
        Args:
            bid_results (Dict): Nested dictionary with structure {bid_id: {equipment_id: {'name': str, 'bidder': str, 'items': List[Dict]}}}
            
        Returns:
            Dict: Dictionary with equipment_id as keys and consolidated data as values
        """
        consolidated_report = {}
        
        # Get all equipment IDs from the first bid (assuming all bids have the same equipment IDs)
        equipment_ids = set()
        for bid_id, bid_data in bid_results.items():
            for equipment_id in bid_data.keys():
                equipment_ids.add(equipment_id)
        
        # Process each equipment type
        for equipment_id in equipment_ids:
            logger.info(f"Generating report for equipment ID: {equipment_id}")
            
            # Collect all data for this equipment ID across all bids
            equipment_data_across_bids = {}
            for bid_id, bid_data in bid_results.items():
                if equipment_id in bid_data:
                    equipment_data = bid_data[equipment_id]
                    # Ensure main_price is structured correctly, even if None
                    main_price_info = equipment_data.get('main_price', {'price': None, 'currency': None, 'comment': None})
                    equipment_data_across_bids[bid_id] = {
                        'bidder': equipment_data.get('bidder', 'Unknown'),
                        'main_price': main_price_info,
                        'items': equipment_data.get('items', [])
                    }
            
            # If we have data for this equipment from multiple bids, generate a consolidated report
            if equipment_data_across_bids:
                consolidated_items = self.consolidate_equipment_items(equipment_id, equipment_data_across_bids)
                consolidated_report[equipment_id] = consolidated_items
            else:
                logger.warning(f"No data found for equipment ID: {equipment_id}")
                consolidated_report[equipment_id] = []
        
        return consolidated_report
    
    def consolidate_equipment_items(self, equipment_id: str, equipment_data_across_bids: Dict[str, Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Use LLM to identify common items across bids and consolidate their information,
        adding an 'other' field to indicate if an item is not the main equipment price.
        
        Args:
            equipment_id (str): The ID of the equipment being processed
            equipment_data_across_bids (Dict): Dictionary mapping bid_ids to dicts containing bidder, main_price and items
            
        Returns:
            List[Dict]: Consolidated data for this equipment
        """
        # Convert the data to a format that's easier for the LLM to process
        formatted_data = json.dumps(equipment_data_across_bids, indent=2)
        
        # Prepare prompt for LLM to consolidate items
        consolidation_prompt = [{
            "role": "user", 
            "content": f"""
            I have equipment pricing data from multiple bids for equipment ID: {equipment_id}.
            Please analyze this data and create a consolidated report that:
            
            1. Identifies common items across different bids.
            2. Standardizes item names where appropriate.
            3. Lists each item with their prices from each bid.
            4. Includes the main equipment price from each bid.
            
            CRITICAL: Price mixing between bids is strictly forbidden. Each price MUST be kept with its original bid_id and bidder. 
            DO NOT mix or swap prices between different bids under any circumstances, as this would invalidate the entire analysis.
            Ensure 100% accuracy in maintaining the correct price-to-bid relationships.
            
            Add a new field, "other": "YES" or "NO", to each price object
            Set "other" to "NO" if the item is the main equipment price for that bidder.
            set "other" to "NO" If item is under the main equipment
            Set "other" to "YES" if the item is not related to the equioment meaning it could be transportation,  taxes, services,maintance, miscellaneous, etc.
            
            Format the output as a JSON array where each object has:
            - item_name: The standardized name of the equipment item
            - prices: An array of objects containing bid_id, bidder, currency, unit_price, comment, and the new 'other' field.
            
            Example output format:
            [
              {{clear
              
                "item_name": "main_equipment_price",
                "prices": [
                  {{
                    "bid_id": "bid_id_1",
                    "bidder": "CITIC",
                    "unit_price": "4988371",
                    "currency": "USD",
                    "comment": "Main equipment price",
                    "other": "NO"
                  }},
                  {{
                    "bid_id": "bid_id_2",
                    "bidder": "NTC", 
                    "unit_price": "5100000",
                    "currency": "USD",
                    "comment": "Main equipment price",
                    "other": "NO"
                  }}
                ]
              }},
              {{
                "item_name": "Ball Mill 22x34 Liners",
                "prices": [
                  {{
                    "bid_id": "bid_id_1",
                    "bidder": "CITIC",
                    "unit_price": "150000",
                    "currency": "USD",
                    "comment": "Liner package",
                    "other": "NO" 
                  }},
                  {{
                    "bid_id": "bid_id_2",
                    "bidder": "NTC", 
                    "unit_price": "160000",
                    "currency": "USD",
                    "comment": "Liner package",
                    "other": "NO" 
                  }}
                ]
              }}
            ]
            
            Only return valid JSON without any explanation or additional text.
            If an item appears in only one bid, still include it in the report.
            Remember: Price mixing between bids is absolutely forbidden.
            
            Here is the data:
            {formatted_data}

NOTE: DO NOT ADD '```json before or after,ONLY JSON PLEASE
            """

        
        }]
        
        try:
            # Generate the consolidated report using LLM
            response = self.llm_service.generate_message_agent_sonnet_new_sync(
                messages=consolidation_prompt,
                temperature=0.00001,
                max_tokens=4096
            )
            print(f"Consolidation REPORT : {response}")
            completion = response.content[0].text
            logger.info(f"Generated consolidated report for equipment ID: {equipment_id}")
            consolidated_items = json.loads(completion)
            return consolidated_items
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON from LLM response for equipment ID: {equipment_id}. Error: {e}. Response: {completion[:500]}...") # Log partial response for debugging
            return []
        except Exception as e:
            logger.error(f"Error consolidating items for equipment ID {equipment_id}: {str(e)}")
            return []

# Main Commercial Bid Processor
class CommercialBidProcessor:
    def __init__(self, socket_manager=None, requirement_id=None):
        self.socket_manager = socket_manager
        self.requirement_id = requirement_id
        self.vector_search = VectorSearchLayer()
        self.ai_processor = AIProcessingLayer()
        self.report_generator = EquipmentReportGenerator()
        self.event_name = "CBP_REPORT"
    
    def add_event(self, requirement_id: str, event_name: str, data: Any):
        """Emit socket event to client for progress tracking."""
        if self.socket_manager:
            self.socket_manager.emit_to_client(requirement_id, event_name, data)
            logger.info(f"Emitted {event_name} event to room {requirement_id}")
    
    async def process_multiple_bids(self, bid_data: List[Dict[str, str]], equipment_list: List[Dict[str, str]]) -> Dict[str, Any]:
        """Process multiple bids asynchronously."""
        try:
            # Notify start of processing
            self.add_event(self.requirement_id, 'progress_message', {
                        'event_type': self.event_name,
                        'request_id': self.requirement_id,
                        'data': {
                            'status': 'Processing_commercial_report', 
                            'message': f"Starting commercial bid analysis for {len(bid_data)} bids..."
                        }
                    })
            
            # Process bids and extract prices
            results = {}
            equipment_names = [item["equipment_name"] for item in equipment_list]
            
            for i, bid_info in enumerate(bid_data, 1):
                bid_id = bid_info["bid_id"]
                bidder_name = bid_info["bid_name"] # Changed from bidder_name to bid_name to match example
                
                # Notify bid processing start
                self.add_event(self.requirement_id, f"progress_message", {
                    "message": f"Analyzing bid from {bidder_name} ({i}/{len(bid_data)})",
                    "current": i,
                    "total": len(bid_data)
                })
                
                # Build search query
                equipment_query = " OR ".join([f'"{eq_type}"' for eq_type in equipment_names])

                equipment_query = f"""({equipment_query})

price table pricing chart cost listing price sheet rate card quotation invoice
pricing comparison table equipment rates price breakdown cost analysis
prices listed price column cost column amount column quantity column "table row" "table cell" "line item"
"item" "description" "quantity" "rate" "amount" "subtotal" "total" "price each" "unit cost"
$50 $199.99 $1,500 $5,000 $10,000 $50,000 $100,000 $500,000 $1,000,000 $5,000,000
€120 €1,000 €5,000 €10,000 €50,000 €100,000 €500,000 €1,000,000
£75 £1,000 £5,000 £10,000 £50,000 £100,000 £500,000 £1,000,000
¥1500 ¥10,000 ¥50,000 ¥100,000 ¥500,000 ¥1,000,000 ¥5,000,000 ¥10,000,000
₹2500 ₹10,000 ₹50,000 ₹100,000 ₹500,000 ₹1,000,000 ₹5,000,000 ₹10,000,000
₩5000 ₩50,000 ₩100,000 ₩500,000 ₩1,000,000 ₩5,000,000 ₩10,000,000
A$200 A$1,000 A$5,000 A$10,000 A$50,000 A$100,000 A$500,000
C$150 C$1,000 C$5,000 C$10,000 C$50,000 C$100,000 C$500,000
"USD" "EUR" "JPY" "GBP" "INR" "KRW" "AUD" "CAD" "CHF" "CNY" "HKD" "SGD"
"per unit" "per piece" "per item" "per box" "per case" "per pallet" "per kg" "per lb" "per ton" "per set"
"units" "pieces" "items" "boxes" "cases" "pallets" "sets" "kits" "bundles" "packages" "systems"
"quantity: 100" "qty: 25" "qty: 50" "qty: 1000" "qty" "min. order" "minimum quantity" "max quantity"
"price without units" "currency not specified" "units not specified" "price varies" "price negotiable"
"call for pricing" "price on request" "contact for quote" "price TBD" "market price" "price upon inquiry"
"price range" "estimated cost" "approximate price" "price tier" "volume discount" "bulk pricing"
"MSRP" "list price" "net price" "retail price" "wholesale price" "dealer price" "distributor price"
"price without tax" "price with tax" "tax included" "plus tax" "excluding VAT" "including VAT"
"FOB price" "CIF price" "Ex-works price" "factory price" "landed cost" "delivered price

"""
                

                search_response = await self.vector_search.search(equipment_query, bid_id)
                
                if not search_response["success"]:
                    logger.error(f"Search failed for bid {bid_id}")
                    continue
                
                # Extract prices
                extracted_prices = await self.ai_processor.extract_prices(
                    search_response["results"],
                    equipment_names
                )
                
                # Format results
                results[bid_id] = {}
                for equipment in equipment_list:
                    eq_id = equipment["equipment_id"]
                    eq_name = equipment["equipment_name"]
                    results[bid_id][eq_id] = {
                        "name": eq_name,
                        "bidder": bidder_name,
                        "main_price": extracted_prices.get(eq_name, {}).get('main_price'), # Get main_price
                        "items": extracted_prices.get(eq_name, {}).get('items', []) # Get other items
                    }
            
            # Generate consolidated report
            self.add_event(self.requirement_id, f"progress_message", {
                "message": "Generating comprehensive bid comparison report...",
                "current": len(bid_data),
                "total": len(bid_data)
            })
            
            consolidated_report = self.report_generator.generate_consolidated_report(results)
            
            # Create equipment ID to name mapping
            equipment_names = {item["equipment_id"]: item["equipment_name"] for item in equipment_list}
            
            # Format final report
            final_report = {}
            for equipment_id, items in consolidated_report.items():
                final_report[equipment_id] = {
                    "equipment": equipment_id,
                    "equipment_name": equipment_names.get(equipment_id, "Unknown Equipment"),
                    "data": items
                }
            
            # Notify completion
            self.add_event(self.requirement_id, f"{self.event_name}_COMPLETE", {
                "message": "Commercial bid analysis completed successfully",
                "success": True
            })
            
            return {"success": True, "results": final_report}
            
        except Exception as e:
            logger.error(f"Error in process_multiple_bids: {str(e)}")
            self.add_event(self.requirement_id, f"{self.event_name}_ERROR", {
                "message": f"Error processing bids: {str(e)}",
                "success": False
            })
            return {"success": False, "results": {}}
    

    def process_multiple_bids_sync(self, bid_data: List[Dict[str, str]], equipment_list: List[Dict[str, str]]) -> Dict[str, Any]:
        """Process multiple bids synchronously."""
        try:
            # Notify start of processing
            self.add_event(self.requirement_id, 'progress_message', {
                        'event_type': self.event_name,
                        'request_id': self.requirement_id,
                        'data': {
                            'status': 'Processing_commercial_report', 
                            'message': f"Starting commercial bid analysis for {len(bid_data)} bids..."
                        }
                    })
            

            # Process bids and extract prices
            results = {}
            equipment_names_list = [item["equipment_name"] for item in equipment_list] # Renamed to avoid confusion with dict later

            equipment_query1 = f"""({" OR ".join([f'"{eq_type}"' for eq_type in equipment_names_list])})


                    price table pricing chart cost listing price sheet rate card quotation invoice
                    pricing comparison table equipment rates price breakdown cost analysis
                    prices listed price column cost column amount column quantity column "table row" "table cell" "line item"
                    "item" "description" "quantity" "rate" "amount" "subtotal" "total" "price each" "unit cost"
                    $50 $199.99 $1,500 $5,000 $10,000 $50,000 $100,000 $500,000 $1,000,000 $5,000,000
                    €120 €1,000 €5,000 €10,000 €50,000 €100,000 €500,000 €1,000,000
                    £75 £1,000 £5,000 £10,000 £50,000 £100,000 £500,000 £1,000,000
                    ¥1500 ¥10,000 ¥50,000 ¥100,000 ¥500,000 ¥1,000,000 ¥5,000,000 ¥10,000,000
                    ₹2500 ₹10,000 ₹50,000 ₹100,000 ₹500,000 ₹1,000,000 ₹5,000,000 ₹10,000,000
                    ₩5000 ₩50,000 ₩100,000 ₩500,000 ₩1,000,000 ₩5,000,000 ₩10,000,000
                    A$200 A$1,000 A$5,000 A$10,000 A$50,000 A$100,000 A$500,000
                    C$150 C$1,000 C$5,000 C$10,000 C$50,000 C$100,000 C$500,000
                    "USD" "EUR" "JPY" "GBP" "INR" "KRW" "AUD" "CAD" "CHF" "CNY" "HKD" "SGD"
                    "per unit" "per piece" "per item" "per box" "per case" "per pallet" "per kg" "per lb" "per ton" "per set"
                    "units" "pieces" "items" "boxes" "cases" "pallets" "sets" "kits" "bundles" "packages" "systems"
                    "quantity: 100" "qty: 25" "qty: 50" "qty: 1000" "qty" "min. order" "minimum quantity" "max quantity"
                    "price without units" "currency not specified" "units not specified" "price varies" "price negotiable"
                    "call for pricing" "price on request" "contact for quote" "price TBD" "market price" "price upon inquiry"
                    "price range" "estimated cost" "approximate price" "price tier" "volume discount" "bulk pricing"
                    "MSRP" "list price" "net price" "retail price" "wholesale price" "dealer price" "distributor price"
                    "price without tax" "price with tax" "tax included" "plus tax" "excluding VAT" "including VAT"
                    "FOB price" "CIF price" "Ex-works price" "factory price" "landed cost" "delivered price

"""
            # equipment_query = f"""({equipment_names_list})
            
            # equipment pricing cost quotation bid price list
            # unit price total cost capital cost spare parts cost
            # commissioning cost operational cost maintenance cost
            # price breakdown cost analysis pricing details
            # equipment components pricing spare parts pricing
            # line item pricing itemized costs detailed pricing
            # project requirements pricing compliance pricing
            # fully priced items complete pricing breakdown
            # """

            # equipment_query = """
            # {equipment_names}
            # Price:
            # "Cost of equipment and all necessary components, including spare parts (commissioning, 1 year of operation, capital).The bidder must inform the prices considered for the equipment 
            # and the spare parts.All line items must be fully priced. Spares pricing must be in accordance with the Project Requirements.
            # """
            for i, bid_info in enumerate(bid_data, 1):
                bid_id = bid_info["bid_id"]
                bidder_name = bid_info["bidder_name"] # Using bidder_name from input data
                
                # Notify bid processing start
                self.add_event(self.requirement_id, f"progress_message", {
                    "message": f"Analyzing bid from {bidder_name} ({i}/{len(bid_data)})",
                    "current": i,
                    "total": len(bid_data)
                })
                
                # Build search query using the equipment_names_list
                search_query = " OR ".join([f'"{eq_type}"' for eq_type in equipment_names_list])
                final_search_query = f"({search_query}) {equipment_query1}" # Combine with the detailed price terms

                search_response = self.vector_search.search_sync(final_search_query, bid_id)
                
                if not search_response["success"]:
                    logger.error(f"Search failed for bid {bid_id}")
                    continue
                
                # Extract prices
                extracted_prices = self.ai_processor.extract_prices_sync(
                    search_response["results"],
                    equipment_names_list # Pass the list of equipment names
                )
                
                # Format results
                results[bid_id] = {}
                for equipment in equipment_list:
                    eq_id = equipment["equipment_id"]
                    eq_name = equipment["equipment_name"]
                    
                    # Ensure main_price and items are extracted correctly from AI output
                    extracted_for_eq = extracted_prices.get(eq_name, {"main_price": None, "items": []})
                    
                    results[bid_id][eq_id] = {
                        "name": eq_name,
                        "bidder": bidder_name,
                        "main_price": extracted_for_eq.get('main_price'),
                        "items": extracted_for_eq.get('items', [])
                    }
            
            # Generate consolidated report
            self.add_event(self.requirement_id, f"progress_message", {
                "message": "Generating comprehensive bid comparison report...",
                "current": len(bid_data),
                "total": len(bid_data)
            })
            
            consolidated_report = self.report_generator.generate_consolidated_report(results)
            
            # Create equipment ID to name mapping
            equipment_names_map = {item["equipment_id"]: item["equipment_name"] for item in equipment_list}
            
            # Format final report
            final_report = {}
            for equipment_id, items in consolidated_report.items():
                final_report[equipment_id] = {
                    "equipment": equipment_id,
                    "equipment_name": equipment_names_map.get(equipment_id, "Unknown Equipment"),
                    "data": items
                }
            
            # Notify completion
            self.add_event(self.requirement_id, f"progress_message", {
                "message": "Commercial bid analysis completed successfully",
                "success": True
            })
            
            return {"success": True, "results": final_report}
            
        except Exception as e:
            logger.error(f"Error in process_multiple_bids_sync: {str(e)}")
            self.add_event(self.requirement_id, f"progress_message", {
                "message": f"Error processing bids: {str(e)}",
                "success": False
            })
            return {"success": False, "results": {}}



def main():
    """Example usage of the CommercialBidProcessor."""
    # Example data
    bid_data = [
        {"bid_id": "6aa67866-913e-42a8-97cc-b0b7d662241e", "bidder_name": "MO GROUP"},
        {"bid_id": "e107d81a-3474-4f5d-ac6f-f95c18072cd1", "bidder_name": "thyssenkrupp"}
    ]
    
    equipment_list = [
        {"equipment_id": "eq001", "equipment_name": "Ball Mill"},
        {"equipment_id": "eq002", "equipment_name": "AG Mill"},
        {"equipment_id": "eq003", "equipment_name": "Crusher"}
    ]
    
    # Create processor instance
    processor = CommercialBidProcessor()
    
    # Process bids synchronously
    results = processor.process_multiple_bids_sync(bid_data, equipment_list)
    
    # Print results
    print(json.dumps(results, indent=2))
    return results

if __name__ == "__main__":
    report = main()
    #save as json 
    with open('report.json', 'w') as f:
        json.dump(report, f, indent=2)