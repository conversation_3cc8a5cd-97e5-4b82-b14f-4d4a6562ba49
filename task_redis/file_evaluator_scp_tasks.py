# file_evaluator_scp_tasks.py
from services.handle_new_specs_documents import SpecsDocumentProcessor
from init import app, socketio, socket_manager

# Redis connection
def evaluate_files_scp_task(req_id):
    try:
        with app.app_context():
            print('now starting scp evaluate...')
            # socket_manager = socket_instance.get_instance()
            scp_loader = SpecsDocumentProcessor(socket_manager)
            scp_loader.process_scp(req_id)
            print('Done processing scp files...')
            return "SCP Files processing completed successfully."
    except Exception as e:
        print(f"Error during file processing: {e}")
        return str(e)