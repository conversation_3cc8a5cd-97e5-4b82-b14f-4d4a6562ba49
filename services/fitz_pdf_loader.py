
import fitz
from concurrent.futures import Thread<PERSON>oolExecutor
# from docling.document_converter import Document<PERSON>onverter
# from docling.datamodel.pipeline_options import PdfPipelineOptions
# from docling.document_converter import DocumentConverter, PdfFormatOption, InputFormat
# from docling.backend.docling_parse_v2_backend import DoclingParseV2DocumentBackend

# from langchain_docling import <PERSON>lingLoader



from pypdf import PdfReader

    

def process_page(page):
    """
    Extract text from a single page.
    """
    return page.get_text()

def load_pdf_parallel(file_path):
    """
    Load a PDF file and extract text from all pages in parallel.
    Returns an array where each entry corresponds to the text content of a page.
    """
    return read_pdf(file_path)
    # return parse_pdf(file_path)
    # with fitz.open(file_path) as pdf:
    #     with ThreadPoolExecutor() as executor:
    #         results = list(executor.map(process_page, pdf))  # Convert map object to a list
    #     return results

def read_pdf(file):
    reader = PdfReader(file)
    text = []
    
    for page in reader.pages:
        text.append(page.extract_text())

    # all = "\n".join(text)
    # print(all)
    return text

# def parse_pdf(file_path):
#     pipeline_options = PdfPipelineOptions()
#     pipeline_options.do_ocr = True # pick what you need
#     pipeline_options.do_table_structure = True # pick what you need

#     converter = DocumentConverter(
#         format_options={
#             InputFormat.PDF: PdfFormatOption(pipeline_options=pipeline_options, backend=DoclingParseV2DocumentBackend)
#         }
#     )
#     result = converter.convert(file_path)
#     text = result.document.export_to_markdown()
#     chunks = chunk_by_paragraphs_with_overlap(text)
#     return chunks


# def chunk_by_paragraphs(markdown_text: str, max_chars: int = 1000):
#     """Splits Markdown into paragraphs and merges small ones."""
#     paragraphs = markdown_text.split('\n\n')
#     chunks, current_chunk = [], ""
    
#     for para in paragraphs:
#         if len(current_chunk) + len(para) <= max_chars:
#             current_chunk += ("\n\n" if current_chunk else "") + para
#         else:
#             chunks.append(current_chunk)
#             current_chunk = para
    
#     if current_chunk:
#         chunks.append(current_chunk)
#         with open("outp.txt", "a") as f:
#             for section in chunks:
#                 f.write(f"{section} \n\n\n")
    
#     return chunks

# def chunk_by_paragraphs_with_overlap(markdown_text: str, max_chars: int = 1000, overlap: int = 100):
#     """Splits Markdown into paragraphs and merges small ones with optional overlap."""
#     paragraphs = markdown_text.split('\n\n')
#     chunks, current_chunk = [], ""
    
#     for i, para in enumerate(paragraphs):
#         if len(current_chunk) + len(para) <= max_chars:
#             current_chunk += ("\n\n" if current_chunk else "") + para
#         else:
#             chunks.append(current_chunk)
#             # Start the next chunk with the last `overlap` characters from the previous chunk
#             current_chunk = current_chunk[-overlap:] + ("\n\n" if current_chunk[-overlap:] else "") + para
    
#     if current_chunk:
#         chunks.append(current_chunk)
    
#     return chunks

# Example Usage
if __name__ == "__main__":
    file_path = "large_file.pdf"  # Replace with your PDF file path
    page_texts = read_pdf(file_path)
    print(f"Extracted text from {len(page_texts)} pages.")
    print(page_texts)
