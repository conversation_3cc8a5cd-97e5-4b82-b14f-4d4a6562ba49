import os
import tempfile
from werkzeug.utils import secure_filename
from zipfile import ZipFile, BadZipFile
import shutil, uuid
from models import BidFile, Bids, BidFileMetadata
import json
import time
from task.file_processor_tasks_bid import process_files_bid_task


class ZipExtractor:
    def __init__(self, base_upload_path, user_id):
        self.base_upload_path = base_upload_path
        self.user_id = user_id
        self._load_environment()
    
    def _load_environment(self):
        """Loads environment variables from a JSON file."""
        # parent_dir = "/workspace/gpu_processor/"
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        env_file_path = os.path.join(parent_dir, 'env.json')
        with open(env_file_path, 'r') as f:
            env_data = json.load(f)
        
        self.EXTRACT_FOLDER = env_data.get("ZIP_EXTRACT_DIR")
        self.METADATA_FOLDER = env_data.get("BID_METADATA_DIR")

    def process_zip(self, zip_file, directory_name):
        """
        Process a zip file and extract its contents to a named directory
        
        Args:
            zip_file: FileStorage object containing the zip file
            directory_name: Name to give the extracted directory
            
        Returns:
            str: Path to the extracted directory
        """
        try:
            self.bid_id = str(uuid.uuid4())
            metadata = {}
            # Create a temporary directory for processing
            with tempfile.TemporaryDirectory() as temp_dir:
                # Save the zip file to temp directory
                zip_path = os.path.join(temp_dir, secure_filename(zip_file.filename))
                zip_file.save(zip_path)

                # Create extraction directory
                extract_path = os.path.join(temp_dir, self.EXTRACT_FOLDER)
                os.makedirs(extract_path, exist_ok=True)

                # Extract the zip file
                with ZipFile(zip_path, 'r') as zip_ref:
                    zip_ref.extractall(extract_path)
                
                bid_data = {
                    'id': self.bid_id,
                    'name': directory_name,
                    'user_id': self.user_id
                }
                Bids.create(**bid_data)

                # Recursively extract nested zip files
                self._extract_nested_zips(extract_path, metadata)

                # Create final directory with given name
                final_path = os.path.join(self.base_upload_path, 'bid', self.bid_id)
                
                # If directory exists, remove it
                if os.path.exists(final_path):
                    shutil.rmtree(final_path)
                
                # Move extracted contents to final directory
                shutil.move(extract_path, final_path)

                # 🔥 Save metadata as a JSON file
                metadata_dir = os.path.join(self.base_upload_path, self.METADATA_FOLDER)
                os.makedirs(metadata_dir, exist_ok=True)  # Ensure directory exists
                
                metadata_filename = f"{self.bid_id}_{int(time.time())}.json"
                metadata_path = os.path.join(metadata_dir, metadata_filename)

                with open(metadata_path, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, indent=4)

                # Store metadata reference in database
                metadata_entry = {
                    "bid_id": self.bid_id,
                    "bid_metadata": metadata_filename,  # Store only filename
                    "status": "processed"
                }
                BidFileMetadata.create(**metadata_entry)


                return final_path
        except (IOError, BadZipFile) as e:
            print(f"An error occurred while processing the zip file: {e}")
            return None

    def _extract_nested_zips(self, directory, metadata):
        """
        Recursively extract nested zip files and store file metadata.

        Args:
            directory: Path to scan for nested zip files.
            metadata: Dictionary to store file structure.
        """
        ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', "csv", "xls", "xlsx"}
        for root, _, files in os.walk(directory):
            relative_path = os.path.relpath(root, directory)
            parent_folder = relative_path if relative_path != "." else "root"
            node = self._get_nested_dict(metadata, relative_path.split(os.sep)) if relative_path != "." else metadata
            
            for file in files:
                file_path = os.path.join(root, file)
                
                if file.endswith('.zip'):
                    # Extract nested zip
                    extract_path = os.path.join(root, file[:-4])
                    os.makedirs(extract_path, exist_ok=True)
                    
                    try:
                        with ZipFile(file_path, 'r') as zip_ref:
                            zip_ref.extractall(extract_path)
                        os.remove(file_path)
                        self._extract_nested_zips(extract_path, node.setdefault(file, {}))
                    except (IOError, BadZipFile) as e:
                        print(f"Error extracting nested zip {file_path}: {e}")
                else:
                    relative_file_path = os.path.relpath(file_path, directory)
                    # Store metadata and add file entry
                    file_extension = file.rsplit('.', 1)[-1].lower()
                    if file_extension in ALLOWED_EXTENSIONS:

                        process_files_bid_task.delay(self.bid_id, file_path)

                        bid_file_id = str(uuid.uuid4())
                        file_data = {
                            'id': bid_file_id,
                            'name': file,
                            'bid_id': self.bid_id,
                            'status': 'done',
                            'file_type': file_extension,
                        }
                        BidFile.create(**file_data)
                        
                        node[bid_file_id] = {
                            'id': bid_file_id,
                            'name': file,
                            'path': relative_file_path,
                            'parent_dir': parent_folder,
                        }

    def _get_nested_dict(self, parent, keys):
        """
        Traverse or create a nested dictionary based on a list of keys.

        Args:
            parent: The root dictionary to traverse.
            keys: List of keys representing the nested structure.
        
        Returns:
            dict: The last level dictionary node.
        """
        for key in keys:
            parent = parent.setdefault(key, {})
        return parent
                        
    def _extract_nested_zips_2(self, directory):
        """
        Recursively extract any nested zip files within the given directory
        
        Args:
            directory: Path to the directory to scan for nested zip files
        """
        ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', "csv", "xls", "xlsx"}
        for root, dirs, files in os.walk(directory):
            for file in files:
                if file.endswith('.zip'):
                    zip_path = os.path.join(root, file)
                    extract_path = os.path.join(root, file[:-4])  # Remove .zip extension for the folder name
                    os.makedirs(extract_path, exist_ok=True)
                    
                    try:
                        # Extract the zip file
                        with ZipFile(zip_path, 'r') as zip_ref:
                            zip_ref.extractall(extract_path)
                        
                        # Remove the zip file after extraction
                        os.remove(zip_path)
                        
                        # Recursively extract nested zips in the newly created directory
                        self._extract_nested_zips(extract_path)
                    except (IOError, BadZipFile) as e:
                        print(f"An error occurred while extracting nested zip file {zip_path}: {e}")
                else:
                    file_extension = file.rsplit('.', 1)[-1].lower()
                    if file_extension in ALLOWED_EXTENSIONS:
                        bid_file_id = str(uuid.uuid4())
                        file_data = {
                            'id': bid_file_id,
                            'name': file,
                            'bid_id': self.bid_id,
                            'file_type': file_extension,
                        }
                        BidFile.create(**file_data)
                    else:
                        # Optionally remove unsupported files
                        file_path = os.path.join(root, file)
                        os.remove(file_path)
                        print(f"Removed unsupported file: {file}")

    def update_file_to_zip(self, zip_file, bid_id):
        """
        Update files in an existing bid by processing a zip file.
        
        Args:
            zip_file: FileStorage object containing the zip file
            bid_id: ID of the bid to update
            
        Returns:
            str: Path to the updated directory
        """
        try:
            self.bid_id = bid_id
            metadata = {}
            # Create a temporary directory for processing
            with tempfile.TemporaryDirectory() as temp_dir:
                # Save the zip file to temp directory
                zip_path = os.path.join(temp_dir, secure_filename(zip_file.filename))
                zip_file.save(zip_path)

                # Create extraction directory
                extract_path = os.path.join(temp_dir, self.EXTRACT_FOLDER)
                os.makedirs(extract_path, exist_ok=True)

                # Extract the zip file
                with ZipFile(zip_path, 'r') as zip_ref:
                    zip_ref.extractall(extract_path)

                # Recursively extract nested zip files
                self._extract_nested_zips(extract_path, metadata)
                # Create final directory with given name
                final_path = os.path.join(self.base_upload_path, 'bid', self.bid_id)
                
                # If directory exists, remove it
                if os.path.exists(final_path):
                    shutil.rmtree(final_path)
                
                # Move extracted contents to final directory
                shutil.move(extract_path, final_path)

                # 🔥 Save metadata as a JSON file
                metadata_dir = os.path.join(self.base_upload_path, self.METADATA_FOLDER)
                os.makedirs(metadata_dir, exist_ok=True)  # Ensure directory exists
                
                metadata_filename = f"{self.bid_id}_{int(time.time())}.json"
                metadata_path = os.path.join(metadata_dir, metadata_filename)

                with open(metadata_path, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, indent=4)
                
                # Store metadata reference in database
                metadata_entry = {
                    "bid_id": self.bid_id,
                    "bid_metadata": metadata_filename,  # Store only filename
                    "status": "processed"
                }
                BidFileMetadata.create(**metadata_entry)

                return final_path

        except (IOError, BadZipFile) as e:
            print(f"An error occurred while updating files in the bid: {e}")
            return None