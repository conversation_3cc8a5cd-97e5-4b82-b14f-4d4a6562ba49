from bs4 import BeautifulSoup
from fpdf import FPDF
import fitz  # PyMuPDF
import tempfile
import os


class PdfExtractor:
    def __init__(self):
        self.temp_pdf_path = None

    @staticmethod
    def clean_html(raw_html):
        # Use BeautifulSoup to parse the HTML and remove tags
        soup = BeautifulSoup(raw_html, "html.parser")
        text = soup.get_text(separator="<p>")  # Use a newline as the separator between tags
        return text

    def html_to_temp_pdf(self, html_string):
        # Clean the HTML to get plain text
        cleaned_text = self.clean_html(html_string)

        # Initialize FPDF
        pdf = FPDF()
        pdf.set_auto_page_break(auto=True, margin=15)
        pdf.add_page()
        pdf.set_font("Arial", size=12)

        # Add the cleaned text to the PDF
        for line in cleaned_text.split('\n'):
            pdf.multi_cell(0, 10, line)

        # Create a temporary file for the PDF
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as temp_pdf_file:
            pdf.output(temp_pdf_file.name)
            # Save the PDF path
            self.temp_pdf_path = temp_pdf_file.name


    def extract_text_from_first_three_pages(self):
        # Open the input PDF
        document = fitz.open(self.temp_pdf_path)

        # Extract text from the first three pages
        extracted_text = ""
        for page_num in range(min(3, document.page_count)):
            page = document.load_page(page_num)
            extracted_text += page.get_text()

        # Close the document
        document.close()

        # Cleanup: Delete the temporary PDF file
        os.remove(self.temp_pdf_path)

        return extracted_text


# html_string = "<p>Hello, world!</p>"
# pdf_extractor = PdfExtractor()
# pdf_extractor.html_to_temp_pdf(html_string)
# extracted_text = pdf_extractor.extract_text_from_first_three_pages()

# Print extracted text
# print(extracted_text)
