
    <style>
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-family: Arial, sans-serif;
        }
        .table th {
            background-color: #f2f2f2;
            font-weight: bold;
            text-align: left;
            padding: 8px;
            border: 1px solid #ddd;
        }
        .table td {
            padding: 8px;
            border: 1px solid #ddd;
            text-align: left;
        }
        .table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .table tr:hover {
            background-color: #f5f5f5;
        }
    </style>
    <table border="1" class="dataframe table table-striped table-bordered">
  <thead>
    <tr style="text-align: left;">
      <th>criteria</th>
      <th>parent</th>
      <th>weight</th>
      <th>yeain sf_score</th>
      <th>yeain sf_weighted_score</th>
      <th>yeain sf_summary</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Technical Criteria</td>
      <td></td>
      <td>40</td>
      <td>N/A</td>
      <td>N/A</td>
      <td></td>
    </tr>
  </tbody>
</table>