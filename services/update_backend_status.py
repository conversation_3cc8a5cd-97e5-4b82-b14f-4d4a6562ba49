import requests
import os
import json

class BackendStatusUpdater:
    def __init__(self):
        # Get the path to the parent directory
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))        
        env_file_path = os.path.join(parent_dir, 'env.json')        
        with open(env_file_path, 'r') as f:
            env_data = json.load(f)
        
        self.project_id = env_data.get('BACKEND_PROJECT_ID', '')
        self.backend_base_url = env_data.get('BACKEND_BASE_URL', '')

    def update_history_status(self, request_id, status_code):
        # Check if the provided status code is valid
        if status_code not in [0, 1, 2]:
            raise ValueError("Invalid status code. Must be 0, 1, or 2.")

        # Construct the URL with the project ID
        url_with_status = f"{self.backend_base_url}/v3/api/custom/aienergy/cbp-request/complete?x-project={self.project_id}"

        # Prepare the payload with request_id and status
        payload = {
            "request_id": request_id
        }

        # Make the PUT request
        try:
            response = requests.post(url_with_status, json=payload)  # Include the payload in the request
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"Error occurred: {e}")
    
    def update_report_status(self, request_id, status_code):
        # Check if the provided status code is valid
        if status_code not in [0, 1]:
            raise ValueError("Invalid status code. Must be 0, or 1.")

        # Construct the URL with the project ID
        url_with_status = f"{self.backend_base_url}/v3/api/custom/aienergy/cbp-report-status/update?x-project={self.project_id}"

        # Prepare the payload with request_id and status
        payload = {
            "request_id": request_id,
            "status": status_code
        }

        # Make the PUT request
        try:
            response = requests.put(url_with_status, json=payload)  # Include the payload in the request
            response.raise_for_status()
            print('updated cbp report status: ', response.json())
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"Error occurred: {e}")
        
        
    def update_qmp_completion_status(self, request_id, log):
        # Check if the provided status code is valid

        # Construct the URL with the project ID
        url = f"{self.backend_base_url}/v3/api/custom/aienergy/qmp-request/request-complete?x-project={self.project_id}"

        # Prepare the payload with request_id and status
        payload = {
            "request_id": request_id,
            "response": json.dumps(log)
        }

        # Make the PUT request
        try:
            response = requests.post(url, json=payload)  # Include the payload in the request
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error occurred: {e}")



# if __name__ == "__main__":
#     backend_status_updater = BackendStatusUpdater()
#     backend_status_updater.update_history_status('41a05f31-dff7-4102-996b-038d67604ac5', 0)
