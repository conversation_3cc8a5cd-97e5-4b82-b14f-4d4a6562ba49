from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from langchain_experimental.graph_transformers import LLMGraphTransformer
from langchain_groq import ChatGroq
from langchain_core.documents import Document
import networkx as nx
import json
import uuid
import hashlib
from dgraph import DgraphService
import os


class DocumentData(BaseModel):
    """
    A Pydantic model for structured document input.

    Attributes:
    - text: Content of the document.
    - page: Page number of the document (optional).
    - section: Section of the document (optional).
    - metadata: Any additional metadata associated with the document (optional).
    """
    text: str
    page: Optional[int] = None
    section: Optional[str] = None
    metadata: Optional[dict] = None


class KnowledgeGraphBuilder:
    """
    Class responsible for building a knowledge graph from document data.
    """

    def __init__(self, llm, document_data: List[DocumentData]):
        """
        Initialize the Knowledge Graph Builder.

        :param llm: Language model for extracting entities.
        :param document_data: List of structured document input (DocumentData instances).
        """
        self.llm = llm
        # Move page and section into metadata since Document doesn't support these fields directly
        self.documents = [
            Document(
                page_content=item.text, 
                metadata={
                    **(item.metadata or {}),
                    "page": item.page,
                    "section": item.section
                }
            )
            for item in document_data
        ]
        self.graph = nx.DiGraph()  # Directed graph to represent entities and relationships
        self.graph_documents = []

    def build_graph(self):
        """
        Build the knowledge graph from documents by extracting entities and relationships.
        """
        # Step 1: Use the LLMGraphTransformer to convert documents into graph documents
        llm_transformer = LLMGraphTransformer(llm=self.llm)
        self.graph_documents = llm_transformer.convert_to_graph_documents(self.documents)

        # Step 2: Add a document node and entities to the graph
        for doc_idx, doc in enumerate(self.documents):
            # Generate a unique document ID based on content and metadata
            content_hash = hashlib.md5(doc.page_content.encode()).hexdigest()[:10]
            source = doc.metadata.get("source", "unknown")
            page = doc.metadata.get("page", "")
            
            # Create a deterministic but unique ID
            doc_node_id = f"Doc_{source}_{page}_{content_hash}"
            
            # Alternative: Use UUID for guaranteed uniqueness
            # doc_node_id = f"Doc_{uuid.uuid4()}"
            
            self.graph.add_node(
                doc_node_id,
                entity="DocumentContainer",
                page=doc.metadata.get("page"),
                section=doc.metadata.get("section"),
                metadata=doc.metadata,
                content=doc.page_content,
                document_idx=doc_idx,
            )

            # Add entities and relationships for this document
            for edge in self.graph_documents[doc_idx].relationships:
                self.graph.add_edge(
                    doc_node_id,
                    edge.source.id,
                    relation=edge.type,
                )
                self.graph.add_edge(
                    doc_node_id,
                    edge.target.id,
                    relation=edge.type,
                )

            # Add individual entity nodes
            for node in self.graph_documents[doc_idx].nodes:
                self.graph.add_node(
                    node.id,
                    entity=node.type,
                    document_source=doc_node_id,  # Link the entity node to the document
                )
                
                # Add explicit source_extracted_from relationship
                self.graph.add_edge(
                    node.id,
                    doc_node_id,
                    relation="source_extracted_from",
                )

    def get_graph_data(self):
        """
        Retrieve the current graph structure (nodes and edges).

        :return: List of nodes and edges.
        """
        return {
            "nodes": list(self.graph.nodes(data=True)),
            "edges": list(self.graph.edges(data=True)),
        }

    def insert_to_dgraph(self, dgraph_service: DgraphService, namespace: str = None, use_upsert: bool = True) -> Dict[str, Any]:
        """
        Insert or upsert the built knowledge graph into Dgraph.
        
        Args:
            dgraph_service: An initialized DgraphService instance
            namespace: Optional namespace for multi-tenancy support
            use_upsert: Whether to use upsert (update if exists) instead of insert
            
        Returns:
            Dictionary with insertion results including UIDs of inserted nodes
        """
        if not self.graph:
            raise ValueError("Graph is empty. Call build_graph() first.")
            
        # Prepare for namespace if provided
        if namespace:
            try:
                # Convert namespace string to integer
                namespace_int = int(namespace)
                # Login to the specified namespace
                # dgraph_service.client.login_into_namespace("groot", "password", namespace_int)
                print(f"Logged into namespace: {namespace}")
            except ValueError as e:
                print(f"Invalid namespace format. Namespace must be a valid integer: {str(e)}")
                raise
            except Exception as e:
                print(f"Failed to login to namespace {namespace}: {str(e)}")
                raise
        
        if use_upsert:
            return self._upsert_to_dgraph(dgraph_service)
        else:
            return self._insert_to_dgraph(dgraph_service)
        
    def _insert_to_dgraph(self, dgraph_service: DgraphService) -> Dict[str, Any]:
        """Insert implementation (creates new nodes)"""
        # Create mutation data from the graph
        mutation_data = {"set": []}
        node_uid_map = {}  # To map our node IDs to Dgraph UIDs
        
        # First, add all nodes
        for node_id, attrs in self.graph.nodes(data=True):
            # Create a node object for Dgraph
            node_data = {
                "dgraph.type": attrs.get("entity", "Entity"),
            }
            
            # Add all attributes except 'entity' which we used for dgraph.type
            for key, value in attrs.items():
                if key != "entity" and value is not None:
                    # Handle special case for content which might be large
                    if key == "content" and isinstance(value, str) and len(value) > 1000:
                        # Truncate long content to avoid issues
                        node_data[key] = value[:1000] + "..."
                    else:
                        node_data[key] = value
            
            # Add node to mutation
            mutation_data["set"].append(node_data)
            
            # Store reference to this node's position in the set array
            node_uid_map[node_id] = len(mutation_data["set"]) - 1
        
        # Now add all edges as relationships
        for source_id, target_id, attrs in self.graph.edges(data=True):
            source_idx = node_uid_map[source_id]
            target_idx = node_uid_map[target_id]
            relation = attrs.get("relation", "related_to")
            
            # Add edge by referencing the target node in the source node
            if relation not in mutation_data["set"][source_idx]:
                mutation_data["set"][source_idx][relation] = []
            
            # Add reference to target node
            mutation_data["set"][source_idx][relation].append({
                "uid": f"_:node{target_idx}"  # Reference to the target node's position
            })
        
        # Execute the mutation
        try:
            result = dgraph_service.execute_mutation(mutation_data)
            print(f"Successfully inserted knowledge graph into Dgraph")
            return result
        except Exception as e:
            print(f"Failed to insert knowledge graph: {str(e)}")
            raise

    def _upsert_to_dgraph(self, dgraph_service: DgraphService) -> Dict[str, Any]:
        """
        Upsert the graph to Dgraph (update if exists, otherwise insert)
        """
        # Create mutation data from the graph
        mutation_data = {"set": []}
        node_uid_map = {}  # To map our node IDs to Dgraph UIDs
        
        # First, check for existing nodes by their unique properties
        for node_id, attrs in self.graph.nodes(data=True):
            entity_type = attrs.get("entity", "Entity")
            
            # Determine unique identifiers based on entity type
            if entity_type == "DocumentContainer":
                # For documents, use source and content hash as unique identifiers
                content = attrs.get("content", "")
                content_hash = hashlib.md5(content.encode()).hexdigest()[:10]
                source = attrs.get("metadata", {}).get("source", "unknown")
                
                # Query to find existing document - Fix: Properly escape entity type
                query = f"""
                {{
                  doc(func: type("{entity_type}")) @filter(eq(content_hash, "{content_hash}") AND eq(source, "{source}")) {{
                    uid
                  }}
                }}
                """
            else:
                # For other entities, use their ID and type - Fix: Properly escape entity type and node_id
                query = f"""
                {{
                  entity(func: type("{entity_type}")) @filter(eq(original_id, "{node_id}")) {{
                    uid
                  }}
                }}
                """
            
            # Execute query to find existing node
            try:
                result = dgraph_service.execute_query(query)
                existing_uid = None
                
                if entity_type == "DocumentContainer":
                    if result.get("doc") and len(result["doc"]) > 0:
                        existing_uid = result["doc"][0]["uid"]
                else:
                    if result.get("entity") and len(result["entity"]) > 0:
                        existing_uid = result["entity"][0]["uid"]
                
                # Create node data for upsert
                node_data = {
                    "dgraph.type": entity_type,
                    "original_id": node_id,  # Store original ID for future reference
                }
                
                # Add content hash for document containers
                if entity_type == "DocumentContainer":
                    node_data["content_hash"] = content_hash
                    node_data["source"] = source
                
                # Add all other attributes
                for key, value in attrs.items():
                    if key not in ["entity", "original_id"] and value is not None:
                        # Handle special case for content which might be large
                        if key == "content" and isinstance(value, str) and len(value) > 1000:
                            node_data[key] = value[:1000] + "..."
                        else:
                            node_data[key] = value
                
                # If node exists, include its UID
                if existing_uid:
                    node_data["uid"] = existing_uid
                
                # Add node to mutation
                mutation_data["set"].append(node_data)
                node_uid_map[node_id] = len(mutation_data["set"]) - 1
            except Exception as e:
                print(f"Query execution failed for node {node_id} with entity type {entity_type}")
                print(f"Query: {query}")
                print(f"Error: {str(e)}")
                raise
        
        # Now add all edges as relationships
        for source_id, target_id, attrs in self.graph.edges(data=True):
            source_idx = node_uid_map[source_id]
            target_idx = node_uid_map[target_id]
            relation = attrs.get("relation", "related_to")
            
            # Add edge by referencing the target node in the source node
            if relation not in mutation_data["set"][source_idx]:
                mutation_data["set"][source_idx][relation] = []
            
            # Add reference to target node
            mutation_data["set"][source_idx][relation].append({
                "uid": f"_:node{target_idx}"  # Reference to the target node's position
            })
        
        # Execute the mutation
        try:
            result = dgraph_service.execute_mutation(mutation_data)
            print(f"Successfully upserted knowledge graph into Dgraph")
            return result
        except Exception as e:
            print(f"Failed to upsert knowledge graph: {str(e)}")
            raise



# Initialize LLM (ChatGroq here)


# Example usage
if __name__ == "__main__":
    parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    env_file_path = os.path.join(parent_dir, 'env.json')
    with open(env_file_path, 'r') as f:
        env_data = json.load(f)
    os.environ["GROQ_API_KEY"] = env_data.get("GROQ_API_KEY", "")
    
    llm = ChatGroq(
        temperature=0, 
        model_name="llama-3.1-8b-instant", 
        api_key=os.environ["GROQ_API_KEY"]
    )

# Example documents data with page, section, and metadata
    document_data = [
        DocumentData(
            text="Metso Outotec Group | Outotec (Canada) Ltd. 1551 Corporate Drive, Mississauga, Ontario, L5B 2C3, Canada",
            page=1,
            section="1",
            metadata={"source": "contract_proposal.pdf"}
        ),
        DocumentData(
            text="The delivery date for mill reline equipment (if not purchased) is 14 weeks from the date of the contract.",
            page=1,
            section="2",
            metadata={"source": "contract_proposal.pdf"}
        )
    ]
    
    # Create the KnowledgeGraphBuilder instance
    kg_builder = KnowledgeGraphBuilder(llm, document_data)
    
    # Build the graph from documents
    kg_builder.build_graph()
    
    # Get graph data (nodes and edges)
    graph_data = kg_builder.get_graph_data()
    print("\nGraph Nodes:")
    for node in graph_data["nodes"]:
        node_id, attrs = node
        print(f"  Node ID: {node_id}")
        print(f"  Attributes: {json.dumps(attrs, indent=4)}")
        print()
    
    print("Graph Edges:")
    for edge in graph_data["edges"]:
        source, target, attrs = edge
        print(f"  {source} -> {target}")
        print(f"  Attributes: {json.dumps(attrs, indent=4)}")
        print()
    
    # Insert into Dgraph
    try:
        # Initialize DgraphService
        dgraph_service = DgraphService(hosts=["localhost"], port=9080)
        
        # Insert the graph into a specific namespace (optional)
        result = kg_builder.insert_to_dgraph(dgraph_service, namespace=1)
        
        print("\nInserted into Dgraph:")
        print(f"  Result: {result}")
    except Exception as e:
        import traceback
        print(f"Error inserting into Dgraph:")
        print(traceback.format_exc())
        print(f"Error inserting into Dgraph: {str(e)}")
    finally:
        # Clean up resources
        if 'dgraph_service' in locals():
            dgraph_service.close()
