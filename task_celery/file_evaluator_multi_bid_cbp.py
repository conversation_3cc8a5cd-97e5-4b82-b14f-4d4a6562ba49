import asyncio

from extensions import celery
from socket_instance import socket_instance
from services.handle_multi_bid_cbp import ChronoMultibidDocumentProcessor

@celery.task(bind=True, max_retries=3, name="task_celery.file_evaluator_multi_bid_cbp.file_evaluator_multi_bid_cbp")
def file_evaluator_multi_bid_cbp(self, request_id):
    try:
        # Get the socket manager instance and emit a test event
        socket_manager = socket_instance.get_instance()
        socket_manager.emit_to_client(
            request_id,
            'test_event',
            {'message': 'Test from CBP Celery'},
            namespace='/cbp'
        )
        
        # Instantiate your document processor
        cbp_loader = ChronoMultibidDocumentProcessor(socket_manager)
        
        # Create an event loop and run the async process_cbp
        try:
            loop = asyncio.get_running_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        loop.run_until_complete(cbp_loader.process_cbp(request_id))
        
        print('Starting to evaluate CBP files...')
        return "CBP File evaluation started."
    except Exception as e:
        print(f"Error during CBP file evaluation: {e}")
        return str(e)
