import asyncio
from services.serverless import Server<PERSON>RunpodAPIClient
from services.open_ai_service import <PERSON><PERSON><PERSON><PERSON>
from services.claude_ai_service import ClaudeService
# from services.groq_ai_service import GroqService
from services.fast_apis_service import FastAPIs


class QuestionGenerator:
    def __init__(self):
        self.serverless_init = ServerlessRunpodAPIClient()
        self.assistant_init = AssistantManager()
        self.claude_ai_init = ClaudeService()
        self.fast_api_init = FastAPIs()

    def _prompt_template(self, text):

        prompt_template = f'''### System Prompt
        Generate a list of queries that  can be used to search on a search engine to gain more information to analyze the given text. Questions needs to be directly connected to the provided text by the user.\n
        ### User Message: {text}
        ### Assistant:
        '''
        return prompt_template

    async def _generate_questions_openai_single(self, text):
        messages = [{"role": "system", "content": f'''Generate a list of key points that  can be used to search or query on a search engine 
        to gain more information to analyze the given text. The key points needs to be directly connected to the provided text by the user.
        If the context is small there will be less key points, if it's big there will be more phrases. For example, if the context is a paragraph of 5 sentences, number of key points should be 4-5 keypoints. 
        If the context is more descriptive number of key points reduced. For example, if one specific key point was described in 3-4 sentences, then, it will still be one key point.
        Provide each key point in a new line''',
        },{
            "role": "user",
            "content": f'''{text}'''
        }]

        print("Generating questions openai single...")
        # print(messages)
        response = asyncio.to_thread(self.assistant_init.get_response, messages)
        response = await response
        print("generated response from openai single")
        print(response)
        return response.choices[0].message.content.split('\n')
    
    async def _generate_questions_claude_single(self, text):
        
        system_prompt = f'''Generate a list of key points that  can be used to search or query on a search engine 
        to gain more information to analyze the given text. The key points needs to be directly connected to the provided text by the user.
        If the context is small there will be less key points, if it's big there will be more phrases. For example, if the context is a paragraph of 5 sentences, number of key points should be 4-5 keypoints. 
        If the context is more descriptive number of key points reduced. For example, if one specific key point was described in 3-4 sentences, then, it will still be one key point. Dont mention any other thing than the keypoints itself. Exclude any headings, like "here are some keywords", etc., as it will be used programatically. Dont use any numbers, bullet points for the key points, but provide each of the key points in new lines.'''

        messages = [
            {"role": "user", "content": f'''{text}'''},
            { "role": "system", "content": system_prompt}
        ]

        print("Generating questions claude single...")

        try:
            report = self.fast_api_init.generate_completion(messages)
            if report:
                print("generated questions from fast api single")
                print(report)
                return report.split('\n')
        except Exception as e:
            system_prompt = f'''Generate a list of 5 key points that  can be used to search or query on a search engine 
            to gain more information to analyze the given text. The key points needs to be directly connected to the provided text by the user.
            If the context is small there will be less key points, if it's big there will be more phrases. For example, if the context is a paragraph of 5 sentences, number of key points should be 4-5 keypoints. 
            If the context is more descriptive number of key points reduced. For example, if one specific key point was described in 3-4 sentences, then, it will still be one key point. Dont mention any other thing than the keypoints itself. Exclude any headings, like "here are some keywords", etc., as it will be used programatically. Dont use any numbers, bullet points for the key points, but provide each of the key points in new lines.'''

            messages = [
                {"role": "user", "content": f'''{text}'''}
            ]
            report = self.claude_ai_init.generate_message_sync_haiku(messages, system=system_prompt)
            if report:
                print("generated questions from claude haiku single")
                print(report)
                return report.split('\n')

            print('failed to generate questions', e)
        
        

    async def _generate_questions_openai(self, texts):
        print("Generating questions...")
        
        print(texts)
        tasks = [self._generate_questions_openai_single(x) for x in texts]
        result = await asyncio.gather(*tasks)
        print(result)
        return result
    
    async def _generate_questions_claude(self, texts):
        print("Generating questions...")
        # print(texts)
        tasks = [self._generate_questions_claude_single(x) for x in texts]
        result = await asyncio.gather(*tasks)
        return result

    def generate_questions(self, texts):
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(self._generate_questions_openai(texts))
        return result

    def generate_questions_and_fields_parallel(self, data):
        queries = [self._prompt_template(x["content"]) for x in data]
        results = self.serverless_init.parallel_request_handler(queries)
        for x, result in zip(data, results):
            x["questions"] = result.split('\n')
        return data


# if __name__ == "__main__":
#     question_generator = QuestionGenerator()
#     texts = "Could you please confirm the warranty details for the valves?"
#     asyncio.run(question_generator._generate_questions_claude_single(texts))
