from services.document_seeder import DocumentSeeder
from models import File, Project, Chunks
from services.pinecone_vector_db import CanopyAI
import asyncio
import traceback
import sys

document_processor = DocumentSeeder()
pinecone_ai = CanopyAI()
 
async def seed_legacy_project_cbp_single(project_id):
    try:
        print('will start to seed legacy project...')
        try:
            pinecone_ai.delete_namespace(project_id)
        except Exception as e:
            print("An error occurred while deleting the namespace:")

        chunks = Chunks.get_by(project_id=project_id)

        for chunk in chunks:
            Chunks.delete_by(id=chunk["id"])
        
        print("Project Cleared...")
        
        files = File.get_by(project_id=project_id)
        print(files)
        for file in files:
            print("Proececcing File -> ", file["id"], file["name"] )
            await document_processor.handle_single_chronobid_document(file["id"])
    except Exception as e:
        print(traceback.print_exc())
        return "error"


async def seed_legacy_project_scp_single(project_id):
    try:
        print('will start to seed legacy project...')
        try:
            pinecone_ai.delete_namespace(project_id)
        except Exception as e:
            print("An error occurred while deleting the namespace:")

        chunks = Chunks.get_by(project_id=project_id)

        for chunk in chunks:
            Chunks.delete_by(id=chunk["id"])
        
        print("Project Cleared...")
        
        files = File.get_by(project_id=project_id)
        for file in files:
            print("Proececcing File -> ", file["id"], file["name"] )
            await document_processor.handle_single_document(file["id"])
    except Exception as e:
        print(traceback.print_exc())
        return "error"

async def move_pinecone_db():
    pass

if __name__ == "__main__":
    asyncio.run(seed_legacy_project_scp_single("58f7f9b2-bfbc-49d5-872d-c9231cb27e6e"))
    pass

    # the below two sedding mechanism are working just fine.
    # project_id_scp = "3247f737-8ab0-444a-91cb-ace7529d32d8"
    # project_id_cbp = "3913fc17-ee29-4182-b0d0-334270918624"
    # asyncio.run(seed_legacy_project_cbp_single(project_id_cbp))
    # asyncio.run(seed_legacy_project_scp_single(project_id_scp))
