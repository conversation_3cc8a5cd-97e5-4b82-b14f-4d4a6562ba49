import gevent
from gevent import monkey
monkey.patch_all()

import anthropic
import os,sys
import json
import random
import asyncio
from pydantic import BaseModel
import functools # Added for wraps
import time # Potentially useful for delays in decorator
from models import LLMUsage
from services.claude_tools import ClaudeTools

# Define the decorator (can be placed above the class or in a utils file)
def retry_with_models(model_list_attr_name):
    """
    Decorator to retry a function call across multiple models from a specified list,
    handling RateLimitErrors by trying the next model.

    Assumes the decorated function accepts 'model_id' and 'client' as keyword arguments.
    Assumes the class instance ('self') has a 'get_client()' method.
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            model_ids = getattr(self, model_list_attr_name, [])
            if not model_ids:
                return None

            core_args = args
            core_kwargs = kwargs.copy()
            core_kwargs.pop('model_id', None)
            core_kwargs.pop('client', None)

            shuffled_models = random.sample(model_ids, len(model_ids))
            last_exception = None

            for model_id in shuffled_models:
                print(f"Attempting model: {model_id}")
                try:
                    client = self.get_client()

                    result = func(self, *core_args, **core_kwargs, model_id=model_id, client=client)
                    return result # Return on success

                except anthropic.RateLimitError as e:
                    last_exception = e
                    time.sleep(10)
                    continue 
                
                except anthropic.BadRequestError as e:
                    last_exception = e
                    if 'credit balance' in str(e):
                        last_exception = e
                        print(f"Credit balance error trying another key: {e}")
                        continue
                    return []



                except Exception as e:
                    print(f"Error generating message with model {model_id}: {e}")
                    last_exception = e
                    return None

            # If the loop completes without returning, all models failed.
            print(f"All models in list '{model_list_attr_name}' failed after retries. Last error: {last_exception}")
            return None
        return wrapper
    return decorator



class ClaudeService:
    def __init__(self,claude_keys=['CLAUDE_API_KEY', 'CLAUDE_API_KEY'],default_key="CLAUDE_API_KEY"):
        # Get the path to the parent directory
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # Load environment variables from env.json file in the parent directory
        env_file_path = os.path.join(parent_dir, 'env.json')

        self.claude_keys = claude_keys
        self.default_key = default_key
        
        # Select a random API key
        selected_api_key = random.choice(self.claude_keys)
        
        print(f"Selected API key: {selected_api_key}")
        
        # Load environment variables from env.json file
        with open(env_file_path, 'r') as f:
            env_data = json.load(f)
            
        self.env_data = env_data
        
        # Set up ClaudeAI client
        self.client = anthropic.Anthropic(api_key=env_data.get(selected_api_key, self.default_key), max_retries=0)

        # Map deployments to models
        self.deployments_map = {
            "Claude 3.5 Sonnet Old": "claude-3-5-sonnet-20240620",
            "Claude 3.5 Sonnet": "claude-3-5-sonnet-20241022",
            "Claude 3 Opus": "claude-3-opus-20240229",
            "Claude 3 Sonnet": "claude-3-sonnet-20240229",
            "Claude 3 Haiku": "claude-3-haiku-20240307",
            "Claude 3.5 Haiku": "claude-3-5-haiku-20241022",
            "Claude 3.7 Sonnet": "claude-3-7-sonnet-20250219"
        }  
        self.model_ids = ["claude-3-5-sonnet-20240620", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-7-sonnet-20250219"]
        
        self.sonnet_model_ids = [
            "claude-3-5-sonnet-20240620",
            "claude-3-5-sonnet-20241022", "claude-3-7-sonnet-20250219", 
            "claude-sonnet-4-20250514"
            ]


        # Select a random model
        # self.selected_model = random.choice(list(self.deployments_map.values()))
        self.selected_model = self.deployments_map["Claude 3 Sonnet"]

    def get_client(self):
        selected_api_key = random.choice(self.claude_keys)
        print(f"Using API key: {selected_api_key}")
        client = anthropic.Anthropic(
            api_key=self.env_data.get(selected_api_key, self.default_key),
            max_retries=0 # Changed from 3 to 0
        )
        return client
        

    async def  generate_message_2(self, messages,temperature=0.01, model="Claude 3.5 Sonnet", max_tokens=4096):
        try:
            model="Claude 3.5 Sonnet"
            message = self.client.messages.create(
                model=self.deployments_map[model],
                temperature=temperature,
                max_tokens=max_tokens,
                messages=messages
            )
            self._record_usage(model, message)
            return message
        except Exception as e:
            print(f"Error generating message from generate_message_2: {e}")
            return None
        
    async def generate_message(self, messages, temperature=0.01, model="Claude 3.5 Sonnet", max_tokens=4096):
        for model_name, model_id in self.deployments_map.items():
            try:
                print(f"Trying model: {model_name}")
                message = self.client.messages.create(
                    model=model_id,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    messages=messages
                )
                self._record_usage(model_id, message)
                return message
            except Exception as e:
                print(f"Error generating message with model {model_name}: {e}")
        
        # If all models fail, return None or handle it appropriately
        print("All models failed to generate a message.")
        return None
    
    async def generate_message_agent_sonnet(self, messages, temperature=0.01, max_tokens=4096):
        # Use Claude 3.5 Sonnet
        model_id = self.deployments_map["Claude 3.5 Sonnet Old"]
        try:
            print(f"Using model: Claude 3.5 Sonnet Old")
            message = self.client.messages.create(
                model=model_id,
                temperature=temperature,
                max_tokens=max_tokens,
                messages=messages,
                top_k=3
            )
            self._record_usage(model_id, message)
            return message
        except Exception as e:
            print(f"Error generating message with Claude 3.5 Sonnet Old: {e}")
            return None
        
    @retry_with_models(model_list_attr_name='sonnet_model_ids')
    def generate_message_agent_sonnet_sync(self, messages, temperature=0.01, max_tokens=4096, *, model_id, client):
        """
        Generates a message using a specific Claude Sonnet model provided by the retry decorator.
        Handles the core API call logic for a single attempt.
        The retry logic (looping through models, handling rate limits) is managed by the decorator.

        Args:
            messages: The message list for the API call.
            temperature: The temperature setting for the API call.
            max_tokens: The max_tokens setting for the API call.
            model_id (str): The specific model ID to use for this attempt (injected by decorator).
            client (anthropic.Anthropic): The client instance to use for this attempt (injected by decorator).

        Returns:
            The message object on success, or raises an exception handled by the decorator.
        """

        
        # The decorator provides 'client' and 'model_id' and handles the retry loop/errors.
        # This function now only needs to make the specific API call.
        print(f"Executing API call with model: {model_id}")
        message = client.messages.create(
            model=model_id,
            temperature=temperature,
            max_tokens=max_tokens,
            messages=messages,
            top_k=3 # Specific parameter for this method
        )
        print(f"\033[92mCLAUDE USAGE : {message.usage}\033[0m")



        self._record_usage(model_id, message)
        self._log_claude_message(message, messages, model_id)
        return message
    
    
    async def generate_message_agent_sonnet_new(self, messages, temperature=0.01, max_tokens=4096):
        # Use Claude 3.5 Sonnet
        model_id = self.deployments_map["Claude 3.5 Sonnet"]
        try:
            print(f"Using model: Claude 3.5 Sonnet")
            message = self.client.messages.create(
                model=model_id,
                temperature=temperature,
                max_tokens=max_tokens,
                messages=messages,
                top_k=3
            )
            self._record_usage(model_id, message)
            return message
        except Exception as e:
            print(f"Error generating message with Claude 3.5 Sonnet: {e}")
            return None
    
    @retry_with_models(model_list_attr_name='sonnet_model_ids')
    def generate_message_agent_sonnet_new_sync(self, messages, temperature=0.01, max_tokens=4096, *, model_id, client):
        # Use Claude 3.5 Sonnet
        # model_id = self.deployments_map["Claude 3.5 Sonnet"]
        message = client.messages.create(
            model=model_id,
            temperature=temperature,
            max_tokens=max_tokens,
            messages=messages,
            top_k=3
        )
        
        print(f"\033[92mCLAUDE USAGE : {message.usage}\033[0m")

        self._record_usage(model_id, message)
        self._log_claude_message(message, messages, model_id)
        return message
    
    @retry_with_models(model_list_attr_name='sonnet_model_ids')
    def generate_final_summary_cbp(self, messages, temperature=0.01, max_tokens=5120, *, model_id, client):
        # Use Claude 3.5 Sonnet
        # model_id = self.deployments_map["Claude 3.5 Sonnet"]
        model_id = random.choice(["claude-3-7-sonnet-20250219", "claude-sonnet-4-20250514"])
        #model_id = random.choice(["claude-3-5-sonnet-latest", "claude-3-5-sonnet-20241022", "claude-3-7-sonnet-20250219", "claude-sonnet-4-20250514"])
        message = client.messages.create(
            model=model_id,
            temperature=temperature,
            max_tokens=max_tokens,
            messages=messages,
            top_k=3
        )
        print(f"\033[92mCLAUDE USAGE : {message.usage}\033[0m")

        self._record_usage(model_id, message)
        self._log_claude_message(message, messages, model_id)
        return message
        
    
    async def generate_message_agent_sonnet_v2_sync(self, schema: BaseModel, messages, temperature=0.01, max_tokens=4096, *, model_id, client):
        # Use Claude 3.5 Sonnet
        # model_id = self.deployments_map["Claude 3.5 Sonnet"]
        _analysis_schema = schema.model_json_schema()

        tools = [
            {
                "name": "build_analysis_result",
                "description": "build the analysis object",
                "input_schema": _analysis_schema
            }
        ]

        message = client.messages.create(
            model=model_id,
            temperature=temperature,
            max_tokens=max_tokens,
            messages=messages,
            tools=tools,
            tool_choice={"type": "tool", "name": "build_analysis_result"},
            top_k=3
        )
        print(f"\033[92mCLAUDE USAGE : {message.usage}\033[0m")

        self._record_usage(model_id, message)
        self._log_claude_message(message, messages, model_id)
        return message
    
      
    @retry_with_models(model_list_attr_name='sonnet_model_ids')
    def generate_message_agent_sonnet_v2_sync(self, schema: BaseModel = None, messages=None, temperature=0.01, max_tokens=4096, *, model_id, client, handle_tool_calls=True):
        """
        Generates a message using a specific model with tool support.
        
        Args:
            schema: Optional Pydantic schema for structured output
            messages: The message list for the API call
            temperature: Temperature setting for generation
            max_tokens: Maximum tokens to generate
            model_id: Model ID (injected by decorator)
            client: Client instance (injected by decorator)
            handle_tool_calls: Whether to automatically handle tool calls
        """
        # Initialize tools list
        all_tools = []
        tool_choice = {"type": "any"}
        
        # Add schema-based tool if schema is provided
        if schema:
            _analysis_schema = schema.model_json_schema()
            schema_tool = {
                "name": "structure_result",
                "description": "Use the provided schema to structure the result. This is essential for building all final outputs/completions. outputs not built like this would crash the pipeline.",
                "input_schema": _analysis_schema
            }
            all_tools.append(schema_tool)
            # tool_choice = {"type": "tool", "name": "build_analysis_result"}
        
        # Add calculator and sum tools
        all_tools.append(ClaudeTools.get_calculator_tool())
        all_tools.append(ClaudeTools.get_sum_tool())
        all_tools.append(ClaudeTools.get_document_source_page_tool())
        
        # Create message with tools
        message = client.messages.create(
            model=model_id,
            temperature=temperature,
            max_tokens=max_tokens,
            messages=messages,
            tools=all_tools,
            tool_choice=tool_choice,
            top_k=3
        )
        
        # Handle tool calls if requested
        if handle_tool_calls:
            # Create a conversation history that we'll update with each tool call
            conversation = messages.copy() if messages else []
            current_message = message
            
            # Keep processing tool calls until we get a final response
            max_tool_calls = 10  # Limit the number of tool calls to prevent infinite loops
            tool_call_count = 0
            
            while current_message.stop_reason == "tool_use" and tool_call_count < max_tool_calls:
                tool_call_count += 1
                print(f"TOOL CALL COUNT: {tool_call_count}")
                print(f"CURRENT MESSAGE: {current_message}")
                
                # Find all tool use blocks
                tool_uses = [block for block in current_message.content 
                            if hasattr(block, 'type') and block.type == "tool_use" 
                            and block.name in ["calculator", "sum_numbers", "fetch_document_source_page"]]
                
                if not tool_uses:
                    break
                
                # Add the assistant's response to the conversation
                conversation.append({"role": "assistant", "content": current_message.content})
                
                # Process all tool calls and create a single response with all results
                tool_results = []
                for tool_use in tool_uses:
                    # Process tool call
                    tool_result = ClaudeTools.process_tool_call(tool_use.name, tool_use.input)
                    
                    # Convert dict result to string if needed
                    if isinstance(tool_result, dict):
                        import json
                        tool_result_json = json.dumps(tool_result)
                    
                    # Add to tool results
                    res = {
                        "type": "tool_result",
                        "tool_use_id": tool_use.id,
                        "content": f'{tool_result.get("result", tool_result_json)}'
                    }
                    tool_results.append(res)
                    
                conversation.append({"role": "user", "content": tool_results})
                
                # Add all tool results to the conversation
                # conversation.append({
                #     "role": "user", 
                #     "content": tool_results
                # })
                
                print(f"CONVERSATION: {conversation}")
                # Get the next response
                next_message = client.messages.create(
                    model=model_id,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    messages=conversation,
                    tools=all_tools,
                    tool_choice=tool_choice if tool_call_count == max_tool_calls else {"type": "tool", "name": "structure_result"},
                    top_k=3
                )
                
                # Record usage and log message
                self._record_usage(model_id, next_message)
                self._log_claude_message(next_message, conversation, model_id)
                
                # Update current message for the next iteration
                current_message = next_message
            
            # Return the final message after all tool calls
            return current_message
        
        # If not handling tool calls, just return the original message
        self._record_usage(model_id, message)
        print(f'{model_id} USAGE: {message.usage}')
        self._log_claude_message(message, messages, model_id)
        return message
    
    
    async def generate_message_agent_haiku(self, messages, temperature=0.01, max_tokens=4096):
        # Use Claude 3.5 Sonnet
        model_id = self.deployments_map["Claude 3.5 Haiku"]
        try:
            print(f"Using model: Claude 3.5 Haiku")
            message = self.client.messages.create(
                model=model_id,
                temperature=temperature,
                max_tokens=max_tokens,
                messages=messages,
                top_k=3
            )
            self._record_usage(model_id, message)
            return message
        except Exception as e:
            print(f"Error generating message with Claude 3.5 Haiku: {e}")
            return None
    
    async def generate_message_agent_opus(self, messages, temperature=0.01, max_tokens=4096):
        # Use Claude 3 Opus
        model_id = self.deployments_map["Claude 3 Haiku"]
        try:
            print(f"Using model: Claude 3 Opus")
            message = self.client.messages.create(
                model=model_id,
                temperature=temperature,
                max_tokens=max_tokens,
                messages=messages
            )
            self._record_usage(model_id, message)
            return message
        except Exception as e:
            print(f"Error generating message with Claude 3 Opus: {e}")
            return None
    
    async def generate_message_agent_master(self, messages, temperature=0.01, max_tokens=4096):
        # Use Claude 3 Haiku
        model_id = self.deployments_map["Claude 3 Opus"]
        try:
            print(f"Using model: Claude 3 Haiku")
            message = self.client.messages.create(
                model=model_id,
                temperature=temperature,
                max_tokens=max_tokens,
                messages=messages
            )
            self._record_usage(model_id, message)
            return message
        except Exception as e:
            print(f"Error generating message with Claude 3 Haiku: {e}")
            return None
        
    def generate_message_sync2(self, messages, system, temperature=0.01, model="Claude 3 Sonnet", max_tokens=4096):
        try:
            message = self.client.messages.create(
                model=self.deployments_map[model],
                temperature=temperature,
                max_tokens=max_tokens,
                system = system,
                messages=messages
            )
            self._record_usage(self.deployments_map[model], message)
            return message.content[0].text
        except Exception as e:
            print(f"Error generating message from generate_message_sync2: {e}")
            return None
    
    def generate_message_sync(self, messages, system, temperature=0.01, model="Claude 3 Sonnet", max_tokens=4096):
        for model_name, model_id in self.deployments_map.items():
            try:
                print(f"Trying model: {model_name}")
                message = self.client.messages.create(
                    model=model_id,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    system=system,
                    messages=messages
                )  
                print(f"\033[32mCLAUDE 3.5 USAGE : {message.usage}\033[0m")
                self._record_usage(model_id, message)
                return message.content[0].text
            except Exception as e:
                print(f"Error generating message with model {model_name}: {e}")
    
    
    def generate_message_sync_haiku(self, messages, system="", temperature=0.01, max_tokens=4096):
        model_id = self.deployments_map["Claude 3.5 Haiku"]
        try:
            message = self.client.messages.create(
                model=model_id,
                temperature=temperature,
                max_tokens=max_tokens,
                system=system,
                messages=messages
            )
            self._record_usage(model_id, message)
            return message.content[0].text
        except Exception as e:
            print(f"Error generating message with model Haiku 3.5: {e}")
            return None        

    def _record_usage(self, model_id, message):
        """Helper method to record Claude API usage"""
        try:
            LLMUsage.create(
                provider="claude",
                model=model_id,
                input_tokens=message.usage.input_tokens,
                output_tokens=message.usage.output_tokens
            )
        except Exception as e:
            print(f"Error recording Claude usage: {e}")

    def _log_claude_message(self, message, messages, model_id):
        return
        """Helper method to log Claude API messages to JSON file"""
        if not os.path.exists('claude_messages.json'):
            with open('claude_messages.json', 'w') as f:
                json.dump([], f)

        # Load existing messages
        with open('claude_messages.json', 'r') as f:
            messages_list = json.load(f)

        # Prepare log entry
        log_entry = {
            'request_messages': messages,
            'model': model_id,
            'usage': {
                'input_tokens': message.usage.input_tokens,
                'output_tokens': message.usage.output_tokens
            }
        }

        # Check content and log accordingly
        if message.content and len(message.content) > 0:
            content_block = message.content[0]
            if content_block.type == "tool_use":
                log_entry['output_type'] = 'tool_use'
                log_entry['tool_name'] = content_block.name
                log_entry['tool_id'] = content_block.id
                log_entry['tool_input'] = content_block.input
            elif content_block.type == "text":
                log_entry['output_type'] = 'text'
                log_entry['text_output'] = content_block.text
            else:
                log_entry['output_type'] = 'unknown'
                log_entry['raw_content'] = str(message.content)
        else:
            log_entry['output_type'] = 'empty_content'
            log_entry['raw_content'] = str(message.content)

        messages_list.append(log_entry)

        # Save updated messages list
        with open('claude_messages.json', 'w') as f:
            json.dump(messages_list, f, indent=4)


if __name__ == "__main__":
    from init import app
    with app.app_context():
        class TestSchema(BaseModel):
            answer: str
            confidence: float
            
        client = ClaudeService()
        response = client.generate_message_agent_sonnet_v2_sync(TestSchema,[
            {"role": "user", 
            "content": """What is the total cost of all items including food and vehicles separately then calculate the average? 

    source: Food items_.pdf
    page_number: 1
    Food items:
    Artisanal Cheese Board ($220)
    Organic Fresh Produce Box ($250)
    Gourmet Coffee Subscription ($300)
    Specialty Olive Oil Set ($350)
    Premium Meat Bundle (Steaks, Roasts, etc.) ($450)
    Fine Wine Selection (6 bottles) ($600)
    Caviar and Truffle Gift Basket ($800)
    High-End Chocolate Assortment ($900)
    Exotic Fruit and Cheese Platter ($1200)
    Luxury Food Hamper (Gourmet meats, cheeses, etc.) ($1500)
    Premium Seafood Platter (Caviar, Lobster, etc.) ($1800) 
    Gourmet Food and Wine Gift Tower ($2000)
    
    Vehicles:
    Toyota Camry ($15,000)
    Honda Civic ($20,000)
    Ford Mustang ($25,000)


    """
            }
            ])
        print(response.usage)
    