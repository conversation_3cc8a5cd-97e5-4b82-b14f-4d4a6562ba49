"""Add bids bidFiles & Users

Revision ID: a6f1982f3617
Revises: 8893287e3963
Create Date: 2025-02-17 22:54:18.779555

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'a6f1982f3617'
down_revision = '8893287e3963'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('users',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('status', sa.String(length=255), nullable=False),
    sa.Column('role', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_users_name'), ['name'], unique=False)
        batch_op.create_index(batch_op.f('ix_users_status'), ['status'], unique=False)

    op.create_table('bids',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('user_id', sa.String(length=36), nullable=False),
    sa.Column('status', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('bids', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_bids_name'), ['name'], unique=False)
        batch_op.create_index(batch_op.f('ix_bids_status'), ['status'], unique=False)
        batch_op.create_index(batch_op.f('ix_bids_user_id'), ['user_id'], unique=False)

    op.create_table('bid_files',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('bid_id', sa.String(length=36), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('file_type', sa.String(length=50), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['bid_id'], ['bids.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('bid_files', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_bid_files_bid_id'), ['bid_id'], unique=False)

    with op.batch_alter_table('chunks', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_chunks_file_id'), ['file_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_chunks_project_id'), ['project_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_chunks_title'), ['title'], unique=False)

    with op.batch_alter_table('file', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_file_file_dirtry'), ['file_dirtry'], unique=False)
        batch_op.create_index(batch_op.f('ix_file_file_type'), ['file_type'], unique=False)
        batch_op.create_index(batch_op.f('ix_file_name'), ['name'], unique=False)
        batch_op.create_index(batch_op.f('ix_file_pdf_conversion_status'), ['pdf_conversion_status'], unique=False)
        batch_op.create_index(batch_op.f('ix_file_project_id'), ['project_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_file_status'), ['status'], unique=False)
        batch_op.create_index(batch_op.f('ix_file_tried'), ['tried'], unique=False)

    with op.batch_alter_table('project', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_project_entity_type'), ['entity_type'], unique=False)
        batch_op.create_index(batch_op.f('ix_project_name'), ['name'], unique=False)

    with op.batch_alter_table('requirement', schema=None) as batch_op:
        batch_op.alter_column('name',
               existing_type=mysql.TEXT(),
               nullable=False)
        batch_op.create_index('ix_requirement_name', ['name'], unique=False, mysql_length=191)
        batch_op.create_index(batch_op.f('ix_requirement_type'), ['type'], unique=False)

    with op.batch_alter_table('tender', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_tender_project_id'), ['project_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_tender_status'), ['status'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('tender', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_tender_status'))
        batch_op.drop_index(batch_op.f('ix_tender_project_id'))

    with op.batch_alter_table('requirement', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_requirement_type'))
        batch_op.drop_index('ix_requirement_name', mysql_length=191)
        batch_op.alter_column('name',
               existing_type=mysql.TEXT(),
               nullable=True)

    with op.batch_alter_table('project', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_project_name'))
        batch_op.drop_index(batch_op.f('ix_project_entity_type'))

    with op.batch_alter_table('file', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_file_tried'))
        batch_op.drop_index(batch_op.f('ix_file_status'))
        batch_op.drop_index(batch_op.f('ix_file_project_id'))
        batch_op.drop_index(batch_op.f('ix_file_pdf_conversion_status'))
        batch_op.drop_index(batch_op.f('ix_file_name'))
        batch_op.drop_index(batch_op.f('ix_file_file_type'))
        batch_op.drop_index(batch_op.f('ix_file_file_dirtry'))

    with op.batch_alter_table('chunks', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_chunks_title'))
        batch_op.drop_index(batch_op.f('ix_chunks_project_id'))
        batch_op.drop_index(batch_op.f('ix_chunks_file_id'))

    with op.batch_alter_table('bid_files', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_bid_files_bid_id'))

    op.drop_table('bid_files')
    with op.batch_alter_table('bids', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_bids_user_id'))
        batch_op.drop_index(batch_op.f('ix_bids_status'))
        batch_op.drop_index(batch_op.f('ix_bids_name'))

    op.drop_table('bids')
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_users_status'))
        batch_op.drop_index(batch_op.f('ix_users_name'))

    op.drop_table('users')
    # ### end Alembic commands ###
