"""modify name column in requirement table

Revision ID: 2ca40ce63b78
Revises: ff1e3f26b86e
Create Date: 2025-01-02 19:34:41.956191

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '2ca40ce63b78'
down_revision = 'ff1e3f26b86e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('requirement', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_requirement_name'), ['name'], unique=False, mysql_length=700)
        batch_op.create_index(batch_op.f('ix_requirement_status'), ['status'], unique=False)
        batch_op.create_index(batch_op.f('ix_requirement_tried'), ['tried'], unique=False)
        batch_op.create_index(batch_op.f('ix_requirement_type'), ['type'], unique=False)

    with op.batch_alter_table('tender', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_tender_project_id'), ['project_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_tender_status'), ['status'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('tender', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_tender_status'))
        batch_op.drop_index(batch_op.f('ix_tender_project_id'))

    with op.batch_alter_table('requirement', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_requirement_type'))
        batch_op.drop_index(batch_op.f('ix_requirement_tried'))
        batch_op.drop_index(batch_op.f('ix_requirement_status'))
        batch_op.drop_index(batch_op.f('ix_requirement_name'))

    # ### end Alembic commands ###
