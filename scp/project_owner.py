import sys
import os
import re
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models import File, Project, Chunks, Tags, Requirement
from sqlalchemy import or_
import uuid
import json
import anthropic
from typing import Dict, List, Any
from services.data_handler import DataManager
import pandas as pd
from services.pinecone_vector_db import CanopyAI
import requests

class EngineeringProjectOwner:
    def __init__(self, req_id, socket_manager, project_id, engineering_id, engineer_file_id):
        self.req_id = req_id
        self.socket_manager = socket_manager
        self.project_id = project_id
        self.engineering_id = engineering_id
        self.engineer_file_id = engineer_file_id
        self.cheat_sheets = {}
        self.working_doc = {}
        self.comparison_summary = {}
        self.ai_comparison_results = {}
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.env_file_path = os.path.join(parent_dir, 'env.json')
        self.setup_agent()
        self.data_manager = DataManager()
        self.vec_db = CanopyAI()
        
        # Get files for both project and engineering
        self.project_files = File.get_by(project_id=project_id)
        self.engineering_files = File.get_by(project_id=engineering_id)
        self.engineering_file = File.get_by(project_id=engineering_id)[0]
        self.section_ids = [chunk['id'] for chunk in Chunks.get_by(file_id=self.engineering_file['id'])]
        print(f"Engineering files: {self.engineering_files}")
        # Read the Engineering Area of Focus Excel file
        excel_path = os.path.join(os.path.dirname(__file__), 'Eng Area of focus.xlsx')
        self.requirements_df = pd.read_excel(excel_path)

    def load_env(self):
        with open(self.env_file_path, 'r') as f:
            return json.load(f)
        
    def setup_claude_client(self, api_key):
        self.client = anthropic.Anthropic(api_key=api_key)

    def setup_agent(self):
        env_data = self.load_env()
        self.setup_claude_client(env_data.get('CLAUDE_API_KEY', ''))

    def add_event(self, request_id, event_name, data):
        self.socket_manager.emit_to_client(request_id, event_name, data, '/epo')

    def _extract_engineering_code(self, file):
        """Extract engineering code from file name or metadata"""
        file_name = file['name']
        document_type = re.search(r'-[A-Za-z0-9]{3}-', file_name)
        document_discipline = re.search(r'-[A-Z]{2}-', file_name)
        if document_type and document_discipline:
            return document_type.group(0)[1:-1], document_discipline.group(0)[1:-1]
        return None

    def get_areas_focus_by_vdrl(self, vdrl_code):
        """Get the areas of focus for a given VDRL code."""
        try:
            headers = {
                "x-project": "YWllbmVyZ3k6aHNxdHJ0cDdoc3dpamp6dWU3c2N3emV4ZDhodTRh",
                "Content-Type": "application/json", 
                "Authorization": ""
            }
            
            payload = {
                "email": "<EMAIL>",
                "password": "a123456",
                "role": "user"
            }

            url = "https://backend.aienergy-oilandgas.com/v3/api/custom/aienergy/area-focus/engineering-documents"
            
            response = requests.get(
                url,
                headers=headers,
                data=json.dumps(payload)
            )
            response.raise_for_status()
            data = response.json()
            
            # Search through the data to find matching VDRL code
            for item in data['list']['data']:
                if item['vdrl_code'] == vdrl_code:
                    return item['areas_focus']
            return None
            
        except Exception as e:
            print(f"Error occurred: {e}")
            return None
        
    def _get_requirements_for_engineering(self, document_type, document_discipline):
        """Get minimum requirements for a given engineering code"""
        
        # Clean whitespace from column names
        self.requirements_df.columns = self.requirements_df.columns.str.strip()

        # Filter by document type and check if discipline is present
        results = self.requirements_df[
            (self.requirements_df['Document Type'] == document_type) &
            (self.requirements_df['Disciplines'].fillna('').str.contains(document_discipline, case=False))
        ]

        # Get key information as list
        key_info = results[['Key information']].dropna().to_dict(orient='records')
        
        # Generate expanded queries using Claude
        expanded_queries = []
        if key_info and len(key_info) > 0:
            prompt = f"""
            Given these key information requirements for {document_type} in {document_discipline}:
            {key_info[0]['Key information']}

            Generate specific search queries that would help find relevant content in technical documents. 
            Focus on key technical terms and specifications.
            Format each query on a new line.
            Keep queries concise but descriptive.
            Do not include variations of technical terminology.
            """
            
            try:
                response = self.client.messages.create(
                    model="claude-3-haiku-20240307",
                    max_tokens=500,
                    temperature=0.15,
                    messages=[{
                        "role": "user",
                        "content": prompt
                    }]
                )
                
                if response.content:
                    # Split response into individual queries
                    expanded_queries = [q.strip() for q in response.content[0].text.split('\n') if q.strip()]
                    
            except Exception as e:
                print(f"Error generating expanded queries: {e}")
                # Fallback to original requirements
                expanded_queries = [info['Key information'] for info in key_info]

        return expanded_queries

    def compare_requirements(self):
        """Compare engineering responses against minimum requirements using vector search"""
        try:
            # Initialize results storage
            comparison_results = []
            
            # Get requirements for each engineering file

                # Extract engineering code from file name or metadata
            document_type, document_discipline = ('DCA', 'PP')

            
            # Get requirements for this engineering code
            requirements = self._get_requirements_for_engineering(document_type, document_discipline)
            
            # Process each requirement
            for requirement in requirements:
                # Create search query from requirement
                search_query = f"{requirement}"
                
                # Query project documents
                project_results = self.vec_db.get_data(self.project_id, [search_query], limit=3)
                
                # Query engineering documents
                engineering_results = self.vec_db.get_data_by_section_ids(self.engineering_id, [search_query], self.section_ids, limit=3)
                
                # Extract relevant content from results
                project_content = []
                if project_results and project_results['metadatas']:
                    for metadata_list in project_results['metadatas']:
                        for metadata in metadata_list:
                            if 'documents' in metadata:
                                project_content.append(metadata['documents'])
                
                engineering_content = []
                if engineering_results and engineering_results['metadatas']:
                    for metadata_list in engineering_results['metadatas']:
                        for metadata in metadata_list:
                            if 'documents' in metadata:
                                engineering_content.append(metadata['documents'])
                
                # Prepare comparison result
                comparison = {
                    'requirement': requirement,
                    'document_type': document_type,
                    'document_discipline': document_discipline,
                    'project_content': project_content,
                    'engineering_content': engineering_content,
                    # 'project_sources': len(project_content),
                    # 'engineering_sources': len(engineering_content)
                }
                
                comparison_results.append(comparison)
            
            return comparison_results
            
        except Exception as e:
            print(f"Error comparing requirements: {e}")
            return []

    def generate_evaluation_summary(self, comparison_results):
        """Generate a comparison analysis using Claude to evaluate the results"""
        try:
            # Prepare the prompt for Claude
            prompt = """You are an expert in analyzing engineering requirements and responses.
            Please analyze the following comparison data and provide a clear evaluation summary.
            
            Format your response in HTML with the following structure:
            
            <div>
                <h3>Area of Focus 1: [Exact Area of Focus Name]</h3>
                <ul>
                    <li><h5>Score: <span class='score'><Score>X%</Score></span></h5></li>
                    <li><h5 class='reason highlight'>Reason: [Detailed analysis of engineering team's response vs project requirements, including specific examples, gaps, and impact assessment]</h5></li>
                    <li><h5 style="font-size: 14px; color: #006699; margin: 0" class='criteria_<area_of_focus_name>_evaluation_summary'>[Concise summary of evaluation with key findings and recommendations]</h5></li>
                </ul>
            </div>

            <div>
                <h3>Overall Evaluation Summary</h3>
                <ul>
                    <li><h5>Total Score: <span class='score'><Score>X%</Score></span></h5></li>
                    <li><h5 class='reason highlight'>[Comprehensive summary of all areas of focus evaluations, highlighting key strengths, weaknesses, and critical recommendations]</h5></li>
                </ul>
            </div>

            IMPORTANT FORMATTING RULES:
            - Title should be size 18
            - Section headers should be size 15
            - Body text should be 11-12 size
            - No text should be bold
            - Maintain exact HTML structure and tag hierarchy
            - Use exact tag names as provided
            - Include % symbol for all scores
            - For partial information scenarios, use the 'highlight-red' class
            - Keep exact casing of area of focus names as provided

            The analysis must include:
            * Comprehensive breakdown of ALL requirements from project and engineering team analysis
            * Exhaustive analysis of each equipment item, examining every relevant detail and component
            * Rigorous gap analysis comparing project requirements vs engineering team's submissions
            * Meticulous explanation of scoring rationale, supported by concrete examples
            * Extensive evidence and direct quotes from both project and engineering team analysis
            * Detailed examples demonstrating compliance or non-compliance
            * In-depth assessment of how any gaps impact project success
            * Specific recommendations for addressing deficiencies

            Here is the comparison data to analyze:
            """
            
            # Add comparison data to prompt
            for result in comparison_results:
                prompt += f"\n\nArea of Focus: {result['requirement']}"
                prompt += f"\nProject Content: {' '.join(result['project_content'])}"
                prompt += f"\nEngineering Content: {' '.join(result['engineering_content'])}"
                prompt += "\n---"
            
            # Get analysis from Claude
            response = self.client.messages.create(
                model="claude-3-haiku-20240307",
                max_tokens=4000,
                temperature=0.3,
                system="You are an expert in analyzing engineering requirements and responses. Provide clear, detailed evaluations in a structured text format.",
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )
            
            # Return the text response directly
            return response.content[0].text
                
        except Exception as e:
            print(f"Error generating evaluation: {e}")
            return f"Error generating evaluation: {str(e)}"

    def process_requirements_comparison(self):
        """Main method to process requirements comparison and generate analysis"""
        try:
            # Step 1: Compare requirements using vector search
            comparison_results = self.compare_requirements()

            if not comparison_results:
                return {
                    "error": "No comparison results generated"
                }
            
            analysis = self.generate_evaluation_summary(comparison_results)
            self.add_event(self.req_id, 'completed', {'message': analysis})
            print(f"Analysis: {analysis}")
            return {
                "analysis": analysis
            }
            
        except Exception as e:
            print(f"Error in process_requirements_comparison: {e}")
            return {
                "error": str(e)
            }

if __name__ == "__main__":
    project_owner = EngineeringProjectOwner("53dc535d-2325-4a5b-a01d-46e6dd3f53d5", None, "project_id", "engineering_id")
    results = project_owner.process_requirements_comparison()
    print(json.dumps(results, indent=2))
    # Store results in a JSON file
    output_file = f"engineering_comparison_results.json"
    try:
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"Results saved to {output_file}")
    except Exception as e:
        print(f"Error saving results to file: {e}")