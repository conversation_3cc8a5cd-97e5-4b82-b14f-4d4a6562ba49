"""add column for similar on category table

Revision ID: 700216b0a1f4
Revises: 6d87021e7dbf
Create Date: 2024-08-22 00:12:21.285117

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '700216b0a1f4'
down_revision = '6d87021e7dbf'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('categories', schema=None) as batch_op:
        batch_op.add_column(sa.Column('similar', sa.Text(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('categories', schema=None) as batch_op:
        batch_op.drop_column('similar')

    # ### end Alembic commands ###
