# /home/<USER>/file_processor_tasks.py

from services.handle_new_cbp_documents import ChronobidDocumentProcessor
from init import app, socketio, socket_manager

def evaluate_files_cbp_task(req_id):
    try:
        with app.app_context():
            cbp_loader = ChronobidDocumentProcessor(socket_manager)
            cbp_loader.process_cbp(req_id)
            print('starting to evaluate cbp files...')
                
            return "CBP File evaluation completed successfully."
    except Exception as e:
        print(f"Error during cbp file evaluation: {e}")
        return str(e)
