import os
import random
import time
from openai import AzureOpenAI, OpenAI
import traceback
import json
class AssistantManager:
    def __init__(self):
        # Get the path to the parent directory
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # Load environment variables from env.json file in the parent directory
        env_file_path = os.path.join(parent_dir, 'env.json')
        print(env_file_path)
        with open(env_file_path, 'r') as f:
            env_data = json.load(f)
        
        # Set the important variables from the JSON data
        self.azure_endpoint = env_data.get("AZURE_ENDPOINT", "")
        self.azure_api_key = env_data.get("AZURE_API_KEY", "")
        self.openai_api_keys = [env_data.get("OPENAI_API_KEY", "")]
        self.assistant_types = [env_data.get("ASSISTANT_TYPE", "")]
        self.assistant_accounts_keys = [env_data.get("ASSISTANT_ACCOUNTS_KEY", "")]
        self.assistant_endpoints = [env_data.get("ASSISTANT_ENDPOINT", "")]

        
        # Initialize AzureOpenAI client
        self.azure_client = AzureOpenAI(
            azure_endpoint=self.azure_endpoint,
            api_key=self.azure_api_key,
            api_version=env_data.get("AZURE_API_VERSION")
        )

        # Map deployments to models
        self.deployments_map = {
            "gpt-3.5-turbo-16k": ["ai_energy-3-16k-1"],
            "gpt-4-turbo": ["ai_energy-4-turbo-1"],
            "gpt-3.5-turbo": ["ai_energy-3-turbo"]
        }

    def get_response(self, messages, temperature=0.01, model="gpt-3.5-turbo-16k", steps=5, **kwargs):
        resp = ""
        while steps > 0:
            steps -= 1
            try:
                completion = self.azure_client.chat.completions.create(
                    model=random.choice(self.deployments_map[model]),
                    temperature=temperature,
                    messages=messages,
                    **kwargs
                )   
                resp = completion 
                break
            except Exception as e:
                traceback.print_exc()
                time.sleep(30)
        return resp

    def create_assistant(self, name):
        account_no = random.randint(0, len(self.assistant_accounts_keys) - 1)
        if self.assistant_types[account_no] == "openai":
            openai_client = OpenAI(
                api_key=self.assistant_accounts_keys[account_no]
            )
        elif self.assistant_types[account_no] == "azure":
            openai_client = AzureOpenAI(
                api_key=self.assistant_accounts_keys[account_no],  
                api_version="2024-02-15-preview",
                azure_endpoint=self.assistant_endpoints[account_no]
            )
        assistant = openai_client.beta.assistants.create(
            name=name,
            tools=[{"type": "retrieval"}],
            model="gpt-4-0125-preview"
        )
        return f"{assistant.id}/{account_no}"

    def get_assistant_response(self, assistant_id, account_no, user_message, instructions="", model="gpt-4-0125-preview"):
        openai_client = OpenAI(
            api_key=self.assistant_accounts_keys[account_no]
        )

        thread = openai_client.beta.threads.create()
        thread_id = thread.id
        thread_message = openai_client.beta.threads.messages.create(
            thread_id,
            role="user",
            content=user_message
        )

        run = openai_client.beta.threads.runs.create(
            thread_id=thread_id,
            assistant_id=assistant_id,
            model=model
        )
        
        run_id = run.id

        while 1:
            if(run.status != "queued" and run.status != "in_progress"):
                break
            time.sleep(10)
            run = openai_client.beta.threads.runs.retrieve(
                thread_id=thread_id,
                run_id=run.id
            )

        thread_messages = openai_client.beta.threads.messages.list(thread_id)
        openai_client.beta.threads.delete(thread_id)
        return thread_messages.data[0].content[0].text

    def create_assistant_file(self, assistant_id, account_no, file_path, purpose="assistant"):
        openai_client = OpenAI(
            api_key=self.assistant_accounts_keys[account_no]
        )

        resp = openai_client.files.create(
            file=open(file_path, "rb"),
            purpose="assistants"
        )
        file_id = resp.id

        assistant_file = openai_client.beta.assistants.files.create(
            assistant_id=assistant_id,
            file_id=file_id,
        )

        return assistant_file

    def delete_assistant_file(self, assistant_id, account_no, file_id):
        openai_client = OpenAI(
            api_key=self.assistant_accounts_keys[account_no]
        )

        deleted_assistant_file = openai_client.beta.assistants.files.delete(
            assistant_id=assistant_id,
            file_id=file_id
        )

        openai_client.files.delete(file_id)

        return deleted_assistant_file

# if __name__ == "__main__":
#     assistant_manager = AssistantManager()
#     print(assistant_manager.azure_api_key,  assistant_manager.azure_endpoint)
#     resp = assistant_manager.get_response([{"role": "system", "content": f'''Generate a list of key points that  can be used to search or query on a search engine 
#         to gain more information to analyze the given text. The key points needs to be directly connected to the provided text by the user.
#         If the context is small there will be less key points, if it's big there will be more phrases. For example, if the context is a paragraph of 5 sentences, number of key points should be 4-5 keypoints. 
#         If the context is more descriptive number of key points reduced. For example, if one specific key point was described in 3-4 sentences, then, it will still be one key point.
#         Provide each key point in a new line''',
#         },{
#             "role": "user",
#             "content": f'''What is geopolitics?'''
#         }])
#     print(resp)
