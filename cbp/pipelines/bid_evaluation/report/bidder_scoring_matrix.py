def generate_bidder_comparison_table(comparison_data):
    """
    Generate bidder comparison table HTML based on comparison table data.
    
    Args:
        comparison_data: Dictionary containing comparison_table with criteria and bidder data
    """
    
    # Extract comparison table data
    comparison_table = comparison_data.get("data_aggregrated_report").get("comparison_table",[])
    
    if not comparison_table:
        return "<p>No comparison data available.</p>"
    
    # Get all bidders from the first criteria entry
    first_criteria = comparison_table[0]
    bidders = []
    bidder_scores = {}
    
    # Extract bidder names and initialize score tracking
    for key in first_criteria.keys():
        if key not in ["criteria", "Scores"] and not key.endswith("_summary"):
            bidders.append(key)
            bidder_scores[key] = 0
    
    # Calculate total scores for each bidder
    for criteria in comparison_table:
        scores = criteria.get("Scores", {})
        for bidder, score in scores.items():
            if score != "N/A" and score:
                # Extract numeric value from percentage string
                numeric_score = float(score.replace('%', '')) if isinstance(score, str) else float(score)
                bidder_scores[bidder] += numeric_score
    
    # Sort bidders by total score (descending)
    sorted_bidders = sorted(bidders, key=lambda x: bidder_scores.get(x, 0), reverse=True)
    
    # Generate HTML
    html_template = f"""
    <div style="font-family: Arial, sans-serif; max-width: 1000px; margin: 20px auto; padding: 20px; background: white; border: 1px solid #ddd;">
        <h2 style="color: #2c5aa0; text-align: left; border-bottom: 2px solid #2c5aa0; padding-bottom: 10px;">
            BIDDER COMPARISON AND SCORING MATRIX
        </h2>
        
        <div style="margin-bottom: 30px;">
            <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                <thead>
                    <tr style="background-color: #2c5aa0; color: white;">
                        <th style="border: 1px solid #ddd; padding: 12px; text-align: left; font-weight: bold;">
                            Factor
                        </th>"""
    
    # Add bidder column headers
    for bidder in sorted_bidders:
        html_template += f"""
                        <th style="border: 1px solid #ddd; padding: 12px; text-align: center; font-weight: bold;">
                            {bidder}
                        </th>"""
    
    html_template += """
                    </tr>
                </thead>
                <tbody>"""
    
    # Add criteria rows
    for criteria in comparison_table:
        criteria_name = criteria.get("criteria", "")
        scores = criteria.get("Scores", {})
        
        html_template += f"""
                    <tr style="background-color: #f8f9fa;">
                        <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">
                            {criteria_name}
                        </td>"""
        
        # Add scores for each bidder
        for bidder in sorted_bidders:
            score = scores.get(bidder, "N/A")
            html_template += f"""
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">
                            {score}
                        </td>"""
        
        html_template += """
                    </tr>"""
    
    # Add total row
    html_template += f"""
                    <tr style="background-color: #2c5aa0; color: white; font-weight: bold;">
                        <td style="border: 1px solid #ddd; padding: 12px;">
                            TOTAL SCORE (100%)
                        </td>"""
    
    for bidder in sorted_bidders:
        total_score = bidder_scores.get(bidder, 0)
        html_template += f"""
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">
                            {total_score:.1f}%
                        </td>"""
    
    html_template += """
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div style="margin-top: 30px;">
            <h3 style="color: #2c5aa0; margin-bottom: 15px;">RECOMMENDATION</h3>
            <p style="font-weight: bold; margin-bottom: 15px;">
                Based on the scores in the above evaluation criteria, DRA recommends that The Client proceeds with negotiations with the following bidders, in order of preference:
            </p>
            <ol style="margin-left: 20px; line-height: 1.6;">"""
    
    # Add ranked recommendations
    for i, bidder in enumerate(sorted_bidders):
        total_score = bidder_scores.get(bidder, 0)
        if total_score > 0:  # Only include bidders with scores
            html_template += f"""
                <li style="margin-bottom: 8px;">
                    <strong>{bidder}</strong> - Total Score: {total_score:.1f}%
                </li>"""
    
    html_template += """
            </ol>
        </div>
        
        <div style="margin-top: 30px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7;">
            <h4 style="color: #856404; margin-top: 0;">EVALUATION NOTES</h4>
            <p style="margin-bottom: 0; color: #856404;">
                This comparison is based on the comprehensive evaluation of all submitted bids against the specified criteria. 
                Scores reflect compliance levels and technical merit as assessed against tender requirements.
            </p>
        </div>
    </div>
    """
    
    return html_template


def generate_detailed_criteria_analysis(comparison_data):
    """
    Generate detailed analysis for each criteria showing bidder performance.
    
    Args:
        comparison_data: Dictionary containing comparison_table with criteria and bidder data
    """
    
    comparison_table = comparison_data.get("data_aggregrated_report").get("comparison_table",[])
    
    if not comparison_table:
        return "<p>No detailed analysis data available.</p>"
    
    html_template = f"""
    <div style="font-family: Arial, sans-serif; max-width: 1000px; margin: 20px auto; padding: 20px; background: white; border: 1px solid #ddd;">
        <h2 style="color: #2c5aa0; text-align: left; border-bottom: 2px solid #2c5aa0; padding-bottom: 10px;">
            DETAILED CRITERIA ANALYSIS
        </h2>
        <p style="font-weight: bold; margin-bottom: 25px;">
            The following provides detailed analysis of each bidder's performance against the evaluation criteria:
        </p>
    """
    
    # Process each criteria
    for criteria in comparison_table:
        criteria_name = criteria.get("criteria", "")
        scores = criteria.get("Scores", {})
        
        html_template += f"""
        <div style="margin-bottom: 40px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <h3 style="background-color: #f5f5f5; margin: 0; padding: 15px; color: #2c5aa0; border-bottom: 1px solid #e0e0e0;">
                {criteria_name}
            </h3>
            <div style="padding: 20px;">"""
        
        # Add bidder analyses for this criteria
        for key, value in criteria.items():
            if key not in ["criteria", "Scores"] and isinstance(value, str) and value != "N/A":
                bidder_name = key
                score = scores.get(bidder_name, "N/A")
                
                html_template += f"""
                <div style="margin-bottom: 20px; padding: 15px; background-color: #fafafa; border-left: 4px solid #2c5aa0;">
                    <h4 style="color: #2c5aa0; margin-top: 0; margin-bottom: 10px;">
                        {bidder_name} - Score: {score}
                    </h4>
                    <div style="line-height: 1.6;">
                        {value}
                    </div>
                </div>"""
        
        html_template += """
            </div>
        </div>"""
    
    html_template += """
    </div>
    """
    
    return html_template


# Example usage with your data format:
def process_scoring_matrix(data):
    """
    Process the comparison data and generate both summary table and detailed analysis.
    
    Args:
        data: Dictionary containing comparison_table data
    """
    
    # Generate comparison table
    comparison_html = generate_bidder_comparison_table(data)
    
    # Generate detailed analysis
    detailed_html = generate_detailed_criteria_analysis(data)
    
    # Combine both sections
    combined_html = f"""
    {comparison_html}
    <div style="margin-top: 40px;"></div>
    {detailed_html}
    """
    
    return combined_html

data = {
        "data_aggregrated_report": {
            "comparison_table": [
                {
                    "criteria": "Price",
                    "Weir (Cyclones)": "<p>Weir (Cyclones) demonstrates partial compliance with pricing requirements, providing detailed equipment and spare parts costs but missing some key pricing elements. They are tied for the highest score (30%) alongside METSO, indicating a relatively strong position. However, Weir's submission appears more comprehensive than METSO's, as they don't have the same issues with pricing validity duration or non-binding terms. Compared to FLS and Multotec, Weir offers a more complete pricing structure, though there's still room for improvement in addressing all tender requirements.</p>",
                    "METSO (Cyclones)": "<p>METSO (Cyclones) shares the highest score (30%) with Weir, providing detailed pricing for main equipment and some spare parts. However, they have notable weaknesses, including failure to price three required line items and uncertainty due to their 'budgetary' and 'non-binding' terms. This creates a significant disadvantage compared to Weir, despite the equal score. METSO's pricing is more comprehensive than FLS's submission but lacks the stability and completeness expected in a competitive bid, particularly when compared to Weir's more solid offering.</p>",
                    "FLS (Cyclones)": "<p>FLS (Cyclones) received the lowest score (22.5%) among the evaluated bidders, reflecting significant gaps in their pricing submission. While they provide basic equipment identification and general terms, their pricing structure falls short in several key areas, particularly in spare parts categorization and detailed cost breakdowns. This places FLS at a distinct disadvantage compared to Weir and METSO, who offer more comprehensive pricing information. The lack of properly structured pricing methodology represents a substantial compliance gap that sets FLS apart negatively from the other bidders.</p>",
                    "Multotec (Cyclones)": "N/A",
                    "Scores": {
                        "Weir (Cyclones)": "30%",
                        "METSO (Cyclones)": "30%",
                        "FLS (Cyclones)": "22.5%",
                        "Multotec (Cyclones)": "N/A"
                    }
                },
                {
                    "criteria": "Delivery",
                    "Weir (Cyclones)": "<p>Weir (Cyclones) demonstrates a mixed performance in delivery, ranking third among the evaluated bidders with a score of 15%. Their strength lies in detailed timeline planning, which is crucial for project management. However, they face significant challenges with critical discrepancies in Incoterms and unclear commercial terms. These issues, particularly the conflicting delivery points, present substantial project risks that set them apart negatively from other bidders. Compared to METSO and Multotec, Weir's proposal lacks the clarity and consistency in delivery conditions that are essential for smooth project execution.</p>",
                    "METSO (Cyclones)": "<p>METSO (Cyclones) emerges as the top performer in the delivery criterion, scoring 18% and outpacing the nearest competitor by 1.5 percentage points. Their proposal stands out with clear FCA Incoterms and well-defined delivery conditions, providing a solid foundation for project planning. However, METSO shares a common weakness with other bidders in lacking specific delivery timeline commitments. This gap in timeline specificity, while a drawback, is less severe compared to the issues faced by Weir and Multotec, particularly in terms of consistency and clarity of terms.</p>",
                    "FLS (Cyclones)": "N/A",
                    "Multotec (Cyclones)": "<p>Multotec (Cyclones) ranks second in the delivery criterion with a score of 16.5%, positioning them between METSO and Weir. Their proposal offers basic delivery timeframes and Incoterms, providing a foundation for project planning. However, Multotec faces challenges similar to Weir with inconsistencies in delivery points and lacks the comprehensive commercial terms found in METSO's proposal. While they outperform Weir in overall delivery compliance, Multotec falls short of METSO's clarity and consistency in delivery conditions. The lack of specific delivery details remains a common weakness across all evaluated bidders.</p>",
                    "Scores": {
                        "Weir (Cyclones)": "15%",
                        "METSO (Cyclones)": "18%",
                        "FLS (Cyclones)": "N/A",
                        "Multotec (Cyclones)": "16.5%"
                    }
                },
                {
                    "criteria": "Compliant bid",
                    "Weir (Cyclones)": "<p>Weir (Cyclones) demonstrates partial compliance with the tender requirements, showing significant gaps across technical specifications. While they provide basic cyclone configuration and some instrumentation details, their submission lacks comprehensive documentation for critical components, detailed electrical specifications, and complete quality/testing procedures. Compared to other bidders, Weir's performance is on par, as all bidders received the same low score, indicating similar levels of non-compliance across the board. However, Weir's submission appears to offer slightly more detail in cyclone configuration compared to some competitors.</p>",
                    "METSO (Cyclones)": "<p>METSO (Cyclones) exhibits significant non-compliance with the project requirements, addressing only about 5% of mandatory technical specifications. Their submission provides basic cyclone equipment details but fails to cover crucial areas such as complete questionnaire responses, instrumentation specifications, and quality management systems. In comparison to other bidders, METSO's performance aligns with the overall low compliance level, as all bidders received identical scores. However, METSO's submission appears to be the least comprehensive, lacking in critical project components that some other bidders partially addressed.</p>",
                    "FLS (Cyclones)": "<p>FLS (Cyclones) shows partial compliance with tender requirements, with notable gaps in documentation and quality management system evidence. While they provide some quality-related documentation, their submission lacks critical elements such as ISO 9001:2008 certification evidence and detailed project quality plans. Compared to other bidders, FLS's performance is consistent with the overall low compliance trend. However, FLS stands out slightly by providing more quality-related documentation than some competitors, although still falling short of full compliance.</p>",
                    "Multotec (Cyclones)": "<p>Multotec (Cyclones) demonstrates partial compliance with tender requirements, showing significant gaps in process guarantees, documentation submissions, and electrical specifications. Their submission indicates a basic understanding of mechanical requirements but fails to address critical aspects of system integration and performance guarantees. In comparison to other bidders, Multotec's performance aligns with the overall low compliance level. However, Multotec's basic understanding of mechanical requirements appears to be a slight advantage, although insufficient to differentiate them significantly from the other bidders in terms of overall compliance.</p>",
                    "Scores": {
                        "Weir (Cyclones)": "9%",
                        "METSO (Cyclones)": "9%",
                        "FLS (Cyclones)": "9%",
                        "Multotec (Cyclones)": "9%"
                    }
                },
                {
                    "criteria": "Fulfilment of technical specification",
                    "Weir (Cyclones)": "<p>Weir (Cyclones) demonstrates partial alignment with technical specifications, addressing approximately 45% of requirements. Their strengths include basic cyclone configuration, some instrumentation, and general mechanical parameters. However, they show significant weaknesses in detailed process specifications, comprehensive electrical/control systems, and specific quality/testing procedures. Compared to other bidders, Weir (Cyclones) appears to be on par with METSO and FLS in terms of technical compliance, but provides more comprehensive information than Multotec, for which no detailed summary is available.</p>",
                    "METSO (Cyclones)": "<p>METSO (Cyclones) shows minimal technical compliance, providing basic cyclone equipment specifications but failing to address the majority of mandatory requirements. Their submission lacks complete questionnaire responses, instrumentation specifications, quality management systems, control systems integration, and critical infrastructure components. This represents a significant gap in required technical information. While METSO's overall compliance level appears similar to Weir and FLS, their submission seems less comprehensive, particularly in terms of quality management and control systems compared to FLS.</p>",
                    "FLS (Cyclones)": "<p>FLS (Cyclones) demonstrates partial compliance with quality management system requirements but falls short in providing comprehensive evidence of ISO 9001:2008 certification and required quality documentation. Their focus on quality management sets them slightly apart from Weir and METSO, who don't explicitly mention this aspect. However, FLS shares a similar overall compliance level with these competitors. The lack of detailed information on other technical aspects makes it challenging to fully compare FLS's performance across all technical specification areas.</p>",
                    "Multotec (Cyclones)": "N/A",
                    "Scores": {
                        "Weir (Cyclones)": "45%",
                        "METSO (Cyclones)": "45%",
                        "FLS (Cyclones)": "45%",
                        "Multotec (Cyclones)": "45%"
                    }
                }
            ],
            "technical_criteria_names": [],
            "bidder_summary": [
                {
                    "criteria": "Price",
                    "Weir (Cyclones)_score": "30%",
                    "Weir (Cyclones)_summary": "The bidder demonstrates partial compliance with pricing requirements, providing detailed equipment and spare parts costs but missing some key pricing elements required by the tender.",
                    "METSO (Cyclones)_score": "30%",
                    "METSO (Cyclones)_summary": "The bidder has provided detailed pricing for the main equipment items and some spare parts categories (commissioning and one-year spares), but has failed to price three required line items: capital spares, first fills, and special tools. Additionally, their proposal indicates the pricing is \"budgetary for 30 days\" and \"non-binding,\" creating uncertainty about their final pricing commitment.",
                    "FLS (Cyclones)_score": "22.5%",
                    "FLS (Cyclones)_summary": "The bidder demonstrates partial compliance with pricing requirements by providing basic equipment identification and general commercial terms. However, the submission significantly fails to meet comprehensive pricing structure requirements, particularly in spare parts categorization (commissioning, 1-year operational, capital) and detailed line item cost breakdowns. The absence of properly structured pricing methodology and missing correlation between spare parts categories and operational phases represents a substantial gap in tender compliance.",
                    "Multotec (Cyclones)_score": "N/A",
                    "Multotec (Cyclones)_summary": "The bidder's pricing submission demonstrates partial compliance with tender requirements, providing detailed equipment costs but showing significant gaps in spare parts pricing structure and documentation. Key discrepancies exist in commissioning spares pricing and operational spares breakdown, with several required pricing components marked as \"Not Required\" without proper justification."
                },
                {
                    "criteria": "Delivery",
                    "Weir (Cyclones)_score": "15%",
                    "Weir (Cyclones)_summary": "The bidder's delivery proposal demonstrates partial compliance with requirements, showing detailed timeline planning but containing critical discrepancies in Incoterms and lacking clarity on commercial terms. The conflicting delivery points and incomplete commercial terms present significant project risks.",
                    "METSO (Cyclones)_score": "18%",
                    "METSO (Cyclones)_summary": "The bidder demonstrates partial compliance with delivery requirements, providing clear FCA Incoterms and delivery conditions but lacking specific delivery timeline commitments that are essential for project planning and execution.",
                    "FLS (Cyclones)_score": "N/A",
                    "FLS (Cyclones)_summary": "The bidder demonstrates partial compliance with delivery requirements, providing key timeline information and payment terms but lacking specific delivery dates and including broad disclaimers regarding potential delays.",
                    "Multotec (Cyclones)_score": "16.5%",
                    "Multotec (Cyclones)_summary": "The bidder demonstrates partial compliance with delivery requirements, providing basic delivery timeframes and Incoterms but showing inconsistencies in delivery points and lacking comprehensive commercial terms and specific delivery details."
                },
                {
                    "criteria": "Compliant bid",
                    "Weir (Cyclones)_score": "9%",
                    "Weir (Cyclones)_summary": "The bidder demonstrates partial compliance with the tender requirements, showing significant gaps in technical specifications across process, mechanical, and electrical requirements. While basic cyclone configuration and some instrumentation details are provided, the submission lacks comprehensive documentation for critical components, detailed electrical specifications, and complete quality/testing procedures.",
                    "METSO (Cyclones)_score": "9%",
                    "METSO (Cyclones)_summary": "The bidder's submission demonstrates significant non-compliance with the Las Truchas 2.3MTPA Production Revamp Project requirements. While basic cyclone equipment specifications were provided, the submission fails to address approximately 95% of mandatory technical requirements including complete questionnaire responses, instrumentation specifications, quality management systems, and critical project components such as control systems, utilities, and documentation standards.",
                    "FLS (Cyclones)_score": "9%",
                    "FLS (Cyclones)_summary": "The bidder demonstrates partial compliance with the tender requirements, with significant gaps in documentation and quality management system evidence. While some quality-related documentation is provided, the submission lacks critical elements including ISO 9001:2008 certification evidence and detailed project quality plans.",
                    "Multotec (Cyclones)_score": "9%",
                    "Multotec (Cyclones)_summary": "The bidder demonstrates partial compliance with tender requirements, showing significant gaps particularly in process guarantees, documentation submissions, and electrical specifications. The submission indicates basic understanding of mechanical requirements but fails to address critical aspects of system integration and performance guarantees."
                },
                {
                    "criteria": "Fulfilment of technical specification",
                    "Weir (Cyclones)_score": "45%",
                    "Weir (Cyclones)_summary": "The bidder shows partial alignment with technical specifications, addressing approximately 45% of requirements. While basic cyclone configuration, some instrumentation, and general mechanical parameters are covered, significant gaps exist in detailed process specifications, comprehensive electrical/control systems, and specific quality/testing procedures.",
                    "METSO (Cyclones)_score": "45%",
                    "METSO (Cyclones)_summary": "The bidder demonstrates minimal technical compliance by providing basic cyclone equipment specifications but fails to address the vast majority of mandatory requirements including complete questionnaire responses, instrumentation specifications, quality management systems, control systems integration, and critical infrastructure components, representing a 95% gap in required technical information.",
                    "FLS (Cyclones)_score": "45%",
                    "FLS (Cyclones)_summary": "The bidder demonstrates partial compliance with the quality management system requirements outlined in the tender document, but fails to provide comprehensive evidence of ISO 9001:2008 certification and required quality documentation.",
                    "Multotec (Cyclones)_score": "45%",
                    "Multotec (Cyclones)_summary": ""
                }
            ],
            "technical_criteria_eval": []
        }
    }
# Example usage:
# Assuming your data is in a variable called 'comparison_data'
html_output = process_scoring_matrix(data)
# 
# To save to file:
with open('bidder_comparison_report.html', 'w', encoding='utf-8') as f:
     f.write(html_output)