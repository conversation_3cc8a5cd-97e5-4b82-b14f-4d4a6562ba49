#app.py

import eventlet
# eventlet.monkey_patch()

from init import app, socketio, socket_manager
from extensions import db
from socket_instance import socket_instance

import os, uuid, json, random
from flask import request, jsonify
from werkzeug.utils import secure_filename
from services.cbp_file_processor import CBPFileProcessor
from services.scp_file_processor import SCPFileProcessor
import asyncio, threading
from flask_socketio import emit, Namespace, leave_room
from models import Project, Requirement, BidFile, Bids, Users
from services.faiss_embedding import FaissEmbeddingGPU
from functools import wraps
from zipfile import ZipFile
import tempfile
import eventlet
import time
import json
from task.file_test_tasks import test_task
from task.file_processor_tasks import process_files_task
from task.file_processor_tasks_scp import process_files_scp_task
from task.file_processor_tasks_bid import process_files_bid_task
from namespaces.file_processing_namespace import ProcessingFilesNamespace
from services.zip_extractor import ZipExtractor
import redis
from datetime import datetime
from dotenv import load_dotenv
load_dotenv()

# Load environment variables from env.json file in the parent directory
env_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'env.json')
with open(env_file_path, 'r') as f:
    env_data = json.load(f)

UPLOAD_FOLDER = env_data.get("DATA_DIR")
CHRONOBID_FOLDER = env_data.get("CHRONOBID_DIR")
SPECS_FOLDER = env_data.get("SPECS_DIR")

ALLOWED_EXTENSIONS = {'doc', 'docx', 'pdf', 'txt', "csv", "xls", "xlsx", 'zip'}

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['CHRONOBID_FOLDER'] = CHRONOBID_FOLDER
app.config['SPECS_FOLDER'] = SPECS_FOLDER


main_event_loop = asyncio.new_event_loop()
loop_thread = threading.Thread(target=main_event_loop.run_forever, daemon=True)
loop_thread.start()

# socket_manager = socket_instance.get_instance()
print(f"check3 Socket manager initialized: {socket_manager is not None}")



socketio.on_namespace(ProcessingFilesNamespace('/processing_files', socket_manager))
REDIS_CLIENT = redis.StrictRedis(host='localhost', port=6379, db=0, decode_responses=True)


@app.route('/health')
def home():
    return 'TEST OK!'

def success_response(message, data={}, status_code=200):
    return jsonify({"success": True, "message": message, "data": data}), status_code

def error_response(message, data={}, status_code=400):
    return jsonify({"success": False, "message": message, "data": data}), status_code

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/shutdown', methods=['POST'])
def shutdown():
    main_event_loop.call_soon_threadsafe(main_event_loop.stop)
    loop_thread.join()
    return "Event loop stopped."

def run_coroutine_in_loop(coroutine):
    """Helper function to run a coroutine in the main event loop"""
    future = asyncio.run_coroutine_threadsafe(coroutine, main_event_loop)
    return future.result()

def async_route(f):
    @wraps(f)
    def wrapper(*args, **kwargs):
        # Run the coroutine in the main event loop
        return run_coroutine_in_loop(f(*args, **kwargs))
    return wrapper

@app.route('/upload_zip_bid', methods=['POST'])
def upload_zip_bid():
    try:
        if 'file' not in request.files:
            return error_response('No file part')

        zip_file = request.files['file']
        directory_name = request.form.get('directory_name')
        user_id = request.form.get('user_id')

        if not zip_file or not directory_name:
            return error_response('Both zip file and directory name are required')

        if not zip_file.filename.endswith('.zip'):
            return error_response('File must be a ZIP archive')

        zip_processor = ZipExtractor(CHRONOBID_FOLDER, user_id)
        result = zip_processor.process_zip(zip_file, directory_name)

        return success_response(
            message="ZIP file extracted successfully",
            data={"directory": result}
        )

    except Exception as e:
        return error_response(f"Error processing ZIP file: {str(e)}")
    

@app.route('/upload_zip_to_bid', methods=['POST'])
def upload_zip_to_bid():
    try:
        if 'file' not in request.files:
            return error_response('No file part')

        zip_file = request.files['file']
        user_id = request.form.get('user_id')
        bid_id = request.form.get('bid_id')

        if not zip_file or not bid_id:
            return error_response('Both zip file and bid are required')

        if not zip_file.filename.endswith('.zip'):
            return error_response('File must be a ZIP archive')
        
        if not user_id:
            return error_response('Invalid User')

        try:
            user = Users.get_single(user_id)
        except Exception as e:
            return 'Invalid user', 400

        zip_processor = ZipExtractor(CHRONOBID_FOLDER, user_id)
        result = zip_processor.update_file_to_zip(zip_file, bid_id)

        return success_response(
            message="ZIP file extracted successfully",
            data={"directory": result}
        )

    except Exception as e:
        return error_response(f"Error processing ZIP file: {str(e)}")

@app.route('/upload_bid_files', methods=['POST'])
def upload_bid_file():
    files = request.files.getlist('file')
    directory_name = request.form.get('directory_name')
    user_id = request.form.get('user_id')

    if not files or not directory_name:
        return error_response('files and directory name are required')
    
    if not user_id:
        return error_response('Invalid User')

    if not all(allowed_file(file.filename) for file in files):
        return error_response('Some files are not allowed', 400)
    
    try:
        user = Users.get_single(user_id)
    except Exception as e:
        return 'Invalid user', 400
    
    bid_id = str(uuid.uuid4())
    folder_path = os.path.join(CHRONOBID_FOLDER, 'bid', bid_id)
    os.makedirs(folder_path, mode=0o775, exist_ok=True)

    try:

        result = []
        file_names_arr = []
        all_success = True
        all_failed = True
        lock = threading.Lock()

        bid_data = {
            'id': bid_id,
            'name': directory_name,
            'user_id': user_id
        }
        Bids.create(**bid_data)

        # Async file upload
        def upload_file_task(file_itm):
            nonlocal all_success, all_failed
            file_name = secure_filename(file_itm.filename)
            file_path = os.path.join(folder_path, file_name)
            file_names_arr.append(file_name)
            
            with app.app_context():
                try:
                    bid_file_id = str(uuid.uuid4())
                    print(f"uploading file: {file_name}")
                    file_itm.save(file_path)
                    file_extension = file_name.rsplit('.', 1)[-1].lower()

                    file_data = {
                        'id': bid_file_id,
                        'name': file_name,
                        'bid_id': bid_id,
                        'status': "done",
                        'file_type': file_extension,
                    }
                    BidFile.create(**file_data)

                    process_files_bid_task.delay(bid_id, file_path)

                    result.append({"success": True, "file": file_name})
                    print(f"Successfully uploaded: {file_name}")
                except Exception as e:
                    with lock:
                        all_success = False
                    result.append({"success": False, "file": file_name, "error": str(e)})
                    print(f"Failed to upload file {file_name}: {str(e)}")

        
        # Run all uploads concurrently
        def _upload_files_concurrently():
            print(f"Starting concurrent file uploads for {len(files)} files.")
            pool = eventlet.greenpool.GreenPool()  # Create a GreenPool to manage concurrent tasks
            for file_itm in files:
                pool.spawn(upload_file_task, file_itm)  # Spawn each file upload as a greenlet
            pool.waitall()
            print("Concurrent file uploads finished now processing...")
        
        async def async_upload_files_concurrently():
            await asyncio.to_thread(_upload_files_concurrently)

        try:
            ensure_event_loop_and_run(async_upload_files_concurrently())
        except Exception as e:
            print(f"Error during file upload processing: {e}")  

        # Call the Celery task to process files asynchronously
        # process_bid_files_task.delay(bid, file_names_arr)

        if all_success:
            message = "All files successfully uploaded"
        elif all_failed:
            message = "All files failed to upload"
        else:
            message = "One or more files failed to upload"
        
        print(f"Upload process complete: {message}")
                
        return {'bid_id': bid_id, 'message' : message, 'result' : result}
    except Exception as e:
        return error_response(f"Error saving file: {str(e)}", 500)


@app.route('/upload_files_to_bid', methods=['POST'])
def upload_files_to_bid():
    files = request.files.getlist('file')
    bid_id = request.form.get('bid_id')
    user_id = request.form.get('user_id')

    print(files, bid_id, user_id)

    if not files or not bid_id:
        return error_response('files and bid are required')
    
    if not user_id:
        return error_response('Invalid User')

    if not all(allowed_file(file.filename) for file in files):
        return error_response('Some files are not allowed', 400)
    
    folder_path = os.path.join(CHRONOBID_FOLDER, 'bid', bid_id)

    try:

        result = []
        file_names_arr = []
        all_success = True
        all_failed = True
        lock = threading.Lock()

        # Async file upload
        def upload_file_task(file_itm):
            nonlocal all_success, all_failed
            file_name = secure_filename(file_itm.filename)
            file_path = os.path.join(folder_path, file_name)
            file_names_arr.append(file_name)
            
            with app.app_context():
                try:
                    bid_file_id = str(uuid.uuid4())
                    print(f"uploading file: {file_name}")
                    file_itm.save(file_path)
                    file_extension = file_name.rsplit('.', 1)[-1].lower()

                    file_data = {
                        'id': bid_file_id,
                        'name': file_name,
                        'bid_id': bid_id,
                        'status': 'done',
                        'file_type': file_extension,
                    }
                    BidFile.create(**file_data)

                    process_files_bid_task.delay(bid_id, file_path)

                    result.append({"success": True, "file": file_name})
                    print(f"Successfully uploaded: {file_name}")
                except Exception as e:
                    with lock:
                        all_success = False
                    result.append({"success": False, "file": file_name, "error": str(e)})
                    print(f"Failed to upload file {file_name}: {str(e)}")

        
        # Run all uploads concurrently
        def _upload_files_concurrently():
            print(f"Starting concurrent file uploads for {len(files)} files.")
            pool = eventlet.greenpool.GreenPool()  # Create a GreenPool to manage concurrent tasks
            for file_itm in files:
                pool.spawn(upload_file_task, file_itm)  # Spawn each file upload as a greenlet
            pool.waitall()
            print("Concurrent file uploads finished now processing...")
        
        async def async_upload_files_concurrently():
            await asyncio.to_thread(_upload_files_concurrently)

        try:
            ensure_event_loop_and_run(async_upload_files_concurrently())
        except Exception as e:
            print(f"Error during file upload processing: {e}")  

        # Call the Celery task to process files asynchronously
        # process_bid_files_task.delay(bid, file_names_arr)

        if all_success:
            message = "All files successfully uploaded"
        elif all_failed:
            message = "All files failed to upload"
        else:
            message = "One or more files failed to upload"
        
        print(f"Upload process complete: {message}")
                
        return {'bid_id': bid_id, 'message' : message, 'result' : result}
    except Exception as e:
        return error_response(f"Error saving file: {str(e)}", 500)


@app.route('/search_tender_requirement', methods=['POST'])
@async_route
async def search_tender_requirement():
    try:
        # Get data from JSON instead of form data since we're sending JSON
        data = request.get_json()
        
        request_id = data.get('request_id')
        search_queries = data.get('search_queries')
        k = data.get('k', 5)  # Get k from request or default to 5

        if not search_queries:
            return error_response("Query parameter is required", 400)

        # Initialize FAISS instance
        faiss_instance = FaissEmbeddingGPU(request_id=request_id, batch_size=1000)
        if not faiss_instance:
            return error_response("FAISS instance not initialized", 500)

        # Directly await the search method
        # results = await faiss_instance.search(query=query, k=k)
        search_results = await asyncio.gather(*[
            faiss_instance.search(query, k=3) for query in search_queries
        ])

        print('this is search results: ', search_results)
        # Format the results
        formatted_results = []
        for query_result in search_results:
            query_matches = []
            for doc, score in query_result:
                query_matches.append({
                    'content': doc.page_content,
                    'metadata': doc.metadata,
                    'score': float(score)  # Convert numpy float to Python float
                })
            formatted_results.append(query_matches)

        # Return success response with formatted results
        return success_response({
            'request_id': request_id,
            'query_count': len(search_queries),
            'results': formatted_results
        })

    except Exception as e:
        print(f"Error in search_tender_requirement: {str(e)}")
        return error_response(f"Search failed: {str(e)}", 500)

@app.route('/get_top_3_pages_documents', methods=['GET'])
@async_route
async def get_top_3_pages_documents():
    try:
        # Get request_id from query parameters
        request_id = request.args.get('request_id')
        if not request_id:
            return error_response("Request ID parameter is required", 400)

        # Initialize FAISS instance
        faiss_instance = FaissEmbeddingGPU(request_id=request_id, batch_size=1000)
        if not faiss_instance:
            return error_response("FAISS instance not initialized", 500)

        # Get top 3 pages documents
        top_3_pages_docs = faiss_instance.get_top_3_pages_documents()
        
        # Format the results
        formatted_results = []
        for doc in top_3_pages_docs:
            formatted_results.append({
                'content': doc.page_content,
                'metadata': doc.metadata
            })

        # Return success response with formatted results
        return success_response({
            'request_id': request_id,
            'document_count': len(top_3_pages_docs),
            'results': formatted_results
        })

    except Exception as e:
        print(f"Error in get_top_3_pages_documents: {str(e)}")
        return error_response(f"Failed to retrieve top 3 pages documents: {str(e)}", 500)


@app.route('/test_zip', methods=['POST'])
def test_zip():
    files = request.files.getlist('file')
    is_zip = request.form.get('is_zip')
    if is_zip == 'true':
        extracted_files = []
        with tempfile.TemporaryDirectory() as temp_dir:
            for file_itm in files:
                file_name = secure_filename(file_itm.filename)
                if not file_name.endswith('.zip'):
                    return error_response(f"File {file_name} is not a zip archive", 400)
                
                zip_path = os.path.join(temp_dir, file_name)
                file_itm.save(zip_path)

                # Extract the zip file
                with ZipFile(zip_path, 'r') as zip_ref:
                    zip_ref.extractall(temp_dir)

                    # Get all file paths inside the extracted directory
                    for root, _, file_names in os.walk(temp_dir):
                        for name in file_names:
                            full_path = os.path.join(root, name)
                            extracted_files.append(full_path)

            # Open all extracted files for further processing
            files = [
                open(file_path, 'rb')
                for file_path in extracted_files
            ]


@app.route('/test_celery', methods=['GET'])
def test_celery():
    request_id = request.args.get('request_id')
    if not request_id:
        return error_response("Request ID parameter is required", 400)
    print('starting the test celery task')
    req_id = '2eee6a65-4aca-41f6-8e38-0fcd24ea2df3'
    filenames_arr = ['1.Invitation_to_Tender.docx', '2.Tender_Specifications.docx', '3.Bill_of_Quantities.docx']
    existing_req = False
    test_task.delay(req_id, filenames_arr, existing_req)
    return {'request_id': req_id, 'message' : 'good job'}






@app.route('/upload_tender_requirement', methods=['POST'])
def upload_tender_requirement():
    
    if 'file' not in request.files:
        return error_response('No file part')

    files = request.files.getlist('file')

    print(f'received {len(files)} files uploaded...')
    package_id = request.form.get('package_id')
    req_id = request.form.get('request_id', False)
    # is_zip = request.form.get('is_zip')

    if not package_id:
        return 'package is required', 400

    if not files:
        return error_response('No selected files', 400)

    if not all(allowed_file(file.filename) for file in files):
        return error_response('Some files are not allowed', 400)
   
    if req_id :
        existing_req = Requirement.get_single(req_id)
        
        file_status_key = f"{req_id}_files_uploaded"
        exist_file_status = json.loads(REDIS_CLIENT.get(file_status_key))
        print('exist file status: ', exist_file_status)
        
        new_file_status = {'uploaded': exist_file_status.get('uploaded') +len(files), 'processed': exist_file_status.get('processed')}
        print('new file status: ', new_file_status)
        REDIS_CLIENT.set(file_status_key, json.dumps(new_file_status))
        
        print('recovered existing request with number of files : ', len(existing_req))

        last_batch = 1
        batch_keys = REDIS_CLIENT.keys(f"{req_id}_files_batch_*")
        print('these are batch keys', batch_keys)
        
        if batch_keys:
            batch_numbers = [int(key.split('_')[-1]) for key in batch_keys]  # Extract batch numbers
            last_batch = max(batch_numbers)  # Get the last batch number
    
        request_batch = last_batch + 1  # Increment batch number
        REDIS_CLIENT.set(f"{req_id}_files_batch_{request_batch}", json.dumps({'uploaded': len(files), 'processed': 0}))

    else:
        existing_req = False
        req_id = str(uuid.uuid4())
        request_batch = 1
        file_status_key = f"{req_id}_files_uploaded"
        REDIS_CLIENT.set(file_status_key, json.dumps({'uploaded': len(files), 'processed': 0}))
        REDIS_CLIENT.set(f"{req_id}_files_batch_{request_batch}", json.dumps({'uploaded': len(files), 'processed': 0}))
    
    # Step 3: Fetch package details
    try:
        package = Project.get_single(package_id)
    except Exception as e:
        return 'Invalid package_id', 400

     
    folder_path = os.path.join(CHRONOBID_FOLDER, package_id, req_id)
    os.makedirs(folder_path, mode=0o775, exist_ok=True)

    try:
        result = []
        file_names_arr = []
        all_success = True
        all_failed = True
        lock = threading.Lock()

        if not existing_req:
            create_requirement_with_retry(req_id, file_names_arr, package_id)

        # Async file upload
        def upload_file_task(file_itm):
            nonlocal all_success, all_failed
            file_name = secure_filename(file_itm.filename)
            file_path = os.path.join(folder_path, file_name)
            file_names_arr.append(file_name)
            try:
                print(f"uploading file: {file_name}")
                file_itm.save(file_path)
                result.append({"success": True, "file": file_name})
                print(f"Successfully uploaded: {file_name}")
            except Exception as e:
                with lock:
                    all_success = False
                result.append({"success": False, "file": file_name, "error": str(e)})
                print(f"Failed to upload file {file_name}: {str(e)}")

        
        # Run all uploads concurrently
        def _upload_files_concurrently():
            print(f"Starting concurrent file uploads for {len(files)} files.")
            pool = eventlet.greenpool.GreenPool()  # Create a GreenPool to manage concurrent tasks
            for file_itm in files:
                pool.spawn(upload_file_task, file_itm)  # Spawn each file upload as a greenlet
            pool.waitall()
            print("Concurrent file uploads finished now processing...")

            # request_file_status = json.loads(REDIS_CLIENT.get(f"{req_id}_files_uploaded"))
            # request_file_status.update({file_name: request_file_status.get(file_name, False) for file_name in file_names_arr})
            # REDIS_CLIENT.set(f"{req_id}_files_uploaded", json.dumps(request_file_status))
        
        async def async_upload_files_concurrently():
            await asyncio.to_thread(_upload_files_concurrently)

        try:
            ensure_event_loop_and_run(async_upload_files_concurrently())
        except Exception as e:
            print(f"Error during file upload processing: {e}")  

        # Call the Celery task to process files asynchronously
        # process_files_task.delay(req_id, file_names_arr, existing_req, request_batch)
        process_files_task.apply_async(args=[req_id, file_names_arr, existing_req, request_batch])

        if all_success:
            message = "All files successfully uploaded"
        elif all_failed:
            message = "All files failed to upload"
        else:
            message = "One or more files failed to upload"
        
        print(f"Upload process complete: {message}")
                
        return {'request_id': req_id, 'message' : message, 'result' : result}
    except Exception as e:
        print(e)

@app.route('/upload_project_requirement', methods=['POST'])
def upload_project_requirement():
    
    if 'file' not in request.files:
        return error_response('No file part')

    files = request.files.getlist('file')

    print(f'received {len(files)} files uploaded...')
    project_id = request.form.get('project_id')
    req_id = request.form.get('request_id', False)
    # is_zip = request.form.get('is_zip')

    if not project_id:
        return 'project is required', 400

    if not files:
        return error_response('No selected files', 400)

    if not all(allowed_file(file.filename) for file in files):
        return error_response('Some files are not allowed', 400)

    if req_id :
        existing_req = Requirement.get_single(req_id)
    else:
        existing_req = False
        req_id = str(uuid.uuid4())
    
    # Step 3: Fetch package details
    try:
        project = Project.get_single(project_id)
    except Exception as e:
        return 'Invalid project', 400

     
    folder_path = os.path.join(SPECS_FOLDER, project_id, req_id)
    os.makedirs(folder_path, mode=0o775, exist_ok=True)

    try:
        
        result = []
        file_names_arr = []
        all_success = True
        all_failed = True
        lock = threading.Lock()

        if not existing_req:
            create_requirement_with_retry(req_id, file_names_arr, project_id, type='scp')

        # Async file upload
        def upload_file_task(file_itm):
            nonlocal all_success, all_failed
            file_name = secure_filename(file_itm.filename)
            file_path = os.path.join(folder_path, file_name)
            file_names_arr.append(file_name)
            try:
                print(f"uploading file: {file_name}")
                file_itm.save(file_path)
                result.append({"success": True, "file": file_name})
                print(f"Successfully uploaded: {file_name}")
            except Exception as e:
                with lock:
                    all_success = False
                result.append({"success": False, "file": file_name, "error": str(e)})
                print(f"Failed to upload file {file_name}: {str(e)}")

        
        # Run all uploads concurrently
        def _upload_files_concurrently():
            print(f"Starting concurrent file uploads for {len(files)} files.")
            pool = eventlet.greenpool.GreenPool()  # Create a GreenPool to manage concurrent tasks
            for file_itm in files:
                pool.spawn(upload_file_task, file_itm)  # Spawn each file upload as a greenlet
            pool.waitall()
            print("Concurrent file uploads finished now processing...")
        
        async def async_upload_files_concurrently():
            await asyncio.to_thread(_upload_files_concurrently)

        try:
            ensure_event_loop_and_run(async_upload_files_concurrently())
        except Exception as e:
            print(f"Error during file upload processing: {e}")  

        # Call the Celery task to process files asynchronously
        process_files_scp_task.delay(req_id, file_names_arr, existing_req)

        if all_success:
            message = "All files successfully uploaded"
        elif all_failed:
            message = "All files failed to upload"
        else:
            message = "One or more files failed to upload"
        
        print(f"Upload process complete: {message}")
                
        return {'request_id': req_id, 'message' : message, 'result' : result}
    except Exception as e:
        print(e)

# Function to ensure an asyncio event loop exists and run a coroutine
def ensure_event_loop_and_run(coro):
    try:
        future = asyncio.run_coroutine_threadsafe(coro, main_event_loop)
        return future.result() 
    except RuntimeError as e:
        print(f"Error running coroutine: {e}")
        raise
                
# Maximum retry attempts
MAX_RETRIES = 5

# Your existing logic to handle the creation
def create_requirement_with_retry(req_id, file_names_arr, package_id, type='cbp'):
    retries = 0
    while retries < MAX_RETRIES:
        try:
            create_kwargs = {
                'id': req_id,
                'name': json.dumps(file_names_arr),
                'project_id': package_id,
                'type': type,
                'file_type': 'pdf',
                'criteria': ""
            }
            print('creating requirement...')
            Requirement.create(**create_kwargs)
            print("Requirement created successfully.")
            break  # Exit the loop if the creation is successful
        except Exception as e:
            retries += 1
            print(f"Failed to create requirement, attempt {retries}/{MAX_RETRIES}. Error: {e}")
            if retries < MAX_RETRIES:
                print("Retrying in 1 second...")
                time.sleep(1)
            else:
                print("Max retries reached. Could not create the requirement.")

@app.route('/get_all_bid_by_user_id/<user_id>', methods=['GET'])
def get_bid_by_user_id(user_id):
    try:
        if not user_id:
            return error_response("User ID is required.")
        print("call get API")
        all_bids = Bids.get_by(user_id=user_id)
        print("all_bids >> ", all_bids)
        return success_response(
            message="Bid get successfully",
            data=all_bids
        )
            # data={"directory", all_bids}
    except Exception as e:
        return error_response(f"Error on get bid: {str(e)}")

@app.route('/get_files_by_bid', methods=['GET'])
def get_files_chronobid():
    try:
        bid_id = request.args.get('bid_id', None)

        if bid_id is None:
            return 'bid is required', 400

        pid = Bids.get_single(bid_id)
        pid_files = BidFile.get_by(bid_id=bid_id)
        print("pid >> ", pid)
        print("pid_files >> ", pid_files)
        if not pid:
            return 'Invalid bid', 400

        # # Extracting only the required fields from each file
        # files_data = []
        # for file_info in pid['files']:
        #     files_data.append({
        #         'bid_id': pid['id'],
        #         'file_id': file_info['id'],
        #         'file_name': file_info['name'],
        #         'status': file_info['status']
        #     })

        return {"results": pid, "pid_files":pid_files}

    except Exception as e:
        print(e)
        return {"error": True, "message": e.__dict__}


@app.route('/delete_bid/<bid_id>', methods=['DELETE'])
def delete_bid(bid_id):
    user_id = request.form.get('user_id')  # Get user_id from the request

    if not user_id:
        return error_response('Invalid User')

    try:
        # Check if the bid exists
        bid = Bids.get_by(id=bid_id, user_id=user_id)
        if not bid or len(bid) == 0:
            return error_response('Bid not found', 404)

        # Delete the project
        Bids.delete_by(id=bid_id)

        # Get the file path to delete from the filesystem
        dir_path = os.path.join(CHRONOBID_FOLDER, 'bid', bid_id)  # Adjust path as necessary
        
        # Delete the actual file from the filesystem
        if os.path.exists(dir_path):
            os.remove(dir_path)
            print(f"Deleted file from filesystem: {dir_path}")
        else:
            print(f"File not found in filesystem: {dir_path}")

        return success_response(f"Bid with id {bid_id} successfully deleted")

    except Exception as e:
        return error_response(f"Error deleting bid: {str(e)}", 500)


@app.route('/delete_bid_file/<file_id>', methods=['DELETE'])
def delete_bid_file(file_id):
    user_id = request.form.get('user_id')  # Get user_id from the request

    if not user_id:
        return error_response('Invalid User')

    try:
        
        # Check if the file exists
        file = BidFile.get_single(file_id)
        if not file:
            return error_response('File not found', 404)

        # Check if the user_id matches the bid's user_id
        bid = Bids.get_by(id=file['bid_id'])  # Get the associated bid
        if bid['user_id'] != user_id:
            return error_response('Unauthorized: You do not have permission to delete this file', 403)

        # Get the file path to delete from the filesystem
        file_path = os.path.join(CHRONOBID_FOLDER, 'bid', file['bid_id'], file['name'])  # Adjust path as necessary
        
        # Delete the actual file from the filesystem
        if os.path.exists(file_path):
            os.remove(file_path)
            print(f"Deleted file from filesystem: {file_path}")
        else:
            print(f"File not found in filesystem: {file_path}")

        # Now delete the BidFile entry from the database
        BidFile.delete_by(id=file_id)
        return success_response(f"BidFile with id {file_id} successfully deleted")

    except Exception as e:
        return error_response(f"Error deleting BidFile: {str(e)}", 500)
    
if __name__ == '__main__':
    app.run(port=5130, host='0.0.0.0', debug=True)