"""Add is_report_generated field to Requirement

Revision ID: ff1e3f26b86e
Revises: 9188177328b2
Create Date: 2024-12-05 21:35:50.298583

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ff1e3f26b86e'
down_revision = '9188177328b2'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('requirement', schema=None) as batch_op:
        batch_op.add_column(sa.Column('is_report_generated', sa.Integer(), nullable=False))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('requirement', schema=None) as batch_op:
        batch_op.drop_column('is_report_generated')

    # ### end Alembic commands ###
