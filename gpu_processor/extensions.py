# extensions.py
from flask_sqlalchemy import SQLAlchemy
from flask_socketio import So<PERSON><PERSON>, emit, Namespace
from flask_migrate import Migrate
from redis import Redis
from celery import Celery
from services.socket_manager import SocketManager

db = SQLAlchemy()
migrate = Migrate()

allowed_origins = [
        '*',
        'null',
        'http://localhost:*',  # Allow all localhost ports
        'http://127.0.0.1:*',  # Allow all localhost IP ports
        'https://backend.aienergy-oilandgas.com',
        'https://app.aienergy-oilandgas.com'
    ]
socketio = SocketIO(cors_allowed_origins=allowed_origins, async_mode="eventlet", logger=True, engineio_logger=True)
socket_manager = SocketManager(socketio)

REDIS_URL = "redis://localhost:6379/0"
celery = Celery(__name__, broker=REDIS_URL, backend=REDIS_URL)
redis_conn = Redis.from_url(REDIS_URL)