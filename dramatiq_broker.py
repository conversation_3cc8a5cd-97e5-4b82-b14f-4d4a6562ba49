import gevent
from gevent import monkey
monkey.patch_all()
# import eventlet
# eventlet.monkey_patch()

import argparse
import asyncio
import os
import random
import sys
import json
import dramatiq
from dramatiq.middleware.asyncio import Async<PERSON>
from flask import Flask
from flask_melodramatiq import RabbitmqBroker
import logging
from dramatiq.results import Results
from dramatiq.results.backends import RedisBackend
# from dramatiq.middleware.prometheus import Prometheus
from dramatiq_middleware import ActivityLoggingMiddleware
from scp.epc_contractor import EPCContractor
from scp.project_owner import EngineeringProjectOwner
    
from socket_instance import socket_instance
from services.handle_new_qmp_query import QMPQueryHandler
from services.handle_new_cbp_documents import ChronobidDocumentProcessor
from services.handle_multi_bid_cbp import ChronoMultibidDocumentProcessor
from cbp.pipelines.bid_evaluation.technical_bid import TechnicalBidProcessor
from cbp.pipelines.bid_evaluation.commercial_bid import CommercialBidProcessor
from cbp.pipelines.bid_evaluation.report import CbpReportGenerator
from models import (
    Requirement,
    CbpRequirementTechnicalReport,
    EquipmentPackage
)
from services.ineight import InEightClient


env_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'env.json')
print(env_file_path)
with open(env_file_path, 'r') as f:
    env_data = json.load(f)
        
RABBITMQ_URL = env_data.get("RABBITMQ_URL", 'guest:guest@localhost:5672/')
REDIS_URL = env_data.get("REDIS_URL", 'redis://localhost:6379/0')
FERNET_KEY = env_data.get("FERNET_KEY", '')

logger = logging.getLogger("dramatiq")

# Configure broker with proper middleware
broker = RabbitmqBroker(url=f"amqp://{RABBITMQ_URL}")

# Add middleware
result_backend = RedisBackend(url=REDIS_URL)
broker.add_middleware(Results(backend=result_backend))
broker.add_middleware(dramatiq.middleware.TimeLimit())
broker.add_middleware(dramatiq.middleware.Callbacks())
broker.add_middleware(ActivityLoggingMiddleware(env_data))
dramatiq.set_broker(broker)

    
@dramatiq.actor(
    queue_name="qmp_tasks",
    priority=0,
    time_limit=3600000,  # 1 hour in milliseconds
    max_retries=3,
    store_results=True
)
def run_qmp_query(req_id, other_project_ids, exist_bid=False, bid_id=None, break_cache=False, quoted_text="", follow_up_query="", language="English"):
    try:
        # Import app at function level to avoid circular imports
        from init import app
        
        # Create application context
        with app.app_context():
            logger.info(f"Starting QMP task for request {req_id}")
            socket_manager = socket_instance.get_instance()
            
            # Test emit
            socket_manager.emit_to_client(
                req_id,
                'test_event',
                {'message': 'Test from QMP Celery'},
                namespace='/qmp'
            )

            processor = QMPQueryHandler(socket_manager, is_test=False, language=language)
            
            if exist_bid and bid_id:
                processor.set_should_query_existing_bid(exist_bid)
                # processor.set_bid_id(bid_id)
            if bid_id:
                processor.set_bid_id(bid_id)
            processor.set_break_cache(break_cache)
            processor.set_quoted_text(quoted_text)
            processor.set_follow_up_query(follow_up_query)
            greenlet = processor.process_query_sync(req_id, other_project_ids)
            
            gevent.joinall([greenlet], timeout=300)  # 5 minute timeout
            
            if not greenlet.successful():
                raise greenlet.exception or Exception("Greenlet failed")
                
            return "QMP evaluation completed successfully."
    except Exception as e:
        logger.error("Error during QMP evaluation: %s", str(e))
        raise e




@dramatiq.actor(
    queue_name="cbp_tasks",
    priority=0,
    time_limit=3600000,  # 1 hour in milliseconds
    max_retries=3,
    store_results=True
)
def run_cbp_task(req_id:str):
    try:
        # Import app at function level to avoid circular imports
        from init import app
        
        # Create application context
        with app.app_context():
            logger.info(f"Starting QMP task for request {req_id}")
            socket_manager = socket_instance.get_instance()
            
            # Test emit
            socket_manager.emit_to_client(
                req_id,
                'test_event',
                {'message': 'Test from QMP Celery'},
                namespace='/cbp'
            )

            processor = ChronobidDocumentProcessor(socket_manager, is_test=False)
            
            
            greenlet = processor.process_cbp_sync(req_id)
            
            gevent.joinall([greenlet], timeout=300)  # 5 minute timeout
            
            if not greenlet.successful():
                raise greenlet.exception or Exception("Greenlet failed")
                
            return "QMP evaluation completed successfully."
    except Exception as e:
        logger.error("Error during QMP evaluation: %s", str(e))
        raise e
    


@dramatiq.actor(
    queue_name="cbp_multibid_tasks",
    priority=0,
    time_limit=3600000,  # 1 hour in milliseconds
    max_retries=0,
    store_results=True
)

def run_cbp_multibid_task(req_id:str):
    try:
        # Import app at function level to avoid circular imports
        from init import app
        
        # Create application context
        with app.app_context():
            logger.info(f"Starting QMP task for request {req_id}")
            socket_manager = socket_instance.get_instance()
            
            # Test emit
            socket_manager.emit_to_client(
                req_id,
                'test_event',
                {'message': 'Test from QMP Celery'},
                namespace='/cbp'
            )
            processor = ChronoMultibidDocumentProcessor(socket_manager, is_test=False)
            
            greenlet = processor.process_cbp_sync(req_id)
            
            gevent.joinall([greenlet], timeout=3600)  # 1 hour timeout
            
            if not greenlet.successful():
                error_msg = f"Greenlet failed with error: {str(greenlet.exception)}" if greenlet.exception else "Greenlet failed with unknown error"
                logger.error(error_msg)
                raise greenlet.exception or Exception(error_msg)
                
            return "QMP evaluation completed successfully."
    except Exception as e:
        logger.error("Error during QMP evaluation: %s", str(e))
        raise e

   
@dramatiq.actor(
    queue_name="cbp_report",
    priority=0,
    time_limit=3600000,  # 1 hour in milliseconds
    max_retries=0,
    store_results=True
)
def run_cbp_report_task(req_id: str,cbp_request_id:str):
    try:
        # Import app at function level to avoid circular imports
        from init import app
        
        # Create application context
        with app.app_context():
            # Check if request has already been processed
            data = CbpRequirementTechnicalReport.get_single(req_id)
            if data and data.get("status") in ["done", "error"]:
                logger.info(f"Request {req_id} has already been processed with status: {data.get('status')}")
                return f"Request {req_id} was already processed with status: {data.get('status')}"
            
            logger.info(f"Starting CBP Report task for request {req_id}")
            socket_manager = socket_instance.get_instance()
            
            # Get the data outside the greenlets but inside the app context
            data = CbpRequirementTechnicalReport.get_single(req_id)

            cbp_request_id_data = Requirement.get_single(cbp_request_id)
            
            data_aggregrated_report = json.loads(cbp_request_id_data["data_aggregrate_report"])


            print(f"DATA AGGREGRATED REPORT : {data_aggregrated_report}")
            # Parse the JSON data with the correct structure
            bids_data_names = json.loads(data["report_bid_ids"])  # List of bid objects
            equipment_data = json.loads(data["report_equipment"])  # List of equipment objects
            
            
            # Extract just the IDs for the technical processor
            bid_ids = [item["bid_id"] for item in bids_data_names]
            equipment_ids = [item["equipment_id"] for item in equipment_data]
            
            user_id = data["user_id"]
            equipment_list = EquipmentPackage.get_equipment_with_specs(equipment_package_ids=equipment_ids)
            
            # Socket test event
            socket_manager.emit_to_client(
                req_id,
                'test_event',
                {'message': 'Test from CBP Report'},
                namespace='/cbp'
            )

            # Define functions that will run in separate greenlets
            def process_technical_report():
                with app.app_context():
                    processor = TechnicalBidProcessor(socket_manager=socket_manager, requirement_id=req_id)
                    return processor.process_multiple_bids_sync(bid_ids=bid_ids, equipment_list=equipment_list, bidder_names=bids_data_names)
            
            def process_commercial_report():
                with app.app_context():
                    processor = CommercialBidProcessor(socket_manager=socket_manager, requirement_id=req_id)
                    return processor.process_multiple_bids_sync(bids_data_names, equipment_data)
            
    
            def generate_cbp_report(technical_data, commercial_data):
                with app.app_context():
                    # Emit progress event
                    socket_manager.emit_to_client(
                        req_id,
                        'progress_message',
                        {
                            'request_id': req_id,
                            'event_type': 'CBP_REPORT',
                            'data': {'status': 'generating', 'message': 'Generating CBP Report...'}
                        },
                        namespace='/cbp'
                    )
                    

                    # Initialize report generator with the data
                    report_generator = CbpReportGenerator(
                        data_cpb_output=data_aggregrated_report,
                        commercial_data=commercial_data,
                        technical_data=technical_data
                    )
                    
                    print(f"Technical_data:{technical_data}")
                    # Generate all reports
                    reports = report_generator.generate_all_reports()
                
                    
                    #report_generator.save_reports()
                    # Emit the report
            
                    return reports
            
            # Start technical and commercial greenlets
            technical_greenlet = gevent.spawn(process_technical_report)
            commercial_greenlet = gevent.spawn(process_commercial_report)
            
            # Wait for both to complete with timeout
            gevent.joinall([technical_greenlet, commercial_greenlet], timeout=1800)  # 10 minute timeout
            
            # Process results
            technical_report = None
            commercial_report = None
            
            # Check technical report status
            if technical_greenlet.successful():
                technical_report = technical_greenlet.value
            else:
                logger.error("Technical report failed: %s", 
                           str(technical_greenlet.exception) if technical_greenlet.exception else "Unknown error")
            
            # Check commercial report status
            if commercial_greenlet.successful():
                commercial_report = commercial_greenlet.value
            else:
                logger.error("Commercial report failed: %s", 
                           str(commercial_greenlet.exception) if commercial_greenlet.exception else "Unknown error")
            
            # Generate CBP report if both technical and commercial reports are available
            cbp_report = None
            if technical_report and commercial_report:
                cbp_report_greenlet = gevent.spawn(generate_cbp_report, technical_report, commercial_report)
                gevent.joinall([cbp_report_greenlet], timeout=1800)  # 30 minute timeout for CBP report
                
                if cbp_report_greenlet.successful():
                    cbp_report = cbp_report_greenlet.value
                else:
                    logger.error("CBP report failed: %s",
                               str(cbp_report_greenlet.exception) if cbp_report_greenlet.exception else "Unknown error")
            
            # Send final event with all successful reports
            if any([technical_report, commercial_report, cbp_report]):
                event_data = {
                    "request_id": req_id,
                    "event_type": "CBP_REPORT",
                    "data": [cbp_report]
                }
                
                socket_manager.emit_to_client(req_id, "completed_event", event_data)
                
                # Update status to done in database
                CbpRequirementTechnicalReport.update(req_id, status='done')
            
            # Update status in database
            if not any([technical_report, commercial_report, cbp_report]):
                CbpRequirementTechnicalReport.update(req_id, status='error')
                return "CBP Reports failed to generate."
            
            return "CBP Reports generation completed."

    except Exception as e:
        logger.error("Error during CBP Report generation: %s", str(e))
        event_data = {
            "request_id": req_id,
            "event_type": "CBP_REPORT",
            'data': {'status': 'error', 'message': f"Oops! Something went wrong while processing report bid", 'error': str(e)}
        }
        socket_manager.emit_to_client(req_id, "error_event", event_data)
        try:
            with app.app_context():
                CbpRequirementTechnicalReport.update(req_id, status='error')
        except Exception as db_error:
            logger.error("Failed to update error status in database: %s", str(db_error))
        raise e
    

@dramatiq.actor(
    queue_name="epc_tasks",
    priority=0,
    time_limit=3600000,  # 1 hour in milliseconds
    max_retries=3,
    store_results=True
)
def run_epc_contractor_task(req_id, package_id, vendor_id, vendor_file_id):
    try:
        # Import app at function level to avoid circular imports
        from init import app
        
        # Create application context
        with app.app_context():
            logger.info(f"Starting EPC Contractor task for request {req_id}")
            socket_manager = socket_instance.get_instance()
            
            # Test emit
            socket_manager.emit_to_client(
                req_id,
                'progress_message',
                {'message': 'Processing EPC Contractor'},
                namespace='/epc'
            )

            processor = EPCContractor(req_id, socket_manager, package_id, vendor_id, vendor_file_id)

            processor.process_requirements_comparison()
            
            return "EPC Contractor evaluation completed successfully."
    except Exception as e:
        logger.error("Error during EPC Contractor evaluation: %s", str(e))
        raise e
    

@dramatiq.actor(
    queue_name="engineering_tasks",
    priority=0,
    time_limit=3600000,  # 1 hour in milliseconds
    max_retries=3,
    store_results=True
)
def run_engineering_project_owner_task(req_id, project_id, engineering_id, engineer_file_id):
    try:
        # Import app at function level to avoid circular imports
        from init import app
        
        # Create application context
        with app.app_context():
            logger.info(f"Starting EPO Contractor task for request {req_id}")
            socket_manager = socket_instance.get_instance()
            
            # Test emit
            socket_manager.emit_to_client(
                req_id,
                'progress_message',
                {'message': 'Processing Engineering Project Owner'},
                namespace='/epo'
            )

            processor = EngineeringProjectOwner(req_id, socket_manager, project_id, engineering_id, engineer_file_id)

            processor.process_requirements_comparison()
            
            return "EPO Contractor evaluation completed successfully."
    except Exception as e:
        logger.error("Error during EPO Contractor evaluation: %s", str(e))
        raise e
    
