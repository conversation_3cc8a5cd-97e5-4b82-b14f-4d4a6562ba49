import os
import shutil
import docx2txt
import docx
import glob
from pdfminer.high_level import extract_text
from services.docx_loader import DocsLoader
import random
import string
from models import File,Merged_file,Project,File_merged_file_association
import logging
import time

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s',
                    filename='file_merger.log',
                    filemode='a')


class ProjectDataAggregator:
    def __init__(self, data_directory, project_id, pending_merged_files):
        self.project_id = project_id
        self.data_directory = data_directory
        self.file_types = ["docx", "pdf"]
        self.max_words_per_file = 1450000
        self.max_file_size = 510 * 1024 * 1024  # 510 MB
        self.pending_merged_files = pending_merged_files

    def get_project_directory(self):
        return os.path.join(self.data_directory, self.project_id)

    def get_most_recent_file(self):
        search_pattern = os.path.join(self.get_project_directory(), f"*.*")
        matched_files = glob.glob(search_pattern)
        filtered_files = [f for f in matched_files if not f.endswith("_merged.docx") and not os.path.basename(f).startswith(self.project_id)]

        if not filtered_files:
            logging.warning("No valid recent files found.")
            return None

        most_recent_file = max(filtered_files, key=lambda f: os.path.getmtime(f))
        logging.info(f"Most recent file found: {most_recent_file}")
        return most_recent_file

    def get_file_content(self, file_path):
        if file_path.endswith(".docx"):
            self.docx_loader = DocsLoader(file_path)
            text = self.docx_loader.load()[0].page_content
            length = len(text)
        elif file_path.endswith(".pdf"):
            text = extract_text(file_path)
            length = len(text.split())
        else:
            text = ""
            length = 0

        logging.info(f"File content loaded from {file_path}")
        return text, length
    
    def get_most_recent_merged_file(self):
        search_pattern = os.path.join(self.get_project_directory(), f"{self.project_id}_*_merged.docx")
        matched_files = glob.glob(search_pattern)
        if not matched_files:
            return self.create_new_merged_file()
        
        most_recent_file = max(matched_files, key=os.path.getmtime)
        file_size = os.path.getsize(most_recent_file)
        word_count = self.count_words_in_file(most_recent_file)
        
        logging.info(f"Most recent merged file found: {most_recent_file}")
        logging.info(f"File size: {file_size}, Word count: {word_count}")
        
        if file_size >= self.max_file_size or word_count >= self.max_words_per_file:
            logging.info("Creating new merged file due to size or word count limit")
            return self.create_new_merged_file()
        
        return most_recent_file
    
    def count_words_in_file(self, file_path):
        file_content, count = self.get_file_content(file_path)
        logging.info(f"Word count in {file_path}: {count}")
        return count
    
    def create_new_merged_file(self):
        random_suffix = ''.join(random.choices(string.digits, k=10))  # Generate a random 10-digit string
        new_file_path = os.path.join(self.get_project_directory(), f"{self.project_id}_{random_suffix}_merged.docx")
        new_document = docx.Document()
        new_document.save(new_file_path)
        logging.info(f"New merged file created: {new_file_path}")
        Merged_file.create(id=f"{self.project_id}_{random_suffix}",project_id=self.project_id,status='pending')
        return new_file_path

    def merge_most_recent_file_content(self):
        pending_merged_files = self.pending_merged_files
        if pending_merged_files:
            for pending_file in pending_merged_files:
                # print(pending_file)
                start_time = time.time()  # Start timer
            
                most_recent_merged_file = self.get_most_recent_merged_file()
                _file_path = os.path.join(self.get_project_directory(), pending_file["name"])
                
                if most_recent_merged_file:
                    logging.info("handled most recent merged files")

                    merged_file_name = os.path.basename(most_recent_merged_file)
                    merged_file_id = merged_file_name.split("_merged")[0]

                    merged_file_data = Merged_file.get_by(id=merged_file_id)[0]
                    logging.info(f"This is merged file data: {merged_file_data}")
                    if merged_file_data["status"] == "syncing" or merged_file_data["status"] == "merging":
                        logging.info(f"Skipping project {merged_file_id} because it is syncing...")
                        continue
                    
                    # Read content of the most recent file
                    file_content, _ = self.get_file_content(_file_path)
                    logging.info("file content read above")
                    
                    # Load the most recent merged file
                    most_recent_merged_document = docx.Document(most_recent_merged_file)
                    # print(_file_path)
                    logging.info("ready to send data into add_content_to_merged_doc")
                    
                    # Add the content of the most recent file to the merged document
                    self.add_content_to_merged_document(most_recent_merged_document, file_content, most_recent_merged_file, pending_file)
                    
                    # Save the updated merged document
                    most_recent_merged_document.save(most_recent_merged_file)

                    end_time = time.time()  # End timer
                    elapsed_time = end_time - start_time  # Calculate elapsed time

                    logging.info(f"File content merged, updated, and renamed successfully. Time spent: {elapsed_time:.2f} seconds")
                    # print(File.get_by(project_id=self.project_id))
                else:
                    logging.warning("No valid recent merged file found.")
        else:
            # print(' i failed')
            logging.warning("No valid recent file found.")

    
    def add_content_to_merged_document(self, merged_document, file_content, most_recent_merged_file, file_data):
        logging.info('Adding content to merged document')

        words = file_content.split()
        word_count = len(words)
        words_added = 0

        # print(f"file content data: {words}")

        merged_file_name = os.path.basename(most_recent_merged_file)
        merged_file_id = merged_file_name.split("_merged")[0]

        file_name_with_extension = file_data["name"]
        file_name, file_extension = os.path.splitext(file_name_with_extension)

        file_to_update = file_data
        logging.info(f'Updating file {file_name_with_extension} in project {self.project_id}')

        # Total word count
        total_word_count = self.count_total_words_in_document(merged_document)

        # Calculate remaining words needed to reach the maximum word count
        remaining_words_needed = self.max_words_per_file - total_word_count

        # Add words to the merged document in one go if remaining_words_needed is greater than 0
        if remaining_words_needed > 0:
            # Update merged file status to merging...
            Merged_file.update(id=merged_file_id, status="merging")

            # print(f"remaining words needed: {remaining_words_needed}")
            # Calculate the number of words to add from the new document
            words_to_add = min(remaining_words_needed, word_count)
            # print(f"words to add: {words_to_add}")

            # Add words from the new document to the merged document
            while words_added < words_to_add:

                merged_document.add_paragraph(' '.join(words[words_added: words_added + words_to_add]))
                words_added += words_to_add

                # print(f"words_added: {words_added}")

                # Create association for merged files
                File_merged_file_association.create(file_id=file_to_update["id"], merged_file_id=merged_file_id)
                logging.info('Association created for newly merged files')

            logging.info(f'{words_to_add} words added to the merged document')

        # Check if the merged document exceeds the maximum file size
        file_size_bytes = self.get_file_size(merged_document)
        if file_size_bytes >= self.max_file_size or total_word_count >= self.max_words_per_file:
            logging.info('Maximum condition reached, now creating new merged document')
            # print('Maximum condition reached, now creating new merged document')
            # print(f"file id: {file_data['id']}")

            # Update merged file status to complete
            Merged_file.update(id=merged_file_id, status="modified")

            # Update file merge status to merged
            File.update(id=file_data["id"], merge_status="merged")

            logging.info('Merged file status updated')

            # Save the current merged document
            merged_document.save(most_recent_merged_file)

            logging.info('Current merged document saved')

            # Create a new merged document
            most_recent_merged_file = self.create_new_merged_file()

            logging.info('New merged document created')

            # Load the new merged document
            merged_document = docx.Document(most_recent_merged_file)

            logging.info('New merged document loaded')

        logging.info('Updating MergedFile & File status to modifief & merged respectively...')
        # Update merged file status to complete
        Merged_file.update(id=merged_file_id, status="modified")
        File.update(id=file_data["id"], merge_status="merged")


    
    def get_file_size(self, document):
        # Save the document to a temporary file to get the file size
        temp_file = "temp.docx"
        document.save(temp_file)
        file_size = os.path.getsize(temp_file)
        os.remove(temp_file)
        return file_size
    
    def count_total_words_in_document(self, document):
        total_words = 0
        for paragraph in document.paragraphs:
            total_words += len(paragraph.text.split())
        return total_words


def monitor_directories(data_directory):
    logging.info(f"Monitoring directories in {data_directory}")
    for project_id in os.listdir(data_directory):
        try:
            # project_id = "f0c0ed14-f7f1-436a-a6f2-f3bd0456120d"
            # logging.info(f"Monitoring files in {project_id}")
            project_directory = os.path.join(data_directory, project_id)
            if os.path.isdir(project_directory):
                pending_documents = File.get_by(project_id=project_id, merge_status="not_merged")
                logging.info(f"Pending documents in {pending_documents}")
                if len(pending_documents) > 0:
                    logging.info(f"Pending merged docs in project directory {project_id}: {pending_documents}")
                    aggregator = ProjectDataAggregator(data_directory, project_id, pending_documents)
                    aggregator.merge_most_recent_file_content()
                else:
                    logging.info(f"No recent modifications in project directory {project_id}")
            else:
                logging.info(f"Not a directory {project_directory}")
            # exit()
        except Exception as e:
            logging.error(f"An error occurred during directory monitoring: {str(e)}")


if __name__ == "__main__":
    data_directory = os.getenv("WORKSPACE_DIR")
    monitor_directories(data_directory)
