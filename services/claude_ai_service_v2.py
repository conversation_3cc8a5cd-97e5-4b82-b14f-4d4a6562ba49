import gevent
from gevent import monkey
monkey.patch_all()

import anthropic
import os,sys
import json
import random
import asyncio
from pydantic import BaseModel
import functools # Added for wraps
import time # Potentially useful for delays in decorator
from models import LL<PERSON>sage

# Define the decorator (can be placed above the class or in a utils file)
def retry_with_models(model_list_attr_name):
    """
    Decorator to retry a function call across multiple models from a specified list,
    handling RateLimitErrors by trying the next model.

    Supports both Claude and Gemini models, using the appropriate client for each.
    Assumes the decorated function accepts 'model_id' and 'client' as keyword arguments.
    Assumes the class instance ('self') has a 'get_client(model_id)' method.
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            model_ids = getattr(self, model_list_attr_name, [])
            if not model_ids:
                return None

            core_args = args
            core_kwargs = kwargs.copy()
            core_kwargs.pop('model_id', None)
            core_kwargs.pop('client', None)

            shuffled_models = random.sample(model_ids, len(model_ids))
            last_exception = None

            for model_id in shuffled_models:
                print(f"Attempting model: {model_id}")
                try:
                    # Get the appropriate client based on the model ID
                    client = self.get_client(model_id)

                    # Call the function with the model-specific client
                    result = func(self, *core_args, **core_kwargs, model_id=model_id, client=client)
                    return result # Return on success

                except anthropic.RateLimitError as e:
                    last_exception = e
                    time.sleep(10)
                    continue 
                
                except anthropic.BadRequestError as e:
                    last_exception = e
                    if 'credit balance' in str(e):
                        last_exception = e
                        print(f"Credit balance error trying another key: {e}")
                        continue
                    return []

                except Exception as e:
                    print(f"Error generating message with model {model_id}: {e}")
                    last_exception = e
                    continue

            # If the loop completes without returning, all models failed.
            print(f"All models in list '{model_list_attr_name}' failed after retries. Last error: {last_exception}")
            return None
        return wrapper
    return decorator



class ClaudeService:
    def __init__(self,claude_keys=['CLAUDE_API_KEY', 'CLAUDE_API_KEY'],default_key="CLAUDE_API_KEY"):
        # Get the path to the parent directory
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # Load environment variables from env.json file in the parent directory
        env_file_path = os.path.join(parent_dir, 'env.json')

        self.claude_keys = claude_keys
        self.default_key = default_key
        
        # Select a random API key
        selected_api_key = random.choice(self.claude_keys)
        
        print(f"Selected API key: {selected_api_key}")
        
        # Load environment variables from env.json file
        with open(env_file_path, 'r') as f:
            env_data = json.load(f)
            
        self.env_data = env_data
        
        # Set up ClaudeAI client
        self.client = anthropic.Anthropic(api_key=env_data.get(selected_api_key, self.default_key), max_retries=0)

        # Map deployments to models
        self.deployments_map = {
            "Claude 3.5 Sonnet Old": "claude-3-5-sonnet-20240620",
            "Claude 3.5 Sonnet": "claude-3-5-sonnet-20241022",
            "Claude 3 Opus": "claude-3-opus-20240229",
            "Claude 3 Sonnet": "claude-3-sonnet-20240229",
            "Claude 3 Haiku": "claude-3-haiku-20240307",
            "Claude 3.5 Haiku": "claude-3-5-haiku-20241022",
            "Claude 3.7 Sonnet": "claude-3-7-sonnet-20250219"
        }  
        self.model_ids = ["claude-3-5-sonnet-20240620", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-7-sonnet-20250219"]
        
        # self.sonnet_model_ids = ["claude-3-5-sonnet-20240620", "claude-3-5-sonnet-20241022", "claude-3-7-sonnet-20250219", "claude-sonnet-4-20250514"]

        self.sonnet_model_ids = ["gemini-1.5-pro", "gemini-1.5-flash"]

        # Select a random model
        # self.selected_model = random.choice(list(self.deployments_map.values()))
        self.selected_model = self.deployments_map["Claude 3 Sonnet"]

    def get_client(self, model_id=None):
        """
        Get the appropriate client based on the model ID.
        Returns either an Anthropic client or a Google Generative AI client.
        
        Args:
            model_id (str, optional): The model ID to determine which client to return.
                                     If None, defaults to Claude client.
        
        Returns:
            The appropriate client instance for the specified model.
        """
        # If model_id starts with "gemini", return a Gemini client
        if model_id and model_id.startswith("gemini"):
            import google.generativeai as genai
            genai.configure(api_key=self.env_data.get("GEMINI_API_KEY", ""))
            return genai
        
        # Otherwise, return a Claude client (default)
        selected_api_key = random.choice(self.claude_keys)
        print(f"Using Claude API key: {selected_api_key}")
        client = anthropic.Anthropic(
            api_key=self.env_data.get(selected_api_key, self.default_key),
            max_retries=0 # Changed from 3 to 0
        )
        return client
        

    async def  generate_message_2(self, messages,temperature=0.01, model="Claude 3.5 Sonnet", max_tokens=4096):
        try:
            model="Claude 3.5 Sonnet"
            message = self.client.messages.create(
                model=self.deployments_map[model],
                temperature=temperature,
                max_tokens=max_tokens,
                messages=messages
            )
            self._record_usage(model, message)
            return message
        except Exception as e:
            print(f"Error generating message from generate_message_2: {e}")
            return None
        
    async def generate_message(self, messages, temperature=0.01, model="Claude 3.5 Sonnet", max_tokens=4096):
        for model_name, model_id in self.deployments_map.items():
            try:
                print(f"Trying model: {model_name}")
                message = self.client.messages.create(
                    model=model_id,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    messages=messages
                )
                self._record_usage(model_id, message)
                return message
            except Exception as e:
                print(f"Error generating message with model {model_name}: {e}")
        
        # If all models fail, return None or handle it appropriately
        print("All models failed to generate a message.")
        return None
    
    async def generate_message_agent_sonnet(self, messages, temperature=0.01, max_tokens=4096):
        # Use Claude 3.5 Sonnet
        model_id = self.deployments_map["Claude 3.5 Sonnet Old"]
        try:
            print(f"Using model: Claude 3.5 Sonnet Old")
            message = self.client.messages.create(
                model=model_id,
                temperature=temperature,
                max_tokens=max_tokens,
                messages=messages,
                top_k=3
            )
            self._record_usage(model_id, message)
            return message
        except Exception as e:
            print(f"Error generating message with Claude 3.5 Sonnet Old: {e}")
            return None
        
    @retry_with_models(model_list_attr_name='sonnet_model_ids')
    def generate_message_agent_sonnet_sync(self, messages, temperature=0.01, max_tokens=4096, *, model_id, client):
        """
        Generates a message using a specific Claude Sonnet model provided by the retry decorator.
        Handles the core API call logic for a single attempt.
        The retry logic (looping through models, handling rate limits) is managed by the decorator.

        Args:
            messages: The message list for the API call.
            temperature: The temperature setting for the API call.
            max_tokens: The max_tokens setting for the API call.
            model_id (str): The specific model ID to use for this attempt (injected by decorator).
            client (anthropic.Anthropic): The client instance to use for this attempt (injected by decorator).

        Returns:
            The message object on success, or raises an exception handled by the decorator.
        """
        # The decorator provides 'client' and 'model_id' and handles the retry loop/errors.
        # This function now only needs to make the specific API call.
        print(f"Executing API call with model: {model_id}")
        message = client.messages.create(
            model=model_id,
            temperature=temperature,
            max_tokens=max_tokens,
            messages=messages,
            top_k=3 # Specific parameter for this method
        )
        self._record_usage(model_id, message)
        self._log_claude_message(message, messages, model_id)
        return message
    
    
    async def generate_message_agent_sonnet_new(self, messages, temperature=0.01, max_tokens=4096):
        # Use Claude 3.5 Sonnet
        model_id = self.deployments_map["Claude 3.5 Sonnet"]
        try:
            print(f"Using model: Claude 3.5 Sonnet")
            message = self.client.messages.create(
                model=model_id,
                temperature=temperature,
                max_tokens=max_tokens,
                messages=messages,
                top_k=3
            )
            self._record_usage(model_id, message)
            return message
        except Exception as e:
            print(f"Error generating message with Claude 3.5 Sonnet: {e}")
            return None
    
    @retry_with_models(model_list_attr_name='sonnet_model_ids')
    def generate_message_agent_sonnet_new_sync(self, messages, temperature=0.01, max_tokens=4096, *, model_id, client):
        # Use Claude 3.5 Sonnet
        # model_id = self.deployments_map["Claude 3.5 Sonnet"]
        message = client.messages.create(
            model=model_id,
            temperature=temperature,
            max_tokens=max_tokens,
            messages=messages,
            top_k=3
        )
        
        print(f"CLAUDE 3.5 USAGE : {message.usage}")
        self._record_usage(model_id, message)
        self._log_claude_message(message, messages, model_id)
        return message
    
    @retry_with_models(model_list_attr_name='sonnet_model_ids')
    def generate_final_summary_cbp(self, messages, temperature=0.01, max_tokens=4096, *, model_id, client):
        # Use Claude 3.5 Sonnet
        # model_id = self.deployments_map["Claude 3.5 Sonnet"]
        model_id = random.choice(["claude-3-5-sonnet-20240620", "claude-3-5-sonnet-20241022"])
        message = client.messages.create(
            model=model_id,
            temperature=temperature,
            max_tokens=max_tokens,
            messages=messages,
            top_k=3
        )
        print(f"CLAUDE 3.5 USAGE : {message.usage}")
        self._record_usage(model_id, message)
        self._log_claude_message(message, messages, model_id)
        return message
        
    
    async def generate_message_agent_sonnet_v2_sync(self, schema: BaseModel, messages, temperature=0.01, max_tokens=4096, *, model_id, client):
        # Use Claude 3.5 Sonnet
        # model_id = self.deployments_map["Claude 3.5 Sonnet"]
        _analysis_schema = schema.model_json_schema()

        tools = [
            {
                "name": "build_analysis_result",
                "description": "build the analysis object",
                "input_schema": _analysis_schema
            }
        ]

        message = client.messages.create(
            model=model_id,
            temperature=temperature,
            max_tokens=max_tokens,
            messages=messages,
            tools=tools,
            tool_choice={"type": "tool", "name": "build_analysis_result"},
            top_k=3
        )
        self._record_usage(model_id, message)
        print('claude 3.5 sonnet usage: ', message.usage)
        self._log_claude_message(message, messages, model_id)
        return message
    
      
    @retry_with_models(model_list_attr_name='sonnet_model_ids')
    def generate_message_agent_sonnet_v2_sync(self, schema: BaseModel, messages, temperature=0.01, max_tokens=4096, *, model_id, client):
        # Use Claude 3.5 Sonnet
        # model_id = self.deployments_map["Claude 3.5 Sonnet"]
        _analysis_schema = schema.model_json_schema()

        tools = [
            {
                "name": "build_analysis_result",
                "description": "build the analysis object",
                "input_schema": _analysis_schema
            }
        ]

        message = client.messages.create(
            model=model_id,
            temperature=temperature,
            max_tokens=max_tokens,
            messages=messages,
            tools=tools,
            tool_choice={"type": "tool", "name": "build_analysis_result"},
            top_k=3
        )
        self._record_usage(model_id, message)
        print('claude 3.5 sonnet USAGE: ', message.usage)
        self._log_claude_message(message, messages, model_id)
        return message
    
    
    async def generate_message_agent_haiku(self, messages, temperature=0.01, max_tokens=4096):
        # Use Claude 3.5 Sonnet
        model_id = self.deployments_map["Claude 3.5 Haiku"]
        try:
            print(f"Using model: Claude 3.5 Haiku")
            message = self.client.messages.create(
                model=model_id,
                temperature=temperature,
                max_tokens=max_tokens,
                messages=messages,
                top_k=3
            )
            self._record_usage(model_id, message)
            return message
        except Exception as e:
            print(f"Error generating message with Claude 3.5 Haiku: {e}")
            return None
    
    async def generate_message_agent_opus(self, messages, temperature=0.01, max_tokens=4096):
        # Use Claude 3 Opus
        model_id = self.deployments_map["Claude 3 Haiku"]
        try:
            print(f"Using model: Claude 3 Opus")
            message = self.client.messages.create(
                model=model_id,
                temperature=temperature,
                max_tokens=max_tokens,
                messages=messages
            )
            self._record_usage(model_id, message)
            return message
        except Exception as e:
            print(f"Error generating message with Claude 3 Opus: {e}")
            return None
    
    async def generate_message_agent_master(self, messages, temperature=0.01, max_tokens=4096):
        # Use Claude 3 Haiku
        model_id = self.deployments_map["Claude 3 Opus"]
        try:
            print(f"Using model: Claude 3 Haiku")
            message = self.client.messages.create(
                model=model_id,
                temperature=temperature,
                max_tokens=max_tokens,
                messages=messages
            )
            self._record_usage(model_id, message)
            return message
        except Exception as e:
            print(f"Error generating message with Claude 3 Haiku: {e}")
            return None
        
    def generate_message_sync2(self, messages, system, temperature=0.01, model="Claude 3 Sonnet", max_tokens=4096):
        try:
            message = self.client.messages.create(
                model=self.deployments_map[model],
                temperature=temperature,
                max_tokens=max_tokens,
                system = system,
                messages=messages
            )
            self._record_usage(self.deployments_map[model], message)
            return message.content[0].text
        except Exception as e:
            print(f"Error generating message from generate_message_sync2: {e}")
            return None
    
    def generate_message_sync(self, messages, system, temperature=0.01, model="Claude 3 Sonnet", max_tokens=4096):
        for model_name, model_id in self.deployments_map.items():
            try:
                print(f"Trying model: {model_name}")
                message = self.client.messages.create(
                    model=model_id,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    system=system,
                    messages=messages
                )  
                print(f"CLAUDE 3.5 USAGE : {message.usage}")
                self._record_usage(model_id, message)
                return message.content[0].text
            except Exception as e:
                print(f"Error generating message with model {model_name}: {e}")
    
    
    def generate_message_sync_haiku(self, messages, system="", temperature=0.01, max_tokens=4096):
        model_id = self.deployments_map["Claude 3.5 Haiku"]
        try:
            message = self.client.messages.create(
                model=model_id,
                temperature=temperature,
                max_tokens=max_tokens,
                system=system,
                messages=messages
            )
            self._record_usage(model_id, message)
            return message.content[0].text
        except Exception as e:
            print(f"Error generating message with model Haiku 3.5: {e}")
            return None        

    def _record_usage(self, model_id, message):
        """Helper method to record Claude API usage"""
        try:
            LLMUsage.create(
                provider="claude",
                model=model_id,
                input_tokens=message.usage.input_tokens,
                output_tokens=message.usage.output_tokens
            )
        except Exception as e:
            print(f"Error recording Claude usage: {e}")

    def _log_claude_message(self, message, messages, model_id):
        return
        """Helper method to log Claude API messages to JSON file"""
        if not os.path.exists('claude_messages.json'):
            with open('claude_messages.json', 'w') as f:
                json.dump([], f)

        # Load existing messages
        with open('claude_messages.json', 'r') as f:
            messages_list = json.load(f)

        # Prepare log entry
        log_entry = {
            'request_messages': messages,
            'model': model_id,
            'usage': {
                'input_tokens': message.usage.input_tokens,
                'output_tokens': message.usage.output_tokens
            }
        }

        # Check content and log accordingly
        if message.content and len(message.content) > 0:
            content_block = message.content[0]
            if content_block.type == "tool_use":
                log_entry['output_type'] = 'tool_use'
                log_entry['tool_name'] = content_block.name
                log_entry['tool_id'] = content_block.id
                log_entry['tool_input'] = content_block.input
            elif content_block.type == "text":
                log_entry['output_type'] = 'text'
                log_entry['text_output'] = content_block.text
            else:
                log_entry['output_type'] = 'unknown'
                log_entry['raw_content'] = str(message.content)
        else:
            log_entry['output_type'] = 'empty_content'
            log_entry['raw_content'] = str(message.content)

        messages_list.append(log_entry)

        # Save updated messages list
        with open('claude_messages.json', 'w') as f:
            json.dump(messages_list, f, indent=4)
            
    @retry_with_models(model_list_attr_name='sonnet_model_ids')
    def _generate_message_agent_sonnet_new_sync(self, messages, temperature=0.01, max_tokens=4096, *, model_id, client):
        # Use Claude 3.5 Sonnet
        # model_id = self.deployments_map["Claude 3.5 Sonnet"]
        if model_id.startswith("gemini"):
            message = self._call_gemini_llm(
                model_id=model_id,
                client=client,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )
        else:  # Default to Claude
            message = self._call_claude_llm(
                model_id=model_id,
                client=client,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )
        
        print(f"{model_id} USAGE: {message.usage}")
        self._record_usage(model_id, message)
        self._log_claude_message(message, messages, model_id)
        return message
            
    @retry_with_models(model_list_attr_name='sonnet_model_ids')
    def _generate_message_agent_sonnet_v2_sync(self, schema: BaseModel, messages, temperature=0.01, max_tokens=4096, *, model_id, client):
        """
        Generates a structured message using a specific model provided by the retry decorator.
        Handles both Claude and Gemini models based on the model_id prefix.
        
        Args:
            schema: The Pydantic schema for structured output
            messages: The message list for the API call
            temperature: The temperature setting for the API call
            max_tokens: The max_tokens setting for the API call
            model_id: The specific model ID to use (injected by decorator)
            client: The client instance to use (injected by decorator)
            
        Returns:
            The message object on success, or raises an exception handled by decorator
        """
        print(f"Executing API call with model: {model_id}")
        
        # Determine which provider to use based on model_id prefix
        if model_id.startswith("gemini"):
            message = self._call_gemini_llm_structured(
                model_id=model_id,
                client=client,
                messages=messages,
                schema=schema,
                temperature=temperature,
                max_tokens=max_tokens
            )
        else:  # Default to Claude
            message = self._call_claude_llm_structured(
                model_id=model_id,
                client=client,
                messages=messages,
                schema=schema,
                temperature=temperature,
                max_tokens=max_tokens
            )
        
        self._record_usage(model_id, message)
        print(f"{model_id} USAGE: ", message.usage)
        self._log_claude_message(message, messages, model_id)
        return message

    def _call_claude_llm_structured(self, model_id, client, messages, schema, temperature=0.01, max_tokens=4096):
        """
        Call Claude API with structured output using tools.
        
        Args:
            model_id: The Claude model ID to use
            client: The Anthropic client instance
            messages: The messages to send to the model
            schema: The Pydantic schema for structured output
            temperature: The temperature parameter
            max_tokens: The maximum number of tokens to generate
            
        Returns:
            The response from the Claude API
        """
        _analysis_schema = schema.model_json_schema()
        
        tools = [
            {
                "name": "build_analysis_result",
                "description": "build the analysis object",
                "input_schema": _analysis_schema
            }
        ]
        
        message = client.messages.create(
            model=model_id,
            temperature=temperature,
            max_tokens=max_tokens,
            messages=messages,
            tools=tools,
            tool_choice={"type": "tool", "name": "build_analysis_result"},
            top_k=3
        )
        
        return message

    def _call_gemini_llm_structured(self, model_id, client, messages, schema, temperature=0.01, max_tokens=4096):
        """
        Call Gemini API with structured output.
        
        Args:
            model_id: The Gemini model ID to use
            client: The Google Generative AI client
            messages: The messages to send to the model
            schema: The Pydantic schema for structured output
            temperature: The temperature parameter
            max_tokens: The maximum number of tokens to generate
            
        Returns:
            A response object that mimics Claude's response structure
        """
        import re
        import json
        # Convert messages format from OpenAI/Claude to Gemini
        gemini_messages = []
        for msg in messages:
            role = "user" if msg["role"] == "user" else "model"
            gemini_messages.append({"role": role, "parts": [msg["content"]]})
        
        # Add schema information to the last user message
        schema_json = schema.model_json_schema() if hasattr(schema, 'model_json_schema') else schema 
        schema_instruction = f"\n\nPlease respond with a valid JSON object that conforms to this schema:\n{json.dumps(schema_json, indent=2)}"
        
        if gemini_messages and gemini_messages[-1]["role"] == "user":
            gemini_messages[-1]["parts"][0] += schema_instruction
        else:
            gemini_messages.append({
                "role": "user", 
                "parts": [schema_instruction]
            })
        
        # Initialize the model
        model = client.GenerativeModel(model_id)
        
        # Generate response
        response = model.generate_content(
            gemini_messages,
            generation_config={
                "temperature": temperature,
                "max_output_tokens": max_tokens,
                "top_p": 1.0
            }
        )
        
        # Extract JSON from response
        try:
          
            
            # Try to find JSON in the response
            text = response.text
            json_match = re.search(r'```json\s*([\s\S]*?)\s*```', text)
            if json_match:
                json_str = json_match.group(1)
            else:
                # If no JSON code block, try to extract JSON directly
                json_str = text
            
            # Parse the JSON
            parsed_json = json.loads(json_str)
            
            # Create a response object that mimics Claude's tool use response
            class GeminiStructuredResponse:
                def __init__(self, json_data, model_id, usage_metadata):
                    self.content = [type('obj', (object,), {
                        'type': 'tool_use',
                        'tool_id': 'build_analysis_result',
                        'tool_name': 'build_analysis_result',
                        'tool_input': json_data
                    })]
                    self.model = model_id
                    self.usage = type('obj', (object,), {
                        'input_tokens': usage_metadata.get('prompt_token_count', 0),
                        'output_tokens': usage_metadata.get('candidates_token_count', 0)
                    })
            
            return GeminiStructuredResponse(parsed_json, model_id, response.usage_metadata)
            
        except Exception as e:
            print(f"Error parsing Gemini structured response: {e}")
            # Return a basic response with error information
            class GeminiErrorResponse:
                def __init__(self, text, model_id):
                    self.content = [type('obj', (object,), {
                        'type': 'text',
                        'text': f"Error parsing structured response: {text}"
                    })]
                    self.model = model_id
                    self.usage = type('obj', (object,), {
                        'input_tokens': 0,
                        'output_tokens': 0
                    })
            
            return GeminiErrorResponse(response.text, model_id)

    def _call_claude_llm(self, model_id, client, messages, temperature=0.01, max_tokens=4096):
        """
        Call Claude API for text generation.
        
        Args:
            model_id: The Claude model ID to use
            client: The Anthropic client instance
            messages: The messages to send to the model
            temperature: The temperature parameter
            max_tokens: The maximum number of tokens to generate
            
        Returns:
            The response from the Claude API
        """
        message = client.messages.create(
            model=model_id,
            temperature=temperature,
            max_tokens=max_tokens,
            messages=messages,
            top_k=3
        )
        
        return message

    def _call_gemini_llm(self, model_id, client, messages, temperature=0.01, max_tokens=4096):
        """
        Call Gemini API for text generation.
        
        Args:
            model_id: The Gemini model ID to use
            client: The Google Generative AI client
            messages: The messages to send to the model
            temperature: The temperature parameter
            max_tokens: The maximum number of tokens to generate
            
        Returns:
            A response object that mimics Claude's response structure
        """
        # Convert messages format from OpenAI/Claude to Gemini
        gemini_messages = []
        for msg in messages:
            role = "user" if msg["role"] == "user" else "model"
            gemini_messages.append({"role": role, "parts": [msg["content"]]})
        
        # Initialize the model
        model = client.GenerativeModel(model_id)
        
        # Generate response
        response = model.generate_content(
            gemini_messages,
            generation_config={
                "temperature": temperature,
                "max_output_tokens": max_tokens,
                "top_p": 1.0
            }
        )
        print('this is gemini response: ', response)
        
        # Create a response object that mimics Claude's response structure
        class GeminiResponse:
            def __init__(self, text, model_id, usage_metadata):
                self.content = [type('obj', (object,), {
                    'type': 'text',
                    'text': text
                })]
                self.model = model_id
                self.usage = type('obj', (object,), {
                    'input_tokens': usage_metadata.get('prompt_token_count', 0),  # Gemini doesn't provide token counts in the same way
                    'output_tokens': usage_metadata.get('candidates_token_count', 0)
                })
        
        return GeminiResponse(response.text, model_id, response.usage_metadata)

if __name__ == "__main__":
    try:
        client = ClaudeService()
        response = client._generate_message_agent_sonnet_v2_sync({"answer": str},[
            {"role": "user", 
            "content": "Hello, how are you? and what do you do, respond like <Answer>Hello, how are you?</Answer>"
            }
            ])
        
        print(response)
    except Exception as e:
        import traceback
        print(traceback.format_exc())
        print(f"Error generating message: {e}")
    
    
