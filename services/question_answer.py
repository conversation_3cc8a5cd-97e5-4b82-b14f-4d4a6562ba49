import os,sys
import asyncio
from models import Project, Requirement
from openai import OpenAI
from services.file_tracker import track_files
from services.data_handler import DataManager
from services.open_ai_service import AssistantManager
from services.claude_ai_service import ClaudeService
from services.report_manager import ReportManager
from services.update_backend_status import BackendStatusUpdater


import json
class QuestionAnswerer:
    def __init__(self):
        # Get the path to the parent directory
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # Load environment variables from env.json file in the parent directory
        env_file_path = os.path.join(parent_dir, 'env.json')
        with open(env_file_path, 'r') as f:
            self.env_data = json.load(f)

        # Access the loaded environment variable
        openai_api_key = self.env_data.get("OPENAI_API_KEY")

        # Print the loaded API key to confirm
        # print("Loaded OPENAI_API_KEY:", openai_api_key)

        # Initialize your objects with the environment variable
        self.client = OpenAI(api_key=openai_api_key)
        self.data_manager = DataManager()
        self.assistant_manager = AssistantManager()
        self.claude_client = ClaudeService()
        self.backend_status_updater = BackendStatusUpdater()

    def get_answer_claude(self, sources, question):
        text = self.format_sources(sources)
        text += f"Question:{question}"
        
        prompt = '''
            <SOURCES>
                {sources}
            </SOURCES>
            <QUESTION>
                {question}
            </QUESTION>
            <ROLE>
                You are an assistant tasked with answering the user's question based on the provided sources.
            </ROLE>
            <TASK>
                - Analyze the <SOURCES> to answer the <QUESTION>.
                - Use only the provided sources to formulate your answer.
                - Ignore any irrelevant sources.
                - Cite the sources used in your answer by mentioning their document names or titles.
                - Do not quote the sources directly; instead, paraphrase and cite them.
                - Provide a convincing answer in one or more paragraphs.
                - Include as many relevant details as possible.
                - If multiple values exist for a query demanding a singular value, mention all of them.
                - Present information confidently without apologizing.
                - Do not use numeric points or bullet points in your answer.
            </TASK>
            <ANSWER FORMAT>
                Provide your answer in the following format:
                <answer>
                Your detailed answer here, citing sources as needed.
                </answer>
                $$$$$section_id1, section_id2, ...
            </ANSWER FORMAT>
            <IMPORTANT>
                - Do not mention section IDs in the answer part.
                - Include section IDs used directly or indirectly in your answer only in the specified format after the $$$$$ separator.
                - Sort the section IDs according to their appearance in your answer.
                - Do not include any content after the section ID list.
            </IMPORTANT>
        '''
        
        formatted_prompt = prompt.format(sources=text, question=question)
        
        completion = self.claude_client.generate_message_sync(
            [{"role": "user", "content": formatted_prompt}],
            "", 
            0.1, "Claude 3 Sonnet", 4096
        )
        # print('Completion:', completion)
        
        return (completion, text)

    def format_sources(self, sources):
        formatted_text = 'Sources:\n'
        for idx, source in enumerate(sources, 1):
            source_title = ".".join(source.get("source", "").split('/')[-1].split('.')[:-1])
            formatted_text += f"Document {idx}:"
            if source_title:
                formatted_text += f" {source_title}"
            if source['section_id']:
                formatted_text += f" (Section ID: {source['section_id']})"
            if source["page_number"]:
                formatted_text += f" (Page: {source['page_number']})"
            formatted_text += f"\n\n{source['content']}\n\n"
        return formatted_text
    
    def get_answer(self, sources, question, include_sources=True):
        text = 'Sources:'
        idx = 1
        for x in sources:
            if include_sources:
                source_title = x["source"].split('/')[-1].split('.')[0]
                text += "Document Name: " + source_title + "\n" + x["title"] + "\n" + x["content"] + "\n"
            else:
                text += x["title"] + "\n" + x["content"] + "\n"
            idx += 1
        
        text += "Question:\n" + question

        completion = self.assistant_manager.get_response(
            [{"role": "system", "content": '''You are an assistant to answer the question from the user. Use the provided sources on user messages to make decisions. You can ignore any of the sources if not relevant. When you are answering, use the source and cite them with the answer. You have to answer the question and establish the validity of the answer using the provided sources. Cite the sources by the name of the document they are coming from and/or section title if found any. The answer should be very detailed and convincing. The answer should be in one or several paragraphs, avoid using numeric points, bullet points, etc. Don't use any other source to answer other than the provided sources. Include the relevant details as much as possible.  If you have multiple values, answer the query, and mention all of them. For example, if a query demands a singular value, and in the documents multiple values exist, answer with multiple values. '''},
                      {"role": "user", "content": f'''{text}'''}],
            0.8,
            model="gpt-4-turbo"
        )
        return (completion.choices[0].message.content, text)

    def answer_question(self, project_id, question):
        sources = asyncio.run(self.data_manager.extract_data(project_id, [question]))
        answer = self.get_answer(sources[0]['source_list'], question, False)
        return {"answer": answer[0], "sources": sources[0]['source_list'], "prompt": answer[1]}

    # async def answer_question_v1_1(self, project_id, question, requirement_id):

    #     self.report_manager = ReportManager(self.env_data.get('DATA_DIR'), project_id, requirement_id)
    #     initial_log = {
    #         "answer": [],
    #         "sources": [],
    #         "prompt": [],
    #         "bid_number": "0023419",
    #         "bid_title": "",
    #         "question": [],
    #         "reference": [],
    #         "raiser_by": "Please Complete",
    #         "responsible": "Please Complete",
    #         "status": "Please Complete",
    #         "tender_clarification_round": []
    #     }
    #     self.report_manager.create_initial_report(initial_log)
    #     response = self.report_manager.load_report()

    #     question_list = question.split('\n')       

    #     for idx, sub_question in enumerate(question_list):
    #         if not sub_question.strip():  # Skip empty lines
    #             continue

    #         attempt = 0
    #         max_attempts = 2
    #         while attempt < max_attempts:
    #             # print('tried: ', attempt)
    #             try:
    #                 sources = asyncio.run(self.data_manager.extract_data(project_id, [sub_question]))
    #                 project = Project.get_single(project_id)
    #                 [answer, prompt] = self.get_answer_claude(sources[0]['source_list'], sub_question)

    #                 answer = answer.split("$$$$$")
    #                 source_list = []
    #                 section_ids = []
    #                 if len(answer) > 1:
    #                     section_ids = answer[1].split(",")
                    
    #                 for section_id in section_ids:
    #                     section_id = section_id.strip()
                        
    #                     if section_id!="" and not section_id.isspace() and section_id!="\n":
    #                         sources[0]['source_map'][section_id]['content'] = self.ai_clean_and_format_text(sources[0]['source_map'][section_id]['content'])
    #                         source_list.append(sources[0]['source_map'][section_id])

    #                 # Append data to response structure
    #                 response["answer"].append(f"Query #{idx + 1}: {self.ai_clean_and_format_text(answer[0])}")
    #                 response["prompt"].append(f"Prompt for Query #{idx + 1}: {prompt}")
    #                 response["sources"].append(source_list)
    #                 response["reference"].append([source.get("content", "") for source in source_list])
    #                 response["bid_title"] = project['name']
    #                 response["question"].append(f"Query #{idx + 1}: {sub_question}")
    #                 response["tender_clarification_round"].append(f"Round #{idx + 1}")

    #                 await self.update_log_and_status(response, requirement_id, 1, True)
    #                 Requirement.update(requirement_id, status='response_started')
    #                 break
    #             except Exception as e:
    #                 attempt += 1
    #                 if attempt == max_attempts:
    #                     response["answer"] += f"Query #{idx + 1}: Failed to retrieve answer after multiple attempts.\n\n"
        
    #     await self.update_log_and_status(response, requirement_id, 2, True)
    #     Requirement.update(requirement_id, status='done')
    
    # async def update_log_and_status(self, log, requirement_id, status="", should_update_status=False):
    #     print('\n\n now updating qmp log with new log data...\n\n')

    #     # Update the log with new data
    #     self.report_manager.update_log(log)
        
    #     # Check if the status should be updated based on should_update_status parameter
    #     if should_update_status and status != "":
    #         self.backend_status_updater.update_history_status(requirement_id, status)


    def ai_clean_and_format_text(self, content):
        prompt = '''
            <HTML_CONTENT>
            {content}
            </HTML_CONTENT>
            <TASK>
                You are an assistant. Your task is to take the given HTML and replace any bad symbol:
                1. If any symbol is not properly shown or displayed then you should fix it by replacing it with the actual right symbol so it can be well displayed when viewed.
                2. Format the text correctly for readability. The response should not be choked up or too dense. Improve the formatting for readability by adjusting line breaks, spacing, and overall text structure where necessary.
            </TASK>
            
            <OUTPUT_FORMAT>
                Do not include any explanations or additional text outside of the given or formatted content.
            </OUTPUT_FORMAT>
            '''
        
        formatted_prompt = prompt.format(content=content)
        completion = self.claude_client.generate_message_sync(
            [{"role": "user", "content": formatted_prompt}],
            "", 
            0.1, "Claude 3 Sonnet", 4096
        )
        
        return completion
    
    def create_html_table(self, answer, sources, question):
        
        # Create the HTML table
        html_table = """
        <table border="1">
            <tr>
                <th>Clarification/Qualification Request</th>
                <th>Response</th>
                <th>Reference</th>
                <th>Raiser by</th>
                <th>Responsible</th>
                <th>Status (Open/Closed)</th>
            </tr>
        """
        
        # Add rows for each source
        for source in sources:
            content = source.get("content", "")
            html_table += f"""
            <tr>
                
                <td>{question}</td>
                <td>{answer}</td>
                <td>{content}</td>
                <td>????</td>
                <td>YYYY</td>
                <td>????</td>
            </tr>
            """
        
        # Close the table
        html_table += "</table>"
        
        return html_table
    
    def answer_question_v2(self, project_id, question):
        project = Project.get_by(id=project_id)[0]
        if not project["assistant_id"]:
            return {"error": "true", "message": "v2 not available for this project"}
        assistant_id = project["assistant_id"].split("/")[0]
        account_no = int(project["assistant_id"].split("/")[1])
        instructions = "Based on the provided files, answer the query of the user as truthfully as possible based on the sources. In the annotations, mention the parts from the document you used to answer."
        answer = self.assistant_manager.get_assistant_response(assistant_id, account_no, question, instructions)
        annotations = [{"content": x.file_citation.quote, "start": x.start_index, "end": x.end_index, "file_info": track_files(x.file_citation.file_id, x.file_citation.quote)} for x in answer.annotations]
        return {"answer": answer.value, "sources": annotations}


# if __name__ == "__main__":
#     question_answerer = QuestionAnswerer()
#     try:
#         print(question_answerer.answer_question_v2("b17ce8ad-6889-48f4-8419-a0256d39f70e", "What are the types of buildings to be provided in the camp site?"))
#     except Exception as e:
#         print(e)
