# celery_app.py
from extensions import celery
from celery import signals
import sentry_sdk
from sentry_sdk.integrations.celery import CeleryIntegration

def init_sentry(dsn, environment):
    sentry_sdk.init(
        dsn=dsn,
        integrations=[CeleryIntegration()],
        environment=environment,
        release="v1.0",
    )

def make_celery(app, flask_env, sentry_dsn):
    # Initialize Sentry for Celery if not in development
    if flask_env != 'development':
        init_sentry(sentry_dsn, flask_env)

    celery.conf.update(
        broker=app.config['CELERY_BROKER_URL'],
        backend=app.config['CELERY_RESULT_BACKEND'],
        include=[
            'task.file_processor_tasks',
            'task.file_processor_tasks_scp',
            'task.file_test_tasks',
            'task.file_processor_tasks_bid'
        ],
        broker_connection_retry_on_startup=True,  # Ensures broker retries only on startup
        task_acks_late=True,  # Ensures tasks are retried only if they fail
        task_reject_on_worker_lost=True,  # Reject tasks on worker loss
        task_default_retry_delay=1,  # Delay between retries (in seconds)
        task_annotations={
            '*': {'max_retries': 2}  # Applies max retries to all tasks
        }
    )
    
    class ContextTask(celery.Task):
        abstract = True
        
        def __call__(self, *args, **kwargs):
            with app.app_context():
                return super().__call__(*args, **kwargs)
    
    celery.Task = ContextTask
    return celery