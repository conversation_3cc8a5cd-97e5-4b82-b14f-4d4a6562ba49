from rq import Retry
import random
from extensions import queues
from task_redis.file_evaluator_scp_tasks import evaluate_files_scp_task as redis_scp_task
from task_celery.file_evaluator_scp_tasks import evaluate_files_scp_task as celery_scp_task
from task_redis.file_evaluator_cbp_tasks import evaluate_files_cbp_task as redis_cbp_task
from task_celery.file_evaluator_cbp_tasks import evaluate_files_cbp_task as celery_cbp_task
from task_redis.file_upload_tasks import upload_files_task as redis_file_upload_tasks
from task_celery.file_upload_tasks import upload_files_task as celery_file_upload_tasks
from task_celery.file_evaluator_qmp_tasks import evaluate_qmp_task as celery_qmp_task

from flask import current_app

class BackgroundTaskProcessor:
    TASK_TYPES = {
        "evaluate_scp": {
            "redis": redis_scp_task,
            "celery": celery_scp_task,
            "queue": "scp_",
            "required_params": ["request_id"]
        },
        "evaluate_cbp": {
            "redis": redis_cbp_task,
            "celery": celery_cbp_task,
            "queue": "cbp_",
            "required_params": ["request_id"]
        },
        "evaluate_qmp": {
            "redis": "",
            "celery": celery_qmp_task,
            "queue": "qmp_",
            "required_params": ["request_id", "other_project_ids"]
        },
        "file_upload": {
            "redis": redis_file_upload_tasks,
            "celery": celery_file_upload_tasks,
            "queue": "file_upload_",
            "required_params": ["upload_folder", "project_id", "file_ids", "file_names"]
        }
    }

    def __init__(self, task_type="redis", process_type="scp"):
        """
        Initialize the background task processor
        Args:
            task_type (str): Type of task processor - "redis" or "celery"
            process_type (str): Type of process - "scp", "cbp", or "file_upload"
        """
        self.task_type = task_type.lower()
        self.process_type = process_type.lower()

        if self.task_type not in ["redis", "celery"]:
            raise ValueError("Task type must be either 'redis' or 'celery'")
        if self.process_type not in self.TASK_TYPES:
            raise ValueError(f"Process type must be one of: {', '.join(self.TASK_TYPES.keys())}")

    def _validate_parameters(self, kwargs):
        """
        Validate that all required parameters for the task type are present
        Args:
            kwargs: Dictionary of parameters
        Raises:
            ValueError: If any required parameter is missing
        """
        required_params = self.TASK_TYPES[self.process_type]["required_params"]
        missing_params = [param for param in required_params if param not in kwargs]
        
        if missing_params:
            raise ValueError(
                f"Missing required parameters for {self.process_type} task: "
                f"{', '.join(missing_params)}"
            )

    def _get_task_parameters(self, kwargs):
        """
        Get the parameters needed for the specific task type
        Args:
            kwargs: Dictionary of parameters
        Returns:
            List of parameters in the correct order for the task
        """
        required_params = self.TASK_TYPES[self.process_type]["required_params"]
        return [kwargs[param] for param in required_params]

    def process_task(self, **kwargs):
        """
        Process the task based on the task type and process type
        Args:
            **kwargs: Parameters required for the specific task type
        Returns:
            The task object from either Redis or Celery
        """
        self._validate_parameters(kwargs)
        
        with current_app.app_context():
            if self.task_type == "redis":
                self._process_redis_task(**kwargs)
            self._process_celery_task(**kwargs)

    def _process_redis_task(self, **kwargs):
        """
        Process Redis task
        Args:
            **kwargs: Parameters for the task
        Returns:
            Redis task object
        """
        queue_key = self.TASK_TYPES[self.process_type]["queue"]
        task_func = self.TASK_TYPES[self.process_type]["redis"]
        q = random.choice(queues[queue_key])
        
        task_params = self._get_task_parameters(kwargs)
        q.enqueue(task_func, *task_params, retry=Retry(max=3))

    def _process_celery_task(self, **kwargs):
        """
        Process Celery task
        Args:
            **kwargs: Parameters for the task
        Returns:
            Celery task object
        """
        task_func = self.TASK_TYPES[self.process_type]["celery"]
        task_params = self._get_task_parameters(kwargs)
        task_func.delay(*task_params)