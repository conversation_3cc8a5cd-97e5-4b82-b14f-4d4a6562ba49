"""add is_test

Revision ID: 9188177328b2
Revises: a25cb9f72829
Create Date: 2024-11-12 23:14:41.991279

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9188177328b2'
down_revision = 'a25cb9f72829'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('requirement', schema=None) as batch_op:
        batch_op.add_column(sa.Column('is_test', sa.Integer(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('requirement', schema=None) as batch_op:
        batch_op.drop_column('is_test')

    # ### end Alembic commands ###
