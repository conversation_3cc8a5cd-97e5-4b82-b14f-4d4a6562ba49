import re
import ast
from collections import defaultdict

def reconstruct_samples(query_answer_list,add_answer=False):
    reconstructed = {
        "equipment_name": None,
        "categories": []
    }
    
    # Helper to organize categories and items
    category_map = defaultdict(list)

    for entry in query_answer_list:
        query = entry["query"]
        answer = entry.get("answer", "")

        # Extract content between XML-like tags
        eq_match = re.search(r"<eq>(.*?)</eq>", query)
        cat_match = re.search(r"<cat>(.*?)</cat>", query)
        item_match = re.search(r"<item>(.*?)</item>", query)
        co_match = re.search(r"<co>({.*?})</co>", query)

        if not (eq_match and cat_match and item_match and co_match):
            continue  # skip malformed entries

        equipment_name = eq_match.group(1)
        category_name = cat_match.group(1)
        item_name = item_match.group(1)
        additional_context = ast.literal_eval(co_match.group(1))

        # Set equipment_name once
        if reconstructed["equipment_name"] is None:
            reconstructed["equipment_name"] = equipment_name

        # Build the item dictionary with the answer
        if add_answer:
            item = {
                "name": item_name,
                "unit": additional_context.get("unit", ""),
                "answer": answer
            }
        else:

            item = {
                "name": item_name,
                "unit": additional_context.get("unit", "")
            }


        # Append to the right category
        category_map[category_name].append(item)

    # Final formatting into the expected structure
    for category_name, items in category_map.items():
        reconstructed["categories"].append({
            "name": category_name,
            "items": items
        })

    return reconstructed

def merge_equipment_lists(base_list, new_list):
    """
    Merge two lists of equipment dictionaries based on 'equipment_name'.
    For matching equipment, merge categories and items.
    """
    # Handle both single dictionaries and lists of dictionaries
    if isinstance(base_list, dict):
        base_list = [base_list]
    if isinstance(new_list, dict):
        new_list = [new_list]
    
    # Convert base_list to a dict for quick lookup
    base_map = {equip["equipment_name"]: equip for equip in base_list if isinstance(equip, dict) and "equipment_name" in equip}

    for new_equip in new_list:
        if not isinstance(new_equip, dict) or "equipment_name" not in new_equip:
            continue
            
        new_name = new_equip["equipment_name"]

        if new_name in base_map:
            # Equipment exists in base list, merge categories
            base_equip = base_map[new_name]
            
            # Create category map for base equipment
            if "categories" not in base_equip:
                base_equip["categories"] = []
            base_cat_map = {cat["name"]: cat for cat in base_equip["categories"] if isinstance(cat, dict) and "name" in cat}

            # Process each category in new equipment
            for new_cat in new_equip.get("categories", []):
                if not isinstance(new_cat, dict) or "name" not in new_cat:
                    continue
                
                cat_name = new_cat["name"]
                
                if cat_name in base_cat_map:
                    # Category exists, merge items
                    base_cat = base_cat_map[cat_name]
                    
                    if "items" not in base_cat:
                        base_cat["items"] = []
                    if "items" not in new_cat:
                        new_cat["items"] = []
                    
                    # Create item map for base category
                    base_item_map = {item["name"]: item for item in base_cat["items"] if isinstance(item, dict) and "name" in item}
                    
                    # Add or update items
                    for new_item in new_cat["items"]:
                        if not isinstance(new_item, dict) or "name" not in new_item:
                            continue
                        
                        item_name = new_item["name"]
                        
                        if item_name in base_item_map:
                            # Update existing item
                            for key, value in new_item.items():
                                base_item_map[item_name][key] = value
                        else:
                            # Add new item
                            base_cat["items"].append(new_item)
                else:
                    # Add new category with all its items
                    base_equip["categories"].append(new_cat)
        else:
            # Add new equipment block
            base_list.append(new_equip)

    return base_list[0] if len(base_list) == 1 else base_list



def construct_queries(samples_search):
    """Constructs a list of queries from the samples_search dictionary."""
    equipment_name = samples_search["equipment_name"]
    queries_with_tags = []
    queries_without_tags = []
    
    for category in samples_search["categories"]:
        category_name = category["name"]
        for item in category["items"]:
            item_name = item["name"]
            # Add additional context from item fields
            additional_context = {
                "unit": item.get("unit", "")
            }
            # Format with XML-style tags
            query_with_tags = f"<eq>{equipment_name}</eq><cat>{category_name}</cat><item>{item_name}</item><co>{additional_context}</co>"
            queries_with_tags.append(query_with_tags)
            
            # Format without tags
            query_without_tags = f"{equipment_name} {category_name} {item_name}"
            queries_without_tags.append(query_without_tags)
    
    return queries_with_tags, queries_without_tags