from cerebras.cloud.sdk import Cerebras
import random
from services.cerebras_api_key_provider import CerebrasAPIKeyProvider

class CerebrasService:
    def __init__(self):
        """Initializes the CerebrasService class.

        Args:
            api_key_provider (CerebrasAPIKeyProvider): An instance of the API key provider.
        """
        self.api_key_provider = CerebrasAPIKeyProvider()
        self.models = ["llama-3.3-70b", "llama3.1-8b"]

    def generate_completion(self, messages):
        """
        Generates a chat completion using the Cerebras client.

        Args:
            messages (list): A list of message dictionaries containing role and content.

        Returns:
            dict: A dictionary containing the generated text content, or None in case of failure.
        """
        api_key = self.api_key_provider.get_ready_api_key()
        client = Cerebras(api_key=api_key)

        # Select two random models
        selected_models = random.sample(self.models, 2)

        for model in selected_models:
            try:
                response = client.chat.completions.create(
                    messages=messages,
                    model=model
                )
                
                
                return {
                    "text_content": response.choices[0].message.content
                }
            except Exception as e:
                print(f"Error with model {model}: {e}")

        # If both attempts fail, return None
        return None
    







    