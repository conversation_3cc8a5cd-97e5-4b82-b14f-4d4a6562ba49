import os
import time
import json
from models import Requirement
from concurrent.futures import ThreadPoolExecutor
from services.parser_openai_parellel import OpenAIProcessor
from services.faiss_embedding import FaissEmbeddingGPU
import asyncio
import redis


class CBPFileProcessor:
    def __init__(self, socket_manager, request_id, existing_req=None, instant_file_names=[], request_batch=1):
        print('initialized the cbp fileprocessor class...')
        print('existing request: ', existing_req)
        # print('request_id: ', request_id)
        if not existing_req:
            self.requirement = Requirement.get_single(request_id)
        else:
            self.requirement = existing_req
            
        self.package_id = self.requirement['project_id']
        self.file_processor = OpenAIProcessor()
        self.request_id = request_id
        self.converted_files = []
        self.processed_count = 0
        self.total_files = 0
        self.file_status = {}
        self.instant_file_names = instant_file_names
        self.socket_manager = socket_manager
        self.redis_client = redis.StrictRedis(host='localhost', port=6379, db=0, decode_responses=True)
        self.file_status_key = f"processing_req_{self.request_id}"
        self.batch_file_status_key = f"{self.request_id}_files_batch_{request_batch}"
    
    # Function to calculate max_concurrent_tasks dynamically
    def calculate_concurrency(self, file_count: int) -> int:
        if file_count >= 100:
            return 100
        elif file_count >= 50:
            return 50
        else:
            return 10 

    def load_file_status(self):
        """Load file status from Redis."""
        self.batch_file_status = json.loads(self.redis_client.get(self.batch_file_status_key))
        
        file_status = self.redis_client.get(self.file_status_key)
        if file_status:
            self.file_status = json.loads(file_status)
            print(f"Loaded file status for {self.request_id} from Redis.")
            # print('file status: ', self.file_status)
        else:
            self.file_status = {}
            print(f"No existing file status found in Redis for {self.request_id}.")

    def save_file_status(self):
        """Save the current file status to Redis."""
        self.redis_client.set(self.file_status_key, json.dumps(self.file_status))
        print(f"Saved file status for {self.request_id} to Redis.")

    async def seed_upload_data(self, file_name, data, file_type):        
        print("uploaded data below before send to vec db....")
        print('file_type: ', file_type)
        # Add texts to the FAISS index
        start_time = time.time()
        if file_type in ["csv", "xls", "xlsx"]:
            print('using v2...')
            result = await self.faiss_embedding.insert_documents_v2(data, file_name, self.request_id)
        else:
            result = await self.faiss_embedding.insert_documents(data, file_name, self.request_id)
        
        
        elapsed_time = time.time() - start_time
        print(f"Time taken to insert Faiss document '{file_name}': {elapsed_time:.2f} seconds")
        return result


    async def handle_single_uploaded_document(self, file_path, project_id, file_type):
        try:
            print('now processing pinecone seed for file: ', file_path)
            print('this is filetype also: ', file_type)
            if file_type == 'pdf':
                data = await self.file_processor.extract_text_from_pdf(file_path)
            elif file_type in ["docx", "doc"]:
                data = await self.file_processor.extract_text_from_docx(file_path)
            elif file_type in ["csv", "xls", "xlsx"]:
                data = await self.file_processor.extract_text_from_excel_or_csv_batch(file_path)
            else:
                raise ValueError(f"Unsupported file type: {file_type}")
            
            # print('data received for above file.......')
            await self.seed_upload_data(os.path.basename(file_path), data[1], file_type)
            print('done processing the above file.... \n')
            
        except Exception as e:
            print('failed to retrieve data for above file...')
            print(e)
    
    async def _and_upload_file(self, file_path):
        """Save a single file and convert if necessary."""
        # print('this is file_path: ', file_path)
        file_type = file_path.rsplit('.', 1)[1].lower()

        # Process the file asynchronously
        await self.handle_single_uploaded_document(file_path, self.package_id, file_type)

        return file_path
    
    def process_files(self):
        asyncio.run(self._async_process_files())
    
    async def _async_process_files(self):
        self.add_event(self.request_id, 'file_processing_started', {
            'event_type': 'processing_files',
            'request_id': self.request_id,
            'data': {'status': 'Start processing Files', 'message': f"Processing files..."}
        })
        
        print('process files meth.....')
        self.load_file_status()
        
        """Process files in the background and return the request_id immediately."""
        if not self.instant_file_names:
            files_names = json.loads(self.requirement["name"])
        else:
            files_names = self.instant_file_names

        print('entering faiss embed...')
            
        self.faiss_embedding = FaissEmbeddingGPU(request_id=self.request_id,batch_size=50)

        print('Processing files efficiently...')
        start_time = time.time()
        
        # Create a background task to handle the file processing
        await self.process_files_in_background(files_names, start_time)
        
        self.batch_file_status['processed'] = self.batch_file_status.get('processed', 0) + len(files_names)
        self.redis_client.set(self.batch_file_status_key, json.dumps(self.batch_file_status))
        
        self.save_file_status()

        total_processed_files, total_uploaded_files = self.get_total_processed_files()

        if self.converted_files:
            # Send completed message
            self.load_file_status()
            self.add_event(self.request_id, 'completed_message', {
                'event_type': 'processing_files',
                'request_id': self.request_id,
                'data': {
                    'status': 'Completed', 
                    'total': total_processed_files,
                    'file_processed': total_processed_files,
                    'message': f"All {total_uploaded_files} files processed successfully."
                }
            })

            elapsed_time = time.time() - start_time
            print(f"Process completed in {elapsed_time:.2f} seconds.")
        else:
            raise ValueError('Allowed file types are .pdf and .docx')

    async def process_files_in_background(self, files_names, start_time):
        """Actual file processing in the background."""
        try:
            print('starting process_files in background....')

            self.add_event(self.request_id, 'progress_message', {
                'event_type': 'processing_files',
                'request_id': self.request_id,
                'data': {'status': 'Preparing Files', 'message': f"Processing files..."}
            })

            print('filenames: ', files_names)

            
            self.file_status.update({file_name: self.file_status.get(file_name, False) for file_name in files_names})
            self.save_file_status()
            
            batch_size = 50
            self.total_files = len(files_names)
    
            # Divide files into batches
            # while not all(self.file_status.values()):
            while not all(self.file_status[file] for file in files_names):
                # Filter unprocessed files for the batch
                # unprocessed_files = [file for file, processed in self.file_status.items() if not processed]
                batch = [file for file in files_names if not self.file_status[file]][:batch_size]
                # batch = unprocessed_files[:batch_size]
                if not batch:
                    break
                print(f"Processing batch with {len(batch)} files...")
    
                # Create tasks for the current batch
                tasks = [
                    self.upload_file_with_progress(
                        os.path.join(self.package_id, self.request_id, file_path),
                        count
                    )
                    for count, file_path in enumerate(batch, start=1)
                ]
    
                # Await completion of the current batch
                await asyncio.gather(*tasks)

        except Exception as e:
            print(f"Error during file processing: {e}")
            raise

    async def upload_file_with_progress(self, file_path, count):
        """Upload file with progress messaging for each file."""
        try:
            print(f'Starting processing for file {count}...')
            print('this is the file path: ', file_path)

            # Send 'Uploading' event for the current file
            self.add_event(self.request_id, 'progress_message', {
                'event_type': 'processing_files',
                'request_id': self.request_id,
                'data': {'status': 'Processing Files', 'message': f"Processing file {count}..."}
            })

            # Process the file (simulate file upload or processing)
            file_path = os.path.join('chronobid', file_path)
            temp_file_path = await self._and_upload_file(file_path)
            
            
            # Update file status and progress count
            file_name = os.path.basename(file_path)
            self.file_status[file_name] = True
            self.save_file_status()
            self.processed_count = sum(self.file_status.values())

            print(f"File {self.processed_count} of {len(self.file_status)} processed successfully.")

            # Send 'Processing Progress' event for the current file
            self.add_event(self.request_id, 'progress_message', {
                'event_type': 'processing_files',
                'request_id': self.request_id,
                'data': {
                    'status': 'Processing Files',
                    'message': f"Processed {self.processed_count}/{len(self.file_status)} files successfully."
                }
            })
            
            self.converted_files.append(temp_file_path)
        except Exception as e:
            print(f"Error processing file {count}: {e}")
            raise

        
    def add_event(self, request_id, event_name, data):
        print(f"Adding event {event_name} to room {request_id}")
        # return
        self.socket_manager.emit_to_client(request_id, event_name, data, '/processing_files')

    def get_total_processed_files(self):
        # Get all keys matching the batch pattern
        batch_keys_pattern = f"{self.request_id}_files_batch_*"
        batch_keys = self.redis_client.keys(batch_keys_pattern)
    
        total_processed = 0
        total_uploaded_files = 0
    
        for batch_key in batch_keys:
            batch_data = self.redis_client.get(batch_key)
            if batch_data:
                batch_info = json.loads(batch_data)
                total_processed += batch_info.get('processed', 0)  # Sum up processed counts
                total_uploaded_files += batch_info.get('uploaded', 0)
        
        return total_processed,total_uploaded_files


# if __name__ == "__main__":
#     async def main():
#         cbp = CBPFileProcessor("", "ef61f630-041b-4015-9864-426c059abd68")
#         await cbp.process_files()

#     # Run the async main function
#     asyncio.run(main())
    
    