import random
from services.groq_ai_service import GroqService
from services.cerebra_ai_service import CerebrasService


class FastAPIs:
    def __init__(self):
        """Initializes the FastAPIs class."""
        self.groq_service = GroqService()
        self.cerebras_service = CerebrasService()

    def _try_groq_service(self, messages, model):
        print("""Attempts to generate a chat completion using GroqService.""")
        try:
            return self.groq_service.generate_completion(messages, model=model)
        except Exception as e:
            print(f"GroqService failed: {e}")
        return None

    def _try_cerebras_service(self, messages):
        print("""Attempts to generate a chat completion using CerebrasService.""")
        try:
            response = self.cerebras_service.generate_completion(messages)
            return response.get("text_content") if response else None
        except Exception as e:
            print(f"CerebrasService failed: {e}")
        return None

    def generate_completion(self, messages, service=None, model=None):
        """
        Generates a chat completion by randomly selecting a service to try first.

        Args:
            messages (list): A list of message dictionaries containing role and content.

        Returns:
            str or None: The generated text content, or None if both services fail.
        """
        if service == 'groq':
            return self._try_groq_service(messages, model)
        elif service == 'cerebras':
            return self._try_cerebras_service(messages)

        print('randomly picking methods...')
        services = [self._try_groq_service, self._try_cerebras_service]
        random.shuffle(services)

        for service in services:
            result = service(messages, model) if service == self._try_groq_service else service(messages)
            if result:
                return result

        return None


# Example usage:
if __name__ == "__main__":
    messages = [
        {"role": "user", "content": "Explain the benefits of AI in healthcare."}
    ]

    fast_apis = FastAPIs()
    result = fast_apis.generate_completion(messages)

    if result:
        print("Generated Text:", result)
    else:
        print("Failed to generate completion.")
