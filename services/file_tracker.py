from models import File_merged_file_association, Merged_file, File
from services.document_loader import load_single_document

def track_files(assistant_file_id, quote):
    
    merged_file = Merged_file.get_by(assistant_file_id=assistant_file_id)
    

    if len(merged_file) == 0:
        return {"file_id": None, "file_name": None}
    else:
        merged_file = merged_file[0]
    print(merged_file)
    files = File_merged_file_association.get_by(merged_file_id=merged_file["id"])

    text = load_single_document(f'''data/{merged_file["project_id"]}/{merged_file["id"]}_merged.docx''')

    num = text.find(quote[:100])
    if num == -1:
        return {"file_id": None, "file_name": None}
    

    for file in files:
        print(file)
        if file["merge_start_index"] is None or file["merge_end_index"] is None:
            continue

        if file["merge_start_index"] <= num and file["merge_end_index"] >= num:
            file_infos = File.get_by(id=file["file_id"])[0]
            return {"file_id": file_infos["id"], "file_name": file_infos["name"], "file_type": file_infos["file_type"]}
        
        
    return {"file_id": None, "file_name": None}
