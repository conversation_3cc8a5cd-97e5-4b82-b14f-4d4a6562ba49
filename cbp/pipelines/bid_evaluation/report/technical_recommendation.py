from typing import Dict, Any
import json
import pandas as pd
import re
from bs4 import Beautiful<PERSON>oup
import json
from services.claude_ai_service import Claude<PERSON>er<PERSON>
from services.prompt_loader import PromptLoader

claude_client = ClaudeService(claude_keys=['CLAUDE_API_CBP', 'CLAUDE_API_CBP'], default_key="CLAUDE_API_CBP")

def generate_technical_recommendation(data: Dict[str, Any], temperature: float = 0.001) -> str:
    """
    Generate technical recommendation based on technical criteria evaluation.
    
    Args:
        data: Dictionary containing the aggregated report data
        temperature: Temperature parameter for AI generation (default: 0.001)
    
    Returns:
        str: HTML formatted technical recommendation
    """
    # Extract technical criteria names and convert to set to remove duplicates
    technical_criteria = set(data["data_aggregrated_report"]["technical_criteria_names"])
    
    # Extract comparison table data for technical criteria
    technical_comparisons = [
        item for item in data["data_aggregrated_report"]["comparison_table"]
        if item["criteria"] in technical_criteria
    ]
    
    # Extract bidder summaries for technical criteria
    technical_summaries = [
        item for item in data["data_aggregrated_report"]["bidder_summary"]
        if item["criteria"] in technical_criteria
    ]
    
    # Generate HTML template
  
    prompt = PromptLoader().get_prompt('cbp_report_technical_recommendation', {
        "criteria_analysis": data
    })
    
    # Prepare messages for LLM
    messages = [{"role": "user", "content": prompt}]
    
    try:
        # Generate response using Claude
        response = claude_client.generate_message_agent_sonnet_sync(
            messages=messages,
            temperature=temperature,
            max_tokens=4096
        )
        
        # Parse and return the response
        response = response.content[0].text
        print(f"Comparison summary response: {response}")
        return response
        
    except Exception as e:
        print(f"Error generating comparison: {e}")
        return {}
    
    
    
    return html_template

# Example usage:
if __name__ == "__main__":
    # Load your data here
    data = {
        "data_aggregrated_report": {
            "comparison_table": [
                {
                    "criteria": "BALL MILL",
                    "Tysen": "<p>Tysen demonstrates strong technical competence in their Ball Mill specifications, providing comprehensive documentation on critical parameters such as dimensions, power requirements, and operating speeds. Their submission includes detailed technical data across multiple reference documents, showcasing a thorough understanding of equipment requirements. Key strengths include precise specifications for shell diameter, grinding length, and critical speed operation. However, the absence of tender requirements prevents a complete compliance assessment. In comparison to Mo group, Tysen's submission appears equally strong, with both bidders receiving identical scores, suggesting comparable technical proficiency in this area.</p>",
                    "Mo group": "<p>Mo group's Ball Mill specifications largely meet the technical requirements, with notable strengths in power capacity, mill dimensions, and liner specifications. Their submission demonstrates good technical compliance overall, but there are areas for improvement, including the need for clarification on the drilling method and more detailed information on certain operational parameters. When compared to Tysen, Mo group's proposal shows similar technical proficiency, as evidenced by their identical scores. However, Mo group's submission appears to have some minor gaps that need addressing for optimal project alignment, which could potentially differentiate them from Tysen in a more detailed assessment.</p>",
                    "Scores": {
                        "Tysen": "32%",
                        "Mo group": "32%"
                    }
                },
                {
                    "criteria": "Technical Criteria",
                    "Tysen": "<p>Tysen's technical submission for the Ball Mill demonstrates good technical competence, providing detailed specifications for critical operational parameters. Their proposal includes comprehensive information on mill dimensions, power requirements, operating conditions, and liner specifications. While the information appears technically sound and consistent with industry standards, the absence of tender specification documents prevents a complete compliance assessment. Compared to Mo group, Tysen's submission is equally strong in terms of technical details, with both bidders receiving the same score. However, Tysen's proposal lacks specific information on drilling methods, which is an area where Mo group provided more clarity.</p>",
                    "Mo group": "<p>Mo group's Ball Mill specifications largely meet the technical requirements, showcasing strong points in power capacity, mill dimensions, and liner specifications. Their proposal demonstrates good technical compliance, aligning well with project needs. Compared to Tysen, Mo group provides more clarity on the drilling method, which is a unique advantage. However, both bidders received the same technical score, indicating a similar overall level of technical competence. Mo group could improve their submission by providing more detailed information on certain operational parameters to further distinguish themselves from competitors like Tysen.</p>",
                    "Scores": {
                        "Tysen": "40%",
                        "Mo group": "40%"
                    }
                }
            ], 
            "technical_criteria_names": [
                "BALL MILL",
                "Technical Criteria",
                "BALL MILL",
                "Technical Criteria"
            ],
            "bidder_summary": [
                {
                    "criteria": "technical_evaluation_criteria",
                    "Tysen_score": "",
                    "Tysen_summary": "",
                    "Mo group_score": "",
                    "Mo group_summary": ""
                },
                {
                    "criteria": "Technical Criteria",
                    "Tysen_score": "40%",
                    "Tysen_summary": "The technical submission for the Ball Mill demonstrates good technical competence with detailed specifications for critical operational parameters. The bidder has provided comprehensive information on mill dimensions, power requirements, operating conditions, and liner specifications. While the information appears technically sound and consistent with industry standards, the absence of tender specification documents prevents a complete compliance assessment. The overall technical score reflects the quality and completeness of the provided information rather than direct compliance with tender requirements.",
                    "Mo group_score": "40%",
                    "Mo group_summary": "The Ball Mill specifications largely meet the technical requirements, with strong points in power capacity, mill dimensions, and liner specifications. Areas for improvement include clarification on the drilling method and more detailed information on certain operational parameters. Overall, the equipment demonstrates good technical compliance, but some minor gaps need addressing for optimal project alignment."
                },
                {
                    "criteria": "BALL MILL",
                    "Tysen_score": "32%",
                    "Tysen_summary": "The Ball Mill specifications are well-documented with detailed information on critical parameters including dimensions, power requirements, operating speeds, and liner specifications. The bidder has provided comprehensive technical data in multiple reference documents that demonstrate a thorough understanding of the equipment requirements. Key specifications include a 6.50m inside shell diameter, 9.80m effective grinding length, 75% critical speed operation at 12.6 rpm, and power draw specifications under different operating conditions. While the technical information is comprehensive, the absence of tender requirements prevents a complete compliance assessment.",
                    "Mo group_score": "32%",
                    "Mo group_summary": "The Ball Mill specifications largely meet the technical requirements, with strong points in power capacity, mill dimensions, and liner specifications. Areas for improvement include clarification on the drilling method and more detailed information on certain operational parameters. Overall, the equipment demonstrates good technical compliance, but some minor gaps need addressing for optimal project alignment."
                }
            ]
        }
    }
    
    technical_recommendation_html = generate_technical_recommendation(data)
    
    # Save as HTML
    with open('technical_recommendation.html', 'w') as f:
        f.write(technical_recommendation_html) 