import json
import re
import time
import asyncio
import os,sys,re
from services.parser_openai_parellel import OpenAIProcessor
from services.pull_reference import find_reference
from services.summarization import SummarizationAssistant
from services.parser_openai import AIProcessor
from services.synonym_expansion import SynonymGenerator
from services.process_cbp_evaluate_chunks import AIChunckProcessor
from services.cohere_embedding import CohereService
from services.engr_doc_types_detection import EngrDocTypesDetection

class Dataset2Extractor:
    def __init__(self):
        self.open_ai_processor = OpenAIProcessor()
        self.summarization = SummarizationAssistant()
        self.find_reference = find_reference
        self.parser_openai = AIProcessor()
        self.synonym_service = SynonymGenerator()
        self.chunck_processor = AIChunckProcessor()
        self.engr_doc_detector = EngrDocTypesDetection()
        self.type = ""
    
    async def  process_new_file_Chronobid(self, file_path, criteria, source_document_text, count, log={}):
        print(f"Entering process new chronobid file {file_path}")
        t1 = time.time()
        #-- the below chunk the data, parse by appending sign & also group into title & contents ---#
        result = await self.open_ai_processor.get_serialized_data_openai_parser(file_path)

        # Calculate time taken for dataset1
        t2 = time.time()
        dataset1_duration = (t2 - t1) / 60  # Convert seconds to minutes
        print(f"serialize text done in {dataset1_duration:.2f} minutes.....\n\n")

        data = result[1] #chunkarray

        # Start counting time for dataset2 retrieval
        t3 = time.time()
        evaluate_data, token_in, token_out = await self.master_evaluation_v2(data, source_document_text, criteria, count, 3)

        # Calculate time taken for dataset2
        t4 = time.time()
        dataset2_duration = (t4 - t3) / 60  # Convert seconds to minutes
        print(f"master evaluation done in {dataset2_duration:.2f} minutes.....\n\n")

        # resetting the array values
        log['evaluation_report_section']['strength_chunk'] = []
        log['evaluation_report_section']['weak_chunk'] = []
        log['evaluation_report_section']['risk_chunk'] = []

        # Evaluate each section and append to the appropriate chunk in the log
        for section in evaluate_data:
            score_str = section['evaluation_data'][1]
            # Remove the percentage sign if it exists
            if score_str.endswith('%'):
                score_str = score_str[:-1]

            score = int(score_str)
            if 20 < score < 81:
                log['evaluation_report_section']['weak_chunk'].append(section)
            elif score >= 81:
                log['evaluation_report_section']['strength_chunk'].append(section)
            elif score <= 20:
                log['evaluation_report_section']['risk_chunk'].append(section)
    

        print("Done with sections evaluation CBP....")
        return evaluate_data, token_in, token_out
    
    async def master_evaluation_v2(self, chunked_text, source_document_text, criteria, count, limit=3):
        tx = time.time()
        synonyms = await self.synonym_service.generate_synonym(f"{criteria['name']}\n Description: {criteria['description']}")
        token_count_in = 0
        token_count_out = 0

        # First run: for the question title & description
        query_title_desc = f"""
            Question {count} 
            Title: {criteria['name']}
            Description: {criteria['description']}
        """

        # Capture both name and description together
        name_descriptions = re.findall(r"Name:\s*(.+?)\n\s*Description:\s*(.+?)(?:\n|</SYNONYMS>)", synonyms, re.DOTALL)

        # Combine each name and description into a single string
        name_description_pairs = [f"{name.strip()} - {description.strip()}" for name, description in name_descriptions]
        
        # Calculate time taken for generate synonym
        ty = time.time()
        synonym_duration = (ty - tx) / 60  # Convert seconds to minutes
        print(f"synonym generation done in {synonym_duration:.2f} minutes.....\n\n")


        t1 = time.time()
        self.cohere_embedding = CohereService()
        retrieved_master_texts = self.cohere_embedding.search_similar(query_title_desc, chunked_text)
        synonym_searches = [
            self.cohere_embedding.search_similar(pair, chunked_text, 2)
            for pair in name_description_pairs
        ]
        synonym_results = [result for results in synonym_searches for result in results]

        # Calculate time taken for dataset1
        t2 = time.time()
        dataset1_duration = (t2 - t1) / 60  # Convert seconds to minutes
        print(f"Cohere searching similar text done in {dataset1_duration:.2f} minutes.....\n\n")

        # Merging the two arrays
        processing_lists = [retrieved_master_texts, synonym_results]
        # Start counting time for dataset2 retrieval
        t3 = time.time()
        unified_responses = []
        for current_list in processing_lists:
            for section in current_list:
                section_text = section
                try:
                    task = self.chunck_processor.get_claude_to_evaluate_chunks_based_on_criteria(section_text, source_document_text, criteria, count)
                    self.type = "claude"
                    if asyncio.iscoroutine(task):
                        evaluation_data, token_in, token_out = await task
                    else:
                        raise RuntimeError("Task from Claude API is not a coroutine")
                except Exception as e:
                    print(f"Error with Claude API: {e}. Falling back to OpenAI API.")

                evaluation_result = await self.extract_data_from_string(evaluation_data)
                unified_responses.append({
                    "text_content": section_text,
                    "evaluation_data": evaluation_result
                })
                token_count_in += token_in
                token_count_out += token_out

                # Check if the score exceeds 60
                score = float(evaluation_result[1]) if evaluation_result[1].isdigit() else 0
                if score >= 60:
                    print(f"Score {score} exceeds 60, stopping further processing.")
                    return unified_responses, token_count_in, token_count_out

        # Calculate time taken for dataset2
        t4 = time.time()
        dataset2_duration = (t4 - t3) / 60  # Convert seconds to minutes
        print(f"Claude evaluation chunk extraction done in {dataset2_duration:.2f} minutes.....\n\n")
        return unified_responses, token_count_in, token_count_out

    async def extract_data_from_string(self, data):
        if self.type == "gpt":
            data_string = data.choices[0].message.content
        elif self.type == "claude":
            data_string = data

        # Define regex patterns for different tags
        evaluation_pattern = re.compile(r'<Evaluation>(.*?)</Evaluation>', re.IGNORECASE | re.DOTALL)
        score_pattern = re.compile(r'<Score>(.*?)</Score>', re.IGNORECASE | re.DOTALL)
        content_pattern = re.compile(r'<Content>(.*?)</Content>', re.IGNORECASE | re.DOTALL)
        reason_pattern = re.compile(r'<Reason>(.*?)</Reason>', re.IGNORECASE | re.DOTALL)
        reference_pattern = re.compile(r'<Reference>(.*?)</Reference>', re.IGNORECASE | re.DOTALL)
    
        # Extract data using each pattern
        evaluation_sections = evaluation_pattern.findall(data_string)
        score_sections = score_pattern.findall(data_string)
        content_sections = content_pattern.findall(data_string)
        reason_sections = reason_pattern.findall(data_string)
        reference_sections = reference_pattern.findall(data_string)
    
        # Check if lists are empty before accessing their elements
        data1 = evaluation_sections[0] if evaluation_sections else ''
        data2 = score_sections[0] if score_sections else ''
        data3 = content_sections[0] if content_sections else ''
        data4 = reason_sections[0] if reason_sections else ''
        data5 = reference_sections[0] if reference_sections else ''
    
        # Merge extracted data into a list
        merge = [data1, data2, data3, data4, data5]
    
        return merge
