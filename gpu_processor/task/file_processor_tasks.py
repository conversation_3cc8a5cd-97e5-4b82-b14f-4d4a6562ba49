# /home/<USER>/file_processor_tasks.py
from extensions import celery
from socket_instance import socket_instance
from models import Requirement
from services.cbp_file_processor import CBPFileProcessor
import json

@celery.task(max_retries=3, name="task.file_processor_tasks.process_files_task")
def process_files_task(req_id, file_names_arr, existing_req, request_batch):
    try:
        print(f"starting celery task for batch {req_id}_files_batch_{request_batch}...")
        
        if existing_req:
            existing_req_names = json.loads(existing_req["name"])
            new_names = file_names_arr + existing_req_names
            Requirement.update(req_id, name=json.dumps(new_names))
        else:
            Requirement.update(req_id, name=json.dumps(file_names_arr))

        try:
            socket_manager = socket_instance.get_instance()

            # Emit a test event to the client using the socket manager
            socket_manager.emit_to_client(
                req_id,
                'test_event',
                {'message': 'Test from Celery CBP'},
                namespace='/processing_files'
            )
            print('starting to process files...')
            file_processor = CBPFileProcessor(socket_manager, req_id, existing_req, instant_file_names=file_names_arr, request_batch=request_batch)
            file_processor.process_files()
        except Exception as e:
            print(f"Error during file processing: {e}")

        return "File processing completed successfully."
    except Exception as e:
        print(f"Error during file processing: {e}")
        return str(e)