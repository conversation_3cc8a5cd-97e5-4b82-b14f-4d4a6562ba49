{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"ename": "IndentationError", "evalue": "unexpected indent (787483610.py, line 2)", "output_type": "error", "traceback": ["\u001b[0;36m  Cell \u001b[0;32mIn[3], line 2\u001b[0;36m\u001b[0m\n\u001b[0;31m    satisfying_data = {\"ids\": [], \"metadatas\": [], \"questions\": [], \"content\":[]}\u001b[0m\n\u001b[0m    ^\u001b[0m\n\u001b[0;31mIndentationError\u001b[0m\u001b[0;31m:\u001b[0m unexpected indent\n"]}], "source": [" print(\"Extracting pinecone data for chronobid main...\")\n", "        satisfying_data = {\"ids\": [], \"metadatas\": [], \"questions\": [], \"content\":[]}\n", "        print('right here...')\n", "        concatenated_criteria = f\"{criteria['description']} {criteria['name']}\"\n", "        print('stopped here....')\n", "       # Generate synonyms\n", "        synonyms = self.synonym_service.generate_synonym_sync(\n", "            f\"{criteria['name']}\\n Description: {criteria['description']}\"\n", "        )\n", "        print('synonym generated successfully...')\n", "        # print('this is synonyms: ', synonyms)\n", "\n", "        # Parse synonyms with error handling\n", "        name_descriptions = re.findall(\n", "            r\"Name:\\s*(.+?)\\n\\s*Description:\\s*(.+?)(?:\\n|</SYNONYMS>)\",\n", "            synonyms,\n", "            re.DOTALL\n", "        )\n", "        # print('name & desc: ', name_descriptions)\n", "\n", "        # Create name-description pairs\n", "        name_description_pairs = [\n", "            f\"{name.strip()} - {description.strip()}\"\n", "            for name, description in name_descriptions\n", "        ]\n", "\n", "        # print('name & desc pairs: ', name_description_pairs)\n", "\n", "        # Query Pinecone\n", "        print(f\"PROJECT ID HERE : {project_id}\")\n", "        results = vec_db.get_data(project_id, [concatenated_criteria], 3)\n", "        results2 = vec_db.get_data(project_id, [name_description_pairs[0]], 2)\n", "        results3 = vec_db.get_data(project_id, [name_description_pairs[1]], 3)\n", "        results4 = vec_db.get_data(project_id, [name_description_pairs[2]], 2)\n", "        # results5 = self.vec_db.get_data(project_id, [name_description_pairs[3]], 3)\n", "        # results6 = self.vec_db.get_data(project_id, [name_description_pairs[4]], 2)\n", "\n", "        total_result = {**results, **results2, **results3, **results4}  # Use unpacking to merge dictionaries\n", "\n", "        # print('total results: ', total_result)\n", "\n", "        # Extract data that satisfies the criteria\n", "        if not any(total_result[\"ids\"]) and not any(total_result[\"metadatas\"]):\n", "            print('empty response from pinecone...')\n", "            return satisfying_data\n", "\n", "        for ids, metadatas in zip(total_result[\"ids\"], total_result[\"metadatas\"]):\n", "            satisfying_data[\"ids\"].append(ids)\n", "            for metadata in metadatas:\n", "                # print('this is metadata: ', metadata)\n", "                chunk = Chunks.get_by(id=metadata[\"section_id\"])\n", "                if len(chunk) > 0:\n", "                    metadata['detailed_chunk'] = chunk[0][\"content\"]\n", "                    metadata['title'] = chunk[0][\"title\"]\n", "                    metadata['source'] = chunk[0][\"source\"]\n", "                    metadata['page_number'] = chunk[0][\"page_number\"]\n", "                satisfying_data[\"metadatas\"].append(metadata)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Desktop/blessing_ai/mkd/ds_oil_and_gas/services/claude_ai_service.py:3: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['urllib3.util (/opt/anaconda3/envs/gs/lib/python3.10/site-packages/urllib3/util/__init__.py)', 'urllib3.util.ssl_ (/opt/anaconda3/envs/gs/lib/python3.10/site-packages/urllib3/util/ssl_.py)']. \n", "  monkey.patch_all()\n"]}], "source": ["from services.synonym_expansion import SynonymGenerator\n", "gen = SynonymGenerator()\n", "word = \"The list of major equipments required for the project.\"\n", "syns = gen.generate_synonym_sync(word)\n", "print(syns)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/anaconda3/envs/gs/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "/Users/<USER>/Desktop/blessing_ai/mkd/ds_oil_and_gas/extensions.py:7: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['urllib3.util.ssl_ (/opt/anaconda3/envs/gs/lib/python3.10/site-packages/urllib3/util/ssl_.py)', 'urllib3.util (/opt/anaconda3/envs/gs/lib/python3.10/site-packages/urllib3/util/__init__.py)']. \n", "  monkey.patch_all()\n"]}], "source": ["from services.pinecone_vector_db import CanopyAI\n", "import asyncio\n", "\n", "#from services.fast_apis_service import FastAPIs\n", "from services.prompt_loader import PromptLoader\n", "from services.fast_apis_service import FastAPIs\n", "import sys\n", "\n", "class SynonymGenerator:\n", "    def __init__(self):\n", "        # Initialize the ClaudeService client\n", "    \n", "        self.fast_apis_client = FastAPIs()\n", "        self.prompt_loader = PromptLoader()\n", "\n", "    async def generate_synonym(self, word, temperature=0.001):\n", "\n", "        prompt = self.prompt_loader.get_prompt('generate_synonym', {\"word\": word})\n", "        messages = [\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\":  prompt\n", "            }\n", "        ]\n", "        return self.fast_apis_client.generate_completion(messages)\n", "    \n", "    def generate_synonym_sync(self, word, temperature=0.001):\n", "\n", "        prompt = self.prompt_loader.get_prompt('generate_synonym', {\"word\": word})\n", "        messages = [\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\":  prompt\n", "            }\n", "        ]\n", "        return self.fast_apis_client.generate_completion(messages)\n", "\n", "        \n", "    def expand_query_with_keywords(self, word, temperature=0.001):\n", "\n", "        prompt = self.prompt_loader.get_prompt('generate_synonym', {\"word\": word})\n", "        messages = [\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\":  prompt\n", "            }\n", "        ]\n", "        return self.fast_apis_client.generate_completion(messages)\n", "        \n", "\n", "\n", "# if __name__ == '__main__':\n", "#     synonym_service = SynonymGenerator()\n", "#     print(asyncio.run(synonym_service.generate_synonym('Name: Evacuation Plan \\n Description: How would an injured person be evacuated to the nearest hospital?')))\n", "if __name__ == \"__main__\":\n", "    import sys\n", "    \n", "    # Test configuration\n", "    test_directory = \"data\"\n", "    test_project_id = \"a255c08a-9a41-4d75-ad00-380421937c85\"\n", "    # Sample test files - adjust these paths to match your test files\n", "    query = \"What are the major equipents required for this project please\"\n", "    syngen = SynonymGenerator()\n", "    sysnonymes = syngen.generate_synonym_sync(query)\n", "    print(sysnonymes)\n", "    vd = CanopyAI()\n", "    data = vd.get_data(test_project_id, [\"What are the major equipents required for this project please\"],13)\n", "\n", "\n", "    print(data)\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Pinecone api key: pcsk_5yQHyr_76ZpD5KpquSadLLzMVMhXTSmurWLtaAxMacKKyB6kfaVyRUvQhodo2NR7tMCeyn\n", "\u001b[94mCreating embeddings for queries...['What is the scope of work for the project?']\u001b[0m\n", "\u001b[94mQueries: ['What is the scope of work for the project?']\u001b[0m\n", "texts:  ['What is the scope of work for the project?']\n", "cohere embeddings usage:  api_version=ApiMetaApiVersion(version='2', is_deprecated=None, is_experimental=None) billed_units=ApiMetaBilledUnits(images=None, input_tokens=10.0, output_tokens=None, search_units=None, classifications=None, image_tokens=0) tokens=None warnings=None\n", "\u001b[94mQuery vectors: [[-0.010070801, -0.07910156, 0.03125, 0.020019531, -0.029296875, 0.04272461, -0.03125, 0.02355957, -0.03515625, -0.017822266, 0.015380859, -0.001953125, -0.0046081543, 0.029541016, -0.02319336, -0.02722168, -0.015991211, -0.00015449524, 0.013122559, -0.036132812, -0.0051879883, 0.047851562, -0.05102539, -0.010253906, -0.056396484, 0.041259766, 0.044189453, -0.00970459, -0.060546875, -0.002822876, -0.0023651123, 0.03491211, 0.018432617, 0.020263672, 0.030029297, 0.020019531, -0.013122559, -0.0009994507, -0.028198242, 0.011779785, 0.004272461, -0.06298828, 0.0015869141, -0.07910156, -0.06640625, 0.05102539, 0.00579834, 0.0234375, -0.012512207, -0.015014648, 0.056152344, 0.021850586, -0.0070495605, 0.014465332, 0.053710938, -0.00970459, 0.0025939941, 0.064453125, 0.0022735596, 0.042236328, -0.030029297, -0.030639648, 0.00092315674, -0.026611328, -0.011779785, -0.01373291, 0.037353516, 0.0015335083, -0.0012817383, 0.027954102, 0.012207031, 0.019165039, -0.032958984, 0.035888672, -0.03881836, 0.0054016113, -0.013427734, -0.009460449, 0.024536133, -0.056396484, -0.025878906, -0.0051574707, 0.013549805, 0.031982422, -0.024536133, -0.032714844, -0.040039062, -0.03466797, -0.018432617, -0.030639648, 0.02758789, 0.068359375, -3.1232834e-05, -0.04736328, 0.057861328, 0.0030059814, 0.032470703, 0.064941406, 0.07470703, -0.010009766, 0.049560547, -0.03857422, 0.014282227, 0.012512207, -0.027954102, -0.0039978027, -0.0014953613, 0.021606445, 0.0054016113, 0.020019531, 0.0234375, -0.024414062, -0.024291992, 0.002822876, 0.021606445, 0.042236328, 0.020019531, -0.053222656, -0.011962891, -0.02722168, -0.04272461, 0.01977539, -0.015380859, 0.033935547, 0.0138549805, 0.0035858154, -0.004699707, -0.021362305, 0.013305664, 0.03125, 0.0095825195, -0.004180908, -0.010131836, 0.01977539, 0.017456055, -0.07128906, 0.011169434, 0.087402344, 0.006591797, -0.043701172, 0.000333786, 0.026245117, 0.03491211, 0.036865234, 0.034179688, -0.0017929077, 0.028564453, 0.008728027, 0.025878906, -0.017089844, 0.02709961, -0.011291504, 0.037841797, 0.028564453, -0.06933594, -0.002822876, -0.036865234, -0.030029297, -0.0064697266, 0.013305664, 0.0017852783, -0.01184082, -0.029541016, 0.018920898, 0.08544922, -0.014282227, -0.032714844, -0.0032196045, -0.004333496, 0.014343262, -0.083984375, -0.038330078, -0.00060653687, -0.0048217773, -0.022705078, -0.00982666, -0.06933594, -0.048583984, -0.016723633, -0.091796875, 0.04345703, 0.013793945, -0.024414062, 0.017333984, -0.022949219, -0.011962891, -0.028564453, -0.00029182434, 0.028564453, 0.028198242, -0.04321289, -0.014465332, 0.007873535, 0.051757812, -0.03466797, 0.025878906, -0.025268555, 0.008056641, -0.03955078, 0.030761719, 0.018432617, 0.016235352, 0.0013809204, -0.041015625, -0.028198242, -0.01361084, 0.019042969, -0.11035156, -0.0042419434, 0.020019531, -0.012756348, 0.012939453, 0.008361816, -0.006378174, -0.036132812, -0.07763672, 0.021606445, 0.005432129, -0.032470703, -0.007385254, 0.012268066, 0.049072266, -0.014099121, -0.020996094, -0.022949219, -0.06298828, -0.019165039, -0.025024414, -0.01977539, -0.010375977, -0.024536133, -0.0041503906, 0.029541016, 0.00680542, 0.024536133, 0.047851562, 0.010314941, -0.04345703, 0.029541016, 0.038085938, -0.026245117, -0.0052490234, -0.010314941, -0.056152344, -0.049072266, -0.017089844, -0.05517578, -0.0008163452, -0.0071411133, -0.0046081543, 0.013977051, 0.01928711, 0.02355957, 0.012634277, 0.0073242188, 0.008056641, 0.029418945, 0.0025787354, 0.0063171387, 0.05883789, 0.022338867, -0.034179688, -0.08154297, 0.017822266, 0.04272461, -0.006439209, 0.052734375, 0.04638672, 0.013427734, -0.022949219, -0.0034484863, 0.040039062, 0.002670288, -0.044189453, 0.049804688, 0.03881836, 0.024291992, -0.0019989014, 0.0068969727, -0.0025634766, -0.0625, -0.045166016, 0.012512207, 0.060058594, 0.043701172, -0.0011444092, 0.07324219, -0.08203125, -0.0064697266, 0.0008049011, 0.0064697266, 0.0016174316, -0.033935547, -0.018432617, -0.024291992, 0.0069885254, -0.021606445, -0.012756348, -0.03564453, -0.061523438, 0.04272461, -0.053466797, 0.020996094, 0.020507812, -0.011047363, -0.002670288, -0.041992188, -0.08544922, 0.0074768066, -0.005859375, 0.022949219, -0.015991211, 0.005065918, -0.042236328, 0.036376953, -0.032714844, -0.026123047, 0.015319824, 0.020629883, 0.0044555664, 0.009460449, 0.017333984, -0.0055236816, -0.0027313232, 0.04272461, 0.0625, -0.0154418945, -0.007507324, -0.017578125, -0.020996094, -0.017089844, -0.011108398, -0.025878906, -0.03149414, 0.017089844, -0.013366699, 0.013793945, 0.007171631, 0.028930664, 0.0041503906, 0.05493164, -0.041503906, 0.014953613, 0.033691406, -0.010253906, 0.031982422, 0.019165039, -0.026611328, -0.021606445, -0.015014648, -0.00043296814, 0.006378174, 0.015136719, 0.012634277, 0.034179688, -0.032714844, 0.048095703, -0.022583008, 0.003112793, -0.010925293, -0.0057678223, 0.0011062622, 0.017578125, 0.015319824, -0.0057373047, -0.013793945, 0.0062561035, 0.011291504, 0.0154418945, -0.012878418, 0.012451172, 0.036376953, -0.00011444092, 0.0045776367, -0.005279541, -0.0008087158, 0.01184082, -0.041992188, 0.025268555, 0.049072266, -0.03125, 0.075683594, -0.013793945, -0.0022125244, 0.018920898, 0.050048828, 0.02709961, -0.005004883, 0.011108398, 0.006134033, -0.014465332, 0.045166016, -0.021362305, 0.007873535, -0.030761719, 0.012756348, -0.0067749023, 0.009521484, 0.029296875, -0.005126953, 0.024780273, -0.03540039, 0.01361084, -0.0038604736, 0.016845703, 0.021118164, 0.047851562, -0.0029907227, -0.023071289, -0.036865234, 0.009094238, 0.01373291, 0.014953613, -0.051757812, 0.017089844, -0.03125, 0.0061950684, 0.009033203, -0.032958984, -0.00063323975, -0.04638672, -0.025268555, 0.008911133, -0.028930664, 0.013122559, -0.024902344, 0.030639648, -0.008422852, -0.013305664, 0.033935547, 0.006134033, -0.04736328, -0.020751953, 0.040771484, 0.031982422, 0.015625, -0.04638672, -0.049804688, -0.05517578, 0.024902344, -0.017089844, -0.017456055, 0.044189453, 0.017456055, -0.03540039, -0.044433594, 0.0037384033, 0.041503906, 0.031982422, -0.07324219, -0.013305664, 0.06982422, 0.0047302246, -0.008850098, -0.026733398, -0.03930664, 0.02709961, 0.037353516, 0.03125, -0.018432617, -0.031982422, -0.03466797, -0.03881836, -0.025512695, -0.020629883, 0.012939453, 0.031982422, 0.057128906, 0.003829956, -0.008544922, 0.029418945, 0.01361084, -0.03857422, -0.017456055, 0.018676758, -0.03125, -0.018432617, 0.0073547363, -0.013549805, 0.013122559, 0.15429688, -0.046142578, 0.011779785, -0.01574707, 0.01928711, -0.025756836, 0.009094238, -0.008483887, -0.0015182495, -0.012756348, -0.075683594, -0.008544922, -0.0057373047, 0.005279541, 0.021850586, -0.0041503906, 0.013122559, 0.0046081543, 0.00062561035, 0.03466797, 0.012268066, -0.013122559, 0.021850586, 0.083984375, -0.0061035156, -0.00076675415, -0.00038909912, 0.015319824, -0.0008392334, 0.005859375, 0.0074768066, -0.008544922, 0.006378174, 0.026611328, 0.012939453, -0.0034484863, 0.025268555, -0.016723633, -0.01977539, 0.01184082, 0.03881836, 0.029296875, -0.020751953, 0.0070495605, 0.009094238, 0.04345703, -0.05493164, 0.08300781, 0.047851562, -0.015991211, -0.003326416, -0.051757812, -0.041503906, -0.080078125, -0.011779785, 0.08105469, 0.022216797, 0.057861328, 0.02758789, 0.017333984, -0.015136719, 0.02319336, -0.027954102, -0.0057373047, 0.13671875, 0.014709473, 0.032958984, -0.018432617, 0.001411438, -0.021362305, -0.028198242, 0.016723633, -0.032958984, 0.03540039, 0.01159668, 0.06542969, -0.0061950684, 0.005340576, -0.047851562, 0.09423828, -0.04345703, -0.021118164, -0.0045776367, -0.014953613, 0.029541016, -0.033447266, -0.018676758, -0.0041503906, 0.005126953, -0.03515625, -0.002670288, 0.049072266, 0.004180908, -0.024291992, -0.00015830994, -0.0045776367, 0.009216309, 0.07763672, 0.022338867, 0.014770508, 0.012756348, -0.012145996, -0.041992188, -0.021728516, -0.02319336, -0.025268555, -0.05908203, 0.033935547, 0.030639648, 0.00680542, -0.010498047, 0.01171875, -0.060546875, -5.507469e-05, 0.006713867, 0.067871094, 0.041992188, -0.014465332, 0.00045204163, -0.0049438477, -0.03564453, 0.024902344, -0.02722168, 0.013366699, 0.057373047, 0.03466797, 0.016113281, -0.055419922, 0.000831604, 0.041015625, 0.017089844, -0.024414062, 0.025390625, -0.015991211, 0.04272461, 0.052734375, 0.04345703, 0.009033203, -0.00038337708, -0.0057373047, -0.007171631, -0.018310547, 0.033203125, -0.026855469, -0.005340576, -0.005004883, -0.002670288, 0.008728027, -0.021118164, 0.00077819824, -0.008544922, -0.008544922, -0.0037384033, -0.006072998, 0.009887695, 0.087402344, 0.0047912598, 0.0026245117, 0.007507324, -0.017333984, 0.048828125, -0.020751953, -0.049072266, -0.025512695, 0.0019989014, -0.030883789, 0.0126953125, -0.041503906, 0.018676758, -0.034179688, 0.059814453, -0.024536133, 0.02709961, -0.057373047, -0.021850586, 0.027954102, 0.015319824, 0.0069885254, 0.017333984, 0.016357422, -0.0625, -0.008911133, -0.014099121, -0.02355957, 0.03540039, 0.001701355, 0.040283203, -0.018188477, 0.016967773, -0.017822266, -0.032470703, -0.005432129, 0.008178711, 0.0018386841, -0.028930664, 0.026367188, 0.0095825195, -0.028686523, -0.016723633, -0.0061950684, -0.041015625, 0.02722168, 0.028076172, -0.030883789, 0.008850098, -0.018188477, 0.03149414, 0.020507812, -0.03564453, -0.060058594, 0.0007095337, -0.030883789, -0.05102539, -0.0029296875, 0.015991211, -0.007171631, -0.009643555, 0.08203125, 0.014282227, -0.016845703, 0.0035247803, -0.015380859, 0.01940918, -0.013549805, -0.021728516, -0.0038452148, -0.012268066, -0.023925781, 0.01574707, 0.053466797, 0.042236328, -0.005645752, -0.040039062, 0.019042969, -0.03955078, -0.03930664, -0.0036621094, 0.02355957, 0.0032806396, -0.030883789, 0.007659912, 0.023925781, 0.020019531, 0.026855469, -0.016967773, -0.0020141602, 0.021362305, -0.03466797, 0.02368164, -0.017089844, 0.004272461, -0.03491211, 0.002029419, 0.018432617, -0.0079956055, -0.0079956055, -0.030029297, -0.019165039, -0.03466797, 0.01940918, 0.007232666, -0.049560547, -0.04321289, -0.012390137, -0.053222656, -0.0071411133, -0.015991211, 0.02319336, -0.053466797, 0.03955078, -0.03540039, 0.008666992, 0.03149414, -0.012634277, 0.040527344, 0.03955078, 0.03466797, -0.045410156, -0.040527344, -0.032470703, 0.013427734, 0.042236328, -0.009521484, -0.008300781, -0.0046081543, -0.0027923584, 0.010681152, -0.006072998, -0.0146484375, 0.05493164, 0.0014419556, -0.03515625, -0.018798828, -0.017333984, 0.01928711, -0.015380859, -0.032470703, 0.016723633, -0.1171875, -0.03466797, 0.022216797, 0.0077209473, 0.024414062, -0.026367188, -0.020385742, 0.015380859, -0.03955078, -0.036865234, -0.0064697266, 0.014709473, 0.018676758, 0.014770508, 0.036865234, 0.04272461, 0.029418945, 0.026855469, -0.06933594, 0.05883789, 0.000541687, -0.010803223, 0.013122559, -0.027954102, 0.004272461, 0.004119873, -0.0073547363, -0.020385742, 0.02746582, -0.025268555, 0.033935547, -0.05102539, 0.010498047, -0.005340576, 0.001876831, -0.02746582, 0.08544922, 0.0007171631, -0.017089844, 0.014282227, -0.01171875, -0.041503906, 0.0057373047, 0.0065307617, 0.024291992, 0.030273438, 0.016479492, 0.003189087, 0.00029182434, -0.008300781, -0.030029297, -0.0039367676, -0.009094238, -0.006713867, 0.025878906, -0.03881836, -0.03930664, 0.0071105957, 0.046875, 0.051757812, -0.016357422, -0.018676758, 0.005004883, -0.013122559, -0.036376953, 0.006866455, -0.0011825562, -0.018432617, -0.008972168, 0.024291992, 0.033935547, -0.02722168, 0.038085938, 0.0020141602, 0.008972168, 0.03515625, 0.004547119, -0.06298828, -0.0234375, -0.014709473, -0.0039367676, -0.012878418, 0.044433594, -0.032958984, -0.0005264282, -0.013427734, -0.0052490234, -0.021362305, 0.05053711, -0.03149414, 0.025268555, -0.015625, -0.03491211, -0.026855469, 0.07373047, 0.022949219, -0.006225586, 0.015014648, 0.033935547, 0.02758789, -0.032226562, -0.0126953125, 0.0115356445, -0.040039062, 0.002090454, 0.010009766, 0.010803223, 0.009216309, 0.047851562, -0.0028686523, -0.029541016, -0.021118164, 0.0035858154, 0.06689453, -0.022216797, 0.018066406, 0.012390137, 0.047851562, -0.049072266, -0.036865234, -0.040039062, 0.0046691895, -0.017700195, -0.024536133, 0.05859375, 0.044189453, -0.028686523, -0.056396484, 0.009338379, -0.026855469, 0.048828125, 0.018432617, 0.005859375, 0.063964844, -0.009887695, -0.00091552734, -0.000415802, 0.035888672, 0.010925293, -0.0015640259, -0.0016098022, -0.061523438, -0.027709961, 0.030883789, -0.0010528564, 0.018432617, -0.01940918, -0.018920898, 0.045410156, -0.013793945, 0.006866455, -0.03466797, 0.0024261475, -0.017944336, 0.018676758, 0.063964844, 0.068359375, 0.049560547, -0.016235352, 0.020263672, -0.00970459, -0.028564453, -0.0014343262, -0.052246094, 0.032958984, -0.012023926, -0.0022583008, -0.03955078, -0.038330078, 0.00047302246, 0.0078125, 0.044433594, -0.012023926, 0.025268555, -0.06298828, 0.036132812, -0.032714844, 0.022583008, 0.042236328, 0.001083374, 0.020019531, 0.016235352, 0.0025787354, -0.00048828125, 0.017944336, 0.00019550323, -0.009033203, -0.036865234, 0.011474609, -0.009338379, -0.01574707, 0.005645752, 0.024414062, 0.028686523, -0.0115356445, -0.03955078, -0.015319824, 0.016723633, 0.01965332, -0.014099121, -0.015991211, -0.03930664, 0.01965332, -0.026367188, 0.02722168, -0.028198242, 0.040283203, 0.018310547, 0.05908203, 0.011108398, -0.0115356445, 0.053710938, 0.01940918, -0.00035858154, 0.040039062, 0.029418945, -0.045166016, -0.009155273, 0.01928711, -0.051513672, 0.0073547363, 0.021850586, -0.024536133, 0.026733398, 0.017333984, 0.00982666, -0.012634277, 0.011108398, -0.020507812, -0.012023926, -0.00060653687, 0.032958984, 0.03515625, -0.020141602, 0.044433594, 0.0050354004, -0.060546875, 0.0025024414, -0.020751953, 0.044189453, -0.045410156, -0.011474609, 0.0051574707, 0.015319824, 0.020996094, 0.012512207]]\u001b[0m\n", "{'ids': [['364f0481-44c3-4750-a50c-3869eec37705', '380c3b9d-72aa-459e-bfd7-cb40ef3b3f54', '252b0fb7-de69-4d42-8474-80af1f7c48c7', '0136ad55-855e-47fe-8a6c-c706f6288d02', 'e45d7b9f-7b68-4628-82e3-d07299436c22', '7052362b-0d7a-4361-b986-60fd01a713e2', '72956390-48fb-4b83-acda-baf63df1fd89', 'dc49c244-d28f-47c3-9be3-1fe1e1e927b7', '4f58185d-349f-40e8-8039-d9c5bbc49407', 'e16ebc8e-3647-4f56-9137-fad053f2d237', 'e6376a51-9337-4078-a7b0-56083a052fb2', 'c17c5b09-c1db-49a5-b1bf-5e81b6a68937', 'ab4c1505-9f81-4b21-b58d-965fa0076efc']], 'distances': [[0.390180469, 0.382781029, 0.353463382, 0.333125979, 0.326673687, 0.316921413, 0.316249102, 0.312897563, 0.312427878, 0.308786601, 0.306880414, 0.304773718, 0.302703559]], 'metadatas': [[{'documents': '.................................................................................. Features of Equipment ............................................................................................................................... Project Execution ....................................................................................................................................... 5 DESIGN BASIS ............................................................................................................. 6 TENDER DOCUMENTS................................................................................................ 7 SCOPE OF WORK........................................................................................................ Equipment Descriptions and Specifications ................................................................................................ Exclusions', 'section_id': '0e432f8c-fbfd-42df-9007-d41f732246ae', 'title': 'PROJECT SUMMARY'}, {'documents': 'order to effectively manage a project from planning and manufacturing to delivery and assembly. This quality plan is prepared in accordance to ISO 10005:2018. 1.2. Scope of quality plan This document aims to establish a quality plan for Metso Outotec Apron Feeder Product Line which includes research and development, design and engineering, manufacturing of equipment and parts and services including, but not limited to, installation, commissioning, field supervision, and performance services for minerals processing. The Metso Outotec Quality Plan for Apron Feeder Product Line encompasses all activities associated with meeting customer, statutory, regulatory and design requirements beginning with an initial inquiry and includes order entry, design review, material procurement, scheduling, manufacturing, inspections, testing, packaging, shipping, installation supervision and field services. This Quality Plan was developed and will be implemented according to the guidelines, policy,', 'section_id': 'a0b023d5-0105-4ad6-996b-f65438900eb1', 'title': '1.2. Scope of quality plan'}, {'documents': '............................................................. 4 1.1. Objective .............................................................................................................................................. 4 1.2. Scope of quality plan ............................................................................................................................ 4 1.3. Quality Plan inputs ............................................................................................................................... 4 2. RESOURCING AND COMMUNICATION .............................................................................................. 5 2.1. Project processes ................................................................................................................................ 5 2.2. Communication .................................................................................................................................... 8 2.3. Provision of resources', 'section_id': '7de93cb4-0ed4-4a60-9d05-71108df25a24', 'title': '1. INTRODUCTION'}, {'documents': '| | | <br/>Others: | | | | | | | | <br/> | 1 | Lot | O&M | Installation, Operation and Maintenance manuals, in English. Maintenance manual must include schedule of maintenance and list of all necessary materials and spares necessary for maintenance. Besides electronic or digital O&M, five (5) printed copies of each are required. | | | w item 0001 above | <br/> | 1 | Lot | Packaging | Export, seaworthy packing (if needed). Specify separate domestic / export packaging allowance, including preservation requirements (if applicable). Bidder must consider package pre-assembly to maximum level possible. | | | included only per attached T124 Export Shipping Standards | <br/> | | | Delivery Schedule | How many weeks, total, after receipt of a Purchase Order, would item(s) be ready for delivery? _________ weeks Provide preliminary project schedule including the following key activities and milestones: • Forecast time to issue initial review drawings, after receipt of order (_____weeks ARO) •', 'section_id': '53fa1469-5611-462e-ab5e-27e3f8b8450d', 'title': 'Others'}, {'documents': 'g large, lumpy, abrasive and heavy materials under wet, sticky or frozen conditions. Project Execution Project Management and Quality Program – Metso Outotec will appoint a project manager for the coordination of technical, contractual, and financial matters relating to the project. The appointed project manager will be M:O’s main point of contact for all project communications. Metso Outotec will execute the project in accordance with its internal quality assurance program. Technical Documentation – Metso Outotec will provide the technical documentation (i.e. drawings, specifications, manuals, etc.) required for installation, operation, and maintenance of the equipment. Metso Outotec will not provide detailed manufacturing drawings, calculations, or other Intellectual Property that reveals the details of Metso Outotec proprietary technologies. All technical documentation supplied by Metso Outotec will be in the English language and is in accordance with Metso Outotec standards.', 'section_id': 'e4995782-57f0-4598-b13f-728bd12ffd0a', 'title': 'Project Execution'}, {'documents': 'below) vary the scope of work by written agreement recording required amendments to the Goods, the Contract Price and Delivery times for the Goods as well as any other relevant matters. Metso Outotec shall not be required to implement any variations without such written agreement. 5. Price. The price for the Goods (“Contract Price”) shall be as specified in the Contract or, if not specified, in Metso Outotec’s quotation for the Goods exclusive of any value added, sales or similar tax. 6. Taxes. The Contract Price shall be paid free and clear of all deductions and withholdings for taxes, duties, levies or other charges imposed by federal, state, regional or other governmental authorities in the country of registration of Purchaser and the country of Purchaser’s site or under any applicable treaty for the avoidance of double taxation except as required by law. If any deduction or withholding is required by law, Purchaser shall on the due date for the payment pay Metso Outotec such', 'section_id': '1d58e5c1-efb5-4919-b63a-5eaa5abaac1f', 'title': '4. Variations'}, {'documents': 'nt in writing. These general conditions shall supersede any conflicting conditions of Purchaser, whether contained in any purchase order issued by Purchaser or elsewhere. All modifications and deviations to these general conditions shall be expressly agreed in writing by Purchaser and Metso Outotec as set out in clause 31 or in a separate written agreement. 3. Scope of Work. The Goods shall be as specified in Metso Outotec’s quotation as may be amended in the Contract. All equipment, materials, commodities and services not specifically mentioned therein, including, without limitation, installation and commissioning of the Goods, are expressly excluded. 4. Variations. Purchaser and Metso Outotec may at any time prior to Delivery (as defined below) vary the scope of work by written agreement recording required amendments to the Goods, the Contract Price and Delivery times for the Goods as well as any other relevant matters. Metso Outotec shall not be required to implement any variations', 'section_id': '1d58e5c1-efb5-4919-b63a-5eaa5abaac1f', 'title': '2. Contract Formation'}, {'documents': 'nagement System. All Metso Outotec employees have access to this SharePoint. 3.2. Project quality objectives All projects have the same key quality objectives: \\uf0b7 accept products and/or services by customers, demonstrating product, customer, statutory and regulatory requirements were met. \\uf0b7 comply with agreed delivery time, demonstrating contractual terms were met.', 'section_id': 'baf8b845-1bb2-4283-a560-0b769bc01bce', 'title': '3.2. Project quality objectives'}, {'documents': 'Quality Manual Restrict 7/23 Doc.ID: York-Quality Manual Created by: Jessica Heckrote Competence: York Approved by: Andrea Jacintho Version:1.0 January 2020 Note: Controlled copies in electronic media only Page 7 of 23 4.3 Determining the scope of the Quality Management System To implement defined objectives and policies relevant to our context, products, and interested parties, Metso Mining Equipment has established the scope of the management system. We have decided to use this manual accompanied by our Global Management System (sharepoint portal) to document the scope of our management system. This manual along with the sharepoint portal describes our management system, delineates authority, defines inter-relationships and responsibilities of process owners and personnel that operate within this system. These documents also illustrate the interactions between this management process and its interaction within our key processes along with external processes. Metso Mining Equipment', 'section_id': '568a035b-d30a-43fc-bd3e-0a36e4efd452', 'title': '4.3 Determining the scope of the Quality Management System'}, {'documents': 'etso Outotec keeps records of inputs which can include \\uf0b7 functional and performance requirements; \\uf0b7 applicable statutory and regulatory requirements. \\uf0b7 requirements specified by the customer, including the requirements for delivery and post- delivery activities; \\uf0b7 requirements not stated by the customer but necessary for specified or intended use, where known; \\uf0b7 information derived from previous similar designs (when applicable); and \\uf0b7 other requirements essential for design and development. 4.2. Scope changes All changes to the scope of the work should be requested to the Project Manager, who then evaluates the impact internally and informs the customer before proceeding with the change. After the agreement, the Project Manager communicates the change internally through the Change Order Notification Form and performs internal actions, if applicable. 4.3. Manage project deliverables During the project, execution reviews with the execution team and board of directors are performed to', 'section_id': 'c4fc8fe1-1ebe-4204-a991-4d46cf24ca74', 'title': '4.2.'}, {'documents': 'N | INCLUDED IN THIS PROJECT | METSO ISSUE FOR REVIEW | | CUSTOMER REVIEW PERIOD | METSO ISSUE FOR CONSTRUCTION | | FORMAT | COMMENTS / CLARIFICATIONS<br/> | | | Qty | Due | Due | Qty | Due | | <br/> | | | | | | | | | <br/> | PLANNING | | | | | | | | <br/> | Drawing List | yes | 1E | A+2W | | | | PDF | <br/> | Manufacturing Schedule | consult Metso | 1E | A+2W | | | | PDF | <br/> | Progress Report - Up-Date Schedule | consult Metso | | | | 1E | A+4W | PDF | every 4 weeks<br/> | | | | | | | | | <br/> | GENERAL DRAWINGS | | | | | | | | <br/> | General Arrangement Drawing including | yes | 1E | A+5W | R+1W | 1E | RG +2W | ACAD/PDF | <br/> | Mounting Hole Drawing | yes | | | | | | | Included on General Arrangement<br/> | Equipment Interface Details | yes | | | | | | | Included on General Arrangement<br/> | Loading (Static & Dynamic) | yes | | | | | | | Included on General Arrangement<br/> | Equipment Sub-Assemblies including: | yes | | | | 1E | RG + 2W | ACAD/PDF | Issued for information', 'section_id': '3a36dae7-278c-4f6e-aec5-14a37ec7c2ba', 'title': 'PLANNING'}, {'documents': '....................................................................... 11 4.1. Project inputs ..................................................................................................................................... 11 4.2. Scope changes .................................................................................................................................. 12 4.3. Manage project deliverables .............................................................................................................. 12 4.4. Project outputs ................................................................................................................................... 14 5. DOCUMENTATED INFORMATION MANAGEMENT .......................................................................... 14 6.0. QUALITY ASSURANCE .................................................................................................................... 15 6.1. Quality assurance role in the project execution', 'section_id': '7de93cb4-0ed4-4a60-9d05-71108df25a24', 'title': ''}, {'documents': 'wed by participating. resources to have the potential impacts considered and addressed prior to approval. These approved changes are communicated and recorded internally through the Change Order Notification Form which is initiated and updated by the Project Manager. Risks and opportunities specifically related to the project with actions addressed are led by Project Manager. Project Manager supports in managing the warranty period and provide assistance to the customer when required.In some cases Market Area will become invloved to assist where needed. Engineering: Engineering receives input during the kickoff meeting to prepare the specific documents for that project based on applicable requirements. Document type: Page QUALITY PLAN C.00000-QP-AF-001 00 5(19) ype position Property of Metso Outotec All rights reserved 5 years Only digital docue nents used directly from the Metso Outote Confidentia official document management system is valid and controlled Restricted', 'section_id': '645156c8-2e11-4501-8cbe-3a7513f1d23d', 'title': 'RISOURCING AND COMMUNICATION'}]], 'uris': None, 'data': None}\n"]}], "source": ["from services.pinecone_vector_db import CanopyAI\n", "\n", "if __name__ == \"__main__\":\n", "    import sys\n", "    \n", "    # Test configuration\n", "    test_directory = \"data\"\n", "    test_project_id = \"a255c08a-9a41-4d75-ad00-380421937c85\"\n", "    # Sample test files - adjust these paths to match your test files\n", "    vd = CanopyAI()\n", "    data = vd.get_data(test_project_id, [\"What is the scope of work for the project?\"],13)\n", "\n", "    \n", "    print(data)\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "V2Client.embed() got an unexpected keyword argument 'output_dimension'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[9], line 5\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mcohere\u001b[39;00m\n\u001b[1;32m      3\u001b[0m co \u001b[38;5;241m=\u001b[39m cohere\u001b[38;5;241m.\u001b[39mClientV2(api_key\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m9qYN32GkC4i7XzP54ynv5S00Ol8i1gO9Obgnjnp7\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m----> 5\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[43mco\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43membed\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m      6\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtexts\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m<PERSON><PERSON>\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mgoodbye\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m      7\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmodel\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43membed-v4.0\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m      8\u001b[0m \u001b[43m    \u001b[49m\u001b[43moutput_dimension\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m1024\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m      9\u001b[0m \u001b[43m    \u001b[49m\u001b[43minput_type\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mclassification\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m     10\u001b[0m \u001b[43m    \u001b[49m\u001b[43membedding_types\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mfloat\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     11\u001b[0m \u001b[43m)\u001b[49m\n\u001b[1;32m     12\u001b[0m \u001b[38;5;28mprint\u001b[39m(response)\n", "\u001b[0;31mTypeError\u001b[0m: V2Client.embed() got an unexpected keyword argument 'output_dimension'"]}], "source": ["import cohere\n", "\n", "co = cohere.ClientV2(api_key=\"9qYN32GkC4i7XzP54ynv5S00Ol8i1gO9Obgnjnp7\")\n", "\n", "response = co.embed(\n", "    texts=[\"hello\", \"goodbye\"],\n", "    model=\"embed-v4.0\",\n", "    input_type=\"classification\",\n", "    embedding_types=[\"float\"],\n", ")\n", "print(response)\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["from services.faiss_embedding import FaissEmbedding\n", "eb = FaissEmbedding()\n", "\n", "res = eb.search_sync(request_id=\"6aa67866-913e-42a8-97cc-b0b7d662241e\",query=[\"PriceCost of equipment and all necessary components, including spare parts (commissioning, 1 year of operation, capital).The bidder must inform the prices considered for the equipment and the spare parts.All line items must be fully priced. Spares pricing must be in accordance with the Project Requirements.\"],k=40)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["[[('ISSUE DATE: | | 2021-07-07 00:00:00 | | | BID DUE DATE: | | 2021-07-15 00:00:00<br/>RFQ No. : 210148- | | | | M001 AG Mill & Ball Mill | | | <br/>BASE CASE | | | | | | | <br/>ITEM | QTY. | UNIT | TAG No. | DESCRIPTION | Comments | UNIT PRICE | EXT. PRICE<br/>1 | 1 | unit | 200-ML-2000 | Primary (AG) Mill | | 7457260 | 7457260<br/>2 | 1 | lot | | Primary Mill Liners | | 1386030 | 1386030<br/>3 | 1 | unit | 200-ZA-2003 | Primary Mill Discharge Chute | | Excluded | Excluded<br/>4 | 1 | unit | 200-FD-2002 | Primary Mill Retractable Feed Chute | | Included | Included<br/>5 | 1 | unit | 200-XL-2092 | Primary Mill Liner Handler | | 1017400 | 1017400<br/>6 | 1 | unit | 200-ZM-2010 | Primary Mill Lube System | | Included | Included<br/>7 | 1 | unit | 200-ZM-2015 | Primary Mill Gear Spray System | | Included | Included<br/>8 | 2 | unit | | Primary Mill Motors (SCIM) | | 187750 | 375500<br/>9 | 2 | unit | | Primary Mill VFD (for SCIM) | | 674210 | 1348420<br/>10 | 1 | lot | | Primary Mill Commissioning Spares | | 6390 | 6390<br/>11 | 1 | lot | | Primary Mill One (1) Year Spares | | 7720 | 7720<br/>12 | 1 | lot | | Primary Mill Capital Spares | | 295240 | 295240<br/>13 | 1 | lot | | Primary Mill First Fills | | Excluded | Excluded<br/>14 | 1 | lot | | Primary Mill Special Tools | | 36750 | 36750<br/>15 | 1 | unit | 500-ML-5000 | Ball Mill - Single Pinion | | 5667125 | 5667125<br/>16 | 1 | lot | | Ball Mill Liners | | 226800 | 226800<br/>17 | 1 | unit | 500-ZA-5002 | Ball Mill Retractable Feed Chute | | Included | Included<br/>18 | 1 | unit | 500-ZA-5003 | Ball Mill Discharge Chute | | Excluded | Excluded<br/>19 | 1 | unit | 500-ZA-5052 | Ball Mill Grinding Media Chute | | Excluded | Excluded<br/>20 | 1 | unit | 500-SR-5020 | Ball Mill Trommel Screen | | Included | Included<br/>21 | 1 | unit | 500-ZA-5008 | Ball Mill Reject Chute | | Excluded | Excluded<br/>22 | 1 | unit | 500-BN-5009 | Ball Mill Trash Bin | | Excluded | Excluded<br/>23 | 1 | unit | 200-XL-5096 | Ball Mill Liner Handler | | Included | Included<br/>24 | 1 | unit | 500-ZM-5005 | Ball Mill Lube System | | Included | Included<br/>25 | 1 | unit | 500-ZM-5010 | Ball Mill Gear Spray System | | Included | Included<br/>26 | 1 | unit | | Ball Mill Motor (WRIM w/ LRS) | | 562740 | 562740<br/>27 | 1 | lot | | Ball Mill Commissioning Spares | | 6390 | 6390<br/>28 | 1 | lot | | Ball Mill One (1) Year Spares | | 7720 | 7720<br/>29 | 1 | lot | | Ball Mill Capital Spares | | 380240 | 380240<br/>30 | 1 | lot | | Ball Mill First Fills | | Excluded | Excluded<br/>31 | 1 | lot | | Ball Mill Special Tools | | 36750 | 36750<br/>32 | 5 | unit | | Installation, Operation and Maintenance Manuals | Hard copies | Included | Included<br/>33 | 1 | lot | | Freight | Specify Incoterms 2020 | TBA | TBA<br/>34 | 1 | lot | | Customs, Duties, Taxes | | Excluded | Excluded<br/>35 | | | | | | | 0<br/>36 | | | | | | | 0<br/>Subtotal, Base Bid Only: | | | | | | | 18818475<br/>USD (or indicate Currency): | | | | | | |',\n", "   0.7358315,\n", "   'C.1.1 210148-M001 Ball and AG Mill_P3803_Pricing Template Rev2 - SP Ball Mill.xlsx',\n", "   1,\n", "   'RFQ Line Items_row_1'),\n", "  ('le of maintenance and list of all necessary materials and spares necessary for maintenance. Besides electronic or digital O&M, five (5) printed copies of each are required. | | Included | Included<br/> | 1 | Lot | Packaging | Export, seaworthy packing (if needed). Specify separate domestic / export packaging allowance, including preservation requirements (if applicable). Bidder must consider package pre-assembly to maximum level possible. | | Included | Included<br/> | | | Delivery Schedule | How many weeks, total, after receipt of a Purchase Order, would item(s) be ready for delivery? __55-60__ weeks Provide preliminary project schedule including the following key activities and milestones: • Forecast time to issue initial review drawings, after receipt of order (__4__weeks ARO) • Forecast production/manufacturing duration from return of approval drawings to equipment delivery FCA Point of Manufacture/Shipping Point (__2__ weeks ARAD) Attach a Gantt schedule with your bid, if multiple milestones exist for the production of the equipment / materials / services you offer. | | | <br/> | | | Warranty | Confirm compliance with project minimum warranty requirement which is twelve (12) months after operational start-up or twentyfour (24) months after shipment, whichever occurs first | | | <br/> | | | | | | Subtotal: | 18818475<br/> | | | | TOTAL BY BIDDER: | | |',\n", "   0.30933657,\n", "   'C.1.1 210148-M001 Ball and AG Mill_P3803_Pricing Template Rev2 - SP Ball Mill.xlsx',\n", "   1,\n", "   'RFQ Line Items_row_1'),\n", "  ('| | | | UTILITIES (water, air, oil) | | | | | | | | | | | | | | | | | | | | | <br/> | AUXILIARIES EQUIPMENT | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | <br/> | | | | | | | | | | | | | | | | | | | | | | | | | | Water: | | | | | Operating pressure: | | | | | 1030 kPag max | | | | Flow | | | | | l/min | <br/> | | Inching Drive system | | | | | | | | | X | | Included | | | | | | Not included | | | | | | | Instrument air: | | | | | Operating pressure: | | | | | 690 kPag max | | | | Flow | | | | | m3/h | <br/> | | Jacking system | | | | | | | | | X | | Included | | | | | | Not included | | | | | | | Plant air: | | | | | Operating pressure: | | | | | 690 kPag max | | | | Flow | | | | | m3/h | <br/> | | Retractable Feed Chute | | | | | | | | | X | | Included | | | | | | Not included | | | | | | | Oil pressure for Jacking system: | | | | | | | | | | | | | | kPa | | | | | | <br/> | | Discharge Chute | | | | | | | | | X | | Included | | | | | | Not included | | | | | | | | | | | | | | | | | | | | | | | | | | | <br/> | | | | | | | | | | | | | | | | | | | | | | | | | LINERS and LIFTERS | | | | | | | | | | | | | | | | | | | | | <br/> | MATERIAL OF FABRICATION | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | <br/> | | | | | | | | | | | | | | | | | | | | | | | | | | Liner Type/Thickness : | | | | | | High-Low - 150 mm average | | | | | | | | | | | | | | <br/> | | Shell: | | | ASTM A36, Q235 or Metso:Outotec approved comparable material | | | | | | | | | | | | | | | | | | | | | Lifter Type/Height : | | | | | | High-Low - 150 mm average | | | | | | | | | | | | | | <br/> | | Flanges: | | | Lamellar tear resistant steel (Z25) per Metso:Outotec specifications | | | | | | | | | | | | | | | | | | | | | Grate Type/Opening: | | | | | | Slots/ | | | | | | | | | | | | | | <br/> | | Heads: | | | Cast SG iron or cast steel | | | | | | | | | | | | | | | | | | | | | Material/Hardness: | | | | | | Chrome-moly steel/ 350 BHN average | | | | | | | | | | | | | | <br/> | | Chutes: | | | Mild steel fabrication with abriasion resistant liners | | | | | | | | | | | | | | | | | | | | | Number of rows: | | | | | | 60 (preliminary) | | | | | | | | | | | | | | <br/> | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | <br/> | INSPECTION & TESTING REQUIREMENT | | | | | | | | | | | | | | | | | | | | | | | | Liner location: | | | | | Head End | | | | Shell | | | Discharge | | | Grates | | | Pulp Lifters | | | <br/> | TEST DATA | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | X | all wetted parts: | | | | | | | | | | | | <br/> | | | | | | | | Witness | | | | Nonwitness | | | | Observed | | | | | | | | | | | | | | | | | | specific location: | | | | | | | | | | | | <br/> | | Performance | | | | | | | X | | | | X | | | | X | | | | | | | | | | | | | | | | | | | | | | | | | | | | | <br/> | | Please see typical Metso:Outotec Inspection and Test lpan with',\n", "   0.29645053,\n", "   'C.1.2 P3803-200-PROC-DS-001 RevQ Metso_Outotec.xlsx',\n", "   2,\n", "   'Questionaire_row_2'),\n", "  ('ual Description of equipment Safety Transportation and storage Installation Commissioning and start-up Operation Maintenance Decommissioning and disposal Services and spare parts Technical appendices Electrical drawing package Hydraulic schematic and parts list Assembly drawings MSDS Nameplate details Lubrication schedule CE/EAC/CSA/AS certification as required 1.3 Electrical Drawing Package The electrical drawing package contains the following items.. Single line diagram Circuit diagrams Cable lists Parts lists Arrangement drawings P&I diagram CANBus network drawing Instrument list',\n", "   0.13386749,\n", "   'C.3.2 Drawing and Data Schedule (VDDR) (Typical) -Mill reline machine.PDF',\n", "   4,\n", "   ''),\n", "  ('rovided in the spare parts list.. Commissioning and start-up (S1) Two year (S2) Strategic (S3) 2 SCHEDULE NOTE: Manufacturing commencement is not dependent on customer approval of any items in the drawing and data schedule.. Drawings and data will be submitted to the EXTENT (Number of sets and type) and within the TIME periods (Weeks after Date of Award) as follows. and Data Schedule ItemDescription Extent Time (Weeks) Manufacturing Schedule 1E 4 General Arrangement Drawing Preliminary 1E 4 8 Certified 1E Foundation Load Drawing Preliminary 1E 4 8 Certified 1E Steering Drawing 1E 4 Equipment datasheet 1E 4',\n", "   0.1301748,\n", "   'C.3.2 Drawing and Data Schedule (VDDR) (Typical) -Mill reline machine.PDF',\n", "   5,\n", "   ''),\n", "  ('odate project specific requirements, the tables in this document allow optional standards for some processes. In instances where a process has multiple standards allocated, Outotec will select (at own discretion) the standard(s) for equipment design, manufacture and testing which are deemed most appropriate to accommodate the project specific requirements. See Figure 1 below for example. Figure 1: Example, Standard options',\n", "   0.060418423,\n", "   'Mill Product - Standard Specification DMS 001.pdf',\n", "   6,\n", "   ''),\n", "  ('| | | | Indicate Currency: | | | USD<br/>REFERENCES: All materials shall be furnished in accordance with the attached specifications, drawings, operating data sheets, and vendor data requirement sheet. | | | | | | |',\n", "   0.05439932,\n", "   'C.1.1 210148-M001 Ball and AG Mill_P3803_Pricing Template Rev2 - SP Ball Mill.xlsx',\n", "   2,\n", "   'RFQ Line Items_row_2'),\n", "  ('en conditions permit and upon payment of all amounts due, Seller shall make Products and repaired equipment available to Buyer for delivery.',\n", "   0.049589027,\n", "   'GE ref 1105582 - Metso - Las Truchas -Budget Proposal (005).docx',\n", "   16,\n", "   ''),\n", "  ('ibution (wiring and cable schedules), etc. \\uf0b7 Torque limiting coupling, brake disk, etc. \\uf0b7 Grounding system, soleplates or fixation material like anchor bolts, etc.',\n", "   0.047869004,\n", "   'Metso-OT Las Truchas Scope of Supply MN-8XMAVQA4_REV00.pdf',\n", "   4,\n", "   ''),\n", "  ('vers a Product that bears a different, superseding or new part or version number compared to the part or version number listed in the Contract. 15. Limitations of Liability 15.1 The total liability of Seller for all claims of any kind arising from or related to the formation, performance or breach of this Contract, or any Products or Services, shall not exceed the (i) Contract Price, or (ii) if Buyer places multiple order(s) under the Contract, the price of each particular order for all claims arising from or related to that order and ten thousand US dollars (US $10,000) for all claims not part of any particular order. 15.2 Seller shall not be liable for loss of profit or revenues, loss of use of equipment or systems, interruption of business, cost of replacement power, cost of capital, downtime costs, increased operating costs, any special, consequential, incidental, indirect, or punitive damages, or claims of Buyer’s customers for any of the foregoing types of damages. 15.3 All',\n", "   0.039937314,\n", "   'GE ref 1105582 - Metso - Las Truchas -Budget Proposal (005).docx',\n", "   27,\n", "   ''),\n", "  ('ch additions to, replacements of, and corrections and adjustments in the equipment in the Equipment as <PERSON><PERSON><PERSON> deems necessary to attain the performance warranted herein or at his option be subject to penalty.',\n", "   0.038394015,\n", "   '3142228 - Las Truchas - AG and BM Power Draw and Conveyance Warranty.docx',\n", "   2,\n", "   ''),\n", "  ('with engraved and painted stainless Steel labels. Labels show the item description and. part number in accordance to the hydraulic diagram and bill of material.',\n", "   0.03308598,\n", "   'C.3.5 General Mechanical Specification - MRM.pdf',\n", "   8,\n", "   ''),\n", "  ('W - NEW EQUIPMENT ORDERS 25-26 20.0 SERVICING 27 Page 4 of 29',\n", "   0.03302355,\n", "   'C.2.2 Quality Plan (Typical).pdf',\n", "   4,\n", "   ''),\n", "  ('| | | | | | | | <br/> | Seller must provide all the standard safety equipment requested by MSHA to perform any activity at mine site. | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | <br/> | Seller representatives to work at job site shall submit copy of a current official identification with photograph to Buyer and Buyer’s Agent. | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | <br/> | Important. Should the equipment you are offering requires the services of one or more sub-suppliers, you must specify what components of the equipment and have they submit their estimate supplementing yours. | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | <br/> | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | <br/> | Add any comments or additional information concerning your estimate: | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | |',\n", "   0.031618766,\n", "   'C.1.1 210148-M001 Ball and AG Mill_P3803_Pricing Template Rev2 - SP Ball Mill.xlsx',\n", "   1,\n", "   'F1 RFQ_row_1'),\n", "  ('hedule and prices herein to offset the effects of Coronavirus delays.” warranty',\n", "   0.031084951,\n", "   'GE ref 1105582 - Metso - Las Truchas -Budget Proposal (005).docx',\n", "   4,\n", "   ''),\n", "  ('s; Special engineering or starting studies; Non-standard documentation; Assembly, Start-up, Commissioning and Site Assistance services; Spare Parts for Operation; Training. Modification record terms and conditions Form ES 104 (Rev. 4) NOTICE: Sale of any Products or Services is expressly conditioned on Buyer\\'s assent to these Terms and Conditions. Any acceptance of <PERSON><PERSON>’s offer is expressly limited to acceptance of these Terms and Conditions and <PERSON><PERSON> expressly objects to any additional or different terms proposed by Buyer. No facility entry form shall modify these Terms and Conditions even if signed by Seller’s representative. Any order to perform work and Seller\\'s performance of work shall constitute Buyer’s assent to these Terms and Conditions. Unless otherwise specified in the quotation, Seller’s quotation shall expire 30 days from its date and may be modified or withdrawn by <PERSON><PERSON> before receipt of Buyer’s conforming acceptance. 1. Definitions \"Buyer\" means the entity to which',\n", "   0.027014792,\n", "   'GE ref 1105582 - Metso - Las Truchas -Budget Proposal (005).docx',\n", "   12,\n", "   ''),\n", "  ('Table of Contents 6.0 ORDERING INFORMATION/ TECHNICAL ASSISTANCE 22 6.1 Equipment Reference .22 7.0 PARTS LISTS 7.1 Single Narrow Element Assemblies .23 7.2 Dual Narrow Element Assemblies .24 7.3 Single Wide Element Assemblies .25 7.4 Dual Wide Element Assemblies .26 7.5 Drums . .26 7.6 Axial Locking Devices 7.7 Friction Block and Rivet Kits .27 7.8 Friction Shoe Assembly, Torque Bar and Release Spring Kits .28 8.0 REVISION . 29 VC 5001 (PDF FORMAT) Copyright Eaton Corp., 2012. All rights reserved.',\n", "   0.026405517,\n", "   'VC 5001 Installation and Maintenance.pdf',\n", "   3,\n", "   ''),\n", "  ('B-ooo3 Purchasing Policies and Control Procedure. 11.4 Suppliers shall be audited by the Quality Assurance/Engineering Departments [Reference Metso (QA) Standard Procedure E-0019 Quality Survey]. Page 17 of 29',\n", "   0.023330769,\n", "   'C.2.2 Quality Plan (Typical).pdf',\n", "   17,\n", "   ''),\n", "  ('shall not be changed by the Buyer or Owner. The exception to this requirement. shall be the main supply cable, for which the Buyer may specify a tag number for tie-point with the supply socket outlet. c) The identification nameplates, tags and labels shall be permanent and clearly identifiable for use. in the Operating Conditions defined in Section 5.',\n", "   0.*********,\n", "   'C.3.9 Electrical, Control & Instrumentation Specification.pdf',\n", "   19,\n", "   ''),\n", "  ('Seller shall be responsible for all corporate taxes measured by net income due to performance of or payment for work under this Contract (“Seller Taxes”). Buyer shall be responsible for all taxes, duties, fees, or other charges of any nature (including, but not limited to, consumption, gross receipts, import, property, sales, stamp, turnover, use, or value-added taxes, and all items of withholding, deficiency, penalty, addition to tax, interest, or assessment related thereto, imposed by any governmental authority on Buyer or Seller or its subcontractors) in relation to the Contract or the performance of or payment for work under the Contract other than Seller Taxes (\"Buyer Taxes\"). The Contract Price does not include the amount of any Buyer Taxes. If Buyer deducts or withholds Buyer Taxes, Buyer shall pay additional amounts so that Seller receives the full Contract Price without reduction for Buyer Taxes. Buyer shall provide to <PERSON>ller, within one month of payment, official receipts',\n", "   0.*********,\n", "   'GE ref 1105582 - Metso - Las Truchas -Budget Proposal (005).docx',\n", "   15,\n", "   ''),\n", "  ('business, cost of replacement power, cost of capital, downtime costs, increased operating costs, any special, consequential, incidental, indirect, or punitive damages, or claims of Buyer’s customers for any of the foregoing types of damages. 15.3 All Seller liability shall end upon expiration of the applicable warranty period, provided that Buyer may continue to enforce a claim for which it has given notice prior to that date by commencing an action or arbitration, as applicable under this Contract, before expiration of any statute of limitations or other legal time limitation but in no event later than one year after expiration of such warranty period. 15.4 Seller shall not be liable for advice or assistance that is not required for the work scope under this Contract.',\n", "   0.0190509,\n", "   'GE ref 1105582 - Metso - Las Truchas -Budget Proposal (005).docx',\n", "   27,\n", "   ''),\n", "  ('icable) applies only to the extent applicable for sale of COTS and/or commercial items and as appropriate for the Contract Price. ****',\n", "   0.019014435,\n", "   'GE ref 1105582 - Metso - Las Truchas -Budget Proposal (005).docx',\n", "   32,\n", "   ''),\n", "  ('ical, items shall be packaged so that the centre of mass of the package is as low and aa) Packing lists will be provided to the Purchaser in accordance with the order requirements. All packages shall be clearly labelled with their contents and have a detailed packing list showing all items packed including fasteners, shims, lugs, etc. A complete packing list protected against water ingress shall be attached to any consolidated components detailing all items packed in the. consolidated set including fasteners, shims and wedges.. bb) Any required handling remarks shall comply with the International handling markings shown in Figure 1. cc) Packaging of goods shall comply with any additional packing specification provided by the Purchaser with this specification.',\n", "   0.018797074,\n", "   'C.3.6 Packaging System Specification.pdf',\n", "   7,\n", "   ''),\n", "  ('ad, sea or rail.. Final transport to site will most likely be over unsealed roads so packaging must be suitable to ensure no transport related damage.. g) Prior to packing, all equipment shall be checked internally and externally to ensure it is free from all weld splatter, scale, rust, cuttings,filings etc. as well as any other foreign matter. Critical wearing surfaces shall be cleaned by dipping or brushing with suitable solvent, such as. petroleum, naptha, or alkaline cleaning compound. After hydro-testing, operation, or performance testing, all fluids (e.g. lubrication oil, fuels or water) shall be completely drained from all tanks, vessels, jacketing, piping etc., and wiped or blown dry. Lint free rags shall be used for wiping critical wear surfaces. h) All packed goods must be secured so the loads contained within packages cannot move during transport. All surfaces must be suitably protected to prevent damage during such transport. Rotating equipment shall be securely braced to',\n", "   0.01840489,\n", "   'C.3.6 Packaging System Specification.pdf',\n", "   6,\n", "   ''),\n", "  ('P3803 Revision: APPROVAL REVISION RECORD DATA SHEET FOR MEDIUM VOLTAGE SYNCHRONOUS MOTOR (Information to be Filled out for Each Motor size quoted) (Vendor to make additional copies as necessary)',\n", "   0.01784874,\n", "   'C.1.5 P3803-000-ELE-DS-004_0 - Data Sheet -Medium Voltage Synchronous Motors - Ball Mill Metso.docx',\n", "   1,\n", "   ''),\n", "  ('P3803\\nRevision:\\nAPPROVAL\\nREVISION RECORD\\nDATA SHEET FOR MEDIUM VOLTAGE SYNCHRONOUS MOTOR\\n(Information to be Filled out for Each Motor size quoted)\\n(Vendor to make additional copies as necessary)',\n", "   0.01784874,\n", "   'C.1.4 P3803-000-ELE-DS-004_0 - Data Sheet -Medium Voltage Synchronous Motors - AG Mill.docx',\n", "   '1, 2, 3',\n", "   ''),\n", "  ('P3803 Revision: APPROVAL REVISION RECORD DATA SHEET FOR MEDIUM VOLTAGE SYNCHRONOUS MOTOR (Information to be Filled out for Each Motor size quoted) (Vendor to make additional copies as necessary)',\n", "   0.01784874,\n", "   'C.1.4 P3803-000-ELE-DS-004_0 - Data Sheet -Medium Voltage Synchronous Motors - AG Mill.docx',\n", "   1,\n", "   ''),\n", "  ('P3803\\nRevision:\\nAPPROVAL\\nREVISION RECORD\\nDATA SHEET FOR MEDIUM VOLTAGE SYNCHRONOUS MOTOR\\n(Information to be Filled out for Each Motor size quoted)\\n(Vendor to make additional copies as necessary)',\n", "   0.01784874,\n", "   'C.1.5 P3803-000-ELE-DS-004_0 - Data Sheet -Medium Voltage Synchronous Motors - Ball Mill Metso.docx',\n", "   '1, 2, 3',\n", "   ''),\n", "  ('Any supplied labor and equipment will be warranted for an initial period of 30 months from the date the equipment is placed in operation, or 36 months from date the materials or workmanship are furnished to Buyer, whichever expires first. Transit packing and protection Provision of suitable packing and protection has been included on the understanding that storage of all equipment will be within a dry, weatherproof facility only. Should equipment be subject to prolonged storage after delivery, it is the Buyer’s responsibility to follow routine maintenance procedures as advised. exclusion of non-material damages Seller shall in no event be held liable for any indirect or consequential damages, such as loss of raw materials, loss of use, loss of production, loss of profit. Buyer waives all rights to claim compensation for any of the above damages. terms and conditions GE Power Conversion ES104 rev4 standard terms and conditions apply to this proposal. Validity This proposal is accurate',\n", "   0.016593177,\n", "   'GE ref 1105582 - Metso - Las Truchas -Budget Proposal (005).docx',\n", "   5,\n", "   ''),\n", "  ('hanges can have price and delivery implications. Exceptions: This proposal presently EXCLUDES supply of the following items: \\uf0b7 MV Switchgear for primary protection of VFC and Ball Mill Motor. \\uf0b7 Harmonic Filters or Power Factor Compensation Equipment. \\uf0b7 Mechanical gear reducers and auxiliary inching devices for mill maintenance. \\uf0b7 LV Switchgears, LV-MCCs \\uf0b7 Installation and supply of Half coupling \\uf0b7 Auxiliaries for fresh water supply (chiller plant, pumps, piping, valves, etc.) \\uf0b7 Electrical Rooms \\uf0b7 Automation system, DCS or PLC Controller and its Operator Panels (HMI) for mill auxiliaries. \\uf0b7 System engineering studies like: harmonics study, protection coordination, short circuit analysis, torsional analysis, seismic certification, power distribution (wiring and cable schedules), etc. \\uf0b7 Torque limiting coupling, brake disk, etc. \\uf0b7 Grounding system, soleplates or fixation material like anchor bolts, etc.',\n", "   0.01627746,\n", "   'Metso-OT Las Truchas Scope of Supply MN-8XMAVQA4_REV00.pdf',\n", "   4,\n", "   ''),\n", "  ('ller shall be entitled to a matching extension of the schedule. If at any time <PERSON><PERSON> reasonably determines that Buyer’s financial condition or payment history does not justify continuation of <PERSON><PERSON>’s performance, Seller shall be entitled to require full or partial payment in advance or otherwise restructure payments, request additional forms of Payment Security, suspend its performance or terminate the Contract. Taxes and Duties',\n", "   0.01621503,\n", "   'GE ref 1105582 - Metso - Las Truchas -Budget Proposal (005).docx',\n", "   14,\n", "   ''),\n", "  ('or pay Seller’s standard shipping charges plus up to twenty-five (25%) percent. Partial deliveries are permitted. Seller may deliver Products in advance of the delivery schedule. Delivery times are approximate and are dependent upon prompt receipt by <PERSON><PERSON> of all information necessary to proceed with the work without interruption. If Products delivered do not correspond in quantity, type or price to those itemized in the shipping invoice or documentation, Buyer shall so notify <PERSON><PERSON> within ten (10) days after receipt.',\n", "   0.*********,\n", "   'GE ref 1105582 - Metso - Las Truchas -Budget Proposal (005).docx',\n", "   15,\n", "   ''),\n", "  ('fety 2006/42/EC European Commission Machinery Supplied equipment to Directive meet requirements of Machinery Directive EN ISO Safety of machinery -- Basic 2006/42/EC. At a 12100 concepts, general principles for minimum the design harmonized standards EN Safety of machinery -- Terminology. referenced in these 1070:1998 documents shall be isO12100 and EN ISO Safety of Machinery - Guards IEC60204, as 14120 General requirements for the design applicable. and construction of fixed and movable guards IEC 60204-1 Safety of machinery - Electrical equipment of machines ISO 13849-1 Safety-related parts of control systems - Part 1: General principles for design ISO 13849-2 Safety of machinery -- Safety-related parts of control systems - Part 2: Validation ISO 7010 Graphical Symbols - Safety colors and safety signs Mechanica AS 1275 Metric screw threads for fasteners ISO 273 Fasteners - Clearance holes for bolts and screws Electrical IEC 60034 Rotating electrical machines - All relevant parts @',\n", "   0.*********,\n", "   'Ball Feeder - Technical description.pdf',\n", "   2,\n", "   ''),\n", "  ('14.2 The scope, Contract Price, schedule, and other provisions will be equitably adjusted to reflect additional costs or obligations incurred by <PERSON><PERSON> resulting from a change, after <PERSON><PERSON>’s proposal date, in Buyer’s Site-specific requirements or procedures, or in industry specifications, codes, standards, applicable laws or regulations. However, no adjustment will be made on account of a general change in <PERSON><PERSON>’s manufacturing or repair facilities resulting from a change in laws or regulations applicable to such facilities. Unless otherwise agreed by the parties, pricing for additional work arising from such changes shall be at <PERSON><PERSON>’s time and material rates. 14.3 It shall be acceptable and not considered a change if <PERSON><PERSON> delivers a Product that bears a different, superseding or new part or version number compared to the part or version number listed in the Contract. 15. Limitations of Liability 15.1 The total liability of <PERSON><PERSON> for all claims of any kind arising from or',\n", "   0.*********,\n", "   'GE ref 1105582 - Metso - Las Truchas -Budget Proposal (005).docx',\n", "   27,\n", "   ''),\n", "  ('work. 18. Software, Leased Equipment, Remote Diagnostic Services, PCB Services',\n", "   0.*********,\n", "   'GE ref 1105582 - Metso - Las Truchas -Budget Proposal (005).docx',\n", "   29,\n", "   ''),\n", "  ('les/Marketing. a. Terms are reviewed and compared with agreements made during negotiations and bidding. b. Technical specification is reviewed and compared with bid specification by Engineering and Quality Assurance.. 19.4.2 Acknowledgment written by Marketing or Contract Management. a. Acknowledgment would include any comments or exceptions necessary following review of the purchase order.. Page 25 of 29',\n", "   0.011687257,\n", "   'C.2.2 Quality Plan (Typical).pdf',\n", "   25,\n", "   ''),\n", "  (\"13.7 Se<PERSON> shall notify Buyer if <PERSON><PERSON> becomes aware of: (i) conditions at the Site differing materially from those disclosed by Buyer, or (ii) previously unknown physical conditions at Site differing materially from those ordinarily encountered and generally recognized as inherent in work of the character provided for in the Contract. If any such conditions cause an increase in Se<PERSON>'s cost of, or the time required for, performance of any part of the work under the Contract, an equitable adjustment in price and schedule shall be made. 13.8 If <PERSON><PERSON> encounters Hazardous Materials in Buyer’s equipment or at the Site that require special handling or disposal, <PERSON><PERSON> is not obligated to continue work affected by the hazardous conditions. In such an event, Buyer shall eliminate the hazardous conditions in accordance with applicable laws and regulations so that Seller’s work under the Contract may safely proceed, and <PERSON><PERSON> shall be entitled to an equitable adjustment of the price and\",\n", "   0.0107767265,\n", "   'GE ref 1105582 - Metso - Las Truchas -Budget Proposal (005).docx',\n", "   26,\n", "   ''),\n", "  ('ty to disassemble the equipment so that it can be properly packed and protected.. c) Consideration must be given for the safe handling and unpacking of the packages. The Vendor will be held liable for any issues that arise from non-compliance to this specification. d)As far as practical the Vendor must optimize the size, weight and complexity of the packaging and packaging materials to minimize the cost of transport, handling, storage, site removal and disposal of packaging materials. Remember waste space within packaging is a source of weakness and therefore the Vendor shall ensure that equipment is nested and packed correctly. e) Unless specified as being suitable for airfreight, all packages shall be suitable for extended transport by road, sea or rail.. Final transport to site will most likely be over unsealed roads so packaging must be suitable to ensure no transport related damage.. g) Prior to packing, all equipment shall be checked internally and externally to ensure it is free',\n", "   0.009559399,\n", "   'C.3.6 Packaging System Specification.pdf',\n", "   6,\n", "   ''),\n", "  ('ing or other in-site assistance. • Bulk material like tongue lugs, cables markers, medium voltage terminals (stress cones) & grounding system (to be provided by others). • Factory Acceptance Testing (witness testing) • Training • Spare parts. • Motor enclosures and main terminal box are offered with protection degree IP55 Siemens scope does not include any fiber optic, communication system, field devices and other auxiliaries such as protocol, copper/fiber optic transducers or accessory required for the integral system operation. This offer has been calculated on the basis that the origin of equipment will be China for the Motors, Germany for the VFCs type SINAMICS GM150 and Turkey for the Isolation Transformers. All documentation will be provided in English only.',\n", "   0.009125637,\n", "   'Metso-OT Las Truchas Scope of Supply MN-8XMAVQA4_REV02.pdf',\n", "   4,\n", "   ''),\n", "  ('ctor Compensation Equipment. • Mechanical gear reducers or external inching devices for mill maintenance. • LV Switchgears, LV-MCCs • Installation and supply of Half coupling • Auxiliaries for fresh water supply (chiller plant, pumps, piping, valves, etc.) • Electrical Rooms • Automation system, DCS or PLC Controller and its Operator Panels (HMI) for mill auxiliaries. • System engineering studies like: harmonics study, protection coordination, short circuit analysis, torsional analysis, seismic certification, power distribution (wiring and cable schedules), etc. • Torque limiting coupling, brake disk, etc. • Grounding system, soleplates or fixation material like anchor bolts, etc. • Installation Supervision, Start-up and Commissioning or other in-site assistance. • Bulk material like tongue lugs, cables markers, medium voltage terminals (stress cones) & grounding system (to be provided by others). • Factory Acceptance Testing (witness testing) • Training • Spare parts. • Motor',\n", "   0.009055264,\n", "   'Metso-OT Las Truchas Scope of Supply MN-8XMAVQA4_REV02.pdf',\n", "   4,\n", "   '')]]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["res"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["workspace/gpu_processor/storage/indices/aienergy_vec_db_  da174136-c6f6-4354-b7e0-ac9235b8b8d2    .index"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["from services.faiss_embedding import FaissEmbedding\n", "eb = FaissEmbedding()\n", "\n", "res = eb.search_sync(request_id=\"da174136-c6f6-4354-b7e0-ac9235b8b8d2\",query=[\"PriceCost of equipment and all necessary components, including spare parts (commissioning, 1 year of operation, capital).The bidder must inform the prices considered for the equipment and the spare parts.All line items must be fully priced. Spares pricing must be in accordance with the Project Requirements.\"])"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["[[('| | <br/> | | | | | | | | | | | | | | | | | | | | | | | | | UTILITIES (water, air, oil) | | | | | | | | | | | | | | | | | | | | | <br/> | AUXILIARIES EQUIPMENT | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | <br/> | | | | | | | | | | | | | | | | | | | | | | | | | | Water: | | | | | Operating pressure: | | | | | 1030 kPag Max | | | | Flow | 150 | | | | l/min | <br/> | | Inching Drive system | | | | | | | | | X | | Included | | | | | | Not included | | | | | | | Instrument air: | | | | | Operating pressure: | | | | | 690 kPag | | | | Flow | 13.5 | | | | m3/h | <br/> | | Jacking system | | | | | | | | | X | | Included | | | | | | Not included | | | | | | | Plant air: | | | | | Operating pressure: | | | | | 690 kPag | | | | Flow | 1 | | | | m3/h | <br/> | | Retractable Feed Chute | | | | | | | | | X | | Included | | | | | | Not included | | | | | | | Oil pressure for Jacking system: | | | | | | | | | | 69000 | | | | kPa | | | | | | <br/> | | Discharge Chute | | | | | | | | | X | | Included | | | | | | Not included | | | | | | | | | | | | | | | | | | | | | | | | | | | <br/> | | | | | | | | | | | | | | | | | | | | | | | | | LINERS | | | | | | | | | | | | | | | | | | | | | <br/> | MATERIAL OF FABRICATION | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | <br/> | | | | | | | | | | | | | | | | | | | | | | | | | | Type/Thickness : | | | | | | Plate & Lifter/110 average | | | | | | | | | | | | | | <br/> | | Shell: | | | ASTM A36, Q235 or Metso:Outotec approved comparable material | | | | | | | | | | | | | | | | | | | | | Material/Hardness: | | | | | | Rubber/60 Shore | | | | | | | | | | | | | | <br/> | | Flanges: | | | Lamellar tear resistant steel (Z25) per Metso:Outotec specifications | | | | | | | | | | | | | | | | | | | | | Number of rows: | | | | | | 42 | | | | | | | | | | | | | | <br/> | | Heads: | | | Cast SG iron or cast steel | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | <br/> | | Chutes: | | | Mild steel fabrication with abriasion resistant liners | | | | | | | | | | | | | | | | | | | | | Liner location: | | | | | | | | Shell | | | Heads | | | Feed | | | Discharge | | | <br/> | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | X | all wetted parts: | | | | | | | | | | | | <br/> | PAINTING | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | specific location: | | | | | | | | | | | | <br/> | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | <br/> | | Surface preparation : | | | | | | | Abrasive Blast to Sa 2.5 (ISO 8501-1) or Equivalent | | | | | | | | | | | | | | | | INSPECTION & TESTING REQUIREMENT | | | | | | | | | | | | | | | | | | | | | <br/> | | Paint supplier : | | | | | | | Supplier Standard | | | | | | | | | | | | | | | | TEST DATA | | | | | | | | | | | | | | | | | | | | | <br/> | | Paint type : | | | | | | | Supplier Standard | | | | | | | | | | | | | | | |',\n", "   0.32188618,\n", "   'C.1.3_P3803-500-PROC-DS-001_RevQ_Metso_Outotec.xlsx',\n", "   2,\n", "   'Questionaire_row_2'),\n", "  ('| | | | UTILITIES (water, air, oil) | | | | | | | | | | | | | | | | | | | | | <br/> | AUXILIARIES EQUIPMENT | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | <br/> | | | | | | | | | | | | | | | | | | | | | | | | | | Water: | | | | | Operating pressure: | | | | | 1030 kPag max | | | | Flow | | | | | l/min | <br/> | | Inching Drive system | | | | | | | | | X | | Included | | | | | | Not included | | | | | | | Instrument air: | | | | | Operating pressure: | | | | | 690 kPag max | | | | Flow | | | | | m3/h | <br/> | | Jacking system | | | | | | | | | X | | Included | | | | | | Not included | | | | | | | Plant air: | | | | | Operating pressure: | | | | | 690 kPag max | | | | Flow | | | | | m3/h | <br/> | | Retractable Feed Chute | | | | | | | | | X | | Included | | | | | | Not included | | | | | | | Oil pressure for Jacking system: | | | | | | | | | | | | | | kPa | | | | | | <br/> | | Discharge Chute | | | | | | | | | X | | Included | | | | | | Not included | | | | | | | | | | | | | | | | | | | | | | | | | | | <br/> | | | | | | | | | | | | | | | | | | | | | | | | | LINERS and LIFTERS | | | | | | | | | | | | | | | | | | | | | <br/> | MATERIAL OF FABRICATION | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | <br/> | | | | | | | | | | | | | | | | | | | | | | | | | | Liner Type/Thickness : | | | | | | High-Low - 150 mm average | | | | | | | | | | | | | | <br/> | | Shell: | | | ASTM A36, Q235 or Metso:Outotec approved comparable material | | | | | | | | | | | | | | | | | | | | | Lifter Type/Height : | | | | | | High-Low - 150 mm average | | | | | | | | | | | | | | <br/> | | Flanges: | | | Lamellar tear resistant steel (Z25) per Metso:Outotec specifications | | | | | | | | | | | | | | | | | | | | | Grate Type/Opening: | | | | | | Slots/ | | | | | | | | | | | | | | <br/> | | Heads: | | | Cast SG iron or cast steel | | | | | | | | | | | | | | | | | | | | | Material/Hardness: | | | | | | Chrome-moly steel/ 350 BHN average | | | | | | | | | | | | | | <br/> | | Chutes: | | | Mild steel fabrication with abriasion resistant liners | | | | | | | | | | | | | | | | | | | | | Number of rows: | | | | | | 60 (preliminary) | | | | | | | | | | | | | | <br/> | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | <br/> | INSPECTION & TESTING REQUIREMENT | | | | | | | | | | | | | | | | | | | | | | | | Liner location: | | | | | Head End | | | | Shell | | | Discharge | | | Grates | | | Pulp Lifters | | | <br/> | TEST DATA | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | X | all wetted parts: | | | | | | | | | | | | <br/> | | | | | | | | Witness | | | | Nonwitness | | | | Observed | | | | | | | | | | | | | | | | | | specific location: | | | | | | | | | | | | <br/> | | Performance | | | | | | | X | | | | X | | | | X | | | | | | | | | | | | | | | | | | | | | | | | | | | | | <br/> | | Please see typical Metso:Outotec Inspection and Test lpan with',\n", "   0.2962469,\n", "   'C.1.2_P3803-200-PROC-DS-001_RevQ_Metso_Outotec.xlsx',\n", "   2,\n", "   'Questionaire_row_2'),\n", "  ('LIABILITY), OR FOR INFRINGEMENT OR <PERSON>DER ANY OTHER LEGAL THEORY, FOR LOSS OF USE, PRODUCTION, REVENUE OR PROFIT, OR FOR COST OF CAPITAL, OR OF SUBSTITUTE USE OR <PERSON><PERSON><PERSON><PERSON><PERSON>NC<PERSON>, OR <PERSON>CREASED COSTS OF OPERATION OR MAINTENANCE, OR FOR INCIDENTAL, IN<PERSON>RE<PERSON>, SP<PERSON><PERSON><PERSON>, OR CONSEQUENTIAL DAMAGES, OR FOR ANY OTHER LOSS OR COST OF SIMILAR TYPE. The limitation of liability contained in this section shall be effective without regard to <PERSON><PERSON>’s performance or failure or delay of performance under any other term or condition of this Agreement, including those contained in any warranty. SELLER’S MAXIMUM LIABILITY UNDER THIS AGREEMENT FOR ANY AND ALL REASONS SHALL BE LIMITED TO THE PURCHASE PRICE FOR THE EQUIPMENT OR COMPONENTS SOLD TO AND PAID FOR BY BUYER. I In any case where <PERSON><PERSON> is supplying only design and components and is not responsible for erection of the equipment all costs related to the assembly, erection and commissioning of the equipment in the field shall be deemed special, indirect,',\n", "   0.17272931,\n", "   'C.2.5_Metso_Outotec_Schedule_of_Rates.pdf',\n", "   4,\n", "   'GENERAL PROVISIONS FOR FIELD SERVICE'),\n", "  ('rument Requirements ................................................................................................ 14',\n", "   0.12199923,\n", "   'C.3.9_Electrical_Control__Instrumentation_Specification.pdf',\n", "   3,\n", "   'TABLE OF CONTENTS'),\n", "  ('aterial, equipment, replacement parts, cranes, rigging and facilities to perform the work on the equipment. Service assistance may be obtained by contacting any Metso Outotec office. FIELD SERVICE HOURLY RATES Field Service Representative Travel Time Standby Time Straight Time Time and One-Half Double Time $ 228.00 $ 228.00 $ 228.00 $ 320.00 $ 410.00 Field Service Engineer Travel Time Standby Time Straight Time Time and One-Half Double Time $ 288.00 $ 288.00 $ 288.00 $ 403.00 $ 518.00 STRAIGHT TIME is defined as time worked on a regular schedule of 8 hours between 7:00 a.m. and 6:00 p.m., Monday through Friday, or as time worked on any other agreed upon regular schedule of 8 hours per day, Monday through Friday. Each hour of straight time shall be paid for at the straight time rate. OVERTIME is defined as time worked in excess of or at times other than the regular straight time schedule. Each hour of overtime shall be paid at one and one-half times the straight time rate, except those',\n", "   0.10837754,\n", "   'C.2.5_Metso_Outotec_Schedule_of_Rates.pdf',\n", "   1,\n", "   'FIELD SERVICE')]]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["res"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "gs", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}