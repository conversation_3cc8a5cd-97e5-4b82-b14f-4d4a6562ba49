import os
import subprocess
import tempfile
from models import File
import json
from multiprocessing import Process, Queue

class DocxToPdf:
    def __init__(self):
        env_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '../env.json')
        with open(env_file_path, 'r') as f:
            env_data = json.load(f)

        self.UPLOAD_FOLDER = env_data.get("DATA_DIR")
        self.CHRONOBID_FOLDER = env_data.get("CHRONOBID_DIR")

    def convert_docx_to_pdf(self, docx_path, output_dir=None):
        
        """
        Convert a DOCX file to a PDF file using LibreOffice, preserving layout and pagination.
        
        Parameters:
        docx_path (str): The path to the DOCX file.
        output_dir (str): The directory where the PDF will be saved. If None, save in the same directory as the DOCX file.
        
        Returns:
        str: The path to the generated PDF file.
        """
        if output_dir is None:
            output_dir = os.path.dirname(docx_path)
        
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        print(docx_path)
        print(output_dir)
        
        
        # Queue to receive the pdf_path from the subprocess
        result_queue = Queue()

        # Start the conversion in a separate process
        p = Process(target=self._run_conversion, args=(docx_path, output_dir, result_queue))
        p.start()

        # Wait for the result (pdf path) from the queue
        p.join()

        # Retrieve the pdf_path from the result_queue
        pdf_path = result_queue.get()
        return pdf_path
    
    def _run_conversion(self, docx_path, output_dir, result_queue):
        command = [
            'libreoffice', '--headless', '--convert-to', 'pdf',
            '--outdir', output_dir, docx_path
        ]
        
        try:
            subprocess.run(command, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            # Generate the PDF file path after conversion
            pdf_path = os.path.join(output_dir, os.path.splitext(os.path.basename(docx_path))[0] + '.pdf')
            # Put the pdf path into the queue to send it back to the main process
            result_queue.put(pdf_path)
        except subprocess.CalledProcessError as e:
            print(f"Error converting file: {e.stderr.decode('utf-8')}")
            result_queue.put(f"Error converting file: {e.stderr.decode('utf-8')}")
        except Exception as e:
            print(f"Unexpected error: {str(e)}")
            result_queue.put(f"Unexpected error: {str(e)}")



    def convert_docx_to_pdf_file_to_file(self, temp_file_path, output_dir=None):
        """
        Convert a DOCX file to a PDF file.

        Parameters:
        file (str): The path to the DOCX file.
        output_dir (str): The directory where the PDF will be saved. If None, save in the same directory as the DOCX file.

        Returns:
        str: The path to the generated PDF file.
        """
        # file.save(temp_file_path)
        print('saved the file: ', temp_file_path)
        
        pdf_path = self.convert_docx_to_pdf(temp_file_path, output_dir)
        print('pdf path: ', pdf_path)
            
        return pdf_path
    
    def convert_idle_docx_files(self):
        """
        Retrieve documents from the database where file_type is 'docx' and pdf_conversion_status is 'idle'.
        Convert these files to PDF and update their status in the database.
        """
        print('picking files for conversion...')
        pending_files = File.get_by(file_type='docx', pdf_conversion_status='pending')
        if len(pending_files) >= 5:
            print('More than 5 files processing...')
            return 
        
        file_record = File.get_by(file_type='docx', pdf_conversion_status='idle')

        try:
            if len(file_record) > 0:
                if file_record[0]['file_dirtry'] == 'data':
                    folder_path = os.path.join(self.UPLOAD_FOLDER, file_record[0]['project_id'])
                elif file_record[0]['file_dirtry'] == 'chronobid':
                    folder_path = os.path.join(self.CHRONOBID_FOLDER, file_record[0]['project_id'])
                
                temp_file_path = os.path.join(folder_path, file_record[0]['name'])
                output_dir = folder_path
                print('temp file path: ', temp_file_path)
                print('output dir: ',output_dir)
                
                # Convert the DOCX file to PDF
                File.update(file_record[0]['id'], pdf_conversion_status='pending')
                pdf_path = self.convert_docx_to_pdf(temp_file_path, output_dir)
                print('conversion complete....')
                print('pdf converted path: ', pdf_path)
                
                if pdf_path and os.path.exists(pdf_path):
                    # Update the file name and status in the database
                    pdf_file_name = os.path.basename(pdf_path)
                    File.update(file_record[0]['id'], file_type='pdf', name=pdf_file_name, pdf_conversion_status='done')
                    # os.remove(temp_file_path)  # Optionally remove the original .docx file
                else:
                    File.update(file_record[0]['id'], pdf_conversion_status='failed')
                print('file saved as: ', pdf_path)
            
            print('no files to convert......')
            
        except Exception as e:
            print(f"Error processing file {file_record[0]['name']}: {str(e)}")
            File.update(file_record[0]['id'], pdf_conversion_status='failed')



if __name__ == '__main__':
    processor = DocxToPdf()
    processor.convert_idle_docx_files()