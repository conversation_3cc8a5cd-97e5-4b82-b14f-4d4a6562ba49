import os, uuid, json, random
from flask import request, jsonify
from werkzeug.utils import secure_filename
from init import app, db, socketio, socket_manager
from services.cbp_file_processor import CBPFileProcessor
from services.scp_file_processor import SCPFileProcessor
import asyncio, threading
from flask_socketio import emit, Namespace, leave_room
from models import Project, Requirement
from services.faiss_embedding import FaissEmbeddingGPU
from functools import wraps
from zipfile import ZipFile
import tempfile
import eventlet
import time
import json

# Load environment variables from env.json file in the parent directory
env_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'env.json')
with open(env_file_path, 'r') as f:
    env_data = json.load(f)

UPLOAD_FOLDER = env_data.get("DATA_DIR")
CHRONOBID_FOLDER = env_data.get("CHRONOBID_DIR")
SPECS_FOLDER = env_data.get("SPECS_DIR")

ALLOWED_EXTENSIONS = {'doc', 'docx', 'pdf', 'txt', 'zip'}

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['CHRONOBID_FOLDER'] = CHRONOBID_FOLDER
app.config['SPECS_FOLDER'] = SPECS_FOLDER

eventlet.monkey_patch()
main_event_loop = asyncio.new_event_loop()
loop_thread = threading.Thread(target=main_event_loop.run_forever, daemon=True)
loop_thread.start()

class ProcessingFilesNamespace(Namespace):
    def __init__(self, namespace, socket_manager):
        super().__init__(namespace)
        self.socket_manager = socket_manager

    def on_connect(self):
        """Handle connection to /processing_files namespace."""
        emit("progress_message", {"message": "Test emit from server, congratulations on joining Processing Files Namespace on GPU"})
        print("Client connected to /processing_files namespace")

    def on_disconnect(self):
        """Handle disconnection from /processing_files namespace."""
        print("Client disconnected from /processing_files namespace")
    
    def on_leave(self, data):
        """Handle client leaving a room."""
        request_id = data.get('request_id')
        if request_id:
            leave_room(request_id)
            print(f"Client left room {request_id} in /processing_files namespace")
    
    def on_join(self, data):
        """Handle client joining the room."""

        request_id = data.get('request_id')
        sid = request.sid
        is_reconnect = data.get("is_reconnect", False)

        if request_id:
            emit("progress_message", {"message": f"Client joined room {request_id} on GPU"}, to=sid)
            self.socket_manager.add_client(sid, request_id)
            # if not is_reconnect:

            #     file_processor = CBPFileProcessor(socket_manager, request_id)

            #     # Create new event loop for this thread
            #     loop = asyncio.new_event_loop()
            #     asyncio.set_event_loop(loop)
                
            #     # Run async methods using the new loop
            #     loop.run_until_complete(file_processor.process_files())


socketio.on_namespace(ProcessingFilesNamespace('/processing_files', socket_manager))


@app.route('/health')
def home():
    return 'TEST OK!'

def success_response(message, data={}, status_code=200):
    return jsonify({"success": True, "message": message, "data": data}), status_code

def error_response(message, data={}, status_code=400):
    return jsonify({"success": False, "message": message, "data": data}), status_code

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/shutdown', methods=['POST'])
def shutdown():
    main_event_loop.call_soon_threadsafe(main_event_loop.stop)
    loop_thread.join()
    return "Event loop stopped."

def async_route(f):
    @wraps(f)
    def wrapper(*args, **kwargs):
        return asyncio.run(f(*args, **kwargs))
    return wrapper

@app.route('/search_tender_requirement', methods=['POST'])
@async_route
async def search_tender_requirement():
    try:
        # Get data from JSON instead of form data since we're sending JSON
        data = request.get_json()
        
        request_id = data.get('request_id')
        search_queries = data.get('search_queries')
        k = data.get('k', 5)  # Get k from request or default to 5

        if not search_queries:
            return error_response("Query parameter is required", 400)

        # Initialize FAISS instance
        faiss_instance = FaissEmbeddingGPU(request_id=request_id, batch_size=1000)
        if not faiss_instance:
            return error_response("FAISS instance not initialized", 500)

        # Directly await the search method
        # results = await faiss_instance.search(query=query, k=k)
        search_results = await asyncio.gather(*[
            faiss_instance.search(query, k=3) for query in search_queries
        ])

        print('this is search results: ', search_results)
        # Format the results
        formatted_results = []
        for query_result in search_results:
            query_matches = []
            for doc, score in query_result:
                query_matches.append({
                    'content': doc.page_content,
                    'metadata': doc.metadata,
                    'score': float(score)  # Convert numpy float to Python float
                })
            formatted_results.append(query_matches)

        # Return success response with formatted results
        return success_response({
            'request_id': request_id,
            'query_count': len(search_queries),
            'results': formatted_results
        })

    except Exception as e:
        print(f"Error in search_tender_requirement: {str(e)}")
        return error_response(f"Search failed: {str(e)}", 500)

@app.route('/test_zip', methods=['POST'])
def test_zip():
    files = request.files.getlist('file')
    is_zip = request.form.get('is_zip')
    # Unzip files if `is_zip` is true
    # Unzip files if `is_zip` is true
    if is_zip == 'true':
        extracted_files = []
        with tempfile.TemporaryDirectory() as temp_dir:
            for file_itm in files:
                file_name = secure_filename(file_itm.filename)
                if not file_name.endswith('.zip'):
                    return error_response(f"File {file_name} is not a zip archive", 400)
                
                zip_path = os.path.join(temp_dir, file_name)
                file_itm.save(zip_path)

                # Extract the zip file
                with ZipFile(zip_path, 'r') as zip_ref:
                    zip_ref.extractall(temp_dir)

                    # Get all file paths inside the extracted directory
                    for root, _, file_names in os.walk(temp_dir):
                        for name in file_names:
                            full_path = os.path.join(root, name)
                            extracted_files.append(full_path)

            # Open all extracted files for further processing
            files = [
                open(file_path, 'rb')
                for file_path in extracted_files
            ]

    
@app.route('/upload_tender_requirement', methods=['POST'])
def upload_tender_requirement():
    
    if 'file' not in request.files:
        return error_response('No file part')

    files = request.files.getlist('file')

    print(f'received {len(files)} files uploaded...')
    package_id = request.form.get('package_id')
    req_id = request.form.get('request_id', False)
    # is_zip = request.form.get('is_zip')

    if not package_id:
        return 'package is required', 400

    if not files:
        return error_response('No selected files', 400)

    if not all(allowed_file(file.filename) for file in files):
        return error_response('Some files are not allowed', 400)

    if req_id :
        existing_req = Requirement.get_single(req_id)
        print('recovered existing request: ', existing_req)
    else:
        existing_req = False
        req_id = str(uuid.uuid4())
    
    # Step 3: Fetch package details
    try:
        package = Project.get_single(package_id)
    except Exception as e:
        return 'Invalid package_id', 400

     
    folder_path = os.path.join(CHRONOBID_FOLDER, package_id, req_id)
    os.makedirs(folder_path, mode=0o775, exist_ok=True)

    try:
        
        result = []
        file_names_arr = []
        all_success = True
        all_failed = True
        lock = threading.Lock()

        if not existing_req:
            create_requirement_with_retry(req_id, file_names_arr, package_id)

        # Async file upload
        def upload_file_task(file_itm):
            nonlocal all_success, all_failed
            file_name = secure_filename(file_itm.filename)
            file_path = os.path.join(folder_path, file_name)
            file_names_arr.append(file_name)
            try:
                print(f"uploading file: {file_name}")
                file_itm.save(file_path)
                result.append({"success": True, "file": file_name})
                print(f"Successfully uploaded: {file_name}")
            except Exception as e:
                with lock:
                    all_success = False
                result.append({"success": False, "file": file_name, "error": str(e)})
                print(f"Failed to upload file {file_name}: {str(e)}")

        
        # Run all uploads concurrently
        def _upload_files_concurrently():
            print(f"Starting concurrent file uploads for {len(files)} files.")
            # tasks = [upload_file_task(file_itm) for file_itm in files]
            # await asyncio.gather(*tasks)
            pool = eventlet.greenpool.GreenPool()  # Create a GreenPool to manage concurrent tasks
            for file_itm in files:
                pool.spawn(upload_file_task, file_itm)  # Spawn each file upload as a greenlet
            pool.waitall()
            print("Concurrent file uploads finished now processing...")
            # print('Completed embedding data into vec db...')
        
        async def async_upload_files_concurrently():
            await asyncio.to_thread(_upload_files_concurrently)

        try:
            ensure_event_loop_and_run(async_upload_files_concurrently())
        except Exception as e:
            print(f"Error during file upload processing: {e}")
        
        async def _process_files():
            try:
                print('starting to process files...')
                # time.sleep(2)
                file_processor = CBPFileProcessor(socket_manager, req_id, existing_req, instant_file_names=file_names_arr)
                await file_processor.process_files()
            except Exception as e:
                print(f"Error during file processing: {e}")
                
        try:
            # ensure_event_loop_and_run(async_upload_files_concurrently())
            # Run the asynchronous function safely in the event loop
            ensure_event_loop_and_run(_process_files())
        except Exception as e:
            print(f"Error during file upload processing: {e}")
        
        if existing_req:
            existing_req_names = json.loads(existing_req["name"])
            new_names = file_names_arr + existing_req_names
            Requirement.update(req_id, name=json.dumps(new_names))
        else:
            Requirement.update(req_id, name=json.dumps(file_names_arr))
            

        if all_success:
            message = "All files successfully uploaded"
        elif all_failed:
            message = "All files failed to upload"
        else:
            message = "One or more files failed to upload"
        
        print(f"Upload process complete: {message}")
                
        return {'request_id': req_id, 'message' : message, 'result' : result}
    except Exception as e:
        print(e)

@app.route('/upload_project_requirement', methods=['POST'])
@async_route
def upload_project_requirement():
    
    if 'file' not in request.files:
        return error_response('No file part')

    files = request.files.getlist('file')

    print(f'received {len(files)} files uploaded...')
    project_id = request.form.get('project_id')
    req_id = request.form.get('request_id', False)
    # is_zip = request.form.get('is_zip')

    if not project_id:
        return 'project is required', 400

    if not files:
        return error_response('No selected files', 400)

    if not all(allowed_file(file.filename) for file in files):
        return error_response('Some files are not allowed', 400)

    if req_id :
        existing_req = Requirement.get_single(req_id)
    else:
        existing_req = False
        req_id = str(uuid.uuid4())
    
    # Step 3: Fetch package details
    try:
        project = Project.get_single(project_id)
    except Exception as e:
        return 'Invalid project', 400

     
    folder_path = os.path.join(SPECS_FOLDER, project_id, req_id)
    os.makedirs(folder_path, mode=0o775, exist_ok=True)

    try:
        
        result = []
        file_names_arr = []
        all_success = True
        all_failed = True
        lock = threading.Lock()

        if not existing_req:
            create_requirement_with_retry(req_id, file_names_arr, project_id)

        # Async file upload
        def upload_file_task(file_itm):
            nonlocal all_success, all_failed
            file_name = secure_filename(file_itm.filename)
            file_path = os.path.join(folder_path, file_name)
            file_names_arr.append(file_name)
            try:
                print(f"uploading file: {file_name}")
                file_itm.save(file_path)
                result.append({"success": True, "file": file_name})
                print(f"Successfully uploaded: {file_name}")
            except Exception as e:
                with lock:
                    all_success = False
                result.append({"success": False, "file": file_name, "error": str(e)})
                print(f"Failed to upload file {file_name}: {str(e)}")

        
        # Run all uploads concurrently
        def _upload_files_concurrently():
            print(f"Starting concurrent file uploads for {len(files)} files.")
            # tasks = [upload_file_task(file_itm) for file_itm in files]
            # await asyncio.gather(*tasks)
            pool = eventlet.greenpool.GreenPool()  # Create a GreenPool to manage concurrent tasks
            for file_itm in files:
                pool.spawn(upload_file_task, file_itm)  # Spawn each file upload as a greenlet
            pool.waitall()
            print("Concurrent file uploads finished now processing...")
            # print('Completed embedding data into vec db...')
        
        async def async_upload_files_concurrently():
            await asyncio.to_thread(_upload_files_concurrently)
        
        async def _process_files():
            try:
                print('starting to process files...')
                time.sleep(2)
                file_processor = SCPFileProcessor(socket_manager, req_id, instant_file_names=file_names_arr)
                await file_processor.process_files()
            except Exception as e:
                print(f"Error during file processing: {e}")

        # Function to ensure an asyncio event loop exists and run a coroutine
        def ensure_event_loop_and_run(coro):
            try:
                future = asyncio.run_coroutine_threadsafe(coro, main_event_loop)
                return future.result() 
            except RuntimeError as e:
                print(f"Error running coroutine: {e}")
                raise

        # Run the upload tasks with Eventlet's greenlet
        def run_upload_tasks():
            try:
                ensure_event_loop_and_run(async_upload_files_concurrently())
                # Run the asynchronous function safely in the event loop
                ensure_event_loop_and_run(_process_files())
            except Exception as e:
                print(f"Error during file upload processing: {e}")

        run_upload_tasks()
        
        if existing_req:
            existing_req_names = json.loads(existing_req["name"])
            new_names = file_names_arr + existing_req_names
            Requirement.update(req_id, name=json.dumps(new_names))
        else:
            Requirement.update(req_id, name=json.dumps(file_names_arr))
            

        if all_success:
            message = "All files successfully uploaded"
        elif all_failed:
            message = "All files failed to upload"
        else:
            message = "One or more files failed to upload"
        
        print(f"Upload process complete: {message}")
                
        return {'request_id': req_id, 'message' : message, 'result' : result}
    except Exception as e:
        print(e)



# Function to ensure an asyncio event loop exists and run a coroutine
def ensure_event_loop_and_run(coro):
    try:
        future = asyncio.run_coroutine_threadsafe(coro, main_event_loop)
        return future.result() 
    except RuntimeError as e:
        print(f"Error running coroutine: {e}")
        raise
                
# Maximum retry attempts
MAX_RETRIES = 5

# Your existing logic to handle the creation
def create_requirement_with_retry(req_id, file_names_arr, package_id):
    retries = 0
    while retries < MAX_RETRIES:
        try:
            create_kwargs = {
                'id': req_id,
                'name': json.dumps(file_names_arr),
                'project_id': package_id,
                'type': 'cbp',
                'file_type': 'pdf',
                'criteria': ""
            }
            print('creating requirement...')
            Requirement.create(**create_kwargs)
            print("Requirement created successfully.")
            break  # Exit the loop if the creation is successful
        except Exception as e:
            retries += 1
            print(f"Failed to create requirement, attempt {retries}/{MAX_RETRIES}. Error: {e}")
            if retries < MAX_RETRIES:
                print("Retrying in 1 second...")
                time.sleep(1)
            else:
                print("Max retries reached. Could not create the requirement.")


if __name__ == '__main__':
    app.run(port=5130, host='0.0.0.0', debug=True)