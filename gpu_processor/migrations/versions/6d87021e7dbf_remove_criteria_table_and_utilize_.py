"""remove criteria table and utilize category table

Revision ID: 6d87021e7dbf
Revises: c4491c3d6ab4
Create Date: 2024-08-22 00:02:38.958563

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '6d87021e7dbf'
down_revision = 'c4491c3d6ab4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('criteria')
    with op.batch_alter_table('categories', schema=None) as batch_op:
        batch_op.add_column(sa.Column('criteria', sa.Text(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('categories', schema=None) as batch_op:
        batch_op.drop_column('criteria')

    op.create_table('criteria',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('description', mysql.VARCHAR(length=1024), nullable=False),
    sa.Column('category_id', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['category_id'], ['categories.id'], name='criteria_ibfk_1'),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    # ### end Alembic commands ###
