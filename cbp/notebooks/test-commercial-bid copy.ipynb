{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "import asyncio\n", "import logging\n", "from typing import List, Dict, Any, Optional, Tuple\n", "from concurrent.futures import ThreadPoolExecutor\n", "from pydantic import BaseModel, Field\n", "\n", "from ....services.embeddings.vectore_database_service import VectorDatabaseService\n", "from ....services.llm.claude import AnthropicService\n", "from cbp.services.llm.claude import AnthropicService\n", "from .utils import *\n", "from models import CbpRequirementTechnicalReport\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "# Data Models\n", "class QueryResult(BaseModel):\n", "    \"\"\"Schema for individual query-answer pairs.\"\"\"\n", "    query: str = Field(..., description=\"The query text\")\n", "    answer: str = Field(..., description=\"The answer text or N/A if not found\")\n", "\n", "class AnalysisResult(BaseModel):\n", "    \"\"\"Sc<PERSON>a for the full analysis response.\"\"\"\n", "    queries: list[QueryResult] = Field(..., description=\"List of query-answer pairs\")\n", "\n", "class SearchResponse(BaseModel):\n", "    \"\"\"Schema for vector search results.\"\"\"\n", "    success: bool = Field(default=True, description=\"Whether the search was successful\")\n", "    results: list = Field(..., description=\"The search results text\")\n", "\n", "class AIResponse(BaseModel):\n", "    \"\"\"Schema for AI response.\"\"\"\n", "    success: bool = Field(default=True, description=\"Whether the AI processing was successful\")\n", "    results: Optional[AnalysisResult] = Field(None, description=\"The structured analysis results\")\n", "\n", "# Vector Search Service Layer\n", "class VectorSearchLayer:\n", "    def __init__(self):\n", "        self.vector_service = VectorDatabaseService()\n", "    \n", "    async def search(self, query: str, request_id: str, num_results: int = 5) -> SearchResponse:\n", "        \"\"\"Execute vector search for a query.\"\"\"\n", "        try:\n", "            results = await self.vector_service._execute_gpu_search(\n", "                search_queries=[query], \n", "                request_id=request_id, \n", "                num_results=num_results\n", "            )\n", "            return SearchResponse(success=True, results=results)\n", "        except Exception as e:\n", "            logger.error(f\"Vector search failed: {str(e)}\")\n", "            return SearchResponse(success=False, results=\"\")\n", "\n", "# AI Processing Service Layer\n", "class AIProcessingLayer:\n", "    def __init__(self):\n", "        self.llm_service = AnthropicService()\n", "    \n", "    async def process_queries(self, document: str, formatted_queries: str) -> AIResponse:\n", "        \"\"\"Process queries using the AI service.\"\"\"\n", "        prompt = self._construct_prompt(document, formatted_queries)\n", "        \n", "        messages = [{\"role\": \"user\", \"content\": prompt}]\n", "        \n", "        response, success = await self.llm_service.generate_structured(\n", "            messages=messages,\n", "            model_name=\"Claude 3.5 Sonnet\",\n", "            temperature=0.005,\n", "            use_fallback=False,\n", "            max_tokens=4096,\n", "            schema=AnalysisResult\n", "        )\n", "        \n", "        if not success:\n", "            logger.error(\"Failed to generate response from <PERSON>\")\n", "            return AIResponse(success=False)\n", "        \n", "        return AIResponse(success=True, results=response)\n", "    \n", "    def _construct_prompt(self, document: str, formatted_queries: str) -> str:\n", "        \"\"\"Construct the prompt for the AI service.\"\"\"\n", "        return f\"\"\"Here is a document to analyze:\n", "\n", "DOCUMENT:\n", "{document}\n", "\n", "Please answer the following queries about this document:\n", "\n", "QUERIES:\n", "{formatted_queries}\n", "\n", "INSTRUCTIONS:\n", "Your task is to extract precise information from the provided document based on the queries. Please follow these guidelines carefully:\n", "\n", "1. For each query:\n", "   - Extract EXACT matches from the document text\n", "   - Do not paraphrase or interpret - use only verbatim text\n", "   - If information is not found, respond with 'N/A'\n", "   - Preserve the original query text exactly as provided\n", "\n", "2. Response Format:\n", "   Provide your response in JSON format with this structure:\n", "   {{\n", "     \"queries\": [\n", "       {{\n", "         \"query\": \"<original query with tags>\",\n", "         \"answer\": \"<exact match from document or N/A>\"\n", "       }},\n", "       ...\n", "     ]\n", "   }}\n", "\n", "3. Query structure explanation:\n", "   Each query contains XML-style tags that define the search parameters:\n", "   - <eq> : Equipment name (e.g., \"BALL MILL\")\n", "   - <cat> : Category within equipment (e.g., \"GENERAL\")\n", "   - <item> : Specific attribute to find\n", "   - <co> : Additional context like units\n", "\n", "4. Search approach:\n", "   - First locate the equipment section\n", "   - Then find the category subsection\n", "   - Finally extract the specific item value\n", "   - Consider units and context from <co> tag\n", "   - Only return information that exactly matches these parameters\n", "\n", "CRITICAL: The document is the sole source of truth. Do not infer, calculate or derive information not explicitly stated. This is extremely important for maintaining data accuracy and integrity.\n", "\n", "Remember to format your entire response as a valid JSON object. NOTHING BEFORE OR AFTER, VALID JSON please\n", "\n", "CRITICAL: RETURN A VALID JSON RESPONSE\n", "\"\"\"\n", "\n", "# Query Processing Service Layer# Query Processing Service Layer\n", "class QueryProcessinLayer:\n", "    def __init__(self, socket_manager=None, requirement_id=None):\n", "        self.socket_manager = socket_manager\n", "        self.requirement_id = requirement_id\n", "        self.vector_search_service = VectorSearchLayer()\n", "        self.ai_processing_service = AIProcessingLayer()\n", "        self.event_name = \"CBP_Technical_Report\"\n", "    \n", "    def add_event(self, requirement_id, event_name, data):\n", "        \"\"\"Emit socket event to client for progress tracking\"\"\"\n", "        print(f\"Adding event {event_name} to room {requirement_id}\")\n", "        if self.socket_manager:\n", "            self.socket_manager.emit_to_client(requirement_id, event_name, data)\n", "\n", "    def format_queries(self, queries_with_tags: List[str]) -> str:\n", "        \"\"\"Format queries with numbering.\"\"\"\n", "        return \"\\n\".join([f\"{i+1}. {query}\" for i, query in enumerate(queries_with_tags)])\n", "    \n", "    async def process_batch(self, queries_with_tags: List[str], queries_without_tags: List[str], request_id: str) -> Dict[str, Any]:\n", "        \"\"\"Process a batch of queries.\"\"\"\n", "        # Join the queries without tags\n", "        queries_str = \", \".join(queries_without_tags)\n", "        logger.info(f\"Processing batch with {len(queries_with_tags)}\")\n", "        \n", "        # Execute search\n", "        search_response = await self.vector_search_service.search(queries_str, request_id)\n", "        if not search_response.success:\n", "            return {\"success\": False, \"batch_results\": []}\n", "        \n", "        # Format queries for AI processing\n", "        formatted_queries = self.format_queries(queries_with_tags)\n", "        \n", "        # Process with AI\n", "        ai_response = await self.ai_processing_service.process_queries(\n", "            search_response.results, \n", "            formatted_queries\n", "        )\n", "        \n", "        if not ai_response.success:\n", "            return {\"success\": False, \"batch_results\": []}\n", "        \n", "        return {\"success\": True, \"batch_results\": ai_response.results}\n", "    \n", "    async def query_processor(self, all_queries_with_tags: List[str], all_queries_without_tags: List[str], \n", "                                request_id: str, batch_size: int = 50) -> Dict[str, Any]:\n", "        \"\"\"Process all queries in batches with parallel execution.\"\"\"\n", "        # Calculate the number of batches\n", "        total_queries = len(all_queries_with_tags)\n", "        num_batches = (total_queries + batch_size - 1) // batch_size  # Ceiling division\n", "        \n", "        logger.info(f\"Processing {total_queries} queries in {num_batches} batches\")\n", "        \n", "        # Send progress update via socket\n", "        if self.socket_manager and self.requirement_id:\n", "            self.add_event(self.requirement_id, 'progress_message', {\n", "                'event_type': self.event_name,\n", "                'request_id': self.requirement_id,\n", "                'data': {\n", "                    'status': 'processing_queries', \n", "                    'message': f\"Processing {total_queries} queries in {num_batches} batches\"\n", "                }\n", "            })\n", "        \n", "        # Create batches\n", "        batch_tasks = []\n", "        for i in range(num_batches):\n", "            start_idx = i * batch_size\n", "            end_idx = min((i + 1) * batch_size, total_queries)\n", "            \n", "            batch_with_tags = all_queries_with_tags[start_idx:end_idx]\n", "            batch_without_tags = all_queries_without_tags[start_idx:end_idx]\n", "            \n", "            batch_tasks.append(self.process_batch(batch_with_tags, batch_without_tags, request_id))\n", "        \n", "        # Execute all batches in parallel\n", "        batch_results = await asyncio.gather(*batch_tasks)\n", "        \n", "        # Aggregate results\n", "        all_results = []\n", "        for batch_result in batch_results:\n", "            if batch_result[\"success\"]:\n", "                all_results.extend(batch_result[\"batch_results\"].queries)\n", "        \n", "        # Return aggregated results\n", "        return {\n", "            \"success\": True,\n", "            \"aggregated_results\": all_results\n", "        }\n", "\n", "    # Main Application Layer\n", "    async def process_multiple_bids(self, equipment_list: list, bid_ids: list):\n", "        \"\"\"\n", "        Process multiple equipment across multiple bid IDs\n", "        \n", "        Args:\n", "            requirement_id: ID of the requirement being processed\n", "            equipment_list: List of equipment dictionaries\n", "            bid_ids: List of bid IDs to process for each equipment\n", "            \n", "        Returns:\n", "            Dictionary with bid IDs as keys and their respective equipment results as values\n", "        \"\"\"\n", "        try:\n", "            requirement_id = self.requirement_id\n", "            all_bid_results = {}\n", "            \n", "            # Update requirement status to pending\n", "            CbpRequirementTechnicalReport.update(requirement_id, status='pending')\n", "            \n", "            # Notify client that processing has started\n", "            self.add_event(requirement_id, 'progress_message', {\n", "                'event_type': self.event_name,\n", "                'request_id': requirement_id,\n", "                'data': {'status': 'processing_started', 'message': f\"Processing {len(bid_ids)} bids for {len(equipment_list)} equipment items\"}\n", "            })\n", "            \n", "            for i, bid_id in enumerate(bid_ids):\n", "                logger.info(f\"Processing bid ID: {bid_id} ({i+1}/{len(bid_ids)})\")\n", "                \n", "                # Update socket with current bid progress\n", "                self.add_event(requirement_id, 'progress_message', {\n", "                    'event_type': self.event_name,\n", "                    'request_id': requirement_id,\n", "                    'data': {'status': 'processing_bid', 'message': f\"Processing bid {i+1} of {len(bid_ids)}: {bid_id}\"}\n", "                })\n", "                \n", "                # Process all equipment for this bid ID\n", "                bid_results = await self.process_multi_equipment(equipment_list, bid_id)\n", "                \n", "                # Store results for this bid ID\n", "                all_bid_results[bid_id] = bid_results\n", "                \n", "                logger.info(f\"Completed processing for bid ID: {bid_id}\")\n", "            \n", "            # All processing completed, update requirement to completed\n", "            CbpRequirementTechnicalReport.update(requirement_id, status='done', result=all_bid_results)\n", "            \n", "            # Send final completion event\n", "            \n", "            '''output_file_path = f\"bid_results_{requirement_id}.json\"\n", "            with open(output_file_path, 'w') as json_file:\n", "                json.dump(all_bid_results, json_file, indent=4)'''\n", "\n", "            self.add_event(requirement_id, 'progress_message', {\n", "                'event_type': self.event_name,\n", "                'request_id': requirement_id,\n", "                'data': {\n", "                    'status': 'completed', \n", "                    'message': f\"Completed processing all bids for all equipments,Total of {len(all_bid_results)}\"\n", "                }\n", "            })\n", "            \n", "            return all_bid_results\n", "            \n", "        except Exception as e:\n", "            logger.error(f\"Error processing multiple bids: {str(e)}\")\n", "            # Update requirement as failed\n", "            CbpRequirementTechnicalReport.update(requirement_id, status='idle', error=str(e))\n", "            \n", "            # Notify client of failure\n", "            self.add_event(requirement_id, 'progress_message', {\n", "                'event_type': self.event_name,\n", "                'request_id': requirement_id,\n", "                'data': {'status': 'failed', 'message': f\"Processing failed: {str(e)}\"}\n", "            })\n", "            \n", "            raise e  # Re-raise the exception\n", "\n", "    async def process_multi_equipment(self, equipment_list: list, bid_id: str):\n", "        \"\"\"\n", "        Process multiple equipment for a single bid ID\n", "        \n", "        Args:\n", "            equipment_list: List of equipment dictionaries\n", "            bid_id: Bid ID to process\n", "            \n", "        Returns:\n", "            List of results for each equipment\n", "        \"\"\"\n", "        final_result_all = []\n", "        \n", "        # Notify beginning of source extraction\n", "        self.add_event(self.requirement_id, 'progress_message', {\n", "            'event_type': self.event_name,\n", "            'request_id': self.requirement_id,\n", "            'data': {'status': 'extracting_sources', 'message': f\"Extracting sources for bid {bid_id}\"}\n", "        })\n", "        \n", "        total_equipment = len(equipment_list)\n", "        for idx, equipment in enumerate(equipment_list):\n", "            try:\n", "                equipment_name = equipment.get(\"equipment_name\", \"Unknown\")\n", "                logger.info(f\"Processing equipment {idx+1}/{total_equipment}: {equipment_name}\")\n", "                \n", "                # Update socket with current equipment progress\n", "                self.add_event(self.requirement_id, 'progress_message', {\n", "                    'event_type': self.event_name,\n", "                    'request_id': self.requirement_id,\n", "                    'data': {\n", "                        'status': 'processing_equipment', \n", "                        'message': f\"Processing equipment {idx+1}/{total_equipment}: {equipment_name}\"\n", "                    }\n", "                })\n", "                \n", "                # Initialize status as failed by default\n", "                equipment_result = {\n", "                    \"equipment\": equipment_name,\n", "                    \"status\": \"failed\",\n", "                    \"data\": None,\n", "                    \"bid_id\": bid_id  # Include bid ID in results for traceability\n", "                }\n", "\n", "                # Construct queries\n", "                queries_with_tags, queries_without_tags = construct_queries(equipment)\n", "                \n", "                # Process all queries - first pass\n", "                first_result = await self.query_processor(\n", "                    queries_with_tags, \n", "                    queries_without_tags, \n", "                    request_id=bid_id,  # Using bid_id as the request_id parameter\n", "                    batch_size=50\n", "                )\n", "                \n", "                if not first_result[\"success\"]:\n", "                    logger.error(f\"Failed to process queries in the first pass for equipment: {equipment_name} with bid ID: {bid_id}\")\n", "                    final_result_all.append(equipment_result)\n", "                    continue\n", "                    \n", "                # Convert to list of dictionaries\n", "                first_pass_results = [{\"query\": query_result.query, \"answer\": query_result.answer} \n", "                                    for query_result in first_result[\"aggregated_results\"]]\n", "                first_pass_results_non_na = [entry for entry in first_pass_results if entry.get(\"answer\") != \"N/A\"]\n", "                \n", "                # Reconstruct the results with answers\n", "                result_reconstructed_initial = reconstruct_samples(first_pass_results, add_answer=True)\n", "                result_reconstructed_initial_non_na = reconstruct_samples(first_pass_results_non_na, add_answer=True)\n", "                \n", "                # Filter for N/A answers\n", "                na_queries = [entry for entry in first_pass_results if entry.get(\"answer\") == \"N/A\"]\n", "                \n", "                # If we have N/A queries, do a second pass\n", "                if na_queries:\n", "                    logger.info(f\"Found {len(na_queries)} queries with N/A answers for bid ID {bid_id}. Running second pass.\")\n", "                    \n", "                    # Update socket for second pass\n", "                    self.add_event(self.requirement_id, 'progress_message', {\n", "                        'event_type': self.event_name,\n", "                        'request_id': self.requirement_id,\n", "                        'data': {\n", "                            'status': 'second_pass', \n", "                            'message': f\"Running second pass for {len(na_queries)} unanswered queries\"\n", "                        }\n", "                    })\n", "                    \n", "                    # Reconstruct the N/A queries without answers\n", "                    na_queries_reconstructed = reconstruct_samples(na_queries, add_answer=False)\n", "                    \n", "                    # Construct queries for second pass\n", "                    na_queries_with_tags, na_queries_without_tags = construct_queries(na_queries_reconstructed)\n", "                    \n", "                    # Process N/A queries in second pass\n", "                    second_result = await self.query_processor(\n", "                        na_queries_with_tags, \n", "                        na_queries_without_tags, \n", "                        request_id=bid_id,  # Using bid_id as the request_id parameter\n", "                        batch_size=50\n", "                    )\n", "                    \n", "                    if not second_result[\"success\"]:\n", "                        logger.error(f\"Failed to process queries in the second pass for equipment: {equipment_name} with bid ID: {bid_id}\")\n", "                        # Still include first pass results as they're valid\n", "                        equipment_result[\"data\"] = result_reconstructed_initial\n", "                        equipment_result[\"status\"] = \"partial_success\"  # Partial success since first pass worked\n", "                        final_result_all.append(equipment_result)\n", "                        continue\n", "                    \n", "                    # Convert second pass results to list of dictionaries\n", "                    second_pass_results = [{\"query\": query_result.query, \"answer\": query_result.answer} \n", "                                        for query_result in second_result[\"aggregated_results\"]]\n", "                    \n", "                    # Reconstruct second pass results with answers\n", "                    result_reconstructed_second = reconstruct_samples(second_pass_results, add_answer=True)\n", "                    \n", "                    # Merge results from both passes\n", "                    final_result = merge_equipment_lists(result_reconstructed_initial_non_na, result_reconstructed_second)\n", "                    \n", "                    logger.info(f\"Successfully merged results from both passes for equipment: {equipment_name} with bid ID: {bid_id}\")\n", "                    \n", "                    # Set success status and data\n", "                    equipment_result[\"status\"] = \"success\"\n", "                    equipment_result[\"data\"] = final_result\n", "                else:\n", "                    logger.info(f\"No N/A answers found for equipment: {equipment_name} with bid ID: {bid_id}. Using first pass results.\")\n", "                    \n", "                    # Set success status and data\n", "                    equipment_result[\"status\"] = \"success\"\n", "                    equipment_result[\"data\"] = result_reconstructed_initial\n", "                \n", "                final_result_all.append(equipment_result)\n", "            \n", "            except Exception as e:\n", "                equipment_name = equipment.get(\"equipment_name\", \"Unknown\")\n", "                logger.error(f\"Error processing equipment {equipment_name} with bid ID: {bid_id}: {str(e)}\")\n", "                \n", "                # Notify of equipment processing error\n", "                self.add_event(self.requirement_id, 'progress_message', {\n", "                    'event_type': self.event_name,\n", "                    'request_id': self.requirement_id,\n", "                    'data': {\n", "                        'status': 'equipment_error', \n", "                        'message': f\"Error processing equipment {equipment_name}: {str(e)}\"\n", "                    }\n", "                })\n", "                \n", "                final_result_all.append({\n", "                    \"equipment\": equipment.get(\"equipment_name\", \"Unknown\"),\n", "                    \"status\": \"failed\",\n", "                    \"data\": None,\n", "                    \"error\": str(e),\n", "                    \"bid_id\": bid_id\n", "                })\n", "\n", "        # Notify completion of processing for this bid\n", "        self.add_event(self.requirement_id, 'progress_message', {\n", "            'event_type': self.event_name,\n", "            'request_id': self.requirement_id,\n", "            'data': {\n", "                'status': 'bid_processed', \n", "                'message': f\"Completed processing bid {bid_id}\",\n", "                'bid_results': final_result_all\n", "            }\n", "        })\n", "\n", "        return final_result_all\n", "\n", "\n", "\n", "\n", "if __name__ == \"__main__\":\n", "\n", "    sample_equipment_list = [{\n", "    \"equipment_name\": \"BALL MILL\",\n", "    \"categories\": [\n", "        {\n", "        \"name\": \"GENERAL\",\n", "        \"items\": [\n", "            {\"name\": \"Mill Type\", \"unit\": \"\", \"discipline\": \"MECHANICAL\"},\n", "            {\"name\": \"Mill ID\", \"unit\": \"\", \"discipline\": \"MECHANICAL\"},\n", "            {\"name\": \"Mill Length (Effective Grinding Length)\", \"unit\": \"\", \"discipline\": \"MECHANICAL\"},\n", "            {\"name\": \"Mill Speed\", \"unit\": \"rpm\", \"discipline\": \"MECHANICAL\"},\n", "            {\"name\": \"N.C.\", \"unit\": \"\", \"discipline\": \"MECHANICAL\"},\n", "            {\"name\": \"Power Consumption\", \"unit\": \"kw (hp)\", \"discipline\": \"MECHANICAL\"},\n", "            {\"name\": \"Pinion power draw at:\", \"unit\": \"\", \"discipline\": \"MECHANICAL\"},\n", "            {\"name\": \"New Liners\", \"unit\": \"kw (hp)\", \"discipline\": \"MECHANICAL\"},\n", "            {\"name\": \"Worn Liners\", \"unit\": \"kw (hp)\", \"discipline\": \"MECHANICAL\"}\n", "        ]\n", "        },\n", "        {\n", "        \"name\": \"MILL SHELL\",\n", "        \"items\": [\n", "            {\"name\": \"Drilling\", \"unit\": \"\", \"discipline\": \"MECHANICAL\"},\n", "            {\"name\": \"Drilling Method\", \"unit\": \"\", \"discipline\": \"MECHANICAL\"},\n", "            {\"name\": \"Shell Material\", \"unit\": \"\", \"discipline\": \"MECHANICAL\"},\n", "            {\"name\": \"Flange Material\", \"unit\": \"\", \"discipline\": \"MECHANICAL\"},\n", "            {\"name\": \"Shell Thickness\", \"unit\": \"mm\", \"discipline\": \"MECHANICAL\"},\n", "            {\"name\": \"<PERSON><PERSON><PERSON>\", \"unit\": \"mm\", \"discipline\": \"MECHANICAL\"},\n", "            {\"name\": \"Number of Manholes\", \"unit\": \"\", \"discipline\": \"MECHANICAL\"}\n", "        ]\n", "        },\n", "        {\n", "        \"name\": \"MILL HEADS\",\n", "        \"items\": [\n", "            {\"name\": \"MILL HEADS\", \"unit\": \"\", \"discipline\": \"MECHANICAL\"},\n", "            {\"name\": \"Material\", \"unit\": \"\", \"discipline\": \"MECHANICAL\"},\n", "            {\"name\": \"Thickness at Trunnion Bearing\", \"unit\": \"mm\", \"discipline\": \"MECHANICAL\"},\n", "            {\"name\": \"Head Mass (KG)\", \"unit\": \"kg\", \"discipline\": \"MECHANICAL\"},\n", "            {\"name\": \"Trunnion Mass (KG)\", \"unit\": \"kg\", \"discipline\": \"MECHANICAL\"}\n", "        ]\n", "        },\n", "        {\n", "        \"name\": \"GIRTH GEAR\",\n", "        \"items\": [\n", "            {\"name\": \"Country of Origin\", \"unit\": \"\", \"discipline\": \"ELECTRICAL\"},\n", "            {\"name\": \"Supplier\", \"unit\": \"\", \"discipline\": \"ELECTRICAL\"},\n", "            {\"name\": \"Diameter\", \"unit\": \"mm\", \"discipline\": \"ELECTRICAL\"},\n", "            {\"name\": \"Face Width\", \"unit\": \"mm\", \"discipline\": \"ELECTRICAL\"},\n", "            {\"name\": \"Number of Segments\", \"unit\": \"\", \"discipline\": \"ELECTRICAL\"},\n", "            {\"name\": \"Gear Type\", \"unit\": \"\", \"discipline\": \"ELECTRICAL\"},\n", "            {\"name\": \"No. of Teeth\", \"unit\": \"\", \"discipline\": \"ELECTRICAL\"},\n", "            {\"name\": \"Material\", \"unit\": \"\", \"discipline\": \"ELECTRICAL\"},\n", "            {\"name\": \"AGMA\", \"unit\": \"\", \"discipline\": \"ELECTRICAL\"},\n", "            {\"name\": \"Surface Finish\", \"unit\": \"µm\", \"discipline\": \"ELECTRICAL\"},\n", "            {\"name\": \"Service Factor: Durability\", \"unit\": \"\", \"discipline\": \"ELECTRICAL\"},\n", "            {\"name\": \"Service Factor: Strength\", \"unit\": \"\", \"discipline\": \"ELECTRICAL\"},\n", "            {\"name\": \"Hardness\", \"unit\": \"HB\", \"discipline\": \"ELECTRICAL\"},\n", "            {\"name\": \"Reversible\", \"unit\": \"yes/no\", \"discipline\": \"ELECTRICAL\"},\n", "            {\"name\": \"Mass (KG)\", \"unit\": \"kg\", \"discipline\": \"ELECTRICAL\"},\n", "            {\"name\": \"Module\", \"unit\": \"mm\", \"discipline\": \"ELECTRICAL\"},\n", "            {\"name\": \"Guard supplier\", \"unit\": \"\", \"discipline\": \"ELECTRICAL\"}\n", "        ]\n", "        }\n", "    ]\n", "    }]\n", "    '''request_id = \"62b432fe-72a1-4a13-9baa-799253d554df\"\n", "    from cbp.services.embeddings.vectore_database_service import VectorDatabaseService\n", "\n", "    async def main():\n", "        vs = VectorDatabaseService()\n", "        res = await vs._execute_gpu_search(search_queries=[\"Hello\"], num_results=3, request_id=request_id)\n", "        print(res)\n", "    \n", "    asyncio.run(main())'''\n", "    \n", "    bids_ids = [\"62b432fe-72a1-4a13-9baa-799253d554df\"]\n", "    requirement_id = \"test\"\n", "    processor = QueryProcessinLayer()\n", "    results = asyncio.run(processor.process_multiple_bids(bid_ids=bids_ids,equipment_list=sample_equipment_list,requirement_id=requirement_id))\n", "    print(f\"Final results: {results}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install gevent"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import asyncio\n", "import logging\n", "from typing import List, Dict, Any, Optional, Tuple\n", "from concurrent.futures import ThreadPoolExecutor\n", "from pydantic import BaseModel, Field\n", "\n", "from cbp.services.embeddings.vectore_database_service import VectorDatabaseService\n", "\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "# Data Models\n", "class QueryResult(BaseModel):\n", "    \"\"\"Schema for individual query-answer pairs.\"\"\"\n", "    query: str = Field(..., description=\"The query text\")\n", "    answer: str = Field(..., description=\"The answer text or N/A if not found\")\n", "\n", "class AnalysisResult(BaseModel):\n", "    \"\"\"Sc<PERSON>a for the full analysis response.\"\"\"\n", "    queries: list[QueryResult] = Field(..., description=\"List of query-answer pairs\")\n", "\n", "class SearchResponse(BaseModel):\n", "    \"\"\"Schema for vector search results.\"\"\"\n", "    success: bool = Field(default=True, description=\"Whether the search was successful\")\n", "    results: list = Field(..., description=\"The search results text\")\n", "\n", "class AIResponse(BaseModel):\n", "    \"\"\"Schema for AI response.\"\"\"\n", "    success: bool = Field(default=True, description=\"Whether the AI processing was successful\")\n", "    results: Optional[AnalysisResult] = Field(None, description=\"The structured analysis results\")\n", "\n", "# Vector Search Service Layer\n", "class VectorSearchLayer:\n", "    def __init__(self):\n", "        self.vector_service = VectorDatabaseService()\n", "    \n", "    async def search(self, query: str, request_id: str, num_results: int = 5) -> SearchResponse:\n", "        \"\"\"Execute vector search for a query.\"\"\"\n", "        try:\n", "            results = await self.vector_service._execute_gpu_search(\n", "                search_queries=[query], \n", "                request_id=request_id, \n", "                num_results=num_results\n", "            )\n", "            return SearchResponse(success=True, results=results)\n", "        except Exception as e:\n", "            logger.error(f\"Vector search failed: {str(e)}\")\n", "            return SearchResponse(success=False, results=\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from cbp.services.llm.fastllm import LLMOrchestrator\n", "import logging  # Import logging module\n", "\n", "if __name__ == \"__main__\":\n", "    # Example usage with logging configuration\n", "    logging.basicConfig(level=logging.INFO)\n", "    logger = logging.getLogger(__name__)  # Create a logger instance\n", "    \n", "    queries = [\n", "        \"What is the mill type?\",\n", "        \"What is the mill ID?\",\n", "        \"What is the effective grinding length?\",\n", "        # Add more queries as needed\n", "    ]\n", "    \n", "    results = [\n", "        \"Trunnion Supported\",\n", "        \"10.37 m\",\n", "        \"5.8 m\",\n", "        # Add corresponding results for each query\n", "    ]\n", "    \n", "    # Prepare messages for LLM\n", "    test_messages = []\n", "    for query, result in zip(queries, results):\n", "        message = {\n", "            \"role\": \"user\",\n", "            \"content\": f\"Query: {query}\\nResult: {result}\\nPlease provide the correct answer or 'N/A' if not found.\"\n", "        }\n", "        test_messages.append(message)\n", "\n", "    llm_orchestrator = LLMOrchestrator()\n", "    if completion := llm_orchestrator.generate_completion(model=\"llama3-8b-819\",messages=test_messages):\n", "        logger.info(f\"Generated Text: {completion}\")\n", "    else:\n", "        logger.error(\"Failed to generate completion from all providers\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from cbp.services.llm.fastllm import LLMOrchestrator\n", "import logging  # Import logging module\n", "\n", "if __name__ == \"__main__\":\n", "    # Example usage with logging configuration\n", "    logging.basicConfig(level=logging.INFO)\n", "    logger = logging.getLogger(__name__)  # Create a logger instance\n", "    \n", "    queries = [\n", "        \"What is the mill type?\",\n", "        \"What is the mill ID?\",\n", "        \"What is the effective grinding length?\",\n", "        # Add more queries as needed\n", "    ]\n", "    \n", "    results = [\n", "        \"Trunnion Supported\",\n", "        \"10.37 m\",\n", "        \"5.8 m\",\n", "        # Add corresponding results for each query\n", "    ]\n", "    \n", "    # Prepare messages for LLM\n", "    test_messages = []\n", "    for query, result in zip(queries, results):\n", "        message = {\n", "            \"role\": \"user\",\n", "            \"content\": f\"Query: {query}\\nResult: {result}\\nPlease provide the correct answer or 'N/A' if not found.\"\n", "        }\n", "        test_messages.append(message)\n", "\n", "    llm_orchestrator = LLMOrchestrator()\n", "    if completion := llm_orchestrator.generate_completion(model=\"llama3-8b-819\",messages=test_messages):\n", "        logger.info(f\"Generated Text: {completion}\")\n", "    else:\n", "        logger.error(\"Failed to generate completion from all providers\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["from cbp.services.llm.fastllm import LLMOrchestrator\n", "import logging  # Import logging module\n", "\n", "if __name__ == \"__main__\":\n", "    # Example usage with logging configuration\n", "    logging.basicConfig(level=logging.INFO)\n", "    logger = logging.getLogger(__name__)  # Create a logger instance\n", "    \n", "    queries = [\n", "        \"What is the mill type?\",\n", "        \"What is the mill ID?\",\n", "        \"What is the effective grinding length?\",\n", "        # Add more queries as needed\n", "    ]\n", "    \n", "    results = [\n", "        \"Trunnion Supported\",\n", "        \"10.37 m\",\n", "        \"5.8 m\",\n", "        # Add corresponding results for each query\n", "    ]\n", "    \n", "    # Prepare messages for LLM\n", "    test_messages = []\n", "    for query, result in zip(queries, results):\n", "        message = {\n", "            \"role\": \"user\",\n", "            \"content\": f\"Query: {query}\\nResult: {result}\\nPlease provide the correct answer or 'N/A' if not found.\"\n", "        }\n", "        test_messages.append(message)\n", "\n", "    llm_orchestrator = LLMOrchestrator()\n", "    if completion := llm_orchestrator.generate_completion(model=\"llama3-8b-819\",messages=test_messages):\n", "        logger.info(f\"Generated Text: {completion}\")\n", "    else:\n", "        logger.error(\"Failed to generate completion from all providers\")\n", "\n", "vd = VectorSearchLayer()\n", "rid = \"19836902-9451-4e3f-b4ab-7ac26915e24c\"\n", "query = '''\n", "price table pricing chart cost listing price sheet rate card\n", "pricing comparison table equipment rates price breakdown\n", "prices listed price column cost column amount column quantity column \"table row\" \"table cell\"\n", "\"item\" \"description\" \"quantity\" \"rate\" \"amount\" \"subtotal\" \"total\" \"price each\"\n", "$50 $199.99 $1,500 €120 £75 ¥1500 ₹2500 ₩5000 A$200 C$150 \"USD\" \"EUR\" \"JPY\" \"GBP\"\n", "\"per unit\" \"per piece\" \"per item\" \"per box\" \"per case\" \"per pallet\" \"per kg\" \"per lb\"\n", "\"units\" \"pieces\" \"items\" \"boxes\" \"pallets\" \"sets\" \"kits\" \"bundles\" \"packages\"\n", "\"quantity: 100\" \"qty: 25\" \"qty\" \"min. order\" \"minimum quantity\" \"max quantity\"\n", "\"price without units\" \"currency not specified\" \"units not specified\" \"price varies\"\n", "\"call for pricing\" \"price on request\" \"contact for quote\" \"price TBD\" \"market price\"\n", "\"price range\" \"estimated cost\" \"approximate price\" \"price tier\" \"volume discount\"\n", "\"MSRP\" \"list price\" \"net price\" \"price without tax\" \"price with tax included\" \"plus tax\"\n", "'''\n", "response = await vd.search(query=query, request_id=rid, num_results=5)"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Processing bid ID: a351c33d-9d9a-4afb-a40d-ca9286c558a0\n", "INFO:__main__:Extracting prices for Ball Mill (ID: eq001) in bid a351c33d-9d9a-4afb-a40d-ca9286c558a0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "INFO:cbp.services.llm.claude:Token Usage - Input: 6064, Output: 382, Total: 6446\n", "INFO:__main__:EXTRACTED DATA: [\n", "    {\n", "        \"item\": \"Ball Mill - 22 x 34, 1 x 9000kW WRIM\",\n", "        \"price\": \"$4988371\"\n", "    },\n", "    {\n", "        \"item\": \"Ball Mill Liners\",\n", "        \"price\": \"$332013\"\n", "    },\n", "    {\n", "        \"item\": \"Ball Mill Retractable Feed Chute\",\n", "        \"price\": \"$73673\"\n", "    },\n", "    {\n", "        \"item\": \"Ball Mill Trommel Screen\",\n", "        \"price\": \"$104975\"\n", "    },\n", "    {\n", "        \"item\": \"Ball Mill Lube System\",\n", "        \"price\": \"$338895\"\n", "    },\n", "    {\n", "        \"item\": \"Ball Mill Gear Spray System\",\n", "        \"price\": \"$17061\"\n", "    },\n", "    {\n", "        \"item\": \"Ball Mill Motor (WRIM including bedplates and lube system)\",\n", "        \"price\": \"$658031\"\n", "    },\n", "    {\n", "        \"item\": \"Ball Mill LRS (For WRIM)\",\n", "        \"price\": \"$102688\"\n", "    },\n", "    {\n", "        \"item\": \"Ball Mill Commissioning Spares\",\n", "        \"price\": \"$4931\"\n", "    },\n", "    {\n", "        \"item\": \"Ball Mill One (1) Year Spares\",\n", "        \"price\": \"$33621\"\n", "    },\n", "    {\n", "        \"item\": \"Ball Mill Capital Spares\",\n", "        \"price\": \"$2428700\"\n", "    },\n", "    {\n", "        \"item\": \"Ball Mill First Fills\",\n", "        \"price\": \"$46530\"\n", "    }\n", "]\n", "INFO:__main__:Successfully extracted 12 items for Ball Mill\n", "INFO:__main__:Extracting prices for AG Mill (ID: eq002) in bid a351c33d-9d9a-4afb-a40d-ca9286c558a0\n", "INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "INFO:cbp.services.llm.claude:Token Usage - Input: 6064, Output: 385, Total: 6449\n", "INFO:__main__:EXTRACTED DATA: [\n", "    {\n", "        \"item\": \"Primary (AG) Mill - 32 x 21, 2 x 6000kW SCIM\",\n", "        \"price\": \"7769217\"\n", "    },\n", "    {\n", "        \"item\": \"Primary Mill Liners\",\n", "        \"price\": \"1086765\"\n", "    },\n", "    {\n", "        \"item\": \"Primary Mill Retractable Feed Chute\",\n", "        \"price\": \"125440\"\n", "    },\n", "    {\n", "        \"item\": \"Primary Mill Lube System\",\n", "        \"price\": \"446498\"\n", "    },\n", "    {\n", "        \"item\": \"Primary Mill Gear Spray System\",\n", "        \"price\": \"27953\"\n", "    },\n", "    {\n", "        \"item\": \"Primary Mill Motors (SCIM including bedplates and lube system)\",\n", "        \"price\": \"895280\"\n", "    },\n", "    {\n", "        \"item\": \"Primary Mill VFD (for SCIM)\",\n", "        \"price\": \"1015799\"\n", "    },\n", "    {\n", "        \"item\": \"Primary Mill Commissioning Spares\",\n", "        \"price\": \"4931\"\n", "    },\n", "    {\n", "        \"item\": \"Primary Mill One (1) Year Spares\",\n", "        \"price\": \"53793\"\n", "    },\n", "    {\n", "        \"item\": \"Primary Mill Capital Spares\",\n", "        \"price\": \"2028929\"\n", "    },\n", "    {\n", "        \"item\": \"Primary Mill First Fills\",\n", "        \"price\": \"50960\"\n", "    },\n", "    {\n", "        \"item\": \"Primary Mill Special Tools\",\n", "        \"price\": \"75852\"\n", "    }\n", "]\n", "INFO:__main__:Successfully extracted 12 items for AG Mill\n", "INFO:__main__:Extracting prices for Crusher (ID: eq003) in bid a351c33d-9d9a-4afb-a40d-ca9286c558a0\n", "INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "INFO:cbp.services.llm.claude:Token Usage - Input: 6066, Output: 289, Total: 6355\n", "INFO:__main__:EXTRACTED DATA: [\n", "    {\"item\": \"Primary (AG) Mill - 32 x 21, 2 x 6000kW SCIM\", \"price\": \"$7769217\"},\n", "    {\"item\": \"Primary Mill Liners\", \"price\": \"$1086765\"},\n", "    {\"item\": \"Primary Mill Retractable Feed Chute\", \"price\": \"$125440\"},\n", "    {\"item\": \"Primary Mill Lube System\", \"price\": \"$446498\"},\n", "    {\"item\": \"Primary Mill Gear Spray System\", \"price\": \"$27953\"},\n", "    {\"item\": \"Primary Mill Motors (SCIM including bedplates and lube system)\", \"price\": \"$895280\"},\n", "    {\"item\": \"Primary Mill VFD (for SCIM)\", \"price\": \"$1015799\"},\n", "    {\"item\": \"Primary Mill Commissioning Spares\", \"price\": \"$4931\"},\n", "    {\"item\": \"Primary Mill One (1) Year Spares\", \"price\": \"$53793\"},\n", "    {\"item\": \"Primary Mill Capital Spares\", \"price\": \"$2028929\"},\n", "    {\"item\": \"Primary Mill First Fills\", \"price\": \"$50960\"},\n", "    {\"item\": \"Primary Mill Special Tools\", \"price\": \"$75852\"}\n", "]\n", "INFO:__main__:Successfully extracted 12 items for Crusher\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"a351c33d-9d9a-4afb-a40d-ca9286c558a0\": {\n", "    \"eq001\": [\n", "      {\n", "        \"item\": \"Ball Mill - 22 x 34, 1 x 9000kW WRIM\",\n", "        \"price\": \"$4988371\"\n", "      },\n", "      {\n", "        \"item\": \"Ball Mill Liners\",\n", "        \"price\": \"$332013\"\n", "      },\n", "      {\n", "        \"item\": \"Ball Mill Retractable Feed Chute\",\n", "        \"price\": \"$73673\"\n", "      },\n", "      {\n", "        \"item\": \"Ball Mill Trommel Screen\",\n", "        \"price\": \"$104975\"\n", "      },\n", "      {\n", "        \"item\": \"Ball Mill Lube System\",\n", "        \"price\": \"$338895\"\n", "      },\n", "      {\n", "        \"item\": \"Ball Mill Gear Spray System\",\n", "        \"price\": \"$17061\"\n", "      },\n", "      {\n", "        \"item\": \"Ball Mill Motor (WRIM including bedplates and lube system)\",\n", "        \"price\": \"$658031\"\n", "      },\n", "      {\n", "        \"item\": \"Ball Mill LRS (For WRIM)\",\n", "        \"price\": \"$102688\"\n", "      },\n", "      {\n", "        \"item\": \"Ball Mill Commissioning Spares\",\n", "        \"price\": \"$4931\"\n", "      },\n", "      {\n", "        \"item\": \"Ball Mill One (1) Year Spares\",\n", "        \"price\": \"$33621\"\n", "      },\n", "      {\n", "        \"item\": \"Ball Mill Capital Spares\",\n", "        \"price\": \"$2428700\"\n", "      },\n", "      {\n", "        \"item\": \"Ball Mill First Fills\",\n", "        \"price\": \"$46530\"\n", "      }\n", "    ],\n", "    \"eq002\": [\n", "      {\n", "        \"item\": \"Primary (AG) Mill - 32 x 21, 2 x 6000kW SCIM\",\n", "        \"price\": \"7769217\"\n", "      },\n", "      {\n", "        \"item\": \"Primary Mill Liners\",\n", "        \"price\": \"1086765\"\n", "      },\n", "      {\n", "        \"item\": \"Primary Mill Retractable Feed Chute\",\n", "        \"price\": \"125440\"\n", "      },\n", "      {\n", "        \"item\": \"Primary Mill Lube System\",\n", "        \"price\": \"446498\"\n", "      },\n", "      {\n", "        \"item\": \"Primary Mill Gear Spray System\",\n", "        \"price\": \"27953\"\n", "      },\n", "      {\n", "        \"item\": \"Primary Mill Motors (SCIM including bedplates and lube system)\",\n", "        \"price\": \"895280\"\n", "      },\n", "      {\n", "        \"item\": \"Primary Mill VFD (for SCIM)\",\n", "        \"price\": \"1015799\"\n", "      },\n", "      {\n", "        \"item\": \"Primary Mill Commissioning Spares\",\n", "        \"price\": \"4931\"\n", "      },\n", "      {\n", "        \"item\": \"Primary Mill One (1) Year Spares\",\n", "        \"price\": \"53793\"\n", "      },\n", "      {\n", "        \"item\": \"Primary Mill Capital Spares\",\n", "        \"price\": \"2028929\"\n", "      },\n", "      {\n", "        \"item\": \"Primary Mill First Fills\",\n", "        \"price\": \"50960\"\n", "      },\n", "      {\n", "        \"item\": \"Primary Mill Special Tools\",\n", "        \"price\": \"75852\"\n", "      }\n", "    ],\n", "    \"eq003\": [\n", "      {\n", "        \"item\": \"Primary (AG) Mill - 32 x 21, 2 x 6000kW SCIM\",\n", "        \"price\": \"$7769217\"\n", "      },\n", "      {\n", "        \"item\": \"Primary Mill Liners\",\n", "        \"price\": \"$1086765\"\n", "      },\n", "      {\n", "        \"item\": \"Primary Mill Retractable Feed Chute\",\n", "        \"price\": \"$125440\"\n", "      },\n", "      {\n", "        \"item\": \"Primary Mill Lube System\",\n", "        \"price\": \"$446498\"\n", "      },\n", "      {\n", "        \"item\": \"Primary Mill Gear Spray System\",\n", "        \"price\": \"$27953\"\n", "      },\n", "      {\n", "        \"item\": \"Primary Mill Motors (SCIM including bedplates and lube system)\",\n", "        \"price\": \"$895280\"\n", "      },\n", "      {\n", "        \"item\": \"Primary Mill VFD (for SCIM)\",\n", "        \"price\": \"$1015799\"\n", "      },\n", "      {\n", "        \"item\": \"Primary Mill Commissioning Spares\",\n", "        \"price\": \"$4931\"\n", "      },\n", "      {\n", "        \"item\": \"Primary Mill One (1) Year Spares\",\n", "        \"price\": \"$53793\"\n", "      },\n", "      {\n", "        \"item\": \"Primary Mill Capital Spares\",\n", "        \"price\": \"$2028929\"\n", "      },\n", "      {\n", "        \"item\": \"Primary Mill First Fills\",\n", "        \"price\": \"$50960\"\n", "      },\n", "      {\n", "        \"item\": \"Primary Mill Special Tools\",\n", "        \"price\": \"$75852\"\n", "      }\n", "    ]\n", "  }\n", "}\n"]}], "source": ["from cbp.services.llm.fastllm import LLMOrchestrator\n", "from cbp.services.llm.claude import AnthropicService\n", "from typing import List, Dict, Any\n", "import logging\n", "import json\n", "\n", "class EquipmentPriceExtractor:\n", "    def __init__(self):\n", "        logging.basicConfig(level=logging.INFO)\n", "        self.logger = logging.getLogger(__name__)\n", "        self.llm_service = AnthropicService()\n", "        self.vd = VectorDatabaseService()  # Assuming this is defined elsewhere in your code\n", "    \n", "    def extract_prices_for_multiple_bids(self, bid_ids: List[str], equipment_list: List[Dict[str, str]]) -> Dict[str, Dict[str, List[Dict[str, str]]]]:\n", "        \"\"\"\n", "        Extract equipment pricing for multiple bids and equipment types\n", "        \n", "        Args:\n", "            bid_ids (List[str]): List of bid IDs to process\n", "            equipment_list (List[Dict]): List of equipment objects with 'equipment_id' and 'equipment_name'\n", "            \n", "        Returns:\n", "            Dict: Nested dictionary with structure {bid_id: {equipment_id: [price_items]}}\n", "        \"\"\"\n", "        results = {}\n", "        \n", "        for bid_id in bid_ids:\n", "            self.logger.info(f\"Processing bid ID: {bid_id}\")\n", "            results[bid_id] = {}\n", "            \n", "            for equipment in equipment_list:\n", "                equipment_id = equipment[\"equipment_id\"]\n", "                equipment_name = equipment[\"equipment_name\"]\n", "                \n", "                self.logger.info(f\"Extracting prices for {equipment_name} (ID: {equipment_id}) in bid {bid_id}\")\n", "                \n", "                price_items = self.extract_equipment_prices(req_id=bid_id, equipment_type=equipment_name)\n", "                results[bid_id][equipment_id] = price_items\n", "        \n", "        return results\n", "    \n", "    def extract_equipment_prices(self, req_id: str, equipment_type: str) -> List[Dict[str, str]]:\n", "        \"\"\"\n", "        Extract equipment items and their prices related to the specified equipment type.\n", "        \n", "        Args:\n", "            req_id (str): Request ID or bid ID to use for the search\n", "            equipment_type (str): The type of equipment to search for\n", "        \n", "        Returns:\n", "            List[Dict]: List of dictionaries containing item names and prices\n", "        \"\"\"\n", "        # Query focused on finding equipment pricing information\n", "        query = f\"\"\"{equipment_type}\n", "\n", "price table pricing chart cost listing price sheet rate card\n", "pricing comparison table equipment rates price breakdown\n", "prices listed price column cost column amount column quantity column \"table row\" \"table cell\"\n", "\"item\" \"description\" \"quantity\" \"rate\" \"amount\" \"subtotal\" \"total\" \"price each\"\n", "$50 $199.99 $1,500 €120 £75 ¥1500 ₹2500 ₩5000 A$200 C$150 \"USD\" \"EUR\" \"JPY\" \"GBP\"\n", "\"per unit\" \"per piece\" \"per item\" \"per box\" \"per case\" \"per pallet\" \"per kg\" \"per lb\"\n", "\"units\" \"pieces\" \"items\" \"boxes\" \"pallets\" \"sets\" \"kits\" \"bundles\" \"packages\"\n", "\"quantity: 100\" \"qty: 25\" \"qty\" \"min. order\" \"minimum quantity\" \"max quantity\"\n", "\"price without units\" \"currency not specified\" \"units not specified\" \"price varies\"\n", "\"call for pricing\" \"price on request\" \"contact for quote\" \"price TBD\" \"market price\"\n", "\"price range\" \"estimated cost\" \"approximate price\" \"price tier\" \"volume discount\"\n", "\"MSRP\" \"list price\" \"net price\" \"price without tax\" \"price with tax included\" \"plus tax\"\"\"\n", "        \n", "        # Perform vector search\n", "        search_results = self.vd._execute_gpu_search_sync(\n", "            search_queries=[query],\n", "            request_id=req_id,\n", "            num_results=5\n", "        )\n", "        \n", "        # Extract content from search results\n", "        documents = []\n", "        for result_group in search_results:\n", "            for result, _ in result_group:\n", "                documents.append(result)\n", "        \n", "        if not documents:\n", "            self.logger.warning(f\"No documents found for {equipment_type} in bid {req_id}\")\n", "            return []\n", "        \n", "        # Prepare prompt for LLM to extract equipment items and prices\n", "        extraction_prompt = {\n", "            \"role\": \"user\",\n", "            \"content\": f\"\"\"\n", "            Extract all equipment items related to {equipment_type} and their corresponding prices from the following text.\n", "            Return ONLY a valid JSON array of objects with 'item' and 'price' keys.\n", "            If the currency is not known, include the price without currency symbol.\n", "\n", "            Format example: [{{\"item\":\"gear\",\"price\":\"$40\"}}, {{\"item\":\"motor\",\"price\":\"1500\"}}]\n", "            \n", "            Here are the documents to analyze:\n", "            {documents}\n", "            \"\"\"\n", "        }\n", "        \n", "        # Generate completion\n", "        response = self.llm_service.generate_sync(\n", "            messages=[extraction_prompt],\n", "            model_name=\"Claude 3.5 Sonnet\",\n", "            temperature=0.005,\n", "            max_tokens=4096,\n", "            use_fallback=False\n", "        )\n", "\n", "        try:\n", "            completion = response[0].content[0].text\n", "            self.logger.info(f\"EXTRACTED DATA: {completion}\")\n", "            extracted_data = json.loads(completion)\n", "            self.logger.info(f\"Successfully extracted {len(extracted_data)} items for {equipment_type}\")\n", "            return extracted_data\n", "        except json.JSONDecodeError:\n", "            self.logger.error(f\"Failed to parse JSON from LLM response for {equipment_type}\")\n", "            self.logger.debug(f\"Raw response: {completion}\")\n", "            return []\n", "        except Exception as e:\n", "            self.logger.error(f\"Error processing {equipment_type}: {str(e)}\")\n", "            return []\n", "\n", "\n", "def main():\n", "    extractor = EquipmentPriceExtractor()\n", "    \n", "    # Example data\n", "    bid_ids = [\"a351c33d-9d9a-4afb-a40d-ca9286c558a0\"]\n", "    equipment_list = [\n", "        {\"equipment_id\": \"eq001\", \"equipment_name\": \"Ball Mill\"},\n", "        {\"equipment_id\": \"eq002\", \"equipment_name\": \"AG Mill\"},\n", "        {\"equipment_id\": \"eq003\", \"equipment_name\": \"Crusher\"}\n", "    ]\n", "    \n", "    # Extract prices for all bids and equipment\n", "    results = extractor.extract_prices_for_multiple_bids(bid_ids, equipment_list)\n", "    \n", "    # Print the results\n", "    print(json.dumps(results, indent=2))\n", "\n", "    return results\n", "    \n", "    # Example of how the output structure will look:\n", "    # {\n", "    #   \"bid_id_1\": {\n", "    #     \"eq001\": [{\"item\": \"Ball Mill 5x8\", \"price\": \"$150,000\"}, ...],\n", "    #     \"eq002\": [{\"item\": \"AG Mill Model X\", \"price\": \"$250,000\"}, ...],\n", "    #     ...\n", "    #   },\n", "    #   \"bid_id_2\": {\n", "    #     ...\n", "    #   }\n", "    # }\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    res = main()"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'a351c33d-9d9a-4afb-a40d-ca9286c558a0': {'eq001': [{'item': 'Ball Mill - 22 x 34, 1 x 9000kW WRIM',\n", "    'price': '$4988371'},\n", "   {'item': 'Ball Mill Liners', 'price': '$332013'},\n", "   {'item': 'Ball Mill Retractable Feed Chute', 'price': '$73673'},\n", "   {'item': 'Ball Mill Trommel Screen', 'price': '$104975'},\n", "   {'item': 'Ball Mill Lube System', 'price': '$338895'},\n", "   {'item': 'Ball Mill Gear Spray System', 'price': '$17061'},\n", "   {'item': 'Ball Mill Motor (WRIM including bedplates and lube system)',\n", "    'price': '$658031'},\n", "   {'item': 'Ball Mill LRS (For WRIM)', 'price': '$102688'},\n", "   {'item': 'Ball Mill Commissioning Spares', 'price': '$4931'},\n", "   {'item': 'Ball Mill One (1) Year Spares', 'price': '$33621'},\n", "   {'item': 'Ball Mill Capital Spares', 'price': '$2428700'},\n", "   {'item': 'Ball Mill First Fills', 'price': '$46530'}],\n", "  'eq002': [{'item': 'Primary (AG) Mill - 32 x 21, 2 x 6000kW SCIM',\n", "    'price': '7769217'},\n", "   {'item': 'Primary Mill Liners', 'price': '1086765'},\n", "   {'item': 'Primary Mill Retractable Feed Chute', 'price': '125440'},\n", "   {'item': 'Primary Mill Lube System', 'price': '446498'},\n", "   {'item': 'Primary Mill Gear Spray System', 'price': '27953'},\n", "   {'item': 'Primary Mill Motors (SCIM including bedplates and lube system)',\n", "    'price': '895280'},\n", "   {'item': 'Primary Mill VFD (for SCIM)', 'price': '1015799'},\n", "   {'item': 'Primary Mill Commissioning Spares', 'price': '4931'},\n", "   {'item': 'Primary Mill One (1) Year Spares', 'price': '53793'},\n", "   {'item': 'Primary Mill Capital Spares', 'price': '2028929'},\n", "   {'item': 'Primary Mill First Fills', 'price': '50960'},\n", "   {'item': 'Primary Mill Special Tools', 'price': '75852'}],\n", "  'eq003': [{'item': 'Primary (AG) Mill - 32 x 21, 2 x 6000kW SCIM',\n", "    'price': '$7769217'},\n", "   {'item': 'Primary Mill Liners', 'price': '$1086765'},\n", "   {'item': 'Primary Mill Retractable Feed Chute', 'price': '$125440'},\n", "   {'item': 'Primary Mill Lube System', 'price': '$446498'},\n", "   {'item': 'Primary Mill Gear Spray System', 'price': '$27953'},\n", "   {'item': 'Primary Mill Motors (SCIM including bedplates and lube system)',\n", "    'price': '$895280'},\n", "   {'item': 'Primary Mill VFD (for SCIM)', 'price': '$1015799'},\n", "   {'item': 'Primary Mill Commissioning Spares', 'price': '$4931'},\n", "   {'item': 'Primary Mill One (1) Year Spares', 'price': '$53793'},\n", "   {'item': 'Primary Mill Capital Spares', 'price': '$2028929'},\n", "   {'item': 'Primary Mill First Fills', 'price': '$50960'},\n", "   {'item': 'Primary Mill Special Tools', 'price': '$75852'}]}}"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["res"]}, {"cell_type": "code", "execution_count": 95, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Processing bid ID: b80eb88d-41ec-49e3-9eac-5a776cd051e0 from bidder: CITIC\n", "INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "INFO:cbp.services.llm.claude:Token Usage - Input: 5537, Output: 550, Total: 6087\n", "INFO:__main__:EXTRACTED DATA: {\n", "  \"AG Mill\": [\n", "    {\n", "      \"item\": \"AG Mill – Ø10.37m x 6.63 F/F (6.1 EGL) – 2×6MW, Dual Pinions Geared Drive\",\n", "      \"price\": \"7437400\",\n", "      \"unit_price\": \"USD\",\n", "      \"comment\": \"Listed as item 1.01\"\n", "    },\n", "    {\n", "      \"item\": \"13.8kV Input Voltage VFD + 2×6,000kW SCIM Motors\",\n", "      \"price\": \"2067300\",\n", "      \"unit_price\": \"USD\",\n", "      \"comment\": \"WEG's Solution, listed as item 1.02\"\n", "    },\n", "    {\n", "      \"item\": \"Spare 6,000kW SCIM Main Motor\",\n", "      \"price\": \"352400\",\n", "      \"unit_price\": \"USD\",\n", "      \"comment\": \"One Set, if ordered with mill together\"\n", "    }\n", "  ],\n", "  \"Ball Mill\": [\n", "    {\n", "      \"item\": \"Ball Mill –Ø6.7m x 10.53 F/F (10.37 EGL) – 9MW, Single Pinion Geared Drive\",\n", "      \"price\": \"5236500\",\n", "      \"unit_price\": \"USD\",\n", "      \"comment\": \"Listed as item 3.01\"\n", "    },\n", "    {\n", "      \"item\": \"9,000kW LRS + 9,000kW WRIM Motor\",\n", "      \"price\": \"528300\",\n", "      \"unit_price\": \"USD\",\n", "      \"comment\": \"MKS's Solution + WEG's Solution\"\n", "    },\n", "    {\n", "      \"item\": \"Spare 9,000kW WRIM Main Motor\",\n", "      \"price\": \"473100\",\n", "      \"unit_price\": \"USD\",\n", "      \"comment\": \"One Set, if ordered with mill together\"\n", "    },\n", "    {\n", "      \"item\": \"4.2x6.65m Ball Mill Water Resistor\",\n", "      \"price\": \"143362.57\",\n", "      \"unit_price\": \"CNY\",\n", "      \"comment\": \"MKS outdoor model, price includes 50% import duty + 13% VAT\"\n", "    }\n", "  ],\n", "  \"Crusher\": []\n", "}\n", "INFO:__main__:Successfully extracted 3 items for AG Mill\n", "INFO:__main__:Successfully extracted 4 items for Ball Mill\n", "INFO:__main__:Successfully extracted 0 items for Crusher\n", "INFO:__main__:Processing bid ID: fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b from bidder: NTC\n", "INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "INFO:cbp.services.llm.claude:Token Usage - Input: 6479, Output: 916, Total: 7395\n", "INFO:__main__:EXTRACTED DATA: {\n", "  \"Ball Mill\": [\n", "    {\n", "      \"item\": \"Ball Mill - 22 x 34, 1 x 9000kW WRIM\",\n", "      \"price\": \"4988371\",\n", "      \"unit_price\": \"USD\",\n", "      \"comment\": \"Listed as item 15 in equipment table\"\n", "    },\n", "    {\n", "      \"item\": \"Ball Mill Liners\",\n", "      \"price\": \"332013\",\n", "      \"unit_price\": \"USD\",\n", "      \"comment\": \"Listed as item 16 in equipment table\"\n", "    },\n", "    {\n", "      \"item\": \"Ball Mill Retractable Feed Chute\",\n", "      \"price\": \"73673\",\n", "      \"unit_price\": \"USD\",\n", "      \"comment\": \"Listed as item 17 in equipment table\"\n", "    },\n", "    {\n", "      \"item\": \"Ball Mill Trommel Screen\",\n", "      \"price\": \"104975\",\n", "      \"unit_price\": \"USD\",\n", "      \"comment\": \"Listed as item 20 in equipment table\"\n", "    },\n", "    {\n", "      \"item\": \"Ball Mill Lube System\",\n", "      \"price\": \"338895\",\n", "      \"unit_price\": \"USD\",\n", "      \"comment\": \"Listed as item 24 in equipment table\"\n", "    },\n", "    {\n", "      \"item\": \"Ball Mill Gear Spray System\",\n", "      \"price\": \"17061\",\n", "      \"unit_price\": \"USD\",\n", "      \"comment\": \"Listed as item 25 in equipment table\"\n", "    },\n", "    {\n", "      \"item\": \"Ball Mill Motor (WRIM including bedplates and lube system)\",\n", "      \"price\": \"658031\",\n", "      \"unit_price\": \"USD\",\n", "      \"comment\": \"Listed as item 25 in equipment table\"\n", "    },\n", "    {\n", "      \"item\": \"Ball Mill LRS (For WRIM)\",\n", "      \"price\": \"102688\",\n", "      \"unit_price\": \"USD\",\n", "      \"comment\": \"Listed as item 26 in equipment table\"\n", "    }\n", "  ],\n", "  \"AG Mill\": [\n", "    {\n", "      \"item\": \"Primary (AG) Mill - 32 x 21, 2 x 6000kW SCIM\",\n", "      \"price\": \"7769217\",\n", "      \"unit_price\": \"USD\",\n", "      \"comment\": \"Listed as item 1 in equipment table\"\n", "    },\n", "    {\n", "      \"item\": \"Primary Mill Liners\",\n", "      \"price\": \"1086765\",\n", "      \"unit_price\": \"USD\",\n", "      \"comment\": \"Listed as item 2 in equipment table\"\n", "    },\n", "    {\n", "      \"item\": \"Primary Mill Retractable Feed Chute\",\n", "      \"price\": \"125440\",\n", "      \"unit_price\": \"USD\",\n", "      \"comment\": \"Listed as item 4 in equipment table\"\n", "    },\n", "    {\n", "      \"item\": \"Primary Mill Lube System\",\n", "      \"price\": \"446498\",\n", "      \"unit_price\": \"USD\",\n", "      \"comment\": \"Listed as item 6 in equipment table\"\n", "    },\n", "    {\n", "      \"item\": \"Primary Mill Gear Spray System\",\n", "      \"price\": \"27953\",\n", "      \"unit_price\": \"USD\",\n", "      \"comment\": \"Listed as item 7 in equipment table\"\n", "    },\n", "    {\n", "      \"item\": \"Primary Mill Motors (SCIM including bedplates and lube system)\",\n", "      \"price\": \"895280\",\n", "      \"unit_price\": \"USD\",\n", "      \"comment\": \"Listed as item 8 in equipment table\"\n", "    },\n", "    {\n", "      \"item\": \"Primary Mill VFD (for SCIM)\",\n", "      \"price\": \"1015799\",\n", "      \"unit_price\": \"USD\",\n", "      \"comment\": \"Listed as item 9 in equipment table\"\n", "    }\n", "  ],\n", "  \"Crusher\": []\n", "}\n", "INFO:__main__:Successfully extracted 8 items for Ball Mill\n", "INFO:__main__:Successfully extracted 7 items for AG Mill\n", "INFO:__main__:Successfully extracted 0 items for Crusher\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"b80eb88d-41ec-49e3-9eac-5a776cd051e0\": {\n", "    \"eq001\": {\n", "      \"name\": \"Ball Mill\",\n", "      \"bidder\": \"CITIC\",\n", "      \"items\": [\n", "        {\n", "          \"item\": \"Ball Mill \\u2013\\u00d86.7m x 10.53 F/F (10.37 EGL) \\u2013 9MW, Single Pinion Geared Drive\",\n", "          \"price\": \"5236500\",\n", "          \"unit_price\": \"USD\",\n", "          \"comment\": \"Listed as item 3.01\"\n", "        },\n", "        {\n", "          \"item\": \"9,000kW LRS + 9,000kW WRIM Motor\",\n", "          \"price\": \"528300\",\n", "          \"unit_price\": \"USD\",\n", "          \"comment\": \"MKS's Solution + WEG's Solution\"\n", "        },\n", "        {\n", "          \"item\": \"Spare 9,000kW WRIM Main Motor\",\n", "          \"price\": \"473100\",\n", "          \"unit_price\": \"USD\",\n", "          \"comment\": \"One Set, if ordered with mill together\"\n", "        },\n", "        {\n", "          \"item\": \"4.2x6.65m Ball Mill Water Resistor\",\n", "          \"price\": \"143362.57\",\n", "          \"unit_price\": \"CNY\",\n", "          \"comment\": \"MKS outdoor model, price includes 50% import duty + 13% VAT\"\n", "        }\n", "      ]\n", "    },\n", "    \"eq002\": {\n", "      \"name\": \"AG Mill\",\n", "      \"bidder\": \"CITIC\",\n", "      \"items\": [\n", "        {\n", "          \"item\": \"AG Mill \\u2013 \\u00d810.37m x 6.63 F/F (6.1 EGL) \\u2013 2\\u00d76MW, Dual Pinions Geared Drive\",\n", "          \"price\": \"7437400\",\n", "          \"unit_price\": \"USD\",\n", "          \"comment\": \"Listed as item 1.01\"\n", "        },\n", "        {\n", "          \"item\": \"13.8kV Input Voltage VFD + 2\\u00d76,000kW SCIM Motors\",\n", "          \"price\": \"2067300\",\n", "          \"unit_price\": \"USD\",\n", "          \"comment\": \"WEG's Solution, listed as item 1.02\"\n", "        },\n", "        {\n", "          \"item\": \"Spare 6,000kW SCIM Main Motor\",\n", "          \"price\": \"352400\",\n", "          \"unit_price\": \"USD\",\n", "          \"comment\": \"One Set, if ordered with mill together\"\n", "        }\n", "      ]\n", "    },\n", "    \"eq003\": {\n", "      \"name\": \"<PERSON><PERSON><PERSON>\",\n", "      \"bidder\": \"CITIC\",\n", "      \"items\": []\n", "    }\n", "  },\n", "  \"fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b\": {\n", "    \"eq001\": {\n", "      \"name\": \"Ball Mill\",\n", "      \"bidder\": \"NTC\",\n", "      \"items\": [\n", "        {\n", "          \"item\": \"Ball Mill - 22 x 34, 1 x 9000kW WRIM\",\n", "          \"price\": \"4988371\",\n", "          \"unit_price\": \"USD\",\n", "          \"comment\": \"Listed as item 15 in equipment table\"\n", "        },\n", "        {\n", "          \"item\": \"Ball Mill Liners\",\n", "          \"price\": \"332013\",\n", "          \"unit_price\": \"USD\",\n", "          \"comment\": \"Listed as item 16 in equipment table\"\n", "        },\n", "        {\n", "          \"item\": \"Ball Mill Retractable Feed Chute\",\n", "          \"price\": \"73673\",\n", "          \"unit_price\": \"USD\",\n", "          \"comment\": \"Listed as item 17 in equipment table\"\n", "        },\n", "        {\n", "          \"item\": \"Ball Mill Trommel Screen\",\n", "          \"price\": \"104975\",\n", "          \"unit_price\": \"USD\",\n", "          \"comment\": \"Listed as item 20 in equipment table\"\n", "        },\n", "        {\n", "          \"item\": \"Ball Mill Lube System\",\n", "          \"price\": \"338895\",\n", "          \"unit_price\": \"USD\",\n", "          \"comment\": \"Listed as item 24 in equipment table\"\n", "        },\n", "        {\n", "          \"item\": \"Ball Mill Gear Spray System\",\n", "          \"price\": \"17061\",\n", "          \"unit_price\": \"USD\",\n", "          \"comment\": \"Listed as item 25 in equipment table\"\n", "        },\n", "        {\n", "          \"item\": \"Ball Mill Motor (WRIM including bedplates and lube system)\",\n", "          \"price\": \"658031\",\n", "          \"unit_price\": \"USD\",\n", "          \"comment\": \"Listed as item 25 in equipment table\"\n", "        },\n", "        {\n", "          \"item\": \"Ball Mill LRS (For WRIM)\",\n", "          \"price\": \"102688\",\n", "          \"unit_price\": \"USD\",\n", "          \"comment\": \"Listed as item 26 in equipment table\"\n", "        }\n", "      ]\n", "    },\n", "    \"eq002\": {\n", "      \"name\": \"AG Mill\",\n", "      \"bidder\": \"NTC\",\n", "      \"items\": [\n", "        {\n", "          \"item\": \"Primary (AG) Mill - 32 x 21, 2 x 6000kW SCIM\",\n", "          \"price\": \"7769217\",\n", "          \"unit_price\": \"USD\",\n", "          \"comment\": \"Listed as item 1 in equipment table\"\n", "        },\n", "        {\n", "          \"item\": \"Primary Mill Liners\",\n", "          \"price\": \"1086765\",\n", "          \"unit_price\": \"USD\",\n", "          \"comment\": \"Listed as item 2 in equipment table\"\n", "        },\n", "        {\n", "          \"item\": \"Primary Mill Retractable Feed Chute\",\n", "          \"price\": \"125440\",\n", "          \"unit_price\": \"USD\",\n", "          \"comment\": \"Listed as item 4 in equipment table\"\n", "        },\n", "        {\n", "          \"item\": \"Primary Mill Lube System\",\n", "          \"price\": \"446498\",\n", "          \"unit_price\": \"USD\",\n", "          \"comment\": \"Listed as item 6 in equipment table\"\n", "        },\n", "        {\n", "          \"item\": \"Primary Mill Gear Spray System\",\n", "          \"price\": \"27953\",\n", "          \"unit_price\": \"USD\",\n", "          \"comment\": \"Listed as item 7 in equipment table\"\n", "        },\n", "        {\n", "          \"item\": \"Primary Mill Motors (SCIM including bedplates and lube system)\",\n", "          \"price\": \"895280\",\n", "          \"unit_price\": \"USD\",\n", "          \"comment\": \"Listed as item 8 in equipment table\"\n", "        },\n", "        {\n", "          \"item\": \"Primary Mill VFD (for SCIM)\",\n", "          \"price\": \"1015799\",\n", "          \"unit_price\": \"USD\",\n", "          \"comment\": \"Listed as item 9 in equipment table\"\n", "        }\n", "      ]\n", "    },\n", "    \"eq003\": {\n", "      \"name\": \"<PERSON><PERSON><PERSON>\",\n", "      \"bidder\": \"NTC\",\n", "      \"items\": []\n", "    }\n", "  }\n", "}\n"]}], "source": ["from cbp.services.llm.fastllm import LLMOrchestrator\n", "from cbp.services.llm.claude import AnthropicService\n", "from typing import List, Dict, Any\n", "import logging\n", "import json\n", "\n", "\n", "class OptimizedEquipmentPriceExtractor:\n", "    def __init__(self):\n", "        logging.basicConfig(level=logging.INFO)\n", "        self.logger = logging.getLogger(__name__)\n", "        self.llm_service = AnthropicService()\n", "        self.vd = VectorDatabaseService()  # Assuming this is defined elsewhere in your code\n", "    \n", "    def extract_prices_for_multiple_bids(self, bid_data: List[Dict[str, str]], equipment_list: List[Dict[str, str]]) -> Dict[str, Dict[str, Any]]:\n", "        \"\"\"\n", "        Extract equipment pricing for multiple bids and equipment types using batch processing\n", "        \n", "        Args:\n", "            bid_data (List[Dict]): List of bid dictionaries containing 'bid_id' and 'bidder_name'\n", "            equipment_list (List[Dict]): List of equipment objects with 'equipment_id' and 'equipment_name'\n", "            \n", "        Returns:\n", "            Dict: Nested dictionary with structure {bid_id: {equipment_id: {'name': equipment_name, 'bidder': bidder_name, 'items': [price_items]}}}\n", "        \"\"\"\n", "        results = {}\n", "        \n", "        # Create a mapping of equipment_id to equipment_name for easy lookup\n", "        equipment_map = {item[\"equipment_id\"]: item[\"equipment_name\"] for item in equipment_list}\n", "        \n", "        # Get all equipment names for search\n", "        all_equipment_names = [item[\"equipment_name\"] for item in equipment_list]\n", "        \n", "        # Process each bid\n", "        for bid_info in bid_data:\n", "            bid_id = bid_info[\"bid_id\"]\n", "            bidder_name = bid_info[\"bidder_name\"]\n", "            \n", "            self.logger.info(f\"Processing bid ID: {bid_id} from bidder: {bidder_name}\")\n", "            results[bid_id] = {}\n", "            \n", "            # Extract all equipment prices in one go\n", "            all_prices = self.extract_all_equipment_prices(\n", "                req_id=bid_id, \n", "                equipment_types=all_equipment_names\n", "            )\n", "            \n", "            # Distribute results to the corresponding equipment IDs\n", "            for equipment_id, equipment_name in equipment_map.items():\n", "                if equipment_name in all_prices:\n", "                    results[bid_id][equipment_id] = {\n", "                        'name': equipment_name,\n", "                        'bidder': bidder_name,\n", "                        'items': all_prices[equipment_name]\n", "                    }\n", "                else:\n", "                    self.logger.warning(f\"No prices found for {equipment_name} in bid {bid_id}\")\n", "                    results[bid_id][equipment_id] = {\n", "                        'name': equipment_name,\n", "                        'bidder': bidder_name,\n", "                        'items': []\n", "                    }\n", "        \n", "        return results\n", "    \n", "    def extract_all_equipment_prices(self, req_id: str, equipment_types: List[str]) -> Dict[str, List[Dict[str, str]]]:\n", "        \"\"\"\n", "        Extract prices for multiple equipment types in a single operation\n", "        \n", "        Args:\n", "            req_id (str): Request ID or bid ID to use for the search\n", "            equipment_types (List[str]): List of equipment types to search for\n", "            \n", "        Returns:\n", "            Dict: Dictionary mapping equipment types to their price lists\n", "        \"\"\"\n", "        # Build a comprehensive query that includes all equipment types\n", "        equipment_query = \" OR \".join([f'\"{eq_type}\"' for eq_type in equipment_types])\n", "        \n", "        query = f\"\"\"({equipment_query})\n", "\n", "price table pricing chart cost listing price sheet rate card quotation invoice\n", "pricing comparison table equipment rates price breakdown cost analysis\n", "prices listed price column cost column amount column quantity column \"table row\" \"table cell\" \"line item\"\n", "\"item\" \"description\" \"quantity\" \"rate\" \"amount\" \"subtotal\" \"total\" \"price each\" \"unit cost\"\n", "$50 $199.99 $1,500 $5,000 $10,000 $50,000 $100,000 $500,000 $1,000,000 $5,000,000\n", "€120 €1,000 €5,000 €10,000 €50,000 €100,000 €500,000 €1,000,000\n", "£75 £1,000 £5,000 £10,000 £50,000 £100,000 £500,000 £1,000,000\n", "¥1500 ¥10,000 ¥50,000 ¥100,000 ¥500,000 ¥1,000,000 ¥5,000,000 ¥10,000,000\n", "₹2500 ₹10,000 ₹50,000 ₹100,000 ₹500,000 ₹1,000,000 ₹5,000,000 ₹10,000,000\n", "₩5000 ₩50,000 ₩100,000 ₩500,000 ₩1,000,000 ₩5,000,000 ₩10,000,000\n", "A$200 A$1,000 A$5,000 A$10,000 A$50,000 A$100,000 A$500,000\n", "C$150 C$1,000 C$5,000 C$10,000 C$50,000 C$100,000 C$500,000\n", "\"USD\" \"EUR\" \"JPY\" \"GBP\" \"INR\" \"KRW\" \"AUD\" \"CAD\" \"CHF\" \"CNY\" \"HKD\" \"SGD\"\n", "\"per unit\" \"per piece\" \"per item\" \"per box\" \"per case\" \"per pallet\" \"per kg\" \"per lb\" \"per ton\" \"per set\"\n", "\"units\" \"pieces\" \"items\" \"boxes\" \"cases\" \"pallets\" \"sets\" \"kits\" \"bundles\" \"packages\" \"systems\"\n", "\"quantity: 100\" \"qty: 25\" \"qty: 50\" \"qty: 1000\" \"qty\" \"min. order\" \"minimum quantity\" \"max quantity\"\n", "\"price without units\" \"currency not specified\" \"units not specified\" \"price varies\" \"price negotiable\"\n", "\"call for pricing\" \"price on request\" \"contact for quote\" \"price TBD\" \"market price\" \"price upon inquiry\"\n", "\"price range\" \"estimated cost\" \"approximate price\" \"price tier\" \"volume discount\" \"bulk pricing\"\n", "\"MSRP\" \"list price\" \"net price\" \"retail price\" \"wholesale price\" \"dealer price\" \"distributor price\"\n", "\"price without tax\" \"price with tax\" \"tax included\" \"plus tax\" \"excluding VAT\" \"including VAT\"\n", "\"FOB price\" \"CIF price\" \"Ex-works price\" \"factory price\" \"landed cost\" \"delivered price\"\"\"\n", "        \n", "        # Perform a single vector search for all equipment types\n", "        search_results = self.vd._execute_gpu_search_sync(\n", "            search_queries=[query],\n", "            request_id=req_id,\n", "            num_results=10  # Increased result count since we're searching for multiple equipment types\n", "        )\n", "        \n", "        # Extract content from search results\n", "        documents = []\n", "        for result_group in search_results:\n", "            for result, _ in result_group:\n", "                documents.append(result)\n", "        \n", "        if not documents:\n", "            self.logger.warning(f\"No documents found for equipment types in bid {req_id}\")\n", "            return {eq_type: [] for eq_type in equipment_types}\n", "        \n", "        # Prepare prompt for LLM to extract and categorize all equipment items and prices\n", "        extraction_prompt = {\n", "            \"role\": \"user\",\n", "            \"content\": f\"\"\"\n", "            Extract ALL equipment items and their corresponding prices from the following text.\n", "            Consider the following equipment types: {\", \".join(equipment_types)}.\n", "            \n", "            For each equipment type, analyze the document and extract ANY relevant items and prices, even if they are not explicitly labeled.\n", "            Include any item that could potentially belong to any of the equipment types.\n", "            \n", "            Return ONLY a valid JSON object where:\n", "            - Keys are the equipment types (exactly as listed above)\n", "            - Values are arrays of objects with these keys:\n", "              - 'item': Name/description of the item (extract ALL items found)\n", "              - 'price': Price value (must include currency if specified: USD, EUR, GBP, JPY etc. If price is not available, use \"N/A\", \"TBA\", \"Contact for Quote\" etc.)\n", "              - 'unit_price': Currency unit (USD, EUR, GBP, JPY etc.) or pricing unit (per unit, per set etc.)\n", "              - 'comment': Source context of the price or pricing status\n", "            \n", "            Format example:\n", "            {{\n", "              \"Ball Mill\": [\n", "                {{\n", "                  \"item\": \"Ball Mill 5x8\",\n", "                  \"price\": \"150000\",\n", "                  \"unit_price\": \"USD\", \n", "                  \"comment\": \"Listed in main equipment price table\"\n", "                }},\n", "                {{\n", "                  \"item\": \"Gear System\",\n", "                  \"price\": \"Contact for Quote\",\n", "                  \"unit_price\": \"N/A\",\n", "                  \"comment\": \"Price to be determined\"\n", "                }}\n", "              ]\n", "            }}\n", "            \n", "            Important:\n", "            - Extract ALL items found, even if not explicitly labeled\n", "            - Include items even if price is listed as N/A, TBA, or requires contact\n", "            - Include currency type in unit_price if specified (USD, EUR, etc.)\n", "            - If currency not specified, use price value as-is\n", "            - If no items found for equipment type, use empty array\n", "            - If no specific source found, use \"Extracted from document\" as comment\n", "            - For items without prices, indicate the actual status (e.g. \"Price on Request\", \"TBD\", etc.)\n", "             \n", "            Here are the documents to analyze:\n", "            {documents}\n", "            \"\"\"\n", "        }\n", "        # Generate completion\n", "        response = self.llm_service.generate_sync(\n", "            messages=[extraction_prompt],\n", "            model_name=\"Claude 3.5 Sonnet\",\n", "            temperature=0.005,\n", "            max_tokens=4096,\n", "            use_fallback=False\n", "        )\n", "\n", "        try:\n", "            completion = response[0].content[0].text\n", "            self.logger.info(f\"EXTRACTED DATA: {completion}\")\n", "            extracted_data = json.loads(completion)\n", "            \n", "            # Verify all expected equipment types are present in the result\n", "            for eq_type in equipment_types:\n", "                if eq_type not in extracted_data:\n", "                    self.logger.warning(f\"Equipment type {eq_type} missing from extraction results, adding empty list\")\n", "                    extracted_data[eq_type] = []\n", "            \n", "            # Log summary of results\n", "            for eq_type, items in extracted_data.items():\n", "                self.logger.info(f\"Successfully extracted {len(items)} items for {eq_type}\")\n", "                \n", "            return extracted_data\n", "            \n", "        except json.JSONDecodeError:\n", "            self.logger.error(\"Failed to parse JSON from LLM response\")\n", "            self.logger.debug(f\"Raw response: {completion}\")\n", "            return {eq_type: [] for eq_type in equipment_types}\n", "        except Exception as e:\n", "            self.logger.error(f\"Error processing equipment types: {str(e)}\")\n", "            return {eq_type: [] for eq_type in equipment_types}\n", "\n", "\n", "def main():\n", "    extractor = OptimizedEquipmentPriceExtractor()\n", "    \n", "    # Example data with bidder names\n", "    bid_data = [\n", "        {\"bid_id\": \"b80eb88d-41ec-49e3-9eac-5a776cd051e0\", \"bidder_name\": \"CITIC\"},\n", "        {\"bid_id\": \"fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b\", \"bidder_name\": \"NTC\"}\n", "    ]\n", "    \n", "    equipment_list = [\n", "        {\"equipment_id\": \"eq001\", \"equipment_name\": \"Ball Mill\"},\n", "        {\"equipment_id\": \"eq002\", \"equipment_name\": \"AG Mill\"},\n", "        {\"equipment_id\": \"eq003\", \"equipment_name\": \"Crusher\"}\n", "    ]\n", "    \n", "    # Extract prices for all bids and equipment\n", "    results = extractor.extract_prices_for_multiple_bids(bid_data, equipment_list)\n", "    \n", "    # Print the results\n", "    print(json.dumps(results, indent=2))\n", "\n", "    return results\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    res = main()"]}, {"cell_type": "code", "execution_count": 97, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'b80eb88d-41ec-49e3-9eac-5a776cd051e0': {'eq001': {'name': 'Ball Mill',\n", "   'bidder': 'CITIC',\n", "   'items': [{'item': 'Ball Mill –Ø6.7m x 10.53 F/F (10.37 EGL) – 9MW, Single Pinion Geared Drive',\n", "     'price': '5236500',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 3.01'},\n", "    {'item': '9,000kW LRS + 9,000kW WRIM Motor',\n", "     'price': '528300',\n", "     'unit_price': 'USD',\n", "     'comment': \"MKS's Solution + WEG's Solution\"},\n", "    {'item': 'Spare 9,000kW WRIM Main Motor',\n", "     'price': '473100',\n", "     'unit_price': 'USD',\n", "     'comment': 'One Set, if ordered with mill together'},\n", "    {'item': '4.2x6.65m Ball Mill Water Resistor',\n", "     'price': '143362.57',\n", "     'unit_price': 'CNY',\n", "     'comment': 'MKS outdoor model, price includes 50% import duty + 13% VAT'}]},\n", "  'eq002': {'name': 'AG Mill',\n", "   'bidder': 'CITIC',\n", "   'items': [{'item': 'AG Mill – Ø10.37m x 6.63 F/F (6.1 EGL) – 2×6MW, Dual Pinions Geared Drive',\n", "     'price': '7437400',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 1.01'},\n", "    {'item': '13.8kV Input Voltage VFD + 2×6,000kW SCIM Motors',\n", "     'price': '2067300',\n", "     'unit_price': 'USD',\n", "     'comment': \"WEG's Solution, listed as item 1.02\"},\n", "    {'item': 'Spare 6,000kW SCIM Main Motor',\n", "     'price': '352400',\n", "     'unit_price': 'USD',\n", "     'comment': 'One Set, if ordered with mill together'}]},\n", "  'eq003': {'name': '<PERSON><PERSON><PERSON>', 'bidder': 'CITIC', 'items': []}},\n", " 'fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b': {'eq001': {'name': 'Ball Mill',\n", "   'bidder': 'NTC',\n", "   'items': [{'item': 'Ball Mill - 22 x 34, 1 x 9000kW WRIM',\n", "     'price': '4988371',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 15 in equipment table'},\n", "    {'item': 'Ball Mill Liners',\n", "     'price': '332013',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 16 in equipment table'},\n", "    {'item': 'Ball Mill Retractable Feed Chute',\n", "     'price': '73673',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 17 in equipment table'},\n", "    {'item': 'Ball Mill Trommel Screen',\n", "     'price': '104975',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 20 in equipment table'},\n", "    {'item': 'Ball Mill Lube System',\n", "     'price': '338895',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 24 in equipment table'},\n", "    {'item': 'Ball Mill Gear Spray System',\n", "     'price': '17061',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 25 in equipment table'},\n", "    {'item': 'Ball Mill Motor (WRIM including bedplates and lube system)',\n", "     'price': '658031',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 25 in equipment table'},\n", "    {'item': 'Ball Mill LRS (For WRIM)',\n", "     'price': '102688',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 26 in equipment table'}]},\n", "  'eq002': {'name': 'AG Mill',\n", "   'bidder': 'NTC',\n", "   'items': [{'item': 'Primary (AG) Mill - 32 x 21, 2 x 6000kW SCIM',\n", "     'price': '7769217',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 1 in equipment table'},\n", "    {'item': 'Primary Mill Liners',\n", "     'price': '1086765',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 2 in equipment table'},\n", "    {'item': 'Primary Mill Retractable Feed Chute',\n", "     'price': '125440',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 4 in equipment table'},\n", "    {'item': 'Primary Mill Lube System',\n", "     'price': '446498',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 6 in equipment table'},\n", "    {'item': 'Primary Mill Gear Spray System',\n", "     'price': '27953',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 7 in equipment table'},\n", "    {'item': 'Primary Mill Motors (SCIM including bedplates and lube system)',\n", "     'price': '895280',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 8 in equipment table'},\n", "    {'item': 'Primary Mill VFD (for SCIM)',\n", "     'price': '1015799',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 9 in equipment table'}]},\n", "  'eq003': {'name': '<PERSON><PERSON><PERSON>', 'bidder': 'NTC', 'items': []}}}"]}, "execution_count": 97, "metadata": {}, "output_type": "execute_result"}], "source": ["from cbp.pipelines.bid_evaluation.commercial_bid import CommercialBidProcessor\n", "import json\n", "\n", "\n", "def main():\n", "    \"\"\"Example usage of the CommercialBidProcessor.\"\"\"\n", "    # Example data\n", "    bid_data = [\n", "        {\"bid_id\": \"b80eb88d-41ec-49e3-9eac-5a776cd051e0\", \"bidder_name\": \"CITIC\"},\n", "        {\"bid_id\": \"fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b\", \"bidder_name\": \"NTC\"}\n", "    ]\n", "    \n", "    equipment_list = [\n", "        {\"equipment_id\": \"eq001\", \"equipment_name\": \"Ball Mill\"},\n", "        {\"equipment_id\": \"eq002\", \"equipment_name\": \"AG Mill\"},\n", "        {\"equipment_id\": \"eq003\", \"equipment_name\": \"Crusher\"}\n", "    ]\n", "    \n", "    \n", "    # Create processor instance\n", "    processor = CommercialBidProcessor()\n", "    \n", "    # Process bids synchronously\n", "    results = processor.process_multiple_bids_sync(bid_data, equipment_list)\n", "    \n", "    # Print results\n", "    print(json.dumps(results.dict(), indent=2))\n", "    return results\n", "\n", "if __name__ == \"__main__\":\n", "    report = main()"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'a351c33d-9d9a-4afb-a40d-ca9286c558a0': {'eq001': [{'item': 'Ball Mill - 22 x 34, 1 x 9000kW WRIM',\n", "    'price': '4988371',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 15 in main equipment table'},\n", "   {'item': 'Ball Mill Liners',\n", "    'price': '332013',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 16 in main equipment table'},\n", "   {'item': 'Ball Mill Retractable Feed Chute',\n", "    'price': '73673',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 17 in main equipment table'},\n", "   {'item': 'Ball Mill Trommel Screen',\n", "    'price': '104975',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 20 in main equipment table'},\n", "   {'item': 'Ball Mill Lube System',\n", "    'price': '338895',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 24 in main equipment table'},\n", "   {'item': 'Ball Mill Gear Spray System',\n", "    'price': '17061',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 25 in main equipment table'},\n", "   {'item': 'Ball Mill Motor (WRIM including bedplates and lube system)',\n", "    'price': '658031',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 25 in main equipment table'},\n", "   {'item': 'Ball Mill LRS (For WRIM)',\n", "    'price': '102688',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 26 in main equipment table'}],\n", "  'eq002': [{'item': 'Primary (AG) Mill - 32 x 21, 2 x 6000kW SCIM',\n", "    'price': '7769217',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 1 in main equipment table'},\n", "   {'item': 'Primary Mill Liners',\n", "    'price': '1086765',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 2 in main equipment table'},\n", "   {'item': 'Primary Mill Retractable Feed Chute',\n", "    'price': '125440',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 4 in main equipment table'},\n", "   {'item': 'Primary Mill Lube System',\n", "    'price': '446498',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 6 in main equipment table'},\n", "   {'item': 'Primary Mill Gear Spray System',\n", "    'price': '27953',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 7 in main equipment table'},\n", "   {'item': 'Primary Mill Motors (SCIM including bedplates and lube system)',\n", "    'price': '895280',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 8 in main equipment table'},\n", "   {'item': 'Primary Mill VFD (for SCIM)',\n", "    'price': '1015799',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 9 in main equipment table'}],\n", "  'eq003': []},\n", " 'fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b': {'eq001': [{'item': 'Ball Mill - 22 x 34, 1 x 9000kW WRIM',\n", "    'price': '4988371',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 15 in equipment table'},\n", "   {'item': 'Ball Mill Liners',\n", "    'price': '332013',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 16 in equipment table'},\n", "   {'item': 'Ball Mill Retractable Feed Chute',\n", "    'price': '73673',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 17 in equipment table'},\n", "   {'item': 'Ball Mill Trommel Screen',\n", "    'price': '104975',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 20 in equipment table'},\n", "   {'item': 'Ball Mill Lube System',\n", "    'price': '338895',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 24 in equipment table'},\n", "   {'item': 'Ball Mill Gear Spray System',\n", "    'price': '17061',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 25 in equipment table'},\n", "   {'item': 'Ball Mill Motor (WRIM including bedplates and lube system)',\n", "    'price': '658031',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 25 in equipment table'},\n", "   {'item': 'Ball Mill LRS (For WRIM)',\n", "    'price': '102688',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 26 in equipment table'}],\n", "  'eq002': [{'item': 'Primary (AG) Mill - 32 x 21, 2 x 6000kW SCIM',\n", "    'price': '7769217',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 1 in equipment table'},\n", "   {'item': 'Primary Mill Liners',\n", "    'price': '1086765',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 2 in equipment table'},\n", "   {'item': 'Primary Mill Retractable Feed Chute',\n", "    'price': '125440',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 4 in equipment table'},\n", "   {'item': 'Primary Mill Lube System',\n", "    'price': '446498',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 6 in equipment table'},\n", "   {'item': 'Primary Mill Gear Spray System',\n", "    'price': '27953',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 7 in equipment table'},\n", "   {'item': 'Primary Mill Motors (SCIM including bedplates and lube system)',\n", "    'price': '895280',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 8 in equipment table'},\n", "   {'item': 'Primary Mill VFD (for SCIM)',\n", "    'price': '1015799',\n", "    'unit_price': 'USD',\n", "    'comment': 'Listed as item 9 in equipment table'}],\n", "  'eq003': []}}"]}, "execution_count": 73, "metadata": {}, "output_type": "execute_result"}], "source": ["res"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['a351c33d-9d9a-4afb-a40d-ca9286c558a0', 'fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b'])"]}, "execution_count": 70, "metadata": {}, "output_type": "execute_result"}], "source": ["res.keys()"]}, {"cell_type": "code", "execution_count": 98, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Generating report for equipment ID: eq002\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "INFO:cbp.services.llm.claude:Token Usage - Input: 1225, Output: 1114, Total: 2339\n", "INFO:__main__:Generated consolidated report for equipment ID: eq002\n", "INFO:__main__:Generating report for equipment ID: eq003\n", "INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "INFO:cbp.services.llm.claude:Token Usage - Input: 561, Output: 4, Total: 565\n", "INFO:__main__:Generated consolidated report for equipment ID: eq003\n", "INFO:__main__:Generating report for equipment ID: eq001\n", "INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "INFO:cbp.services.llm.claude:Token Usage - Input: 1330, Output: 1348, Total: 2678\n", "INFO:__main__:Generated consolidated report for equipment ID: eq001\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"eq002\": {\n", "    \"equipment\": \"eq002\",\n", "    \"equipment_name\": \"AG Mill\",\n", "    \"data\": [\n", "      {\n", "        \"item_name\": \"AG Mill with Dual Pinion Drive\",\n", "        \"prices\": [\n", "          {\n", "            \"bid_id\": \"b80eb88d-41ec-49e3-9eac-5a776cd051e0\",\n", "            \"bidder\": \"CITIC\",\n", "            \"price\": \"7437400\",\n", "            \"unit_price\": \"USD\",\n", "            \"comment\": \"Listed as item 1.01\"\n", "          },\n", "          {\n", "            \"bid_id\": \"fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b\",\n", "            \"bidder\": \"NTC\",\n", "            \"price\": \"7769217\",\n", "            \"unit_price\": \"USD\",\n", "            \"comment\": \"Listed as item 1 in equipment table\"\n", "          }\n", "        ]\n", "      },\n", "      {\n", "        \"item_name\": \"VFD System\",\n", "        \"prices\": [\n", "          {\n", "            \"bid_id\": \"b80eb88d-41ec-49e3-9eac-5a776cd051e0\",\n", "            \"bidder\": \"CITIC\",\n", "            \"price\": \"2067300\",\n", "            \"unit_price\": \"USD\",\n", "            \"comment\": \"WEG's Solution, listed as item 1.02\"\n", "          },\n", "          {\n", "            \"bid_id\": \"fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b\",\n", "            \"bidder\": \"NTC\",\n", "            \"price\": \"1015799\",\n", "            \"unit_price\": \"USD\",\n", "            \"comment\": \"Listed as item 9 in equipment table\"\n", "          }\n", "        ]\n", "      },\n", "      {\n", "        \"item_name\": \"Spare SCIM Motor\",\n", "        \"prices\": [\n", "          {\n", "            \"bid_id\": \"b80eb88d-41ec-49e3-9eac-5a776cd051e0\",\n", "            \"bidder\": \"CITIC\",\n", "            \"price\": \"352400\",\n", "            \"unit_price\": \"USD\",\n", "            \"comment\": \"One Set, if ordered with mill together\"\n", "          }\n", "        ]\n", "      },\n", "      {\n", "        \"item_name\": \"Primary Mill Liners\",\n", "        \"prices\": [\n", "          {\n", "            \"bid_id\": \"fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b\",\n", "            \"bidder\": \"NTC\",\n", "            \"price\": \"1086765\",\n", "            \"unit_price\": \"USD\",\n", "            \"comment\": \"Listed as item 2 in equipment table\"\n", "          }\n", "        ]\n", "      },\n", "      {\n", "        \"item_name\": \"Primary Mill Retractable Feed Chute\",\n", "        \"prices\": [\n", "          {\n", "            \"bid_id\": \"fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b\",\n", "            \"bidder\": \"NTC\",\n", "            \"price\": \"125440\",\n", "            \"unit_price\": \"USD\",\n", "            \"comment\": \"Listed as item 4 in equipment table\"\n", "          }\n", "        ]\n", "      },\n", "      {\n", "        \"item_name\": \"Primary Mill Lube System\",\n", "        \"prices\": [\n", "          {\n", "            \"bid_id\": \"fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b\",\n", "            \"bidder\": \"NTC\",\n", "            \"price\": \"446498\",\n", "            \"unit_price\": \"USD\",\n", "            \"comment\": \"Listed as item 6 in equipment table\"\n", "          }\n", "        ]\n", "      },\n", "      {\n", "        \"item_name\": \"Primary Mill Gear Spray System\",\n", "        \"prices\": [\n", "          {\n", "            \"bid_id\": \"fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b\",\n", "            \"bidder\": \"NTC\",\n", "            \"price\": \"27953\",\n", "            \"unit_price\": \"USD\",\n", "            \"comment\": \"Listed as item 7 in equipment table\"\n", "          }\n", "        ]\n", "      },\n", "      {\n", "        \"item_name\": \"Primary Mill Motors with Accessories\",\n", "        \"prices\": [\n", "          {\n", "            \"bid_id\": \"fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b\",\n", "            \"bidder\": \"NTC\",\n", "            \"price\": \"895280\",\n", "            \"unit_price\": \"USD\",\n", "            \"comment\": \"Listed as item 8 in equipment table\"\n", "          }\n", "        ]\n", "      }\n", "    ]\n", "  },\n", "  \"eq003\": {\n", "    \"equipment\": \"eq003\",\n", "    \"equipment_name\": \"Crusher\",\n", "    \"data\": []\n", "  },\n", "  \"eq001\": {\n", "    \"equipment\": \"eq001\",\n", "    \"equipment_name\": \"Ball Mill\",\n", "    \"data\": [\n", "      {\n", "        \"item_name\": \"Ball Mill - 6.7m x 10.53m with 9MW Drive\",\n", "        \"prices\": [\n", "          {\n", "            \"bid_id\": \"b80eb88d-41ec-49e3-9eac-5a776cd051e0\",\n", "            \"bidder\": \"CITIC\",\n", "            \"price\": \"5236500\",\n", "            \"unit_price\": \"USD\",\n", "            \"comment\": \"Listed as item 3.01\"\n", "          },\n", "          {\n", "            \"bid_id\": \"fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b\",\n", "            \"bidder\": \"NTC\",\n", "            \"price\": \"4988371\",\n", "            \"unit_price\": \"USD\",\n", "            \"comment\": \"Listed as item 15 in equipment table\"\n", "          }\n", "        ]\n", "      },\n", "      {\n", "        \"item_name\": \"Ball Mill Motor - 9000kW WRIM\",\n", "        \"prices\": [\n", "          {\n", "            \"bid_id\": \"b80eb88d-41ec-49e3-9eac-5a776cd051e0\",\n", "            \"bidder\": \"CITIC\",\n", "            \"price\": \"528300\",\n", "            \"unit_price\": \"USD\",\n", "            \"comment\": \"MKS's Solution + WEG's Solution\"\n", "          },\n", "          {\n", "            \"bid_id\": \"fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b\",\n", "            \"bidder\": \"NTC\",\n", "            \"price\": \"658031\",\n", "            \"unit_price\": \"USD\",\n", "            \"comment\": \"Listed as item 25 in equipment table\"\n", "          }\n", "        ]\n", "      },\n", "      {\n", "        \"item_name\": \"Spare 9000kW WRIM Main Motor\",\n", "        \"prices\": [\n", "          {\n", "            \"bid_id\": \"b80eb88d-41ec-49e3-9eac-5a776cd051e0\",\n", "            \"bidder\": \"CITIC\",\n", "            \"price\": \"473100\",\n", "            \"unit_price\": \"USD\",\n", "            \"comment\": \"One Set, if ordered with mill together\"\n", "          }\n", "        ]\n", "      },\n", "      {\n", "        \"item_name\": \"Ball Mill Water Resistor\",\n", "        \"prices\": [\n", "          {\n", "            \"bid_id\": \"b80eb88d-41ec-49e3-9eac-5a776cd051e0\",\n", "            \"bidder\": \"CITIC\",\n", "            \"price\": \"143362.57\",\n", "            \"unit_price\": \"CNY\",\n", "            \"comment\": \"MKS outdoor model, price includes 50% import duty + 13% VAT\"\n", "          },\n", "          {\n", "            \"bid_id\": \"fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b\",\n", "            \"bidder\": \"NTC\",\n", "            \"price\": \"102688\",\n", "            \"unit_price\": \"USD\",\n", "            \"comment\": \"Listed as item 26 in equipment table\"\n", "          }\n", "        ]\n", "      },\n", "      {\n", "        \"item_name\": \"Ball Mill Liners\",\n", "        \"prices\": [\n", "          {\n", "            \"bid_id\": \"fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b\",\n", "            \"bidder\": \"NTC\",\n", "            \"price\": \"332013\",\n", "            \"unit_price\": \"USD\",\n", "            \"comment\": \"Listed as item 16 in equipment table\"\n", "          }\n", "        ]\n", "      },\n", "      {\n", "        \"item_name\": \"Ball Mill Retractable Feed Chute\",\n", "        \"prices\": [\n", "          {\n", "            \"bid_id\": \"fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b\",\n", "            \"bidder\": \"NTC\",\n", "            \"price\": \"73673\",\n", "            \"unit_price\": \"USD\",\n", "            \"comment\": \"Listed as item 17 in equipment table\"\n", "          }\n", "        ]\n", "      },\n", "      {\n", "        \"item_name\": \"Ball Mill Trommel Screen\",\n", "        \"prices\": [\n", "          {\n", "            \"bid_id\": \"fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b\",\n", "            \"bidder\": \"NTC\",\n", "            \"price\": \"104975\",\n", "            \"unit_price\": \"USD\",\n", "            \"comment\": \"Listed as item 20 in equipment table\"\n", "          }\n", "        ]\n", "      },\n", "      {\n", "        \"item_name\": \"Ball Mill Lube System\",\n", "        \"prices\": [\n", "          {\n", "            \"bid_id\": \"fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b\",\n", "            \"bidder\": \"NTC\",\n", "            \"price\": \"338895\",\n", "            \"unit_price\": \"USD\",\n", "            \"comment\": \"Listed as item 24 in equipment table\"\n", "          }\n", "        ]\n", "      },\n", "      {\n", "        \"item_name\": \"Ball Mill Gear Spray System\",\n", "        \"prices\": [\n", "          {\n", "            \"bid_id\": \"fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b\",\n", "            \"bidder\": \"NTC\",\n", "            \"price\": \"17061\",\n", "            \"unit_price\": \"USD\",\n", "            \"comment\": \"Listed as item 25 in equipment table\"\n", "          }\n", "        ]\n", "      }\n", "    ]\n", "  }\n", "}\n"]}], "source": ["from typing import Dict, List, Any\n", "import json\n", "from cbp.services.llm.claude import AnthropicService\n", "import logging\n", "\n", "class EquipmentReportGenerator:\n", "    def __init__(self):\n", "        logging.basicConfig(level=logging.INFO)\n", "        self.logger = logging.getLogger(__name__)\n", "        self.llm_service = AnthropicService()\n", "    \n", "    def generate_consolidated_report(self, bid_results: Dict[str, Dict[str, List[Dict[str, str]]]]) -> Dict[str, List[Dict[str, Any]]]:\n", "        \"\"\"\n", "        Generate a consolidated report of equipment prices across multiple bids\n", "        \n", "        Args:\n", "            bid_results (Dict): Nested dictionary with structure {bid_id: {equipment_id: {'name': str, 'bidder': str, 'items': List[Dict]}}}\n", "            \n", "        Returns:\n", "            Dict: Dictionary with equipment_id as keys and consolidated data as values\n", "        \"\"\"\n", "        consolidated_report = {}\n", "        \n", "        # Get all equipment IDs from the first bid (assuming all bids have the same equipment IDs)\n", "        equipment_ids = set()\n", "        for bid_id, bid_data in bid_results.items():\n", "            for equipment_id in bid_data.keys():\n", "                equipment_ids.add(equipment_id)\n", "        \n", "        # Process each equipment type\n", "        for equipment_id in equipment_ids:\n", "            self.logger.info(f\"Generating report for equipment ID: {equipment_id}\")\n", "            \n", "            # Collect all data for this equipment ID across all bids\n", "            equipment_data_across_bids = {}\n", "            for bid_id, bid_data in bid_results.items():\n", "                if equipment_id in bid_data:\n", "                    equipment_data = bid_data[equipment_id]\n", "                    equipment_data_across_bids[bid_id] = {\n", "                        'bidder': equipment_data.get('bidder', 'Unknown'),\n", "                        'items': equipment_data.get('items', [])\n", "                    }\n", "            \n", "            # If we have data for this equipment from multiple bids, generate a consolidated report\n", "            if equipment_data_across_bids:\n", "                consolidated_items = self.consolidate_equipment_items(equipment_id, equipment_data_across_bids)\n", "                consolidated_report[equipment_id] = consolidated_items\n", "            else:\n", "                self.logger.warning(f\"No data found for equipment ID: {equipment_id}\")\n", "                consolidated_report[equipment_id] = []\n", "        \n", "        return consolidated_report\n", "    \n", "    def consolidate_equipment_items(self, equipment_id: str, equipment_data_across_bids: Dict[str, Dict[str, Any]]) -> List[Dict[str, Any]]:\n", "        \"\"\"\n", "        Use LLM to identify common items across bids and consolidate their information\n", "        \n", "        Args:\n", "            equipment_id (str): The ID of the equipment being processed\n", "            equipment_data_across_bids (Dict): Dictionary mapping bid_ids to dicts containing bidder and items\n", "            \n", "        Returns:\n", "            List[Dict]: Consolidated data for this equipment\n", "        \"\"\"\n", "        # Convert the data to a format that's easier for the LLM to process\n", "        formatted_data = json.dumps(equipment_data_across_bids, indent=2)\n", "        \n", "        # Prepare prompt for LLM to consolidate items\n", "        consolidation_prompt = {\n", "            \"role\": \"user\", \n", "            \"content\": f\"\"\"\n", "            I have equipment pricing data from multiple bids for equipment ID: {equipment_id}.\n", "            Please analyze this data and create a consolidated report that:\n", "            \n", "            1. Identifies common items across different bids\n", "            2. Standardizes item names where appropriate\n", "            3. Lists each item with their prices from each bid\n", "            \n", "            CRITICAL: Price mixing between bids is strictly forbidden. Each price MUST be kept with its original bid_id and bidder. \n", "            DO NOT mix or swap prices between different bids under any circumstances, as this would invalidate the entire analysis.\n", "            Ensure 100% accuracy in maintaining the correct price-to-bid relationships.\n", "            \n", "            Format the output as a JSON array where each object has:\n", "            - item_name: The standardized name of the equipment item\n", "            - prices: An array of objects containing bid_id, bidder, price, unit_price, and comment\n", "            \n", "            Example output format:\n", "            [\n", "              {{\n", "                \"item_name\": \"Ball Mill 22x34\",\n", "                \"prices\": [\n", "                  {{\n", "                    \"bid_id\": \"bid_id_1\",\n", "                    \"bidder\": \"CITIC\",\n", "                    \"price\": \"4988371\",\n", "                    \"unit_price\": \"USD\",\n", "                    \"comment\": \"Listed as item 15 in main equipment table\"\n", "                  }},\n", "                  {{\n", "                    \"bid_id\": \"bid_id_2\",\n", "                    \"bidder\": \"NTC\", \n", "                    \"price\": \"5100000\",\n", "                    \"unit_price\": \"USD\",\n", "                    \"comment\": \"Includes installation\"\n", "                  }}\n", "                ]\n", "              }},\n", "              ...\n", "            ]\n", "            \n", "            Only return valid JSON without any explanation or additional text.\n", "            If an item appears in only one bid, still include it in the report.\n", "            Remember: Price mixing between bids is absolutely forbidden.\n", "            \n", "            Here is the data:\n", "            {formatted_data}\n", "            \"\"\"\n", "        }\n", "        \n", "        # Generate the consolidated report using LLM\n", "        response = self.llm_service.generate_sync(\n", "            messages=[consolidation_prompt],\n", "            model_name=\"Claude 3.5 Sonnet\",\n", "            temperature=0.05,  # Slightly higher temperature for more creative matching\n", "            max_tokens=4096,\n", "            use_fallback=False\n", "        )\n", "\n", "        try:\n", "            completion = response[0].content[0].text\n", "            self.logger.info(f\"Generated consolidated report for equipment ID: {equipment_id}\")\n", "            consolidated_items = json.loads(completion)\n", "            return consolidated_items\n", "        except json.JSONDecodeError:\n", "            self.logger.error(f\"Failed to parse JSON from LLM response for equipment ID: {equipment_id}\")\n", "            self.logger.debug(f\"Raw response: {completion}\")\n", "            return []\n", "        except Exception as e:\n", "            self.logger.error(f\"Error consolidating items for equipment ID {equipment_id}: {str(e)}\")\n", "            return []\n", "\n", "\n", "def generate_final_report(all_bid_results: Dict[str, Dict[str, List[Dict[str, str]]]], equipment_list: List[Dict[str, str]]) -> Dict[str, Dict[str, Any]]:\n", "    \"\"\"\n", "    Generate a final consolidated report for all equipment across all bids\n", "    \n", "    Args:\n", "        all_bid_results: Dictionary mapping bid_ids to equipment data\n", "        equipment_list: List of dictionaries containing equipment_id and equipment_name mappings\n", "        \n", "    Returns:\n", "        Dict: Final report with equipment_id keys and consolidated data including equipment names\n", "    \"\"\"\n", "    generator = EquipmentReportGenerator()\n", "    consolidated_report = generator.generate_consolidated_report(all_bid_results)\n", "    \n", "    # Create equipment ID to name mapping\n", "    equipment_names = {item[\"equipment_id\"]: item[\"equipment_name\"] for item in equipment_list}\n", "    \n", "    # Format into the desired output structure with equipment names\n", "    final_report = {}\n", "    for equipment_id, items in consolidated_report.items():\n", "        final_report[equipment_id] = {\n", "            \"equipment\": equipment_id,\n", "            \"equipment_name\": equipment_names.get(equipment_id, \"Unknown Equipment\"),\n", "            \"data\": items\n", "        }\n", "    \n", "    return final_report\n", "\n", "\n", "# Example usage\n", "def main():\n", "    # Example equipment list\n", "    equipment_list = [\n", "        {\"equipment_id\": \"eq001\", \"equipment_name\": \"Ball Mill\"},\n", "        {\"equipment_id\": \"eq002\", \"equipment_name\": \"AG Mill\"},\n", "        {\"equipment_id\": \"eq003\", \"equipment_name\": \"Crusher\"}\n", "    ]\n", "    \n", "    # Generate final report\n", "    final_report = generate_final_report(res, equipment_list)\n", "    \n", "    # Print the results\n", "    print(json.dumps(final_report, indent=2))\n", "\n", "    return final_report\n", "    \n", "    # Expected output structure:\n", "    # {\n", "    #   \"eq001\": {\n", "    #     \"equipment\": \"eq001\",\n", "    #     \"equipment_name\": \"Ball Mill\",\n", "    #     \"data\": [\n", "    #       {\n", "    #         \"item_name\": \"Ball Mill 22x34\",\n", "    #         \"prices\": [\n", "    #           {\n", "    #             \"bid_id\": \"bid_id_1\",\n", "    #             \"bidder\": \"CITIC\",\n", "    #             \"price\": \"4988371\",\n", "    #             \"unit_price\": \"USD\",\n", "    #             \"comment\": \"Listed as item 15\"\n", "    #           },\n", "    #           {\n", "    #             \"bid_id\": \"bid_id_2\",\n", "    #             \"bidder\": \"NTC\", \n", "    #             \"price\": \"5100000\",\n", "    #             \"unit_price\": \"USD\",\n", "    #             \"comment\": \"Including installation\"\n", "    #           }\n", "    #         ]\n", "    #       },\n", "    #       {\n", "    #         \"item_name\": \"Ball Mill Liners\",\n", "    #         \"prices\": [\n", "    #           {\n", "    #             \"bid_id\": \"bid_id_1\",\n", "    #             \"bidder\": \"CITIC\",\n", "    #             \"price\": \"332013\",\n", "    #             \"unit_price\": \"USD\",\n", "    #             \"comment\": \"Listed as item 16\"\n", "    #           },\n", "    #           {\n", "    #             \"bid_id\": \"bid_id_2\",\n", "    #             \"bidder\": \"NTC\",\n", "    #             \"price\": \"350000\",\n", "    #             \"unit_price\": \"USD\",\n", "    #             \"comment\": \"Wear parts\"\n", "    #           }\n", "    #         ]\n", "    #       }\n", "    #     ]\n", "    #   },\n", "    #   \"eq002\": {\n", "    #     \"equipment\": \"eq002\", \n", "    #     \"equipment_name\": \"AG Mill\",\n", "    #     \"data\": [...]\n", "    #   }\n", "    # }\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    report = main()"]}, {"cell_type": "code", "execution_count": 99, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'eq002': {'equipment': 'eq002',\n", "  'equipment_name': 'AG Mill',\n", "  'data': [{'item_name': 'AG Mill with Dual Pinion Drive',\n", "    'prices': [{'bid_id': 'b80eb88d-41ec-49e3-9eac-5a776cd051e0',\n", "      'bidder': 'CITIC',\n", "      'price': '7437400',\n", "      'unit_price': 'USD',\n", "      'comment': 'Listed as item 1.01'},\n", "     {'bid_id': 'fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b',\n", "      'bidder': 'NTC',\n", "      'price': '7769217',\n", "      'unit_price': 'USD',\n", "      'comment': 'Listed as item 1 in equipment table'}]},\n", "   {'item_name': 'VFD System',\n", "    'prices': [{'bid_id': 'b80eb88d-41ec-49e3-9eac-5a776cd051e0',\n", "      'bidder': 'CITIC',\n", "      'price': '2067300',\n", "      'unit_price': 'USD',\n", "      'comment': \"WEG's Solution, listed as item 1.02\"},\n", "     {'bid_id': 'fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b',\n", "      'bidder': 'NTC',\n", "      'price': '1015799',\n", "      'unit_price': 'USD',\n", "      'comment': 'Listed as item 9 in equipment table'}]},\n", "   {'item_name': 'Spare SCIM Motor',\n", "    'prices': [{'bid_id': 'b80eb88d-41ec-49e3-9eac-5a776cd051e0',\n", "      'bidder': 'CITIC',\n", "      'price': '352400',\n", "      'unit_price': 'USD',\n", "      'comment': 'One Set, if ordered with mill together'}]},\n", "   {'item_name': 'Primary Mill Liners',\n", "    'prices': [{'bid_id': 'fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b',\n", "      'bidder': 'NTC',\n", "      'price': '1086765',\n", "      'unit_price': 'USD',\n", "      'comment': 'Listed as item 2 in equipment table'}]},\n", "   {'item_name': 'Primary Mill Retractable Feed Chute',\n", "    'prices': [{'bid_id': 'fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b',\n", "      'bidder': 'NTC',\n", "      'price': '125440',\n", "      'unit_price': 'USD',\n", "      'comment': 'Listed as item 4 in equipment table'}]},\n", "   {'item_name': 'Primary Mill Lube System',\n", "    'prices': [{'bid_id': 'fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b',\n", "      'bidder': 'NTC',\n", "      'price': '446498',\n", "      'unit_price': 'USD',\n", "      'comment': 'Listed as item 6 in equipment table'}]},\n", "   {'item_name': 'Primary Mill Gear Spray System',\n", "    'prices': [{'bid_id': 'fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b',\n", "      'bidder': 'NTC',\n", "      'price': '27953',\n", "      'unit_price': 'USD',\n", "      'comment': 'Listed as item 7 in equipment table'}]},\n", "   {'item_name': 'Primary Mill Motors with Accessories',\n", "    'prices': [{'bid_id': 'fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b',\n", "      'bidder': 'NTC',\n", "      'price': '895280',\n", "      'unit_price': 'USD',\n", "      'comment': 'Listed as item 8 in equipment table'}]}]},\n", " 'eq003': {'equipment': 'eq003', 'equipment_name': 'Crusher', 'data': []},\n", " 'eq001': {'equipment': 'eq001',\n", "  'equipment_name': 'Ball Mill',\n", "  'data': [{'item_name': 'Ball Mill - 6.7m x 10.53m with 9MW Drive',\n", "    'prices': [{'bid_id': 'b80eb88d-41ec-49e3-9eac-5a776cd051e0',\n", "      'bidder': 'CITIC',\n", "      'price': '5236500',\n", "      'unit_price': 'USD',\n", "      'comment': 'Listed as item 3.01'},\n", "     {'bid_id': 'fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b',\n", "      'bidder': 'NTC',\n", "      'price': '4988371',\n", "      'unit_price': 'USD',\n", "      'comment': 'Listed as item 15 in equipment table'}]},\n", "   {'item_name': 'Ball Mill Motor - 9000kW WRIM',\n", "    'prices': [{'bid_id': 'b80eb88d-41ec-49e3-9eac-5a776cd051e0',\n", "      'bidder': 'CITIC',\n", "      'price': '528300',\n", "      'unit_price': 'USD',\n", "      'comment': \"MKS's Solution + WEG's Solution\"},\n", "     {'bid_id': 'fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b',\n", "      'bidder': 'NTC',\n", "      'price': '658031',\n", "      'unit_price': 'USD',\n", "      'comment': 'Listed as item 25 in equipment table'}]},\n", "   {'item_name': 'Spare 9000kW WRIM Main Motor',\n", "    'prices': [{'bid_id': 'b80eb88d-41ec-49e3-9eac-5a776cd051e0',\n", "      'bidder': 'CITIC',\n", "      'price': '473100',\n", "      'unit_price': 'USD',\n", "      'comment': 'One Set, if ordered with mill together'}]},\n", "   {'item_name': 'Ball Mill Water Resistor',\n", "    'prices': [{'bid_id': 'b80eb88d-41ec-49e3-9eac-5a776cd051e0',\n", "      'bidder': 'CITIC',\n", "      'price': '143362.57',\n", "      'unit_price': 'CNY',\n", "      'comment': 'MKS outdoor model, price includes 50% import duty + 13% VAT'},\n", "     {'bid_id': 'fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b',\n", "      'bidder': 'NTC',\n", "      'price': '102688',\n", "      'unit_price': 'USD',\n", "      'comment': 'Listed as item 26 in equipment table'}]},\n", "   {'item_name': 'Ball Mill Liners',\n", "    'prices': [{'bid_id': 'fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b',\n", "      'bidder': 'NTC',\n", "      'price': '332013',\n", "      'unit_price': 'USD',\n", "      'comment': 'Listed as item 16 in equipment table'}]},\n", "   {'item_name': 'Ball Mill Retractable Feed Chute',\n", "    'prices': [{'bid_id': 'fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b',\n", "      'bidder': 'NTC',\n", "      'price': '73673',\n", "      'unit_price': 'USD',\n", "      'comment': 'Listed as item 17 in equipment table'}]},\n", "   {'item_name': 'Ball Mill Trommel Screen',\n", "    'prices': [{'bid_id': 'fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b',\n", "      'bidder': 'NTC',\n", "      'price': '104975',\n", "      'unit_price': 'USD',\n", "      'comment': 'Listed as item 20 in equipment table'}]},\n", "   {'item_name': 'Ball Mill Lube System',\n", "    'prices': [{'bid_id': 'fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b',\n", "      'bidder': 'NTC',\n", "      'price': '338895',\n", "      'unit_price': 'USD',\n", "      'comment': 'Listed as item 24 in equipment table'}]},\n", "   {'item_name': 'Ball Mill Gear Spray System',\n", "    'prices': [{'bid_id': 'fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b',\n", "      'bidder': 'NTC',\n", "      'price': '17061',\n", "      'unit_price': 'USD',\n", "      'comment': 'Listed as item 25 in equipment table'}]}]}}"]}, "execution_count": 99, "metadata": {}, "output_type": "execute_result"}], "source": ["report"]}, {"cell_type": "code", "execution_count": 100, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["HTML report generated successfully!\n"]}], "source": ["def generate_equipment_report_html(consolidated_report):\n", "    \"\"\"\n", "    Convert the consolidated equipment report to HTML tables\n", "    \n", "    Args:\n", "        consolidated_report (dict): The consolidated report with equipment data\n", "        \n", "    Returns:\n", "        str: HTML string containing tables for each equipment type\n", "    \"\"\"\n", "    html = []\n", "    html.append(\"\"\"\n", "    <!DOCTYPE html>\n", "    <html lang=\"en\">\n", "    <head>\n", "        <meta charset=\"UTF-8\">\n", "        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n", "        <title>Equipment Price Comparison Report</title>\n", "        <style>\n", "            body {\n", "                font-family: <PERSON><PERSON>, sans-serif;\n", "                margin: 20px;\n", "                color: #333;\n", "            }\n", "            h1 {\n", "                color: #2c3e50;\n", "                border-bottom: 2px solid #3498db;\n", "                padding-bottom: 10px;\n", "            }\n", "            h2 {\n", "                color: #3498db;\n", "                margin-top: 30px;\n", "                border-bottom: 1px solid #ddd;\n", "                padding-bottom: 5px;\n", "            }\n", "            table {\n", "                width: 100%;\n", "                border-collapse: collapse;\n", "                margin-bottom: 30px;\n", "                box-shadow: 0 2px 3px rgba(0,0,0,0.1);\n", "            }\n", "            th {\n", "                background-color: #3498db;\n", "                color: white;\n", "                text-align: left;\n", "                padding: 12px 15px;\n", "            }\n", "            td {\n", "                padding: 10px 15px;\n", "                border-bottom: 1px solid #ddd;\n", "            }\n", "            tr:nth-child(even) {\n", "                background-color: #f2f2f2;\n", "            }\n", "            tr:hover {\n", "                background-color: #e9f7fe;\n", "            }\n", "            .price {\n", "                text-align: right;\n", "                font-family: monospace;\n", "                font-size: 1.1em;\n", "            }\n", "            .comment {\n", "                font-size: 0.85em;\n", "                color: #666;\n", "                font-style: italic;\n", "            }\n", "            .equipment-section {\n", "                margin-bottom: 40px;\n", "            }\n", "        </style>\n", "    </head>\n", "    <body>\n", "        <h1>Equipment Price Comparison Report</h1>\n", "    \"\"\")\n", "    \n", "    # For each equipment\n", "    for equipment_id, equipment_data in consolidated_report.items():\n", "        equipment_name = equipment_data.get(\"equipment_name\", equipment_id)\n", "        \n", "        html.append(f\"\"\"\n", "        <div class=\"equipment-section\">\n", "            <h2>{equipment_name} (ID: {equipment_id})</h2>\n", "        \"\"\")\n", "        \n", "        if not equipment_data.get(\"data\"):\n", "            html.append(\"<p>No data available for this equipment.</p>\")\n", "            html.append(\"</div>\")\n", "            continue\n", "        \n", "        # Get all bidders to create table headers\n", "        all_bidders = set()\n", "        for item in equipment_data[\"data\"]:\n", "            for price_info in item[\"prices\"]:\n", "                all_bidders.add(price_info.get(\"bidder\", price_info[\"bid_id\"]))\n", "        \n", "        bidders_list = sorted(list(all_bidders))\n", "        \n", "        # Create the table\n", "        html.append(\"<table>\")\n", "        \n", "        # Table header\n", "        html.append(\"<thead><tr>\")\n", "        html.append(\"<th>Item Name</th>\")\n", "        \n", "        for bidder in bidders_list:\n", "            html.append(f\"<th>{bidder}</th>\")\n", "        \n", "        html.append(\"</tr></thead>\")\n", "        \n", "        # Table body\n", "        html.append(\"<tbody>\")\n", "        \n", "        for item in equipment_data[\"data\"]:\n", "            html.append(\"<tr>\")\n", "            html.append(f\"<td>{item['item_name']}</td>\")\n", "            \n", "            # Create a mapping of bidder to price info for this item\n", "            bidder_price_map = {}\n", "            for price_info in item[\"prices\"]:\n", "                bidder = price_info.get(\"bidder\", price_info[\"bid_id\"])\n", "                bidder_price_map[bidder] = price_info\n", "            \n", "            # Add cells for each bidder\n", "            for bidder in bidders_list:\n", "                if bidder in bidder_price_map:\n", "                    price_info = bidder_price_map[bidder]\n", "                    price_display = f\"{price_info['price']} {price_info['unit_price']}\"\n", "                    comment = price_info.get(\"comment\", \"\")\n", "                    \n", "                    html.append(f\"\"\"\n", "                    <td>\n", "                        <div class=\"price\">{price_display}</div>\n", "                        <div class=\"comment\">{comment}</div>\n", "                    </td>\n", "                    \"\"\")\n", "                else:\n", "                    html.append(\"<td>-</td>\")\n", "            \n", "            html.append(\"</tr>\")\n", "        \n", "        html.append(\"</tbody>\")\n", "        html.append(\"</table>\")\n", "        html.append(\"</div>\")\n", "    \n", "    html.append(\"\"\"\n", "    </body>\n", "    </html>\n", "    \"\"\")\n", "    \n", "    return \"\".join(html)\n", "\n", "\n", "# Example usage\n", "def main():\n", "    \n", "    \n", "    # Generate HTML\n", "    html_report = generate_equipment_report_html(report)\n", "    \n", "    # Write to file\n", "    with open(\"1_equipment_price_report.html\", \"w\") as f:\n", "        f.write(html_report)\n", "    \n", "    print(\"HTML report generated successfully!\")\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'equipment': 'eq001',\n", " 'equipment_name': 'Ball Mill',\n", " 'data': [{'item_name': 'Ball Mill 22x34 9000kW WRIM',\n", "   'prices': [{'bid_id': 'a351c33d-9d9a-4afb-a40d-ca9286c558a0',\n", "     'bidder': 'CITIC',\n", "     'price': '4988371',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 15 in equipment table'},\n", "    {'bid_id': 'fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b',\n", "     'bidder': 'NTC',\n", "     'price': '4988371',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 15 in equipment table'}]},\n", "  {'item_name': 'Ball Mill Liners',\n", "   'prices': [{'bid_id': 'a351c33d-9d9a-4afb-a40d-ca9286c558a0',\n", "     'bidder': 'CITIC',\n", "     'price': '332013',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 16 in equipment table'},\n", "    {'bid_id': 'fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b',\n", "     'bidder': 'NTC',\n", "     'price': '332013',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 16 in equipment table'}]},\n", "  {'item_name': 'Ball Mill Retractable Feed Chute',\n", "   'prices': [{'bid_id': 'a351c33d-9d9a-4afb-a40d-ca9286c558a0',\n", "     'bidder': 'CITIC',\n", "     'price': '73673',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 17 in equipment table'},\n", "    {'bid_id': 'fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b',\n", "     'bidder': 'NTC',\n", "     'price': '73673',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 17 in equipment table'}]},\n", "  {'item_name': 'Ball Mill Trommel Screen',\n", "   'prices': [{'bid_id': 'a351c33d-9d9a-4afb-a40d-ca9286c558a0',\n", "     'bidder': 'CITIC',\n", "     'price': '104975',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 20 in equipment table'},\n", "    {'bid_id': 'fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b',\n", "     'bidder': 'NTC',\n", "     'price': '104975',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 20 in equipment table'}]},\n", "  {'item_name': 'Ball Mill Lube System',\n", "   'prices': [{'bid_id': 'a351c33d-9d9a-4afb-a40d-ca9286c558a0',\n", "     'bidder': 'CITIC',\n", "     'price': '338895',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 24 in equipment table'},\n", "    {'bid_id': 'fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b',\n", "     'bidder': 'NTC',\n", "     'price': '338895',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 24 in equipment table'}]},\n", "  {'item_name': 'Ball Mill Gear Spray System',\n", "   'prices': [{'bid_id': 'a351c33d-9d9a-4afb-a40d-ca9286c558a0',\n", "     'bidder': 'CITIC',\n", "     'price': '17061',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 25 in equipment table'},\n", "    {'bid_id': 'fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b',\n", "     'bidder': 'NTC',\n", "     'price': '17061',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 25 in equipment table'}]},\n", "  {'item_name': 'Ball Mill Motor WRIM',\n", "   'prices': [{'bid_id': 'a351c33d-9d9a-4afb-a40d-ca9286c558a0',\n", "     'bidder': 'CITIC',\n", "     'price': '658031',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 25 in equipment table'},\n", "    {'bid_id': 'fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b',\n", "     'bidder': 'NTC',\n", "     'price': '658031',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 25 in equipment table'}]},\n", "  {'item_name': 'Ball Mill LRS WRIM',\n", "   'prices': [{'bid_id': 'a351c33d-9d9a-4afb-a40d-ca9286c558a0',\n", "     'bidder': 'CITIC',\n", "     'price': '102688',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 26 in equipment table'},\n", "    {'bid_id': 'fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b',\n", "     'bidder': 'NTC',\n", "     'price': '102688',\n", "     'unit_price': 'USD',\n", "     'comment': 'Listed as item 26 in equipment table'}]}]}"]}, "execution_count": 89, "metadata": {}, "output_type": "execute_result"}], "source": ["report[\"eq001\"]"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'eq001': [{'item': 'Ball Mill - 22 x 34, 1 x 9000kW WRIM',\n", "   'price': '4988371',\n", "   'unit_price': 'USD',\n", "   'comment': 'Listed as item 15 in equipment table'},\n", "  {'item': 'Ball Mill Liners',\n", "   'price': '332013',\n", "   'unit_price': 'USD',\n", "   'comment': 'Listed as item 16 in equipment table'},\n", "  {'item': 'Ball Mill Retractable Feed Chute',\n", "   'price': '73673',\n", "   'unit_price': 'USD',\n", "   'comment': 'Listed as item 17 in equipment table'},\n", "  {'item': 'Ball Mill Trommel Screen',\n", "   'price': '104975',\n", "   'unit_price': 'USD',\n", "   'comment': 'Listed as item 20 in equipment table'},\n", "  {'item': 'Ball Mill Lube System',\n", "   'price': '338895',\n", "   'unit_price': 'USD',\n", "   'comment': 'Listed as item 24 in equipment table'},\n", "  {'item': 'Ball Mill Gear Spray System',\n", "   'price': '17061',\n", "   'unit_price': 'USD',\n", "   'comment': 'Listed as item 25 in equipment table'},\n", "  {'item': 'Ball Mill Motor (WRIM including bedplates and lube system)',\n", "   'price': '658031',\n", "   'unit_price': 'USD',\n", "   'comment': 'Listed as item 25 in equipment table'},\n", "  {'item': 'Ball Mill LRS (For WRIM)',\n", "   'price': '102688',\n", "   'unit_price': 'USD',\n", "   'comment': 'Listed as item 26 in equipment table'}],\n", " 'eq002': [{'item': 'Primary (AG) Mill - 32 x 21, 2 x 6000kW SCIM',\n", "   'price': '7769217',\n", "   'unit_price': 'USD',\n", "   'comment': 'Listed as item 1 in equipment table'},\n", "  {'item': 'Primary Mill Liners',\n", "   'price': '1086765',\n", "   'unit_price': 'USD',\n", "   'comment': 'Listed as item 2 in equipment table'},\n", "  {'item': 'Primary Mill Retractable Feed Chute',\n", "   'price': '125440',\n", "   'unit_price': 'USD',\n", "   'comment': 'Listed as item 4 in equipment table'},\n", "  {'item': 'Primary Mill Lube System',\n", "   'price': '446498',\n", "   'unit_price': 'USD',\n", "   'comment': 'Listed as item 6 in equipment table'},\n", "  {'item': 'Primary Mill Gear Spray System',\n", "   'price': '27953',\n", "   'unit_price': 'USD',\n", "   'comment': 'Listed as item 7 in equipment table'},\n", "  {'item': 'Primary Mill Motors (SCIM including bedplates and lube system)',\n", "   'price': '895280',\n", "   'unit_price': 'USD',\n", "   'comment': 'Listed as item 8 in equipment table'},\n", "  {'item': 'Primary Mill VFD (for SCIM)',\n", "   'price': '1015799',\n", "   'unit_price': 'USD',\n", "   'comment': 'Listed as item 9 in equipment table'}],\n", " 'eq003': []}"]}, "execution_count": 80, "metadata": {}, "output_type": "execute_result"}], "source": ["res[\"fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rid = \"a351c33d-9d9a-4afb-a40d-ca9286c558a0\""]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'item': 'Ball Mill - 22 x 34, 1 x 9000kW WRIM',\n", "  'price': '4988371',\n", "  'unit_price': 'USD',\n", "  'comment': 'Listed as item 15 in equipment table'},\n", " {'item': 'Ball Mill Liners',\n", "  'price': '332013',\n", "  'unit_price': 'USD',\n", "  'comment': 'Listed as item 16 in equipment table'},\n", " {'item': 'Ball Mill Retractable Feed Chute',\n", "  'price': '73673',\n", "  'unit_price': 'USD',\n", "  'comment': 'Listed as item 17 in equipment table'},\n", " {'item': 'Ball Mill Trommel Screen',\n", "  'price': '104975',\n", "  'unit_price': 'USD',\n", "  'comment': 'Listed as item 20 in equipment table'},\n", " {'item': 'Ball Mill Lube System',\n", "  'price': '338895',\n", "  'unit_price': 'USD',\n", "  'comment': 'Listed as item 24 in equipment table'},\n", " {'item': 'Ball Mill Gear Spray System',\n", "  'price': '17061',\n", "  'unit_price': 'USD',\n", "  'comment': 'Listed as item 25 in equipment table'},\n", " {'item': 'Ball Mill Motor (WRIM including bedplates and lube system)',\n", "  'price': '658031',\n", "  'unit_price': 'USD',\n", "  'comment': 'Listed as item 25 in equipment table'},\n", " {'item': 'Ball Mill LRS (For WRIM)',\n", "  'price': '102688',\n", "  'unit_price': 'USD',\n", "  'comment': 'Listed as item 26 in equipment table'}]"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["res[\"fc87f9dd-1c3b-4e54-9787-0fb83ca2d83b\"][\"eq001\"]"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Generating report for equipment ID: eq002\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "INFO:cbp.services.llm.claude:Token Usage - Input: 1196, Output: 1530, Total: 2726\n", "ERROR:__main__:Error consolidating items: Expecting value: line 1 column 1 (char 0)\n", "INFO:__main__:Generating report for equipment ID: eq003\n", "INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "INFO:cbp.services.llm.claude:Token Usage - Input: 356, Output: 118, Total: 474\n", "ERROR:__main__:Error consolidating items: Expecting value: line 1 column 1 (char 0)\n", "INFO:__main__:Generating report for equipment ID: eq001\n", "INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "INFO:cbp.services.llm.claude:Token Usage - Input: 1294, Output: 1761, Total: 3055\n", "ERROR:__main__:Error consolidating items: Expecting value: line 1 column 1 (char 0)\n"]}], "source": ["from typing import Dict, List, Any\n", "import json\n", "from cbp.services.llm.claude import AnthropicService\n", "import logging\n", "\n", "class EquipmentReportGenerator:\n", "    def __init__(self):\n", "        logging.basicConfig(level=logging.INFO)\n", "        self.logger = logging.getLogger(__name__)\n", "        self.llm_service = AnthropicService()\n", "    \n", "    def generate_consolidated_report(self, bid_results: Dict[str, Dict[str, List[Dict[str, str]]]], output_format=\"html\") -> str:\n", "        \"\"\"\n", "        Generate a consolidated HTML report of equipment prices across multiple bids\n", "        \n", "        Args:\n", "            bid_results (Dict): Nested dictionary with bid data\n", "            output_format (str): Output format - \"html\" or \"json\"\n", "            \n", "        Returns:\n", "            str: HTML table or JSON string\n", "        \"\"\"\n", "        consolidated_data = {}\n", "        \n", "        # Get all equipment IDs\n", "        equipment_ids = set()\n", "        for bid_id, bid_data in bid_results.items():\n", "            for equipment_id in bid_data.keys():\n", "                equipment_ids.add(equipment_id)\n", "        \n", "        # Process each equipment type\n", "        for equipment_id in equipment_ids:\n", "            self.logger.info(f\"Generating report for equipment ID: {equipment_id}\")\n", "            \n", "            equipment_data_across_bids = {}\n", "            for bid_id, bid_data in bid_results.items():\n", "                if equipment_id in bid_data:\n", "                    equipment_data = bid_data[equipment_id]\n", "                    equipment_data_across_bids[bid_id] = {\n", "                        'bidder': equipment_data.get('bidder', 'Unknown'),\n", "                        'items': equipment_data.get('items', [])\n", "                    }\n", "            \n", "            if equipment_data_across_bids:\n", "                consolidated_data[equipment_id] = self.consolidate_equipment_items(equipment_id, equipment_data_across_bids)\n", "            else:\n", "                consolidated_data[equipment_id] = []\n", "\n", "        if output_format == \"html\":\n", "            return self.generate_html_table(consolidated_data)\n", "        else:\n", "            return json.dumps(consolidated_data, indent=2)\n", "\n", "    def generate_html_table(self, consolidated_data: Dict) -> str:\n", "        \"\"\"Generate HTML table from consolidated data\"\"\"\n", "        html = \"\"\"\n", "        <style>\n", "            .equipment-table {\n", "                width: 100%;\n", "                border-collapse: collapse;\n", "                margin: 20px 0;\n", "                font-family: <PERSON><PERSON>, sans-serif;\n", "            }\n", "            .equipment-table th, .equipment-table td {\n", "                border: 1px solid #ddd;\n", "                padding: 12px;\n", "                text-align: left;\n", "            }\n", "            .equipment-table th {\n", "                background-color: #f5f5f5;\n", "                font-weight: bold;\n", "            }\n", "            .equipment-table tr:nth-child(even) {\n", "                background-color: #f9f9f9;\n", "            }\n", "            .equipment-table tr:hover {\n", "                background-color: #f0f0f0;\n", "            }\n", "            .equipment-header {\n", "                background-color: #e0e0e0;\n", "                font-size: 1.2em;\n", "                font-weight: bold;\n", "            }\n", "            .price-cell {\n", "                text-align: right;\n", "            }\n", "        </style>\n", "        \"\"\"\n", "\n", "        for equipment_id, items in consolidated_data.items():\n", "            html += f\"\"\"\n", "            <table class=\"equipment-table\">\n", "                <tr class=\"equipment-header\">\n", "                    <td colspan=\"5\">{equipment_id}</td>\n", "                </tr>\n", "                <tr>\n", "                    <th>Item</th>\n", "                    <th>Bidder</th>\n", "                    <th>Price</th>\n", "                    <th><PERSON><PERSON><PERSON>cy</th>\n", "                    <th>Comments</th>\n", "                </tr>\n", "            \"\"\"\n", "            \n", "            for item in items:\n", "                for price_info in item['prices']:\n", "                    html += f\"\"\"\n", "                    <tr>\n", "                        <td>{item['item_name']}</td>\n", "                        <td>{price_info['bidder']}</td>\n", "                        <td class=\"price-cell\">{price_info['price']}</td>\n", "                        <td>{price_info['unit_price']}</td>\n", "                        <td>{price_info.get('comment', '')}</td>\n", "                    </tr>\n", "                    \"\"\"\n", "            \n", "            html += \"</table>\"\n", "\n", "        return html\n", "\n", "    def consolidate_equipment_items(self, equipment_id: str, equipment_data_across_bids: Dict[str, Dict[str, Any]]) -> List[Dict[str, Any]]:\n", "        \"\"\"Consolidate equipment items using LLM\"\"\"\n", "        formatted_data = json.dumps(equipment_data_across_bids, indent=2)\n", "        \n", "        consolidation_prompt = {\n", "            \"role\": \"user\", \n", "            \"content\": f\"\"\"\n", "            I have equipment pricing data from multiple bids for equipment ID: {equipment_id}.\n", "            Please analyze this data and create a consolidated report that:\n", "            \n", "            1. Identifies common items across different bids\n", "            2. Standardizes item names where appropriate\n", "            3. Lists each item with their prices from each bid\n", "            \n", "            Format the output as a JSON array where each object has:\n", "            - item_name: The standardized name of the equipment item\n", "            - prices: An array of objects containing bid_id, bidder, price, unit_price, and comment\n", "            \n", "            Example output format:\n", "            [\n", "              {{\n", "                \"item_name\": \"Standardized Name\",\n", "                \"prices\": [\n", "                  {{\n", "                    \"bid_id\": \"bid_id\",\n", "                    \"bidder\": \"Bidder Name\",\n", "                    \"price\": \"Price Value\",\n", "                    \"unit_price\": \"Currency\",\n", "                    \"comment\": \"Additional Info\"\n", "                  }}\n", "                ]\n", "              }}\n", "            ]\n", "            \n", "            Data to analyze:\n", "            {formatted_data}\n", "            \"\"\"\n", "        }\n", "\n", "        try:\n", "            response = self.llm_service.generate_sync(\n", "                messages=[consolidation_prompt],\n", "                model_name=\"Claude 3.5 Sonnet\",\n", "                temperature=0.05,\n", "                max_tokens=4096,\n", "                use_fallback=False\n", "            )\n", "            completion = response[0].content[0].text\n", "            return json.loads(completion)\n", "        except Exception as e:\n", "            self.logger.error(f\"Error consolidating items: {str(e)}\")\n", "            return []\n", "\n", "def generate_equipment_report(bid_results: Dict, equipment_list: List[Dict[str, str]], output_format=\"html\") -> str:\n", "    \"\"\"Generate equipment report in HTML or JSON format\"\"\"\n", "    generator = EquipmentReportGenerator()\n", "    report = generator.generate_consolidated_report(bid_results, output_format)\n", "    return report\n", "\n", "def main():\n", "    equipment_list = [\n", "        {\"equipment_id\": \"eq001\", \"equipment_name\": \"Ball Mill\"},\n", "        {\"equipment_id\": \"eq002\", \"equipment_name\": \"AG Mill\"},\n", "        {\"equipment_id\": \"eq003\", \"equipment_name\": \"Crusher\"}\n", "    ]\n", "    \n", "    html_report = generate_equipment_report(res, equipment_list, \"html\")\n", "    #self.logger.info(f\"Generated HTML table report for equipment ID: {res}\")\n", "    return html_report\n", "\n", "if __name__ == \"__main__\":\n", "    report = main()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["ERROR:asyncio:Task exception was never retrieved\n", "future: <Task finished name='Task-89' coro=<main() done, defined at /var/folders/xp/jfsq68yd6md3w078h7gx7sn40000gn/T/ipykernel_50502/1836125061.py:89> exception=TypeError('the JSON object must be str, bytes or bytearray, not tuple')>\n", "Traceback (most recent call last):\n", "  File \"/var/folders/xp/jfsq68yd6md3w078h7gx7sn40000gn/T/ipykernel_50502/1836125061.py\", line 91, in main\n", "    equipment_data = await extract_equipment_prices(\"Ball Mill\")\n", "  File \"/var/folders/xp/jfsq68yd6md3w078h7gx7sn40000gn/T/ipykernel_50502/1836125061.py\", line 79, in extract_equipment_prices\n", "    extracted_data = json.loads(completion)\n", "  File \"/opt/anaconda3/envs/oil/lib/python3.10/json/__init__.py\", line 339, in loads\n", "    raise TypeError(f'the JSON object must be str, bytes or bytearray, '\n", "TypeError: the JSON object must be str, bytes or bytearray, not tuple\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "INFO:cbp.services.llm.claude:Token Usage - Input: 10670, Output: 267, Total: 10937\n"]}, {"name": "stdout", "output_type": "stream", "text": [" EXTRACTED DATA : (Message(id='msg_01SVkBs6T9C6hKChhE6oLvGG', content=[TextBlock(citations=None, text='[\\n    {\\n        \"item\": \"Ball Mill Shell\",\\n        \"price\": \"TBA\"\\n    },\\n    {\\n        \"item\": \"Ball Mill Girth Gear\",\\n        \"price\": \"TBA\"\\n    },\\n    {\\n        \"item\": \"Ball Mill Pinion Gear\",\\n        \"price\": \"TBA\"\\n    },\\n    {\\n        \"item\": \"Ball Mill Main Gear Reducer\",\\n        \"price\": \"TBA\"\\n    },\\n    {\\n        \"item\": \"Ball Mill Motor (9000kW)\",\\n        \"price\": \"TBA\"\\n    },\\n    {\\n        \"item\": \"Ball Mill Trunnion Bearings\",\\n        \"price\": \"TBA\"\\n    },\\n    {\\n        \"item\": \"Ball Mill Shell Liners (Rubber)\",\\n        \"price\": \"TBA\"\\n    },\\n    {\\n        \"item\": \"Ball Mill Feed Spout\",\\n        \"price\": \"TBA\"\\n    },\\n    {\\n        \"item\": \"Ball Mill Trommel Screen\",\\n        \"price\": \"TBA\"\\n    }\\n]', type='text')], model='claude-3-5-sonnet-20241022', role='assistant', stop_reason='end_turn', stop_sequence=None, type='message', usage=Usage(cache_creation_input_tokens=0, cache_read_input_tokens=0, input_tokens=10670, output_tokens=267)), True)\n"]}], "source": ["from cbp.services.llm.fastllm import LLMOrchestrator\n", "from cbp.services.llm.claude import AnthropicService\n", "from cbp.services.embeddings.vectore_database_service import VectorDatabaseService\n", "import logging\n", "import json\n", "import asyncio\n", "\n", "async def extract_equipment_prices(equipment_type=\"Ball Mill\"):\n", "    \"\"\"\n", "    Extract equipment items and their prices related to the specified equipment type.\n", "    \n", "    Args:\n", "        equipment_type (str): The type of equipment to search for (default: \"Ball Mill\")\n", "    \n", "    Returns:\n", "        list: List of dictionaries containing item names and prices\n", "    \"\"\"\n", "    logging.basicConfig(level=logging.INFO)\n", "    logger = logging.getLogger(__name__)\n", "    \n", "    # First, perform vector search to find relevant documents\n", "    vd = VectorDatabaseService()\n", "    rid = \"61a9d623-1059-4a47-9f00-1d4a636ec6fe\"\n", "    \n", "    # Query focused on finding equipment pricing information\n", "    query = f\"\"\"{equipment_type}\n", "    \n", "price table pricing chart cost listing price sheet rate card\n", "pricing comparison table equipment rates price breakdown\n", "prices listed price column cost column amount column quantity column \"table row\" \"table cell\"\n", "\"item\" \"description\" \"quantity\" \"rate\" \"amount\" \"subtotal\" \"total\" \"price each\"\n", "$50 $199.99 $1,500 €120 £75 ¥1500 ₹2500 ₩5000 A$200 C$150 \"USD\" \"EUR\" \"JPY\" \"GBP\"\n", "\"per unit\" \"per piece\" \"per item\" \"per box\" \"per case\" \"per pallet\" \"per kg\" \"per lb\"\n", "\"units\" \"pieces\" \"items\" \"boxes\" \"pallets\" \"sets\" \"kits\" \"bundles\" \"packages\"\n", "\"quantity: 100\" \"qty: 25\" \"qty\" \"min. order\" \"minimum quantity\" \"max quantity\"\n", "\"price without units\" \"currency not specified\" \"units not specified\" \"price varies\"\n", "\"call for pricing\" \"price on request\" \"contact for quote\" \"price TBD\" \"market price\"\n", "\"price range\" \"estimated cost\" \"approximate price\" \"price tier\" \"volume discount\"\n", "\"MSRP\" \"list price\" \"net price\" \"price without tax\" \"price with tax included\" \"plus tax\"\"\"\n", "    \n", "    # Perform vector search\n", "    search_results = await vd.(query=query, request_id=rid, num_results=5)\n", "    \n", "    # Extract content from search results\n", "    documents = []\n", "    if search_results.success and search_results.results:\n", "        for result_group in search_results.results:\n", "            for result, _ in result_group:\n", "                documents.append(result)\n", "    \n", "    # Prepare prompt for LLM to extract equipment items and prices\n", "    extraction_prompt = {\n", "        \"role\": \"user\",\n", "        \"content\": f\"\"\"\n", "        Extract all equipment items related to {equipment_type} and their corresponding prices from the following text.\n", "        Return ONLY a valid JSON array of objects with 'item' and 'price' keys.\n", "        If the currency is not known, include the price without currency symbol.\n", "        Format example: [{{\"item\":\"gear\",\"price\":\"$40\"}}, {{\"item\":\"motor\",\"price\":\"1500\"}}]\n", "        \n", "        Here are the documents to analyze:\n", "        {documents}\n", "        \"\"\"\n", "    }\n", "    \n", "    # Initialize Anthropic service and generate completion\n", "    llm_service = AnthropicService()\n", "    completion = llm_service.generate_sync(\n", "        messages=[extraction_prompt],\n", "        model_name=\"Claude 3.5 Sonnet\",\n", "        temperature=0.005,\n", "        max_tokens=4096,\n", "        use_fallback=False\n", "    )\n", "    \n", "   \n", "\n", "\n", "    try:\n", "        print(f\" EXTRACTED DATA : {completion}\")\n", "        extracted_data = json.loads(completion)\n", "        logger.info(f\"Successfully extracted {len(extracted_data)} items\")\n", "        return extracted_data\n", "    except json.JSONDecodeError:\n", "        logger.error(\"Failed to parse JSON from LLM response\")\n", "        logger.debug(f\"Raw response: {completion}\")\n", "        return []\n", "\n", "# Since we're in a Ju<PERSON><PERSON> notebook with an existing event loop,\n", "# we should use asyncio.create_task() instead of asyncio.run()\n", "async def main():\n", "    # Extract equipment prices for Ball Mills\n", "    equipment_data = await extract_equipment_prices(\"Ball Mill\")\n", "    \n", "    # Print the results\n", "    print(json.dumps(equipment_data, indent=2))\n", "\n", "# Create and run the task\n", "if __name__ == \"__main__\":\n", "    task = asyncio.create_task(main())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response.results"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["vd = VectorSearchLayer()\n", "rid = \"61a9d623-1059-4a47-9f00-1d4a636ec6fe\"\n", "query = '''\n", "AG MILL \n", "'''\n", "response = await vd.search(query=query, request_id=rid, num_results=5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(response.results[0])"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["x = response.results[0][0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(x)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response.results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "oil", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}