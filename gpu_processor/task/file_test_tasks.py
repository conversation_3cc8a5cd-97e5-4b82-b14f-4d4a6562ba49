# /home/<USER>/file_processor_tasks.py
from extensions import celery
from socket_instance import socket_instance
from models import Requirement
# from services.cbp_file_processor import CBPFileProcessor
from services.scp_file_processor import SCPFileProcessor
import json

@celery.task(bind=True, max_retries=1, name="task.file_test_tasks.test_task")
def test_task(self, req_id, file_names_arr, existing_req):
    try:
        try:
            
            socket_manager = socket_instance.get_instance()
            # Test emit
            socket_manager.emit_to_client(
                req_id,
                'test_event',
                {'message': 'Test from Celery'},
                namespace='/processing_files'
            )

            # if existing_req:
            #     existing_req_names = json.loads(existing_req["name"])
            #     new_names = file_names_arr + existing_req_names
            #     Requirement.update(req_id, name=json.dumps(new_names))
            # else:
            #     Requirement.update(req_id, name=json.dumps(file_names_arr))
            
            print('starting to process test files...')
            file_processor = SCPFileProcessor(socket_manager, req_id, existing_req, instant_file_names=file_names_arr)
            file_processor.process_files()
        except Exception as e:
            print(f"Error during file processing: {e}")

        return "File processing completed successfully."
    except Exception as e:
        print(f"Error during file processing: {e}")
        return str(e)