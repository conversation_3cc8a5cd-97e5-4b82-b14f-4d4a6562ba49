aiohappyeyeballs==2.4.4
aiohttp==3.11.11
aiosignal==1.3.2
alembic==1.14.0
amqp==5.3.1
annotated-types==0.7.0
anyio==4.8.0
asgiref==3.8.1
async-timeout==4.0.3
attrs==24.3.0
bidict==0.23.1
billiard==4.2.1
celery==5.4.0
certifi==2024.12.14
charset-normalizer==3.4.1
click==8.1.8
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
cohere==5.13.8
dataclasses-json==0.6.7
dnspython==2.7.0
eventlet==0.38.2
exceptiongroup==1.2.2
faiss-gpu-cu12==1.9.0.post1
fastavro==1.10.0
filelock==3.16.1
Flask==2.0.1
Flask-Cors==5.0.0
Flask-Migrate==4.0.7
Flask-SocketIO==5.5.1
Flask-SQLAlchemy==2.5.1
frozenlist==1.5.0
fsspec==2024.12.0
greenlet==3.1.1
gunicorn==23.0.0
h11==0.14.0
httpcore==1.0.7
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.27.1
idna==3.10
itsdangerous==2.2.0
Jinja2==3.1.5
jsonpatch==1.33
jsonpointer==3.0.0
kombu==5.4.2
langchain==0.1.12
langchain-cohere==0.1.5
langchain-community==0.0.28
langchain-core==0.1.53
langchain-text-splitters==0.0.2
langsmith==0.1.147
lxml==5.3.0
Mako==1.3.8
MarkupSafe==3.0.2
marshmallow==3.25.1
multidict==6.1.0
mypy-extensions==1.0.0
numpy==1.26.4
nvidia-cublas-cu12==12.6.4.1
nvidia-cuda-runtime-cu12==12.6.77
orjson==3.10.14
packaging==23.2
parameterized==0.9.0
pinecone-client==3.1.0
prompt_toolkit==3.0.50
propcache==0.2.1
pydantic==2.10.5
pydantic_core==2.27.2
PyMuPDF==1.25.2
PyMySQL==1.1.1
python-dateutil==2.9.0.post0
python-docx==1.1.2
python-engineio==4.11.2
python-socketio==5.12.1
PyYAML==6.0.2
redis==5.2.1
requests==2.32.3
requests-toolbelt==1.0.0
simple-websocket==1.1.0
six==1.17.0
sniffio==1.3.1
SQLAlchemy==1.4.20
tenacity==8.5.0
tokenizers==0.21.0
tqdm==4.67.1
types-requests==2.32.0.20241016
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2025.1
urllib3==2.3.0
uuid==1.30
vine==5.1.0
wcwidth==0.2.13
Werkzeug==2.0.1
wsproto==1.2.0
yarl==1.18.3