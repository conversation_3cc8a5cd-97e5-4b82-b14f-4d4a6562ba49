# FILE 2: AIChunckProcessor Class and Related Functions
import os
import json
import asyncio
from services.claude_ai_service import Claude<PERSON>ervice
from services.fast_apis_service import FastAPIs
from services.prompt_saver import PromptSaver
from services.prompt_loader import PromptLoader
import time
from pydantic import BaseModel


class ResponseInfo(BaseModel):
    Evaluation: str
    Score: int
    Reason: str
    Reference: str

class AIChunckProcessor:
    def __init__(self):
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        with open(os.path.join(parent_dir, 'env.json'), 'r') as f:
            env_data = json.load(f)

        self.claude_client = ClaudeService()
        self.fast_apis_client = FastAPIs()
        self.prompt_saver = PromptSaver()
        self.prompt_loader = PromptLoader()

    async def get_claude_to_evaluate_chunks_based_on_criteria(self, text, source_text, criteria, count, temperature=0.001):
        start_time = time.time()

        messages = self.prompt_loader.get_prompt('claude_to_evaluate_chunks_based_on_criteria')
        messages.append({"role": "user", "content": f"<SOURCE_DOCUMENT>\n{source_text}</SOURCE_DOCUMENT>\n<UPLOADED_DOCUMENT>\n{text}</UPLOADED_DOCUMENT>\n<Question>\n{criteria}\n Description: {criteria}\n</Question>"})

        # response_agent_1, response_agent_2, token_count_in, token_count_out = "", "", 0, 0
        for _ in range(2):
            try:
                task_start_time = time.time()
                completion = await self.claude_client.generate_message_agent_sonnet_v2(ResponseInfo, messages, 0.01)        
                if completion and hasattr(completion.content[0], 'input'):
                    response = completion.content[0].input
                    master_response = ResponseInfo(**response)
            
                new_response = {
                    'Evaluation': master_response.Evaluation,
                    'Score': master_response.Score,
                    'Reason': master_response.Reason,
                    'Reference': master_response.Reference,
                }
                return new_response
            except Exception as e:
                print(f"Error: {e}, retrying...")
        
        total_duration = time.time() - start_time  # Time taken for the entire function
        print(f"Total time taken for get_claude_to_evaluate_chunks_based_on_criteria: {total_duration:.2f} seconds")

    async def generate_master_agent_response(self, data, response_agent_1, response_agent_2):
        if not (response_agent_1 or response_agent_2):
            raise ValueError("Both agent responses are empty")

        messages = self.prompt_loader.get_prompt('master_agent_response')
        messages[-1]['content'] = messages[-1]['content'].format(
            data=data, response_agent_1=response_agent_1, response_agent_2=response_agent_2
        )

        completion = await self.claude_client.generate_message_agent_sonnet_v2(ResponseInfo, messages, 0.01)
        if completion and hasattr(completion.content[0], 'input'):
            response = completion.content[0].input
            return response
        else:
            raise ValueError("Master agent response is empty")
