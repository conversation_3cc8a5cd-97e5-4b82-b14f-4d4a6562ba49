import os
import json
from uuid import uuid4
from typing import List
from langchain_cohere import CohereEmbeddings
import faiss
from langchain_community.docstore.in_memory import InMemoryDocstore
from langchain_community.vectorstores import FAISS
from langchain_core.documents import Document
import asyncio
import time
import aiohttp
from typing import List, Tuple

class FaissEmbedding:
    def __init__(self, max_concurrent_tasks=10):
        self.model = "embed-english-v3.0"
        self.embeddings = None
        self.vector_store = None
        self.vector_name = "aienergy_vec_db"
        self._load_environment()
        self.semaphore = asyncio.Semaphore(max_concurrent_tasks)

    def _load_environment(self):
        """Loads environment variables from a JSON file."""
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        env_file_path = os.path.join(parent_dir, 'env.json')
        with open(env_file_path, 'r') as f:
            env_data = json.load(f)
        os.environ["COHERE_API_KEY"] = env_data.get("COHERE_API_KEY", "")
        self.gpu_server_url = env_data.get("GPU_SERVER_URL", "")

    def _initialize_vector_store(self):
        """Initializes the FAISS vector store."""
        self.embeddings = CohereEmbeddings(model=self.model)
       
        try:
            self.vector_store = FAISS.load_local(
                self.vector_name, self.embeddings, allow_dangerous_deserialization=True
            )
        except Exception as e:
            print(f"An error occurred while loading the FAISS vector store: {e}")
            self.vector_store = None  # or you can assign a default value or handle it in another way


    async def insert_documents(self, content: List[str], file_name: str, request_id: str):
        self._initialize_vector_store()
        """Inserts documents into the vector store asynchronously.

        Args:
            content (List[str]): List of document content.
            file_name (str): Name of the file (metadata).
            project_id (str): ID for grouping documents (metadata).
        """
        documents = []
        doc_uuids = []

        for data in content:
            documents.append(Document(
                page_content=data,
                metadata={"source": file_name, "request_id": request_id},
            ))
            doc_uuids.append(uuid4())

        # Schedule _add_documents_async as a background task
        asyncio.create_task(self._add_documents_async(documents, doc_uuids))
        # asyncio.create_task(self._add_documents_with_semaphore(documents, doc_uuids))

    async def _add_documents_async(self, documents: List[Document], ids: List[uuid4]):
        """Adds documents to the vector store asynchronously."""
        # self.vector_store.add_documents(documents=documents, ids=ids)
        # self.vector_store.save_local(self.vector_name)
        # Measure add_documents time
        add_start_time = time.time()
        self.vector_store.add_documents(documents=documents, ids=ids)
        add_elapsed_time = time.time() - add_start_time
        print(f"add_documents took {add_elapsed_time:.2f} seconds for {len(documents)} documents")

        # Measure save_local time
        save_start_time = time.time()
        self.vector_store.save_local(self.vector_name)
        save_elapsed_time = time.time() - save_start_time
        print(f"save_local took {save_elapsed_time:.2f} seconds")

    async def search(self, query: List[str], request_id: str, k: int = 5):
        """Searches the vector store asynchronously.

        Args:
            query (str): Search query.
            project_id (str): Filter for project ID.
            k (int): Number of top results to return.

        Returns:
            List[Tuple[Document, float]]: List of documents with similarity scores.
        """
        # results = await self._similarity_search_with_score_async(query, request_id, k)
        results = await self._gpu_search(query, request_id, k)
        return results
    
    async def _gpu_search(self, query: List[str], request_id: str, k: int) -> List[List[Tuple[str, float]]]:
        """
        Sends a request to the GPU server for search.

        Args:
            query (List[str]): List of search queries.
            request_id (str): Request ID for filtering.
            k (int): Number of top results to return.

        Returns:
            List[List[Tuple[str, float]]]: List of results for each query, where each result is a list of (content, score) tuples.
        """
        url = f"{self.gpu_server_url}/search_tender_requirement"
        payload = {
            "search_queries": query,
            "request_id": request_id,
            "k": k
        }

        try:
            timeout = aiohttp.ClientTimeout(
                total=240,  # Allow up to 3 minutes for completion
                connect=60,  # Max 60s to establish connection
                sock_read=150,  # Max 150s to wait for response
                sock_connect=60 
            )
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(url, json=payload, headers={'Content-Type': 'application/json'}) as response:
                    response_text = await response.text()
                    
                    if response.status == 200:
                        data = json.loads(response_text)
                        if data.get("success"):
                            # Process results for each query
                            all_results = []
                            for query_results in data["message"]["results"]:
                                query_matches = [
                                    (item["content"], item["score"])
                                    for item in query_results
                                ]
                                all_results.append(query_matches)
                            return all_results
                        else:
                            raise RuntimeError(f"GPU server responded with failure: {data}")
                    else:
                        raise RuntimeError(f"GPU server error: {response.status} - {response_text}")
        except asyncio.TimeoutError:
            raise RuntimeError("GPU server request timed out")
        except Exception as e:
            raise RuntimeError(f"Error during GPU search: {str(e)}")
    
    async def _gpu_search_top_3(self, request_id: str) -> List[List[Tuple[str, float]]]:
        """
        Sends a request to the GPU server for search.

        Args:
            request_id (str): Request ID for filtering.

        Returns:
            List[List[Tuple[str, float]]]: List of results for each query, where each result is a list of (content, score) tuples.
        """
        url = f"{self.gpu_server_url}/get_top_3_pages_documents?request_id={request_id}"  # Include request_id as a query parameter

        try:
            timeout = aiohttp.ClientTimeout(
                total=180,
                connect=60
                )
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url, headers={'Content-Type': 'application/json'}) as response:  # Removed json=payload
                    response_text = await response.text()
                    
                    if response.status == 200:
                        data = json.loads(response_text)
                        if data.get("success"):
                            return data["message"]["results"]
                        else:
                            raise RuntimeError(f"GPU server responded with failure: {data}")
                    else:
                        raise RuntimeError(f"GPU server error: {response.status} - {response_text}")
        except asyncio.TimeoutError:
            raise RuntimeError("GPU server request timed out")
        except Exception as e:
            raise RuntimeError(f"Error during GPU top3 search: {str(e)}")

    async def _similarity_search_with_score_async(self, query: str, request_id: str, k: int):
        """Performs similarity search with scores asynchronously."""
        results = self.vector_store.similarity_search_with_score(
            query,
            k=k,
            filter={"request_id": request_id},
        )
        return results
