import json, time
import redis
import dramatiq
from datetime import datetime




all_actors = {
    "run_qmp_query": "Qmp Query",
    "run_cbp_task": "Cbp Task",
    "run_cbp_multibid_task": "Cbp Multibid Task",
    "run_cbp_report_task": "Cbp Report Task",
    "run_file_upload_task": "File Upload Task",
    "run_epc_contractor_task": "EPC Contractor Task",
    "run_engineering_project_owner_task": "Engineering Project Owner Task",
    "ineight_download_task": "InEight Download Task"
}

class ActivityLoggingMiddleware(dramatiq.Middleware):
    def __init__(self, env_data, ttl=86400):
        self.env_data = env_data
        self.REDIS_URL = env_data.get("REDIS_URL", 'redis://localhost:6379/0')
        self.r = redis.Redis.from_url(self.REDIS_URL)
        self.TTL = ttl

    def after_enqueue(self, broker, message, delay):
        self.r.sadd("queue:queued", message.message_id)
        self.r.set(f"task_state:{message.message_id}", json.dumps({
            "status": "queued",
            "actor": message.actor_name,
            "actor_name": all_actors[message.actor_name],
            "args": message.args,
            "enqueue_time": time.time(),
            "enqueue_date_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }), ex=self.TTL)

    def before_process_message(self, broker, message):
        self.r.srem("queue:queued", message.message_id)
        self.r.sadd("queue:running", message.message_id)
        self.r.set(f"task_state:{message.message_id}", json.dumps({
            "status": "running",
            "actor": message.actor_name,
            "actor_name": all_actors[message.actor_name],
            "args": message.args,
            "start_time": time.time(),
            "start_date_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }), ex=self.TTL)

    def after_process_message(self, broker, message, *, result=None, exception=None):
        self.r.srem("queue:running", message.message_id)
        status = "failed" if exception else "succeeded" 
        self.r.sadd(f"queue:{status}", message.message_id)
        state = {
            "status": status,
            "actor": message.actor_name,
            "actor_name": all_actors[message.actor_name],
            "args": message.args,
            "end_time": time.time(),
            "end_date_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        prev = self.r.get(f"task_state:{message.message_id}")
        if prev:
            prev_data = json.loads(prev)
            state["start_time"] = prev_data.get("start_time")
            state["start_date_time"] = prev_data.get("start_date_time")
            if state["start_time"]:
                state["duration"] = state["end_time"] - state["start_time"]
        if exception:
            state["error"] = str(exception)
        self.r.set(f"task_state:{message.message_id}", json.dumps(state), ex=self.TTL)

