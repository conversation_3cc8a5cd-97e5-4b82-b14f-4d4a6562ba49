import os,sys
import asyncio
import json
import sys
import uuid
import time
import re
from docx import Document
import pandas as pd


sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from services.fitz_pdf_loader import load_pdf_parallel

class OpenAIProcessor:
    def __init__(self):        
        # Get the path to the parent directory
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # Load environment variables from env.json file in the parent directory
        env_file_path = os.path.join(parent_dir, 'env.json')
        with open(env_file_path, 'r') as f:
            env_data = json.load(f)
    
    async def chunked_data_by_sections(self, text):

        text_length_threshold = 2000  # Adjust this value based on the estimated character count for a one-page document

        # Check if the text is small
        if len(text) <= text_length_threshold:
            return [text]
        
        # Function to check if text is HTML
        def is_html(text):
            return bool(re.search(r'<[^>]+>', text))
        
        if is_html(text):

            # Define regular expression patterns to find sections within <table> tags and standalone sections
            table_pattern = re.compile(r'<table>.*?</table>', re.IGNORECASE | re.DOTALL)
            table_pattern_2 = re.compile(r'table>.*?</table>', re.IGNORECASE | re.DOTALL)
            standalone_pattern = re.compile(r'(?<=</table>)(?:(?!<table>|<tr>).)*', re.IGNORECASE | re.DOTALL)
            standalone_pattern = re.compile(r'(?<=</table>)(?:(?=\n\n).)*', re.IGNORECASE | re.DOTALL)
            between_tables_pattern = re.compile(r'(?<=</table>)(.*?)(?=<table>)', re.IGNORECASE | re.DOTALL)

            # Find all sections within <table> tags
            table_sections = table_pattern.findall(text)
            table_sections_2 = table_pattern_2.findall(text)
            standalone_sections = standalone_pattern.findall(text)
            between_tables_sections = between_tables_pattern.findall(text)

            # Split standalone sections by double new lines
            split_standalone_sections = []
            for section in standalone_sections:
                if section.strip():  # Check if section is not empty
                    split_standalone_sections.extend(section.split("\n\n"))

            # Split between table sections by double new lines
            split_between_tables_sections = []
            for table_section in between_tables_sections:
                if table_section.strip():  # Check if section is not empty
                    split_between_tables_sections.extend(table_section.split("\n\n"))
            
            final_sections = table_sections + table_sections_2 + split_standalone_sections + split_between_tables_sections
        
        else:
            # For plain text, split by double new lines
            final_sections = text.split("\n\n")
        
        
        return final_sections

    def estimate_pages(self, text_content, chars_per_page=2500):
        """
        Estimate the content of pages based on character count.
        
        Args:
            text_content (list): List of paragraphs extracted from the DOCX file.
            chars_per_page (int): Estimated number of characters that fit on a page.
            
        Returns:
            list: A list containing the content of the top 3 pages.
        """
        pages = []
        current_page = []
        current_chars = 0
    
        for paragraph in text_content:
            paragraph_length = len(paragraph)
            if current_chars + paragraph_length > chars_per_page:
                pages.append("\n".join(current_page)) 
                current_page = [paragraph]
                current_chars = paragraph_length
            else:
                current_page.append(paragraph)
                current_chars += paragraph_length 
    
        # Add any remaining content as the last page
        if current_page:
            pages.append("\n".join(current_page))
    
        return pages

    async def extract_text_from_excel_or_csv_batch(self, file_path):
        """
        Extract text from an Excel (.xlsx, .xls) or CSV (.csv) file and return a list of indexed strings.
        Each entry in the list corresponds to a batch of 100 rows.
        """
        try:
            start_time = time.time()
            file_extension = os.path.splitext(file_path)[-1].lower()
            grouped_content = []
            
            if file_extension == ".csv":
                df = pd.read_csv(file_path, dtype=str, keep_default_na=False)
                sheets = {"Sheet1": df}  # Treat CSV as a single-sheet Excel file
            elif file_extension in [".xls", ".xlsx"]:
                sheets = pd.read_excel(file_path, sheet_name=None, dtype=str, keep_default_na=False)
            else:
                raise ValueError(f"Unsupported file format: {file_extension}")

            chunk_size = 70  # Number of rows per chunk

            for sheet_name, df in sheets.items():
                # Convert each row into a text string
                text_content = df.astype(str).apply(lambda row: " | ".join(row.dropna()), axis=1).tolist()

                # Group rows into chunks of 70, joined by <br/>
                sheet_chunks = [
                    "<br/>".join(text_content[i:i + chunk_size]) 
                    for i in range(0, len(text_content), chunk_size)
                ]

                # Append sheet name and its processed content
                grouped_content.append({sheet_name: sheet_chunks})


            return ["", grouped_content]  # Returning a dictionary with indexed chunks
        except Exception as e:
            raise ValueError(f"Failed to extract text from {file_path}: {e}")

    async def extract_text_from_docx(self, file_path):
        """
        Extract text from a DOCX file and return an array of strings.
        Each entry in the array corresponds to a paragraph in the document.
        Optimized for large DOCX files.
        """
        try:
            start = time.time()
            doc = Document(file_path)
            text_content = [p.text.strip() for p in doc.paragraphs if p.text.strip()]
            process_end_time = time.time()  # End time for PDF processing
            print(f"Docx processing done in {process_end_time - start:.2f} seconds")
            return ["", self.estimate_pages(text_content)]
        except Exception as e:
            raise ValueError(f"Failed to extract text from {file_path}: {e}")
    
    
    async def extract_text_from_pdf(self, file_path):
        process_start_time = time.time()  # Start time for PDF processing
        text = load_pdf_parallel(file_path)
        chunks = text
        text_single_string = ""
        process_end_time = time.time()  # End time for PDF processing
        print(f"PDF processing done in {process_end_time - process_start_time:.2f} seconds")
        return text_single_string, chunks

    async def get_serialized_data_openai_parser(self, file_path):
        start = time.time()  # Start time for the entire method

        # Load the document
        load_start_time = time.time()  # Start time for loading the document
        text = load_pdf_parallel(file_path)
        load_end_time = time.time()  # End time for loading the document

        if isinstance(text, list): #this is for pdf docs
            process_start_time = time.time()  # Start time for PDF processing
            # chunks = self.process_pdf(text)
            chunks = text
            process_end_time = time.time()  # End time for PDF processing
            print(f"PDF processing done in {process_end_time - process_start_time:.2f} seconds")
        else:
            chunk_start_time = time.time()  # Start time for chunking the document
            chunks = await self.chunked_data_by_sections(text)
            chunk_end_time = time.time()  # End time for chunking the document
            print(f"Text chunking done in {chunk_end_time - chunk_start_time:.2f} seconds")
                       
        # Assuming chunks2 is a list of strings
        text_single_string = ""

        total_duration = time.time() - start  # Total time for the entire method
        print(f"Total time for get_serialized_data_openai_parser: {total_duration:.2f} seconds")
        return text_single_string, chunks

