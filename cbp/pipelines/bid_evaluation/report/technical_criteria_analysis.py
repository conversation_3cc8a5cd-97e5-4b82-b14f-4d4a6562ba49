def generate_technical_criteria_analysis(technical_evaluations):
    """
    Generate technical criteria analysis HTML based on technical criteria evaluation data.
    """
    # Extract technical criteria evaluations

    # Generate HTML
    html_template = f"""
    <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; background: white; border: 1px solid #ddd;">
        <h2 style="color: #2c5aa0; text-align: left; border-bottom: 2px solid #2c5aa0; padding-bottom: 10px;">
            TECHNICAL CRITERIA ANALYSIS
        </h2>
        
        <h3 style="color: #2c5aa0;">Technical Evaluation Summary</h3>
        <p style="font-weight: bold; margin-bottom: 15px;">
            The following is a detailed analysis of each bidder's technical submission.
        </p>
    """

    # Add bidder evaluations
    for evaluation in technical_evaluations:
        bidder_name = evaluation.get("name", "Unknown Bidder")
        evaluation_text = evaluation.get("evaluation", "No evaluation available")
        
        # Clean up the evaluation text by removing extra whitespace and newlines
        evaluation_text = " ".join(evaluation_text.split())
        
        html_template += f"""
        <div style="margin-top: 25px; padding: 15px; background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 5px;">
            <h4 style="color: #2c5aa0; margin-top: 0;">{bidder_name}</h4>
            <div style="line-height: 1.6;">
                {evaluation_text}
            </div>
        </div>
        """

    # Close the HTML template
    html_template += """
        <div style="margin-top: 30px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7;">
            <h4 style="color: #856404; margin-top: 0;">ASSESSMENT NOTES</h4>
            <p style="margin-bottom: 0; color: #856404;">
                This technical analysis is based on the detailed evaluation of each bidder's technical submission and its comparison with the project requirements.
            </p>
        </div>
    </div>
    """

    return html_template


# Example usage
if __name__ == "__main__":
    data = [
            {
                "criteria_name": "Technical Criteria",
                "name": "Mo group",
                "evaluation": "The evaluation of the Ball Mill equipment is based on the technical specifications provided in the bidder's documents. Since no specific tender requirements were provided for comparison, the assessment focuses on the completeness and adequacy of the information provided by the bidder."
            },
            {
                "criteria_name": "Technical Criteria",
                "name": "Tysen",
                "evaluation": "The ball mill technical specifications demonstrate comprehensive coverage of critical operational parameters including power and performance specifications, dimensional specifications, and operational parameters."
            }
        ]
    

    technical_analysis_html = generate_technical_criteria_analysis(data)
    # Save as HTML
    with open('technical_criteria_analysis.html', 'w') as f:
        f.write(technical_analysis_html)
