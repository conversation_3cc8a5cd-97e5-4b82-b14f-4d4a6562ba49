import os
import asyncio
import time
import random
from time import sleep
from openai import AzureOpenAI, OpenAI
import json

class AIProcessor:
    def __init__(self):
        # Get the path to the parent directory
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # Load environment variables from env.json file in the parent directory
        env_file_path = os.path.join(parent_dir, 'env.json')
        
        # Load environment variables from env.json file
        with open(env_file_path, 'r') as f:
            env_data = json.load(f)
        
        # Set up Azure OpenAI client
        self.azure_client = AzureOpenAI(
            azure_endpoint=env_data.get("AZURE_ENDPOINT", ""),
            api_key=env_data.get("AZURE_API_KEY", ""),
            api_version=env_data.get("AZURE_API_VERSION", "")
        )

        self.deployments_map = {
            "gpt-3.5-turbo-16k": ["ai_energy-3-16k-1"],
            "gpt-4-turbo": ["ai_energy-4-turbo-1"],
            "gpt-3.5-turbo": ["ai_energy-3-turbo"]
        }

    async def get_openai_response(self, text, temperature=0.01, model="gpt-3.5-turbo", steps=5):
        steps = 5
        resp = ""
        messages = [
            {"role": "system", "content": """
            You are a helpful assistant.
            Your response should be in JSON format.
            Given the text below, parse it into a structured JSON format with result(title,content-> Str)
            You don't change anything to the given text. Just format it.
            For example, titles like "2.9 PLANT Location and Battery limits" and "2.9.1 Location" should be identified as such, with their subsequent text as content.
            If a text section lacks a title, leave the 'title' field empty ("") and include only the 'content'. Remember, the title should include its numerical part too. 
            You will always respond in this format no matter what:
            {
            "result": [{"title": "2.9 PLANT Location and Battery limits",
                "content": "..."},
                {"title": "2.9.1 Location",
                "content": ""},
                {"title": "",
                "content":"..."}
            ]
            }
            """},
            {"role": "user", "content": text}
        ]
    
        while steps > 0:
            steps -= 1
            try:
                completion = self.azure_client.chat.completions.create(
                    model=random.choice(self.deployments_map[model]),
                    temperature=temperature,
                    messages=messages,
                    response_format={"type": "json_object"}
                )
                resp = completion
                break
            except Exception as e:
                print(e)
                await asyncio.sleep(30)

        return resp.choices[0].message.content

    async def count_max_tokens(self, text):
        max_token = max(len(text.split(' ')) * 2, 10000)
        return max_token
    
    async def parser(self, text):
        sections = text.split('##')
        ret = []
        for section in sections:
            tmp = section.replace("#", '').split('$$')
            if len(tmp) != 2:
                if len(tmp) == 1:
                    if len(ret) != 0:
                        ret[-1]["content"] += tmp[0]
                    else:
                        ret.append({
                            "title": tmp[0],
                            "content": tmp[0]
                        })
                continue
            ret.append({
                "title": tmp[0],
                "content": tmp[1]
            })
        return ret

    async def get_openai_response_v2(self, text, temperature=0.001, model="gpt-3.5-turbo", steps=5):
        print("generating ai response for text formatting 1....")
        steps = 5
        resp = ""
        messages = [
        {"role": "system", "content": """
        As an AI parser assistant, your task is to parse the provided text according to the following rules:
        1. **Section and Subsection Identification**: 
        
        - Define what constitutes a 'Section' and 'Subsection' in the text. This could be based on specific formatting cues such as headings, titles, or certain keywords. If any text doesn't have a Section or Subsection title, just give me the text. You don't change anything to the given text.
        2. **Symbol Insertion**: 
        - Prepend each 'Section' title with "###".
        - Prepend each 'Subsection' title with "####".
        - Append "$$" immediately after each 'Section' and 'Subsection' title.

        3. **Whitespace Cleanup**: 
        - Ensure there are no superfluous spaces or line breaks around the symbols or titles.

        4. **Original Text Preservation**: The body of the sections and subsections should remain unaltered.
        
        Here is some example:

        Text:
        3.5 Training
        Training of COMPANY personnel as described in Section 10.
        3.5.1 Design Quality Assurance Report
        CONTRACTOR is asked in supporting client in preparing DQAR.Scope and Objectives of Design Review.The design review will cover the implementation of design and functional
        specification into detail design for the Project1 project. It will be based on review of key design documents and interviews with key members of
        project team/CONTRACTOR.
        4.10 Reporting 
        4.11 Progress Reporting 
        Refer to “PROJECT CONTROL” section of EXHIBIT - P – Administration  Instructions & Project Control. 
        4.12 Interface Management 
        Refer to the “PROJECT CONTROL” section of EXHIBIT - P – Administration  Instructions & Project Control. 
        CONTRACTOR shall acknowledge the COMPANY Interface Management  Procedure (Project1 INTERFACE CONTROL PROCEDURE – Ref. Project1-Company-PMT 
        0000-PM-PRO-0029), the Interface Register (INTERFACE REGISTER – Ref. NA 
        company-PMT-0812-PM-REG-0006-1), the Interface Alignment sheets (Project1  INTERFACE ALIGNMENT SHEETS – Ref. MUL-company-Project1 -0812-MD-IAS-0001 ) and  shall actively participate in the interface alignment process as required by  COMPANY.
        
        Response:
        ### 3.5 Training $$
        Training of COMPANY personnel as described in Section 10.
        ####  3.5.1 Design Quality Assurance Report $$
        CONTRACTOR is asked in supporting client in preparing DQAR.Scope and Objectives of Design Review.The design review will cover the implementation of design and functional
        specification into detail design for the Project1 project. It will be based on review of key design documents and interviews with key members of
        project team/CONTRACTOR.
        ### 4.10 Reporting $$  
        ### 4.11 Progress Reporting $$
        Refer to “PROJECT CONTROL” section of EXHIBIT - P – Administration  Instructions & Project Control. 
        ### 4.12 Interface Management $$ 
        Refer to “PROJECT CONTROL” section of EXHIBIT - P – Administration  Instructions & Project Control. 
        CONTRACTOR shall acknowledge the COMPANY Interface Management  Procedure (Project1 INTERFACE CONTROL PROCEDURE – Ref. Project1-Company-PMT 
        0000-PM-PRO-0029), the Interface Register (INTERFACE REGISTER – Ref. NA 
        company-PMT-0812-PM-REG-0006-1), the Interface Alignment sheets (Project1  INTERFACE ALIGNMENT SHEETS – Ref. MUL-company-Project1 -0812-MD-IAS-0001 ) and  shall actively participate in the interface alignment process as required by  COMPANY.

    """},
        {"role": "user", "content": f"Text: \n{text}"}
    ]
        while steps > 0:
            steps -= 1
            try:
                completion = self.azure_client.chat.completions.create(
                    model=random.choice(self.deployments_map[model]),
                    temperature=temperature,
                    messages=messages,
                    max_tokens= await self.count_max_tokens(text)
                )
                resp = completion
                break
            except Exception as e:
                print(e)
                await asyncio.sleep(30)

        return resp.choices[0].message.content


    async def get_openai_to_parse_chunks(self, text, criteria, temperature=0.001, model="gpt-3.5-turbo-16k", steps=5):
        print("generating ai response for text formatting 2....")
        print(text)
        steps = 5
        count = 0
        resp = ""

        messages = [
        {"role": "system", "content": """
            As an AI parser assistant, 
            
            Your task is to parse the provided text according to the following rules:
         
            <Instruction>

                1. **Section and Subsection Identification**:
                    - Define what constitutes a 'Section' and 'Subsection' in the text. This could be based on specific formatting cues such as headings, titles, or certain keywords. If any text doesn't have a Section or Subsection title, just give me the text. You don't change anything to the given text.
                    
                2. **Symbol Insertion**:
                    - Prepend each 'Section' title with "###".
                    - Prepend each 'Subsection' title with "####".
                    - Append "$$" immediately after each 'Section' and 'Subsection' title.

                3. **Whitespace Cleanup**:
                    - Ensure there are no superfluous spaces or line breaks around the symbols or titles.

                4. **Original Text Preservation**:
                    - The body of the sections and subsections should remain unaltered.
                    
                5. **Text Preservation**:
                    - Text that doesn't belong to any section should be returned in their actual positions. Ensure the text is presented cleanly.
            </Instruction>
           """
        },
        {"role": "user", "content": f"TEXT: \n{text}"}
    ]
        while steps > 0:
            steps -= 1
            try:
                completion = self.azure_client.chat.completions.create(
                    model=random.choice(self.deployments_map[model]),
                    temperature=temperature,
                    messages=messages,
                    max_tokens= await self.count_max_tokens(text)
                )
                resp = completion
                break
            except Exception as e:
                print(e)
                # await asyncio.sleep(30)

        return resp.choices[0].message.content

    
    async def get_openai_to_evaluate_chunks_based_on_criteria(self, text, criteria, count, temperature=0.001, model="gpt-3.5-turbo-16k", steps=5):
        print("Generating AI response for text formatting v1...")

        concatenated_criteria = f"Criteria {count}: {criteria['name']} \n Description: {criteria['description']}\n"
        
        messages = [
            {
                "role": "user",
                "content": """
                    As an AI Industrial Engineer Evaluator assistant, 
                    
                    <Instruction>
                        STEP1A:Your First task is to go through the document and understand it, so you know if any part of the document can be used to support the Questions given.
                        Understand the document so you can be an expert in it, and then decide if the knowledge can be used to give answers to the Question.
                                            
                        Rules for STEP1:
                        - Search for sections or paragraphs that may contain relevant information related to each Question.
                        - Look for keywords or phrases that indicate alignment with the Question.
                        - Ensure that all relevant sections are thoroughly examined to avoid missing any information.
                        
                        STEP2: Your next task is to Evaluate the text based on the following Question: 
                
                        Note: The text should be scored based on the following:
                        - 'Non-Conforming': Score -1 if it does not answers the Question.
                        - 'Compliant': Score 1 if it clearly answers with the Question.
                        - 'Neutral': Score 0 if the document satisfies the Question but does not strongly align with it.
                
                        Rules for STEP2:
                        - Evaluate each section identified in STEP1 against the Question provided.
                        - Apply the scoring Question strictly based on the alignment of the text with the given Questions.
                        - Consider the context and relevance of the text to the Question.
                
                        STEP3: Response should follow the format below strictly:
                        
                        **Evaluation Format:**
                        - <Evaluation>(Required)</Evaluation>
                        - <Score>(Required, integer)</Score>
                        - <Reason>(Required)</Reason>
                    </Instruction>
            
                    Important: 
                   - The Content should contain exactly the full text which you evaluated, without any addition or subtraction.
                   - Do not summarize or paraphrase the text in the content key, it should be returned as given(provided).
                   - Most-times the questions asked in the Question is tricky, so you have to understand the document as an industrial engineer.
                   - You have to ensure you understand the document perfectly before answering, just like an industrial engineer will do.
                   - Also lookout for any suggestions, examples or any form of statement that supportts the Questions.
                   - We need every single statement no matter how small that could help the Questions.
                """
            },
            {
                "role": "assistant",
                "content": "okay!"
            },
            {
                "role": "user",
                "content": """
                    <Document>
                    **Emergency Evacuation Plan**:\n- A bidder is planning an emergency evacuation plan for medical assistance. They want to ensure it is prepared correctly.
                    </Document>
                    <Question>
                    Title: Emergency Evacuation Plan
                    Description: Is the emergency evacuation plan prepared correctly?
                    Weight: 10
                    </Question>
                """
            },
            {
                "role": "assistant",
                "content": """
                    <output>
                        <Evaluation>Non-Conforming</Evaluation>
                        <Score>-1</Score>
                        <Reason>The document does not provide any information related to the bidder's emergency evacuation plan for medical assistance.</Reason>
                    </output>
                """
            },
            {
                "role": "user",
                "content": """
                    <Document>
                    **Process Efficiency Improvement**:\n- An automotive assembly line wants to optimize its production process. They are considering implementing lean manufacturing principles.
                    </Document>
                    <Question>
                    Title: Process Efficiency
                    Description: Is the production process aligned with lean principles?
                    Weight: 8
                    </Question>
                """                 
            },
            {
                "role": "assistant",
                "content": """
                        <output>
                            <Evaluation>Compliant</Evaluation>
                            <Score>80%</Score>
                            <Reason>Lean principles focus on minimizing waste, improving efficiency, and maximizing value, which aligns with the assembly line's goal.</Reason>
                        </output>
                """
            },
            {
                "role": "user",
                "content": """
                    <Document>
                        **Inventory Management**:\n- A warehouse manages inventory for an e-commerce company. They want to reduce excess stock and stockouts.
                    </Document>
                    <Question>
                        Title: Inventory Management
                        Description: Is the inventory level optimized to prevent excess stock and stockouts?
                        Weight: 9
                    </Question>
                """
            },
            {
                "role": "assistant",
                "content": """
                    <output>
                        <Evaluation>Compliant</Evaluation>
                        <Score>70%</Score>
                        <Reason>Proper inventory management ensures optimal stock levels, minimizing excess stock and preventing stockouts.</Reason>
                    </output>"""
            },
            {
                "role": "user",
                "content": f"<Document>: \n{text} </Document>\n\n\n<Question>\n{concatenated_criteria}\n</Question>"
            }
        ]
    
        while steps > 0:
          steps -= 1
          try:
            completion = self.azure_client.chat.completions.create(
              model=random.choice(self.deployments_map[model]),
              temperature=temperature,
              messages=messages,
              max_tokens= await self.count_max_tokens(text)
            )
            resp = completion
            break
          except Exception as e:
            print(e)
            await asyncio.sleep(30)
        return resp


    async def get_openai_to_evaluate_chunks_based_on_criteria_v2(self, text, criterias, temperature=0.001, model="gpt-3.5-turbo-16k", steps=3):
        print("Generating AI response for text formatting v2...")
        resp = ""
        steps = 5
        for criterion in criterias:
            
            messages = [
              {
                "role": "system",
                "content": f"""

                As an AI Industrial Engineer Evaluator assistant,

                <Instruction>

                 **Task:** Analyze the text as an industrial engineer expert and evaluate it with the provided Questions.
        
                  STEP1A: Your First task is to go through the document and understand it, so you know if any part of the document can be used to support the Questions given.
                  Every single line of the document needs to be understood
                  Understand the document so you can be an expert in it, and then decide if the knowledge can be used to give answers to the criteria.

                  **Evaluation Rules:**
                   - Identify sections relevant that can help you answer to the Question.
                   - Score based on alignment:
                     - **Non-Conforming:** -1 (missing information or mismatch)
                     - **Compliant:** 1 (clearly answers Question)
                     - **Neutral:** 0 (partially answers Question)
        
                  **Evaluation Format:**
                   - <Evaluation>(Required)</Evaluation>
                   - <Score>(Required, integer)</Score>
                   -  <Content>(Required) </Content>
                   - <Reason>(Required)</Reason>
        
                  **Important:** 
                   - The Content should contain exactly the full text which you evaluated, without any addition or subtraction. 
                   - Do not summarize or paraphrase the content, it should be returned as given(provided).
                   - Sometimes the questions asked in the Question is tricky, so you have to understand the document as an industrial engineer.
                   - You have to ensure you understand the document perfectly before answering, just like an industrial engineer will do.
                   - Also lookout for any suggestions, examples or any form of statement that supportts the Question.
                   - We need every single statement no matter how small that could help to answer to the Question.
                </Instruction>
                <example>
                    UPLOADED DOCUMENT
                    The new smartphone model "XPhone2024" should have a 6.5 inches OLED display, a battery life of at least 48 hours, and a triple lens camera with a minimum of 48 MP main sensor. It must be water-resistant with an IP68 rating, run on XOS v5.0 or higher, and support wireless charging.                    
                    **Question**
                    Name: Phone Battery
                    Desc: Can a phone battery last 48hrs?
                    Weight: 10

                    <output>
                        <Evaluation>Compliant </Evaluation>
                        <Score>50%</Score>
                        <Content>
                        The new smartphone model "XPhone2024" should have a 6.5 inches OLED display, a battery life of at least 48 hours, and a triple lens camera with a minimum of 48 MP main sensor. It must be water-resistant with an IP68 rating, run on XOS v5.0 or higher, and support wireless charging.
                        </Content>
                        <Reason>Because in the above text it talks about a mobile called XPhone2024 with a battery of 48hrs.<Reason>
                    </output>
                </example>
                <example>
                    <uploaded_document>
                        **Quality Control in Manufacturing**:
                        - A manufacturing company produces electronic components. They want to ensure that the defect rate is below 1% for a specific product line.
                    </uploaded_document>
                    <Question>
                        Name: Defect Rate
                        Desc: Is the defect rate below 1%?
                        Weight: 10
                    </Question>
                    <output>
                        <Evaluation>Compliant</Evaluation>
                        <Score>90%</Score>
                        <Content>
                            **Quality Control in Manufacturing**:
                            - A manufacturing company produces electronic components. They want to ensure that the defect rate is below 1% for a specific product line.
                        </Content>
                        <Reason>The requirement specifies a maximum defect rate of 1%, which aligns with the company's goal.</Reason>
                    </output>
                </example>
                <example>
                    <uploaded_document>
                        <table><tr><td></td><td></td><td>Company identification  01304200DFPA13804 </td><td>Contractor identification  20030-0000-S00-PL-00004 </td><td>Rev. index </td><td>Sheet of  Sheets  11/31 </td></tr><tr><td>Valid.  status </td><td>Rev.  No. </td></tr><tr><td>EX-DE </td><td>04 </td></tr></table>
                    </uploaded_document>
                    <Question>
                        Name: emergency evacuation plan
                        Desc: emergency evacuation plan
                        Weight: 10
                    </Question>
                    <output>
                        <Evaluation>Non-Conforming</Evaluation>
                        <Score>-1</Score>
                        <Content>
                            <table><tr><td></td><td></td><td>Company identification  01304200DFPA13804 </td><td>Contractor identification  20030-0000-S00-PL-00004 </td><td>Rev. index </td><td>Sheet of  Sheets  11/31 </td></tr><tr><td>Valid.  status </td><td>Rev.  No. </td></tr><tr><td>EX-DE </td><td>04 </td></tr></table>  
                        </Content>
                        <Reason>The document does not provide any information related to the bidder's emergency evacuation plan for medical assistance.</Reason>
                    </output>
                </example>
                <example>
                    <uploaded_document>
                        **Inventory Management**:
                        - A warehouse manages inventory for an e-commerce company. They want to reduce excess stock and stockouts.
                    </uploaded_document>
                    <Question>
                        Name: Inventory Management
                        Desc: Is the inventory level optimized to prevent excess stock and stockouts?
                        Weight: 9
                    </Question>
                    <output>
                        <Evaluation>Compliant</Evaluation>
                        <Score>70%</Score>
                        <Content>
                            **Inventory Management**:
                            - A warehouse manages inventory for an e-commerce company. They want to reduce excess stock and stockouts.
                        </Content>
                        <Reason>Proper inventory management ensures optimal stock levels, minimizing excess stock and preventing stockouts.</Reason>
                    </output>
                </example>
                """
              },
              {
                "role": "user",
                "content": f"""
                    <Document>
                        **TEXT:**
                        {text}
                
                        **Question:**
                        Title: {criterion["name"]} - Description: {criterion["description"]}
                    </Document>
                """
              }
            ]
    
            while steps > 0:
              steps -= 1
              try:
                completion = self.azure_client.chat.completions.create(
                  model=random.choice(self.deployments_map[model]),
                  temperature=temperature,
                  messages=messages,
                  max_tokens= await self.count_max_tokens(text)
                )
                resp2 = completion
                break
              except Exception as e:
                print(e)
                await asyncio.sleep(30)
        
            resp += resp2.choices[0].message.content
        return resp

    async def unify_and_score_responses(self, response1, response2, criterias, temperature=0.001, model="gpt-3.5-turbo-16k"):
        print('Generating Ai response for master evaluation... \n')
        count = 0
        steps = 5
        for criterion in criterias:
            count += 1
            concatenated_criteria = f"Question {count} \n Title: {criterion['name']} \n Description: {criterion['description']}\n"
        messages = [
              {
                "role": "system",
                "content": f"""

                As an AI Industrial Engineer Evaluator assistant,

                    <Instruction>
                        **Task:** 
                        STEP1: Analyze the text as an industrial enginner expert & use the information and determine the final evaluation based on the Question
                        Understand the document so you can be an expert in it, and then decide if the knowledge can be used to give answers to the Question. 
                    
                        ** STEP2: Unify the evaluations:**
                    
                        - If both reports are compliance, use that score as the final evaluation.
                        - If any of the reports is non-compliance, consider the strengths and weaknesses of each explanation:
                            - Does one explanation provide stronger evidence for compliance or non-compliance?
                            - Are there any inconsistencies between the reports?
                        - Based on your analysis, choose the final evaluation (Compliant, Non-Conforming, or Neutral) that best reflects the alignment of the text with the Question.
                    
                        **STEP3: Explanation:**
                    
                        * Merge the explanations from both reports, maintaining the original wording and structure.
                        * Ensure the unified explanation clearly presents the perspectives from both Report 1 and Report 2.
                    
                        **STEP4: Important:** 
                        - Follow the evaluation format strictly
                        - Do not summarize or paraphrase the explanations.
                        - Also lookout for any suggestions, examples or any form of statement that supportts the Question.
                        - We need every single statement no matter how small that could help the Question.
                        - Maintain a clear and organized structure for the unified explanation.
                        - You have to ensure you understand the document perfectly before answering, just like an industrial engineer will do.
                        - And return the right text from either Response 1 or Response 2, then below the overall text  you should add the evaluation as below.
                            **Evaluation Format:**
                            - <Evaluation>(Required)</Evaluation>
                            - <Score>(Required, integer)</Score>
                            -  <Content>(Required) </Content>
                            - <Reason>(Required)</Reason>

                        **Important:**  
                    </Instruction>
                    """
                  },
            {"role": "user", "content": f'''
             
            <example>
                Response 1:
                2.1. HSSE Discipline Areas and Requirements The HSSE functional areas considered in the Workplace Risk Assessment are described below along with an indication of their area of focus and the areas of potential overlap
                Evaluation: Non-Conforming
                Score: -1
            
                Response 2:
                MANAGEMENT SAFETY OBSERVATION TOUR Content: In Contractor organisation a Safety Observation Tour (SOT) is a site visit carried out by Top an Middle Management with the aim of verifying the Safety condition of the workplace and leading the HSE development and improvement of the site. 
                Evaluation: Non-Conforming
                Score: -1

                Question:
                Safety in work.
            </example>
            
            '''
            },
            {"role": "assistant", "content": f'''
             
            <output>
                <Content>
                    MANAGEMENT SAFETY OBSERVATION TOUR Content: In Contractor organisation a Safety Observation Tour (SOT) is a site visit carried out by Top an Middle Management with the aim of verifying the Safety condition of the workplace and leading the HSE development and improvement of the site.
                </Content>
                <Evaluation>Compliant</Evaluation>
                <Score>1</Score>
                <Reason>Because the document talks about safety observation tour and many more safety benefits at site.</Reason>
            </output>
            
            '''
            },
            {
            "role": "user",
            "content": f'''
                <example>
                    Response 1:
                    MACHINE EFFICIENCY ANALYSIS:
                    Utilizing data-driven methods, the machine efficiency analysis assesses the performance of manufacturing equipment to optimize production processes, minimize downtime, and enhance overall productivity.

                    Evaluation: Non-compliance
                    Score: -1

                    Response 2:
                    SIX SIGMA IMPLEMENTATION:
                    Implementing Six Sigma methodologies involves rigorous process improvement techniques aimed at reducing defects and variation in manufacturing processes, thereby enhancing quality and efficiency.

                    Evaluation: Compliance
                    Score: 70%

                    Question:
                    Efficiency in manufacturing processes.
                </example>
                '''
            },
            {
                "role": "assistant",
                "content": f'''
                    <output>
                        <Content>
                            SIX SIGMA IMPLEMENTATION:
                            Implementing Six Sigma methodologies involves rigorous process improvement techniques aimed at reducing defects and variation in manufacturing processes, thereby enhancing quality and efficiency.
                        </Content>
                        <Evaluation>Compliant</Evaluation>
                        <Score>1</Score>
                        <Reason>Because the documents talks about sigma methodologies for site and also efficiency analysis.</Reason>
                    </output>
                '''
            },
            {
                "role": "user",
                "content": f"""
                    Response 1:
                    {response1}
                
                    Response 2:
                    {response2}
        
                    **QUESTION:**
                    {concatenated_criteria}
                """
            }
            ]
        both_responses = f'{response1}\n{response2}'
        while steps > 0:
            steps -= 1
            try:
                completion = self.azure_client.chat.completions.create(
                    model=random.choice(self.deployments_map[model]),
                    temperature=temperature,
                    messages=messages,
                    max_tokens=10000
                )
                response = completion
                break
            except Exception as e:
                print(e)
                await asyncio.sleep(30)
        
        # print(response.choices)
        # print(response.choices[0].message.content)
                  
        return response.choices[0].message.content

        
