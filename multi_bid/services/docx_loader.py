import re
import mammoth

class Output:
    def __init__(self, page_content):
        self.page_content = page_content

class DocsLoader:
    def __init__(self, file_path, allowed_tags=''):
        self.file_path = file_path
        self.allowed_tags = allowed_tags

    @staticmethod
    def remove_tags_except_table(html, allowed_tags=''):
        tag_regex = re.compile(r'<(/?)([^>]+)>')

        def is_allowed(tag):
            default_allowed_tags = {'table', 'tr', 'td', 'th'}
            if not allowed_tags:
                return tag in default_allowed_tags
            return tag in allowed_tags

        def replace_tag(match):
            tag = match.group(2).split()[0].lower()  # Get the tag name
            if is_allowed(tag):
                return f'<{match.group(1)}{tag}>'
            return ''

        return tag_regex.sub(replace_tag, html)

    def load(self):
        with open(self.file_path, "rb") as docx_file:
            result = mammoth.convert_to_html(docx_file)
            html = result.value  # The generated HTML
            
            # Use the remove_tags_except_table method to clean the HTML
            cleaned_html = self.remove_tags_except_table(html, self.allowed_tags)
            return [Output(cleaned_html)]


