# FILE 1: Dataset2Extractor Class and Related Functions
import json
import re
import time
import asyncio
import os, sys
from services.parser_openai_parellel import OpenAIProcessor
from services.pull_reference import find_reference
from services.summarization import SummarizationAssistant
from services.parser_openai import AIProcessor
from services.synonym_expansion import SynonymGenerator
from services.process_scp_evaluate_chunks import AIChunckProcessor
from services.cohere_embedding import CohereService
from services.engr_doc_types_detection import EngrDocTypesDetection
from services.faiss_embedding import FaissEmbedding

class Dataset2Extractor:
    def __init__(self, socket_manager):
        self.open_ai_processor = OpenAIProcessor()
        self.summarization = SummarizationAssistant()
        self.find_reference = find_reference
        self.parser_openai = AIProcessor()
        self.synonym_service = SynonymGenerator()
        self.chunck_processor = AIChunckProcessor()
        self.engr_doc_detector = EngrDocTypesDetection()
        self.faiss_processor = FaissEmbedding()
        self.socket_manager = socket_manager
        self.type = "claude"

    async def process_new_file_SpecsComply(self, criteria, source_document_text, count, requirement_id, log={}):
        self.requirement_id = requirement_id
        # print(f"Entering process new chronobid file {base_file_path}")
        t1 = time.time()

        self.add_event(self.requirement_id, 'progress_message', {
            'event_type': 'SCP',
            'request_id': self.requirement_id,
            'data': {'status': 'processing', 'message': f"Starting evaluation for Query {count}.", 'query_idx': count}
        })

        query = f"Question {count}\nTitle: {criteria}\nDescription: {criteria}"

        print('now trying to do the search....')
        close_upload_text = await self.faiss_processor.search([query], self.requirement_id)
        print('this is uploaded doc text:', close_upload_text)

        
        start_time = time.time()
        synonyms = await self.synonym_service.generate_synonym(f"{criteria}\n Description: {criteria}")
        generate_synonym_time = time.time() - start_time
        print(f"Time for generate_synonym: {generate_synonym_time:.2f} seconds")
        synonyms_name_descriptions = [f"{name.strip()} - {description.strip()}" for name, description in re.findall(r"Name:\s*(.+?)\n\s*Description:\s*(.+?)(?:\n|</SYNONYMS>)", synonyms, re.DOTALL)]

        print('this is synonym desc: ', synonyms_name_descriptions)
        close_synonyms = await self.faiss_processor.search(synonyms_name_descriptions, self.requirement_id, 2)

        print('this is close synonyms: ', close_synonyms)

        t3 = time.time()
        evaluate_data = await self.master_evaluation(source_document_text, close_upload_text[0], close_synonyms, criteria, count, 3)
        t4 = time.time()
        print(f"master evaluation for criteria {count} done in {t4 - t3:.2f} seconds\n")
        print(f'this is evaluation for criteria {count} data: ', evaluate_data)
        

        # resetting the array values
        log['evaluation_report_section']['strength_chunk'][count -1] = []
        log['evaluation_report_section']['weak_chunk'][count -1] = []
        log['evaluation_report_section']['risk_chunk'][count -1] = []
        
        for section in evaluate_data:
            print('this is section: ', section)

            # Check if section is a dictionary or a list
            if isinstance(section, dict):
                # Process as dictionary
                try:
                    # section['evaluation_data'] = section['evaluation_data'].__dict__
                    score = int(section['evaluation_data']['Score'])
                    print('this is score....', score)
                    print('section type: ', type(section))

                    if 20 < score < 81:
                        print('if.....')
                        log['evaluation_report_section']['weak_chunk'][count -1].append(section)
                    elif score >= 81:
                        print('elif....')
                        log['evaluation_report_section']['strength_chunk'][count -1].append(section)
                    else:
                        print('else.....')
                        log['evaluation_report_section']['risk_chunk'][count -1].append(section)
                except (KeyError, ValueError, IndexError) as e:
                    print(f"Error processing dictionary section: {e}")
            elif isinstance(section, list):
                # Process as list (assuming all items in the list are valid dictionary sections)
                for item in section:
                    try:
                        # item['evaluation_data'] = item['evaluation_data'].__dict__
                        score = int(item['evaluation_data']['Score'])
                        print('this is score....', score)
                        print('item type: ', type(item))

                        if 20 < score < 81:
                            print('if.....')
                            log['evaluation_report_section']['weak_chunk'][count -1].append(item)
                        elif score >= 81:
                            print('elif....')
                            log['evaluation_report_section']['strength_chunk'][count -1].append(item)
                        else:
                            print('else.....')
                            log['evaluation_report_section']['risk_chunk'][count -1].append(item)
                    except (KeyError, ValueError, IndexError) as e:
                        print(f"Error processing list item: {e}")
            else:
                print("Unknown type for section:", type(section))
        
        # Add an event for evaluation completion
        self.add_event(self.requirement_id, 'progress_message', {
            'event_type': 'SCP',
            'request_id': self.requirement_id,
            'data': {'status': 'completed', 'message': f"Evaluation completed for Query {count}.", 'query_idx': count}
        })

        print(f"Done with sections evaluation SCP for criteria {count}....")
        return evaluate_data

    async def master_evaluation(self, source_document_text, uploaded_text, synonyms_text, criteria, count, limit=3):
        if uploaded_text:
            # Extracting documents and sorting by score
            high_score_texts_sorted = sorted(uploaded_text, key=lambda x: x[1], reverse=True)  # x[1] is the score
            print('top 5 high scores text: ', high_score_texts_sorted)

            async def process_texts_concurrently(high_texts):
                tasks = [
                    self.chunck_processor.get_claude_to_evaluate_chunks_based_on_criteria(
                        high_text[0], source_document_text, criteria, count  # high_text[0] is the Document
                    ) for high_text in high_texts
                ]
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                unified_responses = []
                for text, result in zip(high_texts, results):
                    if isinstance(result, Exception):
                        print(f"Error processing high texts: {result}")
                        continue
                    json_result = result
                    unified_responses.append({"text_content": text[0], "evaluation_data": json_result})  # text[0] is the Document
                    
                return unified_responses

            high_text_responses = await process_texts_concurrently(high_score_texts_sorted)
    
            self.add_event(self.requirement_id , 'progress_message', {
                'event_type': 'SCP',
                'request_id': self.requirement_id,
                'data': {'status': 'completed', 'message': f"Evaluation completed for query {count}.", 'query_idx': count}
            })

            return high_text_responses
        
        # If synonyms have high similarity, process them
        if synonyms_text:
            # Sort the close score synonyms by the highest score in descending order
            # close_synonyms_sorted = sorted(synonyms_text, key=lambda x: x[1], reverse=True)  # x[1] is the score
            all_results = []
            for sublist in synonyms_text:
                all_results.extend(sublist)

            close_synonyms_sorted = sorted(all_results, key=lambda x: x[1], reverse=True)[:5]
            print('synonyms top 5: ', close_synonyms_sorted)

            async def process_synonyms_concurrently(synonyms):
                tasks = [
                    self.chunck_processor.get_claude_to_evaluate_chunks_based_on_criteria(
                        synonym[0], source_document_text, criteria, count  # synonym[0] is the Document
                    ) for synonym in synonyms
                ]
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                unified_responses = []
                for text, result in zip(synonyms, results):
                    if isinstance(result, Exception):
                        print(f"Error processing synonym: {result}")
                        continue
                    json_result = result
                    unified_responses.append({"text_content": text[0], "evaluation_data": json_result})  # text[0] is the Document
                    
                return unified_responses

            synonym_responses = await process_synonyms_concurrently(close_synonyms_sorted)
            self.add_event(self.requirement_id , 'progress_message', {
                'event_type': 'SCP',
                'request_id': self.requirement_id,
                'data': {'status': 'completed', 'message': f"Evaluation completed for query {count}.", 'query_idx': count}
            })
            return synonym_responses
    
    async def extract_data_from_string(self, data):
        data_string = data if self.type == "claude" else data.choices[0].message.content
        patterns = {tag: re.compile(fr"<{tag}>(.*?)</{tag}>", re.DOTALL | re.IGNORECASE) for tag in ["Evaluation", "Score", "Content", "Reason", "Reference"]}
        return [patterns[tag].search(data_string).group(1) if patterns[tag].search(data_string) else '' for tag in patterns]

    def add_event(self, request_id, event_name, data):
        print(f"Adding event {event_name} to room {request_id}")
        # return
        self.socket_manager.emit_to_client(request_id, event_name, data, '/scp')