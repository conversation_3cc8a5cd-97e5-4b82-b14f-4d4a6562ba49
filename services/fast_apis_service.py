import random
from services.groq_ai_service import GroqService
from services.cerebra_ai_service import CerebrasService


class FastAPIs:
    def __init__(self):
        """Initializes the FastAPIs class."""
        self.groq_service = GroqService()
        self.cerebras_service = CerebrasService()

    def _try_groq_service(self, messages, model, max_tokens=1024):
        print("""Attempts to generate a chat completion using GroqService.""")
        try:
            return self.groq_service.generate_completion(messages, model=model, max_tokens=max_tokens)
        except Exception as e:
            print(f"GroqService failed: {e}")
        return None

    def _try_cerebras_service(self, messages):
        print("""Attempts to generate a chat completion using CerebrasService.""")
        try:
            response = self.cerebras_service.generate_completion(messages)
            return response.get("text_content") if response else None
        except Exception as e:
            print(f"CerebrasService failed: {e}")
        return None

    def generate_completion(self, messages, service='groq', model=None, max_tokens=1024):
        """
        Generates a chat completion by randomly selecting a service to try first.

        Args:
            messages (list): A list of message dictionaries containing role and content.
            service (str): Specific service to use ('groq' or 'cerebras')
            model (str): Model to use (only for Groq)
            max_tokens (int): Optional max tokens for the response

        Returns:
            str or None: The generated text content, or None if both services fail.
        """
        if service == 'groq':
            return self._try_groq_service(messages, model, max_tokens)
        elif service == 'cerebras':
            return self._try_cerebras_service(messages)

        services = [self._try_groq_service, self._try_cerebras_service]
        random.shuffle(services)

        for service in services:
            result = service(messages, model, max_tokens) if service == self._try_groq_service else service(messages)
            if result:
                return result

        return None


# Example usage:
# if __name__ == "__main__":
    # messages = [
    #     {"role": "user", "content": "Explain the benefits of AI in healthcare."}
    # ]
    
    # from services.evaluation_templates import EvaluationTemplates
    # prompt_template = EvaluationTemplates.get_template('detect_section_heading')
    # vars = {
    #     "target_section_content": """
        
    #     """,
    #     "preceding_section_1_content": "",
    #     "preceding_section_2_content": "",
    #     "preceding_section_3_content": "",
    # }
    # prompt = prompt_template[0]
    # prompt = prompt.format(**vars)
    # messages = [
    #     {"role": "user", "content": prompt}
    # ]

    # fast_apis = FastAPIs()
    # result = fast_apis.generate_completion(messages, model="llama-3.1-8b-instant")

    # if result:
    #     print("Generated Text:", result)
    # else:
    #     print("Failed to generate completion.")
