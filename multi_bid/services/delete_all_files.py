import openai
from time import sleep

class FileManager:
    def __init__(self, api_key):
        self.client = openai.OpenAI(api_key=api_key)

    def list_files(self):
        try:
            files_response = self.client.files.list()
            return files_response.data
        except Exception as e:
            print(f"An error occurred while listing files: {e}")
            return []

    def delete_file(self, file_id):
        try:
            self.client.files.delete(file_id)
            print(f"Deleted file with ID: {file_id}")
        except Exception as e:
            print(f"An error occurred while deleting file with ID {file_id}: {e}")

    def delete_files_with_suffix(self, suffix):
        files = self.list_files()
        for file in files:
            if file.filename.endswith(suffix):
                self.delete_file(file.id)
        print("Deletion complete.")

    def list_and_delete_files_with_suffix(self, suffix):
        while True:
            files = self.list_files()
            if not files:
                break
            self.delete_files_with_suffix(suffix)
            sleep(5)  # Adjust the sleep duration as needed

# # Usage
# api_key = os.getenv('OPENAI_API_KEY')
# file_manager = FileManager(api_key)
# file_manager.list_and_delete_files_with_suffix("_merged.docx")
