import os
import json
import anthropic
import time
from DocumentNumber.documentnumber import DocumentNumbering

class DocumentNumberingAnalysisAgent:
    def __init__(self, client_id):
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        env_file_path = os.path.join(parent_dir, 'env.json')

        self.client_id = client_id
        self.env_file_path = env_file_path
        self.client = None
        self.model = "claude-3-5-sonnet-20241022"
        self.document_numbering_processor = DocumentNumbering()
        self.setup_agent()

    def load_env(self):
        with open(self.env_file_path, 'r') as f:
            return json.load(f)

    def setup_claude_client(self, api_key):
        self.client = anthropic.Anthropic(api_key=api_key)

    def setup_agent(self):
        env_data = self.load_env()
        self.setup_claude_client(env_data.get('CLAUDE_API_KEY', ''))
        client_document_numbering_data = self.document_numbering_processor.view(self.client_id)
        self.client_template = client_document_numbering_data['template']

    def prepare_messages(self, uploaded_document_text):
        return [
            [
                {
                    "type": "text",
                    "text": "You are an EPC(engineering, procurement, construction) AI assistant tasked with analyzing project documentation. Your goal is to provide analyze any project document given to you and from the existing document numbering system you'll be able to say which discipline the uploaded project belongs to.",
                },
                {
                    "type": "text",
                    "text": self.client_template,
                    "cache_control": {"type": "ephemeral"}
                }
            ],
            [
                {
                    "role": "user",
                    "content": f"""
                        Analyze the uploaded document and tell me what discipline it belongs to? 
                        <UPLOADED_DOCUMENT>{uploaded_document_text}</UPLOADED_DOCUMENT>
                        <RULE>
                            - The response should strictly follow the below output format.
                            - Do not add additional explanation or reason, just return the document number and then discipline and also the discipline code strictly.
                        </RULE>
                        <OUTPUT_FORMAT>
                            <DOCUMENT_NUMBER>
                                the uploaded document number
                            </DOCUMENT_NUMBER>
                            <DISCIPLINE>
                                The discipline the uploaded document belongs to.
                            </DISCIPLINE>
                            <DISCIPLINE_CODE>
                                The discipline code the uploaded document belongs to e.g ST, PR MS etc
                            </DISCIPLINE_CODE>
                        </OUTPUT_FORMAT>
                    """
                }
            ]
        ]

    def ask_question(self, upload_text):
        messages = self.prepare_messages(upload_text)
        response = self.client.messages.create(
            model=self.model,
            max_tokens=1024,
            system=messages[0],
            messages=messages[1]
        )
        return response



# Example usage
if __name__ == "__main__":
    client_id = '7a199498-9384-4031-a00e-59082b8123e6'
    agent = DocumentNumberingAnalysisAgent(client_id)
    
    # Ask a question
    upload_text = """

    Heat and Material Balance
    Design case  
    Discipline:Process

    Project
    PROJECT1 DEVELOPMENT PROJECT
    Originated By
    LY
    Client
    Company
    Checked By
    JL
    Location
    location x
    Approved By
    CF
    Job No.
    PROJECT NUMBER
    Revision
    002
    Doc No.
    PROJECT1-FEED CONTRACTOR-MUL-E000-PR-LST-0001
    Date
    15/02/date

    HEAT AND MATERIAL BALANCES - LEAN SUMMER
    GENERAL

    Project
    PROJECT1 DEVELOPMENT PROJECT
    Originated By
    JG
    Client
    Company
    Checked By
    AA
    Location
    Central Processing Facility
    Approved By
    CF
    Job No.
    PROJECT NUMBER
    Revision
    002
    Doc No.
    PROJECT1FEED CONTRACTOR-PF01-E000-PR-CAL-0001
    Date
    05/09/12


    Abbreviations
    Abbreviations
    Description
    KO
    Knock Out
    NNF
    Normally No Flow
    MSCMD
    Million Standard Cubic Metre per Day
    MTA
    Million Tonnes per Annum
    MW
    Molecular Weight
    STD
    Standard


    Specifications

    Simulation
    RVP Specification for Stabilised Condensate
    < 0.8 bara
    0.5 bara
    Carbon Dioxide in Export Gas
    < 2.0%
    1.8%
    Water dew point of Export Gas
    -12°C (@ > 19 barg)
    -15.84°C (@ 44 barg)
    Hydrocarbon dew point of Export Gas
    10°C (@ 35 barg)
    6.3 °C (@ any pressure)


    Streams 36, 37, 46 and 47 have been simulated using Bryan Research and Engineering software, ProMax. These include additional components (namely MDEA, TEG and Amine Promoter, therefore are shown separately on Page 5.
    All other streams have been simulated using AspenTech software, HYSYS v7.1.
    Further information regarding the HYSYS simulations can be found in the Process Simulation Report, document number PROJECT1FEED CONTRACTOR-PF01-E000-PR-REP-0001.

    """
    start_time = time.time()
    # First query: before caching
    response = agent.ask_question(upload_text)
    print(response)

    end_time = time.time()
    elapsed_time = end_time - start_time
    print(f"Query time: {elapsed_time:.2f} seconds")
    

    # start_time2 = time.time()
    # # Second query: after caching
    # response = agent.ask_question(upload_text)
    # print(response)

    # end_time = time.time()
    # elapsed_time = end_time - start_time2
    # print(f"Query time: {elapsed_time:.2f} seconds")

