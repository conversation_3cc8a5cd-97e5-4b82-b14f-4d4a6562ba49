
from openai import OpenAI

client = OpenAI(
    api_key="***************************************************",
)


def criteria_classify(sources, specifications):

    text = ''
    idx = 1
    for x in sources:
        text += 'Source ' + str(idx) + ": " + x["content"] + "\n"
        idx+=1
    
    text += "Specifications:\n" + specifications

    completion = client.chat.completions.create(
        model="gpt-3.5-turbo",
        temperature=0,
        messages=[
            {
                "role": "system", 
                "content": f'''You are an assistant to find out the most relevent criteria for the mentioned text.'''
            },
            {"role": "user", "content": f'''{text}'''}
        ]
    )
    # print(completion.choices[0].message["content"])
    return parseData(completion.choices[0].message.content)