import os, sys
import asyncio
import time
import random
from time import sleep
from services.claude_ai_service import Claude<PERSON>ervice
from services.prompt_saver import PromptSaver
from services.prompt_loader import PromptLoader
import json

class AIChunckProcessor:
    def __init__(self):
        # Get the path to the parent directory
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # Load environment variables from env.json file in the parent directory
        env_file_path = os.path.join(parent_dir, 'env.json')
        
        # Load environment variables from env.json file
        with open(env_file_path, 'r') as f:
            env_data = json.load(f)
        
        self.claude_client = ClaudeService()
        self.prompt_saver = PromptSaver()
        self.prompt_loader = PromptLoader()

    
    async def get_claude_to_evaluate_chunks_based_on_criteria(self, text, source_text, criteria, count, temperature=0.001, model="Claude 3 Sonnet", steps=2):
        print("Generating AI response for text formatting claude...")
        # Initialize variables
        concatenated_criteria = f"{count}: {criteria['name']} \n Description: {criteria['description']}\n"
        # print('uploaded text: ', text)

        comparison_prompt = self.prompt_loader.get_prompt('claude_to_evaluate_chunks_based_on_criteria')
        messages = comparison_prompt.copy()
        messages.append({
            "role": "user",
            "content": f"<SOURCE_DOCUMENT>\n{source_text}</SOURCE_DOCUMENT>\n<UPLOADED_DOCUMENT>\n{text}</UPLOADED_DOCUMENT>\n<Question>\n{concatenated_criteria}\n</Question>"
        })
        
        response_agent_1 = ""
        response_agent_2 = ""
        token_count_in = 0
        token_count_out = 0
        while steps > 0:
            steps -= 1
            try:
    
                # Try agent 1 (Sonnet new) by 0.2 temp
                completion1 = await self.claude_client.generate_message_agent_sonnet_new(
                    messages=messages,
                    temperature=0.1
                )

                # Try agent 1 (Haiku) by 0.2 temp
                completion2 = await self.claude_client.generate_message_agent_haiku(
                    messages=messages,
                    temperature=0.1
                )
                

                # Handle response for agent 1
                if completion1 and hasattr(completion1.content[0], 'text'):
                    response_agent_1 = completion1.content[0].text
                    token_count_in += completion1.usage.input_tokens
                    token_count_out += completion1.usage.output_tokens
                

                # Handle response for agent 2
                if completion2 and hasattr(completion2.content[0], 'text'):
                    response_agent_2 = completion2.content[0].text
                    token_count_in += completion2.usage.input_tokens
                    token_count_out += completion2.usage.output_tokens
                
                # Return both responses for comparison
                if response_agent_1 or response_agent_2:
                    data_supplied = f"<SOURCE_DOCUMENT>\n{source_text}</SOURCE_DOCUMENT>\n<UPLOADED_DOCUMENT>\n{text}</UPLOADED_DOCUMENT>\n<Question>\n{concatenated_criteria}\n</Question>"
                    return await self.generate_master_agent_response(data_supplied, response_agent_1, response_agent_2, token_count_in, token_count_out)
                else:
                    raise ValueError("Invalid responses received from both agents")


            except Exception as e:
                print(f"Error generating message from evaluate chunk: {e}")
                await asyncio.sleep(10)
    
    async def generate_master_agent_response(self, data, response_agent_1, response_agent_2, token_count_in, token_count_out):
        if not response_agent_1 and not response_agent_2:
            raise ValueError("No responses to compare")

        comparison_prompt = self.prompt_loader.get_prompt('master_agent_response')
        messages = comparison_prompt.copy()
        messages[-1] = messages[-1].format(
            data = data,
            response_agent_1 = response_agent_1,
            response_agent_2 = response_agent_2
        )

        try:
            # Use the master agent (sonnet) for comparison
            completion_master = await self.claude_client.generate_message_agent_sonnet_new(
                messages=messages,
                temperature=0.09
            )

            if completion_master and hasattr(completion_master.content[0], 'text'):
                response_master = completion_master.content[0].text
                token_count_in += completion_master.usage.input_tokens
                token_count_out += completion_master.usage.output_tokens
                return response_master, token_count_in, token_count_out, response_agent_1, response_agent_2
            else:
                raise ValueError("Invalid response from master agent")
        except Exception as e:
            print(f"Error generating message: {e}")
            raise RuntimeError("Master agent failed to compare responses")


        
