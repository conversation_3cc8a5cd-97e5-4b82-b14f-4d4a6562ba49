from services.prompt_loader import Prompt<PERSON>oader
from services.claude_ai_service import Claude<PERSON><PERSON><PERSON>
from services.data_handler import DataManager
import json


def auto_detect_criteria(project_id):
    """
    Function to automatically detect criteria using default questions and subquestions.
    Only requires a project_id parameter.
    
    Args:
        project_id (str): The ID of the project to analyze
        
    Returns:
        dict: The detected criteria data
    """
    # Default questions and subquestions
    default_questions = ["What are the major equipments required for this project please"]
    default_subquestions = [
        "Can you list the essential equipment for this project?",
        "What tools or machinery will be used in this project?"
    ]
    
    if not project_id:
        raise ValueError("Project ID is required")
        
    # Initialize data manager
    data_manager = DataManager()
    
    # Run data extraction with default questions
    result = data_manager.extract_data_v2_sync(project_id, default_questions, default_subquestions, 20)
    
    
    # Default output format
    output_format = [
        "Ball Mill",
        "AG Mill",
        "Fedder",
        "Pressure Meter"
    ]
    
    # Prepare prompt with document content
    document_content = json.dumps(result[0]['source_list'])
    prompt = PromptLoader().get_prompt('auto_criteria_detection', {
        "document_content": document_content,
        "output_format": output_format
    })
    
    # Call LLM to generate comparison
    messages = [{"role": "user", "content": prompt}]
    temperature = 0.0001
    claude_client = ClaudeService()
    response = claude_client.generate_message_sync(messages, "", temperature, "Claude 3 Sonnet", 4096)
    data = json.loads(response)

    return data
    
  
    
  
