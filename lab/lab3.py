import json
import asyncio
from services.data_handler import DataManager
from init import app

# Initialize DataManager
data_manager = DataManager()

# Define project data
projects = [

    {
        "project_id": "a16c6a2a-666d-48da-831c-36aa446300cf",
        "sub_question":  ['What role does the Integrated Control and Safety System (ICSS) play in overpressure prevention?'] 
,
        "similar_questions": ['Overpressure Prevention System - A system designed to detect and prevent overpressure conditions in a vessel or system.', 'High-Pressure Monitoring System - A system used to monitor and control high-pressure levels within a vessel or system.', 'Safety Shutdown System - A system designed to automatically shut down a vessel or system in response to abnormal pressure conditions.', 'Pressure Relief Mechanism - A device or system used to relieve excessive pressure within a vessel or system.', 'Safety Valve - A valve that automatically opens in response to excessive pressure to prevent overpressuring.', 'Pressure Management System - A system used to control and manage pressure levels within a vessel or system.', 'Process Safety System - A system designed to prevent accidents and mitigate the risks associated with process systems.', 'Emergency Shutdown System - A system designed to automatically shut down a vessel or system in response to an emergency situation, such as overpressuring.', 'Safeguarding System - A system designed to protect against unforeseen events, such as overpressuring.']
,
    },
    {
        "project_id": "a16c6a2a-666d-48da-831c-36aa446300cf",
        "sub_question": ['What components are included in the pressure relief system, and how do they function?']
,
        "similar_questions": ['Pressure Regulation System - A system designed to control and manage pressure levels, often including components such as valves, sensors, and controllers.', 'Safety Valve Mechanism - A device or system that automatically releases pressure when it exceeds a certain threshold, helping to prevent damage or accidents.', 'Overpressure Protection Device - A component or system designed to protect equipment or systems from damage caused by excessive pressure, often through the release of pressure or other safety measures.']
,
    },
    {
        "project_id": "a16c6a2a-666d-48da-831c-36aa446300cf",
        "sub_question":  ['How does the system ensure compliance with safety regulations?'],
        "similar_questions": ['Adherence to Standards - The act of conforming to established rules or regulations, especially those related to safety.', 'Regulatory Conformance - The state of being in compliance with laws, regulations, or standards, particularly in regards to safety protocols.', 'Safety Protocol Compliance - The process of following established guidelines or procedures to ensure safety and minimize risks, in accordance with relevant regulations or laws.'],
    },
    {
        "project_id": "a16c6a2a-666d-48da-831c-36aa446300cf",
        "sub_question":   ['Which regulations and standards govern the design of the overpressure protection system?'],
        "similar_questions": ['Safety Protocols - Established procedures for ensuring the safety and well-being of individuals or systems.', 'Pressure Relief Systems - Mechanisms designed to prevent excessive pressure buildup in systems or equipment.', 'Protective Measures - Actions taken to prevent or mitigate potential hazards or risks.'],
    },
    {
        "project_id": "a16c6a2a-666d-48da-831c-36aa446300cf",
        "sub_question":  ['What are the primary risk scenarios considered in the overpressure protection philosophy?'],
        "similar_questions": ['Hazard Scenarios - Potential events or situations that pose a threat to safety or the environment.', 'Risk Profiles - Detailed descriptions of potential risks or hazards associated with a particular situation or system.', 'Failure Modes - Ways in which a system, component, or process might fail, leading to potential consequences such as overpressure.', 'Technical Requirements - Specific criteria that a system or product must meet in terms of performance, functionality, and reliability.', 'Safety Standards - Rules, regulations, or guidelines that govern the safe design, operation, and maintenance of a system.', 'Cost Benefit Analysis - A method for evaluating the potential costs and benefits of a particular course of action.', 'Feasibility Study - An examination of the potential risks, challenges, and opportunities associated with a project or initiative.', 'Emergency Response Plan - A strategy for responding to emergencies or unexpected events that affect personnel or the environment.', 'Incident Investigation - An investigation into the cause of an incident or accident in order to prevent future occurrences.'],
    },
]

async def extract_and_save_data():
    with app.app_context():
        extracted_data = []
        
        for project in projects:
            sources = await data_manager.extract_data_v2(
                project["project_id"], project["sub_question"], project["similar_questions"], 5
            )
            extracted_data.append({
                "project_id": project["project_id"],
                "sub_question": project["sub_question"],
                "sources": sources,
            })

        # Save to a JSON file
        with open("extracted_sources.json", "w") as json_file:
            json.dump(extracted_data, json_file, indent=4)

# Run the extraction and save process
asyncio.run(extract_and_save_data())
