#!/bin/bash

# Navigate to the project directory
source /home/<USER>/dev-ds-oil-and-gas/venv/bin/activate
sudo pkill -f gunicorn || true;
# pkill -f uwsgi || true;
sleep 2;
cd "/home/<USER>/dev-ds-oil-and-gas/"
# Start Gunicorn server with the specified configuration
# gunicorn --bind=0.0.0.0:5120 app:app --timeout=3000 --workers=8 --access-logfile /home/<USER>/dev-ds-oil-and-gas/logs/server.log --error-logfile /home/<USER>/dev-ds-oil-and-gas/logs/server.log --capture-output --log-level debug --max-requests 300 --max-requests->
# uwsgi --http-socket 0.0.0.0:5120 --wsgi-file app.py --callable app --processes 8 --threads 1 --ha<PERSON><PERSON> 3000 --max-requests 300 --http-websockets --logto /home/<USER>/dev-ds-oil-and-gas/logs/server.log --log-master --daemonize /home/<USER>/dev-ds-oil-and-gas/log>
gunicorn --bind 0.0.0.0:5120 app:app \
    --worker-class gevent \
    --timeout 120 \
    --workers 4 \
    --access-logfile /home/<USER>/dev-ds-oil-and-gas/logs/server.log \
    --error-logfile /home/<USER>/dev-ds-oil-and-gas/logs/server.log \
    --capture-output \
    --log-level debug \
    --max-requests 200 \
    --max-requests-jitter 30 \
    --daemon
    
service cron start



