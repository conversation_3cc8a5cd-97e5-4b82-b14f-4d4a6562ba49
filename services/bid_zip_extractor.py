import os
import uuid
import json
import time
import shutil
import tempfile
from zipfile import ZipFile, BadZipFile
from werkzeug.utils import secure_filename
from models import BidFile, Bids, BidFileMetadata
import traceback


class BidZipExtractor:
    def __init__(self, base_upload_path, user_id):
        self.base_upload_path = base_upload_path
        self.user_id = user_id
        self._load_environment()
        self.file_batch = []  # Store file info in batch
        self.BATCH_SIZE = 50  # Process files in batches of 50
        self.ALLOWED_EXTENSIONS = {'doc', 'docx', 'pdf', 'txt', "csv", "xls", "xlsx", "xlsm"}
        self.unsupported_files = []  # Track unsupported files

    def _load_environment(self):
        """Loads environment variables from a JSON file."""
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        env_file_path = os.path.join(parent_dir, 'env.json')
        with open(env_file_path, 'r') as f:
            env_data = json.load(f)
        
        self.EXTRACT_FOLDER = env_data.get("ZIP_EXTRACT_DIR")
        self.METADATA_FOLDER = env_data.get("BID_METADATA_DIR")

    def _is_system_file(self, file_path, filename):
        """Check if a file is a system file that should be ignored."""
        ignore_patterns = [
            '__MACOSX',      # macOS metadata folder
            '.DS_Store',     # macOS metadata file
            'Thumbs.db',     # Windows thumbnail cache
            'desktop.ini',   # Windows folder settings
            '._.DS_Store',   # macOS resource fork
        ]
        
        if filename.startswith('._'):
            return True
            
        path_parts = file_path.split(os.sep)
        for part in path_parts:
            if part in ignore_patterns:
                return True
                
        if filename in ignore_patterns:
            return True
            
        return False

    def process_zip(self, zip_file, directory_name, request_id):
        """
        Process a zip file and extract its contents to a named directory
        
        Args:
            zip_file: FileStorage object containing the zip file
            directory_name: Name to give the extracted directory
            request_id: Unique request ID for tracking
            
        Returns:
            tuple: (Path to the extracted directory, bid_id, list of unsupported files)
        """
        try:
            self.bid_id = str(uuid.uuid4())
            metadata = {}
            self.unsupported_files = []  # Reset unsupported files list
            
            # Create a temporary directory for processing
            with tempfile.TemporaryDirectory() as temp_dir:
                # Save the zip file to temp directory
                zip_path = os.path.join(temp_dir, secure_filename(zip_file.filename))
                zip_file.save(zip_path)

                # Create extraction directory
                extract_path = os.path.join(temp_dir, self.EXTRACT_FOLDER)
                os.makedirs(extract_path, exist_ok=True)

                # Extract the zip file
                with ZipFile(zip_path, 'r') as zip_ref:
                    zip_ref.extractall(extract_path)
                
                bid_data = {
                    'id': self.bid_id,
                    'name': directory_name,
                    'user_id': self.user_id
                }
                Bids.create(**bid_data)

                # Create final directory with given name
                final_path = os.path.join(self.base_upload_path, 'bid', self.bid_id)
                
                # If directory exists, remove it
                if os.path.exists(final_path):
                    shutil.rmtree(final_path)
                os.makedirs(final_path, exist_ok=True)
                
                # Generate file mapping and metadata
                file_mapping = {}  # Maps temp paths to final paths
                self._extract_nested_zips(extract_path, metadata, file_mapping)
                
                # First, copy all files to the final location while preserving structure
                for temp_path, rel_path in file_mapping.items():
                    dest_path = os.path.join(final_path, rel_path)
                    
                    # Ensure directory exists
                    os.makedirs(os.path.dirname(dest_path), exist_ok=True)
                    
                    # Only copy if source path exists (defensive coding)
                    if os.path.exists(temp_path):
                        shutil.copy2(temp_path, dest_path)
                    else:
                        print(f"Warning: Source file not found during copy: {temp_path}")
                        self.unsupported_files.append(f"{rel_path} (source file not found)")
                
                # Update file paths in the file_batch to use final paths
                files_to_process = []
                for temp_file in self.file_batch:
                    temp_path = temp_file["file_path"]
                    if temp_path in file_mapping:
                        relative_path = file_mapping[temp_path]
                        final_file_path = os.path.join(final_path, relative_path)
                        # Verify the file exists at the final location
                        if os.path.exists(final_file_path):
                            files_to_process.append({"file_path": final_file_path, "file_id": temp_file["file_id"]})
                        else:
                            print(f"Warning: File not found at expected final path: {final_file_path}")
                            self.unsupported_files.append(f"{relative_path} (file not found at final location)")
                            # Update database status to reflect the issue
                            BidFile.update(status="error", message="File not found at final location").where(
                                BidFile.id == temp_file["file_id"]).execute()
                
                # Process files if we have any
                if files_to_process:
                    process_files_bid_bulk_task.delay(self.bid_id, files_to_process, request_id)
                
                # Save metadata as a JSON file
                metadata_dir = os.path.join(self.base_upload_path, self.METADATA_FOLDER)
                os.makedirs(metadata_dir, exist_ok=True)
                
                metadata_filename = f"{self.bid_id}_{int(time.time())}.json"
                metadata_path = os.path.join(metadata_dir, metadata_filename)

                with open(metadata_path, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, indent=4)

                # Store metadata reference in database
                metadata_entry = {
                    "bid_id": self.bid_id,
                    "bid_metadata": metadata_filename,
                    "status": "processed"
                }
                BidFileMetadata.create(**metadata_entry)

                return final_path, self.bid_id, self.unsupported_files

        except (IOError, BadZipFile) as e:
            print(f"An error occurred while processing the zip file: {e}")
            return None, None, []

    def _extract_nested_zips(self, directory, metadata, file_mapping):
        """
        Recursively extract nested zip files and store file metadata.

        Args:
            directory: Path to scan for nested zip files.
            metadata: Dictionary to store file structure.
            file_mapping: Dictionary to map temporary paths to relative paths
        """
        for root, _, files in os.walk(directory):
            relative_path = os.path.relpath(root, directory)
            parent_folder = relative_path if relative_path != "." else "root"
            node = self._get_nested_dict(metadata, relative_path.split(os.sep)) if relative_path != "." else metadata
            
            for file in files:
                file_path = os.path.join(root, file)
                
                # Skip system files
                if self._is_system_file(file_path, file):
                    print(f"Skipping system file: {file}")
                    self.unsupported_files.append(f"{os.path.relpath(file_path, directory)} (system file)")
                    continue
                
                if file.endswith('.zip'):
                    # Extract nested zip
                    extract_path = os.path.join(root, file[:-4])
                    os.makedirs(extract_path, exist_ok=True)
                    
                    try:
                        with ZipFile(file_path, 'r') as zip_ref:
                            zip_ref.extractall(extract_path)
                        os.remove(file_path)
                        self._extract_nested_zips(extract_path, node.setdefault(file, {}), file_mapping)
                    except (IOError, BadZipFile) as e:
                        print(f"Error extracting nested zip {file_path}: {e}")
                        self.unsupported_files.append(f"{os.path.relpath(file_path, directory)} (corrupted zip)")
                else:
                    relative_file_path = os.path.relpath(file_path, directory)
                    # Store mapping of temp path to relative path
                    file_mapping[file_path] = relative_file_path
                    
                    # Store metadata and add file entry
                    file_extension = file.rsplit('.', 1)[-1].lower() if '.' in file else ""
                    if file_extension in self.ALLOWED_EXTENSIONS:
                        # Add file to batch for processing
                        bid_file_id = str(uuid.uuid4())
                        self.file_batch.append({"file_path": file_path, "file_id": bid_file_id})
                        print(f"Updating file {file} as pending")
                        file_data = {
                            'id': bid_file_id,
                            'name': file,
                            'bid_id': self.bid_id,
                            'status': "idle",
                            'file_type': file_extension
                        }
                        BidFile.create(**file_data)
                        print(f"Done with the update")
                        
                        node[bid_file_id] = {
                                'id': bid_file_id,
                                'name': file,
                                'path': relative_file_path,
                                'parent_dir': parent_folder,
                            }
                    else:
                        self.unsupported_files.append(f"{relative_file_path} (unsupported extension: {file_extension})")

    def _get_nested_dict(self, parent, keys):
        """Traverse or create a nested dictionary based on a list of keys."""
        for key in keys:
            if key:  # Skip empty keys
                parent = parent.setdefault(key, {})
        return parent

    def update_file_to_zip(self, zip_file, bid_id, req_id=None):
        """
        Update files in an existing bid by processing a zip file.
        
        Args:
            zip_file: FileStorage object containing the zip file
            bid_id: ID of the bid to update
            req_id: Optional request ID for tracking
            
        Returns:
            tuple: (Path to the updated directory, list of unsupported files)
        """
        try:
            self.bid_id = bid_id
            self.unsupported_files = []  # Reset unsupported files list
            metadata = {}
            file_mapping = {}
            
            # Create a temporary directory for processing
            with tempfile.TemporaryDirectory() as temp_dir:
                # Save the zip file to temp directory
                zip_path = os.path.join(temp_dir, secure_filename(zip_file.filename))
                zip_file.save(zip_path)

                # Create extraction directory
                extract_path = os.path.join(temp_dir, self.EXTRACT_FOLDER)
                os.makedirs(extract_path, exist_ok=True)

                # Extract the zip file
                with ZipFile(zip_path, 'r') as zip_ref:
                    zip_ref.extractall(extract_path)

                # Recursively extract nested zip files
                self._extract_nested_zips(extract_path, metadata, file_mapping)
                
                # Create final directory with given name
                final_path = os.path.join(self.base_upload_path, 'bid', self.bid_id)
                
                # If directory doesn't exist, create it
                if not os.path.exists(final_path):
                    os.makedirs(final_path, exist_ok=True)
                
                # Copy files to the final location preserving structure
                for temp_path, rel_path in file_mapping.items():
                    dest_path = os.path.join(final_path, rel_path)
                    
                    # Ensure directory exists
                    os.makedirs(os.path.dirname(dest_path), exist_ok=True)
                    
                    # Only copy if source path exists
                    if os.path.exists(temp_path):
                        shutil.copy2(temp_path, dest_path)
                    else:
                        print(f"Warning: Source file not found during copy: {temp_path}")
                        self.unsupported_files.append(f"{rel_path} (source file not found)")
                
                # Update file paths in the file_batch to use final paths
                files_to_process = []
                for temp_file in self.file_batch:
                    temp_path = temp_file["file_path"]
                    if temp_path in file_mapping:
                        relative_path = file_mapping[temp_path]
                        final_file_path = os.path.join(final_path, relative_path)
                        # Verify the file exists at the final location
                        if os.path.exists(final_file_path):
                            files_to_process.append({"file_path": final_file_path, "file_id": temp_file["file_id"]})
                        else:
                            print(f"Warning: File not found at expected final path: {final_file_path}")
                            self.unsupported_files.append(f"{relative_path} (file not found at final location)")
                            # Update database status to reflect the issue
                            BidFile.update(status="error", message="File not found at final location").where(
                                BidFile.id == temp_file["file_id"]).execute()
                
                # Process files if we have any
                if files_to_process:
                    process_files_bid_bulk_task.delay(self.bid_id, files_to_process, req_id)

                # Save metadata as a JSON file
                metadata_dir = os.path.join(self.base_upload_path, self.METADATA_FOLDER)
                os.makedirs(metadata_dir, exist_ok=True)
                
                metadata_filename = f"{self.bid_id}_{int(time.time())}.json"
                metadata_path = os.path.join(metadata_dir, metadata_filename)

                with open(metadata_path, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, indent=4)
                
                # Store metadata reference in database
                metadata_entry = {
                    "bid_id": self.bid_id,
                    "bid_metadata": metadata_filename,
                    "status": "processed"
                }
                BidFileMetadata.create(**metadata_entry)

                return final_path, self.unsupported_files

        except (IOError, BadZipFile) as e:
            print(f"An error occurred while updating files in the bid: {e}")
            return None, [] 