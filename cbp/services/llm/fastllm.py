"""
LLM Service Orchestrator for managing multiple AI service providers.

This module provides a high-availability solution for LLM services by implementing
a failover mechanism between different AI providers (Groq and Cerebras).
"""

from typing import List, Dict, Optional, Callable
import random
import logging
from .cerebras import CerebrasService
from .groq import GroqService

# Configure logging
logger = logging.getLogger(__name__)

class LLMOrchestrator:
    """
    Orchestrates multiple LLM services with failover capabilities.
    
    This class manages multiple LLM service providers and implements a strategy
    for load balancing and failover between them to ensure high availability.
    """

    def __init__(self) -> None:
        """Initialize LLM service providers."""
        self.groq_service = GroqService()
        self.cerebras_service = CerebrasService()

    def _execute_groq_inference(
        self, 
        messages: List[Dict[str, str]], 
        model: Optional[str] = None
    ) -> Optional[str]:
        """
        Execute inference using Groq's LLM service.

        Args:
            messages: List of message dictionaries with role and content.
            model: Optional model identifier for Groq service.

        Returns:
            Generated text content or None if the service fails.
        """
        try:
            return self.groq_service.generate_completion(messages, model=model)
        except Exception as error:
            logger.error(f"Groq service error: {error}")
            return None

    def _execute_cerebras_inference(
        self, 
        messages: List[Dict[str, str]]
    ) -> Optional[str]:
        """
        Execute inference using Cerebras' LLM service.

        Args:
            messages: List of message dictionaries with role and content.

        Returns:
            Generated text content or None if the service fails.
        """
        try:
            response = self.cerebras_service.generate_completion(messages)
            return response.get("text_content") if response else None
        except Exception as error:
            logger.error(f"Cerebras service error: {error}")
            return None

    def generate_completion(
        self, 
        messages: List[Dict[str, str]], 
        provider: Optional[str] = None,
        model: Optional[str] = None
    ) -> Optional[str]:
        """
        Generate text completion using available LLM services.

        This method implements a smart routing strategy:
        1. If a specific provider is requested, it will be used exclusively
        2. Otherwise, it randomly selects providers for load balancing

        Args:
            messages: List of message dictionaries with role and content.
            provider: Optional specific provider to use ('groq' or 'cerebras').
            model: Optional model identifier for specific providers.

        Returns:
            Generated text content or None if all services fail.
        """
        if provider == 'groq':
            return self._execute_groq_inference(messages, model)
        elif provider == 'cerebras':
            return self._execute_cerebras_inference(messages)

        logger.info('Executing random provider selection strategy')
        services: List[Callable] = [
            lambda: self._execute_groq_inference(messages, model),
            lambda: self._execute_cerebras_inference(messages)
        ]
        random.shuffle(services)

        for service_call in services:
            if result := service_call():
                return result

        logger.error("All LLM services failed to generate completion")
        return None


if __name__ == "__main__":
    # Example usage with logging configuration
    logging.basicConfig(level=logging.INFO)
    
    test_messages = [
        {"role": "user", "content": "Explain the benefits of AI in healthcare."}
    ]

    llm_orchestrator = LLMOrchestrator()
    if completion := llm_orchestrator.generate_completion(test_messages):
        logger.info(f"Generated Text: {completion}")
    else:
        logger.error("Failed to generate completion from all providers")
