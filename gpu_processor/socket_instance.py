# socket_instance.py
from flask_socketio import SocketIO
from services.socket_manager import SocketManager

class SocketManagerInstance:
    _instance = None
    _redis_url = None
    _socketio = None

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            if cls._redis_url is None:
                raise RuntimeError("Redis URL not set. Call set_redis_url first.")
            if cls._socketio is None:
                cls._socketio = SocketIO(message_queue=cls._redis_url)

            cls._instance = SocketManager(cls._socketio)
        return cls._instance

    @classmethod
    def set_instance(cls, instance):
        cls._instance = instance

    @classmethod
    def set_redis_url(cls, redis_url):
        cls._redis_url = redis_url

# Create a global instance
socket_instance = SocketManagerInstance()