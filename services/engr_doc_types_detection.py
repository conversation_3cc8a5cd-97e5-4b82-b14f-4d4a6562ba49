import json
import os
import asyncio
import re
from services.claude_ai_service import ClaudeSer<PERSON>
from services.faiss_embedding import FaissEmbedding
from services.fast_apis_service import FastAPIs
from services.groq_ai_service import GroqService
from pydantic import BaseModel, Field, field_validator
from typing import List, Union

class EngrDocTypesDetection:
    def __init__(self):

        self.claude_client = ClaudeService()
        self.groq_client = GroqService()
        self.fast_apis = FastAPIs()
        self.discipline_codes = {
            "AR": "Architectural",
            "CV": "Civil & Structural",
            "EL": "Electrical",
            "CI": "Control & Instrumentation",
            "ME": "Mechanical & Piping",
            "MT": "Material & Corrosion",
            "MD": "Multi-discipline",
            "OR": "Operations Readiness & Assurance",
            "PR": "Process",
            "HS": "HSE",
            "RE": "Reservoir Simulation",
            "GM": "Geo-Modeling",
            "WD": "Well Delivery",
            "GT": "Geo-Mechanics and Testing",
            "SV": "Surveying / Topography",
            "QC": "Quality Assurance and Control",
            "PM": "Project Management"
        }

        # Get the path to the parent directory
        self.parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        print('parent_dir:', self.parent_dir)

        # Load environment variables from env.json file in the parent directory
        env_file_path = os.path.join(self.parent_dir, 'env.json')
        print('env_file_path:', env_file_path)
        # Load environment variables from env.json file
        with open(env_file_path, 'r') as f:
            env_data = json.load(f)
        
        self.env_data = env_data
        
        self.specs_dir_path = os.path.join(self.parent_dir, env_data.get("SPECS_DIR", ""))
        self.engr_doc_keywords_path = os.path.join(self.specs_dir_path, env_data.get("ENGR_DOCS", ""))

    
    def detect_document_type_from_number(self, document_number):
        """Detects the document type based on the provided document number."""
        try:
            # Split the document number by '-' and check each part for discipline codes
            parts = document_number.split('-')
            for part in parts:
                # Check if any part matches a discipline code
                for code, discipline in self.discipline_codes.items():
                    if code.lower() in part.lower():
                        return discipline
            return "Unknown"
        except Exception as e:
            # Handle any unexpected errors gracefully
            print(f"An error occurred while detecting document type: {e}")
            return "Unknown"
        
    async def detect_document_category(self, file, discipline_code, category):
        print("i enter detect doc category", file, discipline_code, category)
        file_path = os.path.join(self.specs_dir_path, file)
        discipline = {'discipline':self.discipline_codes[discipline_code], 'code':discipline_code}
        print('discipline: ', discipline)
    
        return self.extract_keywords_and_subkeywords(discipline, category)

    async def format_criteria_text(self, question_text):
        try:
            # Check if the input text is empty
            if not question_text or not question_text.strip():
                print("Error: The input question_text is empty.")
                return None
            prompt_template = """
                You are an **EPC (Engineering, Procurement, and Construction) Project Engineer** assistant working for **Worley** or **Fulor**.
                Your task is to look into the following areas of focus, and extract them into individual areas of focus while maintaining their exact wording and meaning.  
                Return in the below format.

                <INSTRUCTIONS>  
                    - Identify and extract **all** key area of focus from the given text.  
                    - Do **not** paraphrase, modify, or interpret the extracted key areas and return them exactly as written.  
                    - Ensure strict adherence to the **output format** provided. 
                    - Sometimes it can be a simple minimal text.
                </INSTRUCTIONS>

                <EXAMPLE>
                    "Issued for each type of valves (ball, butterfly, gate, globe, knife, etc).
                    Contains specification of the material
                    Painting requirements
                    Inspection requirements
                    Applicable project specification, company standards and international codes"
                <EXAMPLE>
                <OUTPUT FORMAT>
                    <Question>Issued for each type of valves (ball, butterfly, gate, globe, knife, etc).</Question>
                    <Question>Contains specification of the material</Question>
                    <Question>Painting requirements</Question>
                    <Question>Inspection requirements</Question>
                    <Question>Applicable project specification, company standards and international codes</Question>
                <OUTPUT FORMAT>

                <EXAMPLE> 
                    List of the cables to be supplied and installed for the project.
                    Unique number of each cable.
                    Size of the cable
                    Number of conductors
                    Material of the cable (external sheath, conductor, armour, shield)
                    From and To equipment where the cable is connected (both ends)
                    Length of the cable
                    Voltage of the cable
                <EXAMPLE>
                <OUTPUT FORMAT>
                    <Question>List of the cables to be supplied and installed for the project.</Question>
                    <Question>Unique number of each cable.</Question>
                    <Question>Size of the cable</Question>
                    <Question>Number of conductors</Question>
                    <Question>Material of the cable (external sheath, conductor, armour, shield)</Question>
                    <Question>From and To equipment where the cable is connected (both ends)</Question>
                    <Question>Length of the cable</Question>
                    <Question>Voltage of the cable</Question>
                <OUTPUT FORMAT>

                NOTE: Strictly follow the output format.
                NOTE: Do not paraphrase or change the extracted text.
                NOTE: Just extract queries from the RFIs text, but don't change meaning.

                Here is the input text:
                <QUERY>
                    {question_text}
                </QUERY>
            """

            prompt = prompt_template.format(question_text=question_text)
            formatted_prompt = [{"role": "user", "content": prompt}]

            class FormatText(BaseModel):
                Questions: List

            
            print('about to run completion....')
            
            completion = await self.claude_client.generate_message_agent_sonnet_v2(FormatText, formatted_prompt, 0.01)    
            print('this is completion: ', completion)    
            if completion and hasattr(completion.content[0], 'input'):
                response = completion.content[0].input
                master_response = FormatText(**response)
            
                print('received response from claude formatted...')
            
                new_response = {
                    'Questions': master_response.Questions,
                }

                print('new response by claude: ', json.dumps(new_response))
                return new_response['Questions']
            
            print("Warning: Completion returned an unexpected format or empty response.")
            return None  
        except Exception as e:
            print(f"Error occurred: {str(e)}")
            return None
    
    async def extract_criteria_info(self, criteria_data, data_extracted, count):
        """Extract criteria information based on discipline and deliverable."""
        
        deliverable = data_extracted['document_deliverable'][count -1]
        
        for item in criteria_data:
            # print('i enter criteria data...')
            print(item.get("Deliverable/activity", "").lower())
            # Check if this is the matching deliverable
            if (item.get("Deliverable/activity", "").strip().lower() == deliverable.strip().lower()):
                # print('this: ', item)
                # Extract the key information
                key_info = item.get("Key information to focus on when reviewing the document", "")
                if key_info:
                    data_extracted['document_criteria'][count - 1] = key_info
        
        print('done here in extract criteria')
                    
    async def detect_document_category_from_document_pages(self, requirement_id):
        try:
            self.faiss_processor = FaissEmbedding()

            # Load disciplines (consider caching this)
            specs_dir_path = os.path.join(self.parent_dir, self.env_data.get("SPECS_DIR", ""), 'Aienergy.discipline.json')
            specs_criteria_path = os.path.join(self.parent_dir, self.env_data.get("SPECS_DIR", ""), 'Aienergy.discipline.criteria.json')

            with open(specs_dir_path, 'r', encoding='utf-8') as file:
                disciplines = json.load(file)
            
            with open(specs_criteria_path, 'r', encoding='utf-8') as file:
                criteria_data = json.load(file)

            result = await self.faiss_processor._gpu_search_top_3(requirement_id)

            # result = [result[0], result[-1]]

            # print('result len is: ', len(result))
            # print(result)
            data_extracted = {
                "document_number": [None] * len(result),
                "document_discipline": [None] * len(result),
                "document_deliverable": [None] * len(result),
                "document_criteria": [None] * len(result),
                "document_formatted_criteria": [None] * len(result)
            }
            
            async def process_document(data, count):
                try:
                    # Validate data structure
                    if not isinstance(data, dict) or 'content' not in data:
                        print(f"Invalid data structure for document {count}:")
                        # print(f"Data: {data}")
                        return None
                    
                    if not data['content']:
                        print(f"Empty content for document {count}")
                        return None

                    print(f"Content length for document {count}: {len(data['content'])}")
                
                    document_info = await self.Agent_document_info_extraction(data['content'], disciplines)
                    print(f'Document {count} info: {document_info}')
                    
                    await self.extract_document_info_v2(document_info, data_extracted, count)
                    print(f'Completed v2 extract for document {count}')
                    
                    await self.extract_criteria_info(criteria_data, data_extracted, count)
                    print(f'Completed criteria info extraction for document {count}')

                    print(document_info) 
                    
                    return document_info
                except Exception as e:
                    print(f"Error processing document {count}: {str(e)}")
                    return None

            tasks = [
                process_document(data, count) 
                for count, data in enumerate(result, start=1)
            ]
            
            await asyncio.gather(*tasks, return_exceptions=True)

            print('done waiting.....')

            print('data_extracted: ', data_extracted)
            data_extracted['document_formatted_criteria'] = await self.format_criteria_text("\n".join(data_extracted['document_criteria']))

            print('new data extracted: ', data_extracted)
            return data_extracted

        except Exception as e:
            print(f"Error in document category detection: {str(e)}")
            raise
    
    async def Agent_document_info_extraction(self, content, disciplines):
        prompt = '''
            <ENGR_DOC_DISCIPLINES>
                {disciplines}
            </ENGR_DOC_DISCIPLINES>

            <DOCUMENT>
                {content}
            </DOCUMENT>

            <TASK>
                You are an **EPC (Engineering, Procurement, and Construction) Project Engineer** assistant working for **Worley** or **Fulor**. Your task is to:
                1.) Identify the correct engineering discipline and deliverable based on the list provided in <ENGR_DOC_DISCIPLINES>.
                2.) Analyze the <DOCUMENT> content and match it to the most appropriate discipline and deliverable from the list.
                3.) Strictly follow the format in <OUTPUT_FORMAT>. If you cannot confidently determine the discipline or deliverable, return **"[INSUFFICIENT DATA]"**.
            </TASK>

            <EXAMPLE>
                <CONTENT>
                    Document Number: P4258-000-PHL-PR-001  
                    **Emergency Shut Down (ESD) Philosophy for Process Facility**  
                    ---  
                    ### 1. **Introduction**  
                    This document outlines the process, requirements, and sequences for Emergency Shut Down (ESD) operations in the facility.  
                    The ESD system ensures the safety of personnel, equipment, and the environment by providing a structured shutdown response.  
                    ---  
                    ### 2. **ESD Levels According to Company Philosophy**  
                    - **Level 1: Normal ESD (Automatic Shutdown)**  
                    Initiated by process parameters exceeding predefined limits (e.g., pressure, temperature, flow).  
                </CONTENT>
                <OUTPUT>
                    <DOCUMENT_NUMBER>P4258-000-PHL-PR-001</DOCUMENT_NUMBER>  
                    <DISCIPLINE>Process</DISCIPLINE>  
                    <DELIVERABLE>ESD (Emergency Shut Down) Philosophy</DELIVERABLE>
                </OUTPUT>
            </EXAMPLE>

            <EXAMPLE>
                <CONTENT>
                    Document Number: P4258-000-REP-ME-001  
                    ### **Performance Curve for Rotating Equipment**  
                    #### **Project Name: [Project 1 Facility Plant]**  
                    #### **1. Predicted Performance Curve(s) of the Equipment Supplied by the Vendor**  
                    The following performance curves show the predicted operating characteristics of rotating equipment.  
                    These curves are based on mechanical and process datasheets.  
                    ---  
                    ##### **Pump Performance Curve (Example for Condensate Pump)**  
                    - **Pump Curve Type**: Single Stage Centrifugal  
                    - **Nominal Capacity**: 500 m³/h  
                    - **Design Head**: 50 m  
                    - **Rated Power**: 100 kW  
                    - **Efficiency**: 85%  
                </CONTENT>
                <OUTPUT>
                    <DOCUMENT_NUMBER>P4258-000-REP-ME-001</DOCUMENT_NUMBER>  
                    <DISCIPLINE>Mechanical</DISCIPLINE>  
                    <DELIVERABLE>Performance Curve Report for Rotating Equipment</DELIVERABLE>
                </OUTPUT>
            </EXAMPLE>

            <IMPORTANT>
                - Never request the `<DOCUMENT>` content again. Assume it is always provided.
                - If the document does not contain a document number, return only **"[NO DOCUMENT NUMBER]"** (nothing else).
                - If the discipline or deliverable is unclear, return only **"[INSUFFICIENT DATA]"**.
                - Match the discipline and deliverable **exactly** as listed in `<ENGR_DOC_DISCIPLINES>`. No guessing.
                - Do not provide explanations, only return the structured output.
            </IMPORTANT>

            <OUTPUT_FORMAT>
                <DOCUMENT_NUMBER>[existing document number or "[NO DOCUMENT NUMBER]"]</DOCUMENT_NUMBER>  
                <DISCIPLINE>[matched discipline or "[INSUFFICIENT DATA]"]</DISCIPLINE>  
                <DELIVERABLE>[matched deliverable or "[INSUFFICIENT DATA]"]</DELIVERABLE>
            </OUTPUT_FORMAT>
        '''
        formatted_prompt = prompt.format(content=content, disciplines=disciplines)
        # print('this is formatted response: ', formatted_prompt)
        try:
            completion = self.fast_apis.generate_completion(
                [{"role": "user", "content": formatted_prompt}],
                'groq',
                'llama3-70b-8192'
            )
            if completion:
                return completion

        except Exception as e:
            print(e)
            return None
        
    def extract_keywords_and_subkeywords(self, discipline, category):
        print("doc keywords ....")
        with open(self.engr_doc_keywords_path, 'r') as f:
            engr_doc_data = json.load(f)
        
        discipline_data = engr_doc_data[discipline['discipline']]
  
        return discipline_data
    
    async def extract_document_info_v2(self, document_string, log, count):
        # Regular expressions to match document title and number, with optional whitespace
        number_match = re.search(r'<DOCUMENT_NUMBER>\s*(.*?)\s*</DOCUMENT_NUMBER>', document_string, re.DOTALL)
        discipline_match = re.search(r'<DISCIPLINE>\s*(.*?)\s*</DISCIPLINE>', document_string, re.DOTALL)
        deliverable_match = re.search(r'<DELIVERABLE>\s*(.*?)\s*</DELIVERABLE>', document_string, re.DOTALL)
        
        # Extracting the values and stripping whitespace
        doc_number = number_match.group(1).strip() if number_match else None
        doc_discipline = discipline_match.group(1).strip() if discipline_match else None
        doc_deliverable = deliverable_match.group(1).strip() if deliverable_match else None

        # print([doc_number, doc_discipline, doc_deliverable])
        
        log['document_number'][count-1] = doc_number
        log['document_discipline'][count-1] = doc_discipline
        log['document_deliverable'][count-1] = doc_deliverable

    def extract_document_info(self, document_string, log):
        # Regular expressions to match document title and number
        title_match = re.search(r'<DOCUMENT_TITLE>(.*?)</DOCUMENT_TITLE>', document_string)
        number_match = re.search(r'<DOCUMENT_NUMBER>(.*?)</DOCUMENT_NUMBER>', document_string)
        
        # Extracting the values
        doc_title = f"<DOCUMENT_TITLE>{title_match.group(1)}</DOCUMENT_TITLE>" if title_match else None
        doc_number = f"<DOCUMENT_NUMBER>{number_match.group(1)}</DOCUMENT_NUMBER>" if number_match else None
        
        log['document_number'] = doc_number
        log['document_title'] = doc_title
    
    async def get_claude_to_detect_document_details(self, document, log):
        steps = 3
        messages = [
            {
                "role": "user",
                "content": f'''
                    <UPLOADED_DOCUMENT>
                        {document}
                    </UPLOADED_DOCUMENT>
                    <ROLE>
                        1.) You are an Industrial engineer, a helpful assistant to detect key document information from the above document.
                    </ROLE>
                    <INSTRUCTION>
                        - Look into the uploaded document and extract the project document number.
                        - Look into the uploaded document and extract the project document title.
                        - The report should strictly follow the below format.
                        <EXAMPLE>
                            <DOCUMENT_NUMBER>34-06-12345-Specifications API Pump - rev0</DOCUMENT_NUMBER>
                            <DOCUMENT_TITLE>Specifications for API 610 Centrifugal Pump</DOCUMENT_TITLE>
                        </EXAMPLE>
                        <EXAMPLE>
                            <DOCUMENT_NUMBER>Project1-FEED CONTRACTOR-FACILITY-0000-PR-LST-0001 Rev 006</DOCUMENT_NUMBER>
                            <DOCUMENT_TITLE>Project1-FEED CONTRACTOR--MUL-E000-PR-LST-0001</DOCUMENT_TITLE>
                        </EXAMPLE>
                        <EXAMPLE>
                            <DOCUMENT_NUMBER>22-08-45678-Piping Layout - rev1</DOCUMENT_NUMBER>
                            <DOCUMENT_TITLE>Piping Layout for Chemical Plant</DOCUMENT_TITLE>
                        </EXAMPLE>
                    </INSTRUCTION> 
                    '''
            }
        ]
        while steps > 0:
            steps -= 1
            try:
                completion = await self.claude_client.generate_message(
                    messages=messages,
                    temperature=0.001,
                    model="Claude 3 Sonnet"
                )
                if completion and hasattr(completion.content[0], 'text'):
                    response = completion.content[0].text
                    token_count_in = completion.usage.input_tokens
                    token_count_out = completion.usage.output_tokens

                    self.extract_document_info(response, log)

                    log['input_token'] += token_count_in
                    log['output_token'] += token_count_out

                    return
                else:
                    raise ValueError("Invalid response received")
            except Exception as e:
                print(e)
                await asyncio.sleep(10)

        
    
# async def main():
#     doc_detector = EngrDocTypesDetection()
#     file = 'test.docx'
#     discipline_code = 'PR'
#     category = 'Heat and Material Balance'
#     document_category = await doc_detector.detect_document_category(file, discipline_code, category)
#     print(f'Document category: {document_category}')


# if __name__ == "__main__":
#     asyncio.run(main())
