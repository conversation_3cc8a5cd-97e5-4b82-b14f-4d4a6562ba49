import json
from typing import Dict, List, Any

def generate_technical_adjudication(technical_data: Dict, output_file: str = "technical_adjudication_report.html"):
    """
    Generate an HTML report of the technical adjudication results in a simple table format
    
    Args:
        technical_data: Dictionary containing technical bid data
        output_file: Path to save the HTML report
    """
    html_content = []
    html_content.append("""
    <html>
    <head>
        <title>Attachment 1: Technical Adjudication</title>
    </head>
    <body>
        <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; background: white; border: 1px solid #ddd;">
            <h2 style="color: #2c5aa0; text-align: center; border-bottom: 2px solid #2c5aa0; padding-bottom: 10px;">
                Attachment 1: Technical Adjudication
            </h2>
            <table border="1" style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                <tr style="background-color: #f8f9fa;">
                    <th style="padding: 8px; border: 1px solid #ddd;">Item No</th>
                    <th style="padding: 8px; border: 1px solid #ddd;">Description</th>
    """)
    
    # Get all bidders first
    all_bidders = set()
    for bid_id, equipment_results_list in technical_data.items():
        for equipment in equipment_results_list:
            all_bidders.add(equipment["bidder_name"])
    
    # Add bidder columns
    for bidder in sorted(all_bidders):
        html_content.append(f'<th style="padding: 8px; border: 1px solid #ddd;">{bidder}</th>')
    
    html_content.append('<th style="padding: 8px; border: 1px solid #ddd;">Comments</th></tr>')
    
    # Group results by equipment
    equipment_results = {}
    for bid_id, equipment_results_list in technical_data.items():
        for equipment in equipment_results_list:
            equipment_name = equipment["equipment"]
            if equipment_name not in equipment_results:
                equipment_results[equipment_name] = []
            # Access the first item in the data list which contains the categories
            equipment_data = equipment["data"][0]
            equipment_results[equipment_name].append({
                "bidder_name": equipment["bidder_name"],
                "categories": equipment_data["categories"]
            })
    
    # Generate table rows for each equipment
    for equipment_name, bidders in equipment_results.items():
        # Add equipment header
        html_content.append(f"""
            <tr>
                <td colspan="{len(all_bidders) + 3}" style="padding: 8px; border: 1px solid #ddd; background-color: #f8f9fa; text-align: center; font-weight: bold; color: #2c5aa0;">{equipment_name}</td>
            </tr>
        """)
        
        # Get all categories from the first bidder
        categories = bidders[0]["categories"]
        
        for category in categories:
            category_name = category["name"]
            # Add category header
            html_content.append(f"""
                <tr>
                    <td colspan="{len(all_bidders) + 3}" style="padding: 8px; border: 1px solid #ddd; background-color: #f8f9fa; font-weight: bold;">Category: {category_name}</td>
                </tr>
            """)
            
            # Get all items from the first bidder's category
            items = category["items"]
            for item in items:
                html_content.append(f"""
                    <tr>
                        <td style="padding: 8px; border: 1px solid #ddd;">{item["name"]}</td>
                        <td style="padding: 8px; border: 1px solid #ddd;">{item["name"]}</td>
                """)
                
                # Add values from each bidder
                for bidder in sorted(all_bidders):
                    # Find matching item in bidder's category
                    bidder_data = next((b for b in bidders if b["bidder_name"] == bidder), None)
                    if bidder_data:
                        bidder_category = next((c for c in bidder_data["categories"] if c["name"] == category_name), None)
                        bidder_item = next((i for i in bidder_category["items"] if i["name"] == item["name"]), None) if bidder_category else None
                        value = bidder_item["answer"] if bidder_item else "N/A"
                    else:
                        value = "N/A"
                    html_content.append(f'<td style="padding: 8px; border: 1px solid #ddd;">{value}</td>')
                
                # Add empty comments column
                html_content.append('<td style="padding: 8px; border: 1px solid #ddd;"></td></tr>')
    
    html_content.append("""
            </table>
        </div>
    </body>
    </html>
    """)
    
    return html_content

def main():
    

    data={'e107d81a-3474-4f5d-ac6f-f95c18072cd1': [{'equipment': 'Cyclone Cluster ', 'status': 'success', 'data': [{'equipment_name': 'Cyclone Cluster ', 'categories': [{'name': 'Automation', 'items': [{'name': 'Junction Box and Local Panel', 'unit': '', 'answer': 'N/A'}, {'name': 'Pressure Transmitter', 'unit': '', 'answer': 'N/A'}, {'name': 'No of Input Signals & Type', 'unit': '', 'answer': 'N/A'}, {'name': 'No of Instruments Included', 'unit': '', 'answer': 'N/A'}, {'name': 'No of Output Signals & Type', 'unit': '', 'answer': 'N/A'}, {'name': 'Hart protocole version 5 or higher ', 'unit': '', 'answer': 'N/A'}]}, {'name': 'Shipping & Handling', 'items': [{'name': 'Overall dimension:', 'unit': 'mxmxm', 'answer': 'N/A'}, {'name': 'Max. weight to be lifted (erection):', 'unit': 'kg', 'answer': 'N/A'}, {'name': 'Max. weight to be lifted (maintenance):', 'unit': 'kg', 'answer': 'N/A'}, {'name': 'Dimension of largest part (maintenance):', 'unit': 'mxmxm', 'answer': 'N/A'}, {'name': 'Number of packages:', 'unit': '', 'answer': 'N/A'}, {'name': 'Factory Location:', 'unit': '', 'answer': 'N/A'}, {'name': 'Total Volume (m3)', 'unit': '', 'answer': 'N/A'}, {'name': 'Dimension of largest part (erection):', 'unit': 'mxmxm', 'answer': 'N/A'}, {'name': 'Total weight of equipment:', 'unit': 'kg', 'answer': 'N/A'}]}, {'name': 'Utilities', 'items': [{'name': 'Compressed air Operating Pressure', 'unit': 'kPa', 'answer': 'N/A'}, {'name': 'Water Operating Pressure', 'unit': 'kPa', 'answer': 'N/A'}, {'name': 'Instrument Air Flow', 'unit': 'Nm³/min', 'answer': 'N/A'}, {'name': 'Compressed Air Flow', 'unit': 'Nm³/min', 'answer': 'N/A'}, {'name': 'Instrument air Operating Pressure', 'unit': 'kPa', 'answer': 'N/A'}, {'name': 'Water Flow', 'unit': 'm³/h', 'answer': 'N/A'}]}, {'name': 'Mechanical', 'items': [{'name': 'Cone Angle', 'unit': 'degree', 'answer': 'N/A'}, {'name': 'Capacity (design)', 'unit': 'tph', 'answer': 'N/A'}, {'name': 'Size of Apex', 'unit': 'mm', 'answer': 'N/A'}, {'name': 'No. of standby cyclones:', 'unit': '', 'answer': 'N/A'}, {'name': 'Size of Vortex Finder', 'unit': 'mm', 'answer': 'N/A'}, {'name': 'Diameter of Individual Cyclone:', 'unit': 'mm', 'answer': 'N/A'}, {'name': 'Operating Pressure', 'unit': 'kPa', 'answer': 'N/A'}, {'name': 'No. of Operating cyclones:', 'unit': '', 'answer': 'N/A'}]}, {'name': 'Testing Data', 'items': [{'name': 'Dye penetrant', 'unit': '', 'answer': 'N/A'}, {'name': 'Inspection required for casting', 'unit': '', 'answer': 'N/A'}, {'name': 'Casting repair procedure approval', 'unit': '', 'answer': 'N/A'}, {'name': 'Shop inspection', 'unit': '', 'answer': 'N/A'}, {'name': 'Dismantle and inspect after test', 'unit': '', 'answer': 'N/A'}, {'name': 'Alignment report', 'unit': '', 'answer': 'N/A'}, {'name': 'Radiographic', 'unit': '', 'answer': 'N/A'}, {'name': 'Inspection required for nozzle welds', 'unit': '', 'answer': 'N/A'}, {'name': 'Magnetic particles', 'unit': '', 'answer': 'N/A'}, {'name': 'Vibration report', 'unit': '', 'answer': 'N/A'}, {'name': 'Ultrasonic', 'unit': '', 'answer': 'N/A'}]}, {'name': 'Painting', 'items': [{'name': 'Top Coat Color', 'unit': 'dry μm', 'answer': 'N/A'}, {'name': 'Paint type', 'unit': '', 'answer': 'N/A'}, {'name': 'Top coat', 'unit': 'dry μm', 'answer': 'N/A'}, {'name': 'Surface preparation', 'unit': '', 'answer': 'N/A'}, {'name': 'Primer', 'unit': 'dry μm', 'answer': 'N/A'}, {'name': 'Coat', 'unit': '', 'answer': 'N/A'}, {'name': 'Paint manufacturer', 'unit': '', 'answer': 'N/A'}, {'name': 'Base coat', 'unit': 'dry μm', 'answer': 'N/A'}]}, {'name': 'Liner', 'items': [{'name': 'Cyclone - lining thickness', 'unit': 'mm', 'answer': 'N/A'}, {'name': 'Overflow pipe- lining material', 'unit': '', 'answer': 'N/A'}, {'name': 'Underflow launder - lining thickness', 'unit': 'mm', 'answer': 'N/A'}, {'name': 'Feed distributor - lining material', 'unit': '', 'answer': 'N/A'}, {'name': 'Feed distributor - lining thickness', 'unit': 'mm', 'answer': 'N/A'}, {'name': 'Cyclone feed pipe - lining material', 'unit': '', 'answer': 'N/A'}, {'name': 'Underflow launder - lining material', 'unit': '', 'answer': 'N/A'}, {'name': 'Overflow launder - lining material', 'unit': '', 'answer': 'N/A'}, {'name': 'Cyclone - lining material', 'unit': '', 'answer': 'N/A'}, {'name': 'Cyclone feed pipe - lining thickness', 'unit': 'mm', 'answer': 'N/A'}, {'name': 'Overflow pipe- lining thickness', 'unit': 'mm', 'answer': 'N/A'}, {'name': 'Overflow launder - lining thickness', 'unit': 'mm', 'answer': 'N/A'}]}, {'name': 'Piping', 'items': [{'name': 'Underflow discharge pipe diameter ', 'unit': 'mm', 'answer': 'N/A'}, {'name': 'Knife Gate Valve - Diameter ', 'unit': 'mm', 'answer': 'N/A'}, {'name': 'Flange Specifications :', 'unit': '', 'answer': 'N/A'}, {'name': 'Type of piping connections', 'unit': '', 'answer': 'N/A'}, {'name': 'Pressure Gauge ', 'unit': '', 'answer': 'N/A'}, {'name': 'Overflow discharge pipe diameter ', 'unit': 'mm', 'answer': 'N/A'}, {'name': 'Feed distributor pipe diameter', 'unit': 'mm', 'answer': 'N/A'}, {'name': 'Knife Gate Valve - Model', 'unit': '', 'answer': 'N/A'}]}, {'name': 'Description of Equipment', 'items': [{'name': 'Quotation #', 'unit': '', 'answer': 'N/A'}, {'name': 'Vendor Name', 'unit': '', 'answer': 'N/A'}, {'name': 'Model', 'unit': '', 'answer': 'N/A'}, {'name': 'Service', 'unit': '', 'answer': 'N/A'}, {'name': '% of Components to be Assembled on Site', 'unit': '', 'answer': 'N/A'}, {'name': 'Number of cyclones', 'unit': '', 'answer': 'N/A'}, {'name': 'Number of unit(s)', 'unit': '', 'answer': 'N/A'}, {'name': 'Equipment type', 'unit': '', 'answer': 'N/A'}, {'name': 'Warranty (commissioning/shipping)', 'unit': '', 'answer': 'N/A'}]}]}], 'bid_id': 'e107d81a-3474-4f5d-ac6f-f95c18072cd1', 'bidder_name': 'Mo group'}], '6aa67866-913e-42a8-97cc-b0b7d662241e': [{'equipment': 'Cyclone Cluster ', 'status': 'success', 'data': [{'equipment_name': 'Cyclone Cluster ', 'categories': [{'name': 'Utilities', 'items': [{'name': 'Compressed air Operating Pressure', 'unit': 'kPa', 'answer': '690 kPag max'}, {'name': 'Water Operating Pressure', 'unit': 'kPa', 'answer': '1030 kPag max'}, {'name': 'Instrument air Operating Pressure', 'unit': 'kPa', 'answer': '690 kPag'}, {'name': 'Instrument Air Flow', 'unit': 'Nm³/min', 'answer': 'N/A'}, {'name': 'Compressed Air Flow', 'unit': 'Nm³/min', 'answer': 'N/A'}, {'name': 'Water Flow', 'unit': 'm³/h', 'answer': 'N/A'}]}, {'name': 'Testing Data', 'items': [{'name': 'Dye penetrant', 'unit': '', 'answer': 'Dye penetrant'}, {'name': 'Inspection required for casting', 'unit': '', 'answer': 'Inspection required for casting'}, {'name': 'Casting repair procedure approval', 'unit': '', 'answer': 'Casting repair procedure approval'}, {'name': 'Shop inspection', 'unit': '', 'answer': 'Shop inspection'}, {'name': 'Alignment report', 'unit': '', 'answer': 'Alignment report'}, {'name': 'Radiographic', 'unit': '', 'answer': 'Radiographic'}, {'name': 'Inspection required for nozzle welds', 'unit': '', 'answer': 'Inspection required for nozzle welds'}, {'name': 'Magnetic particles', 'unit': '', 'answer': 'Magnetic particles'}, {'name': 'Vibration report', 'unit': '', 'answer': 'Vibration report'}, {'name': 'Ultrasonic', 'unit': '', 'answer': 'Ultrasonic'}, {'name': 'Dismantle and inspect after test', 'unit': '', 'answer': 'N/A'}]}, {'name': 'Painting', 'items': [{'name': 'Top Coat Color', 'unit': 'dry μm', 'answer': 'Metso:Outotec Gray'}, {'name': 'Paint type', 'unit': '', 'answer': 'Supplier Standard'}, {'name': 'Top coat', 'unit': 'dry μm', 'answer': '150'}, {'name': 'Surface preparation', 'unit': '', 'answer': 'Abrasive Blast to Sa 2.5 (ISO 8501-1) or Equivalent'}, {'name': 'Primer', 'unit': 'dry μm', 'answer': '75'}, {'name': 'Base coat', 'unit': 'dry μm', 'answer': '150'}, {'name': 'Coat', 'unit': '', 'answer': 'N/A'}, {'name': 'Paint manufacturer', 'unit': '', 'answer': 'N/A'}]}, {'name': 'Automation', 'items': [{'name': 'Junction Box and Local Panel', 'unit': '', 'answer': 'N/A'}, {'name': 'Pressure Transmitter', 'unit': '', 'answer': 'N/A'}, {'name': 'No of Input Signals & Type', 'unit': '', 'answer': 'N/A'}, {'name': 'No of Instruments Included', 'unit': '', 'answer': 'N/A'}, {'name': 'No of Output Signals & Type', 'unit': '', 'answer': 'N/A'}, {'name': 'Hart protocole version 5 or higher ', 'unit': '', 'answer': 'N/A'}]}, {'name': 'Shipping & Handling', 'items': [{'name': 'Overall dimension:', 'unit': 'mxmxm', 'answer': 'N/A'}, {'name': 'Max. weight to be lifted (erection):', 'unit': 'kg', 'answer': 'N/A'}, {'name': 'Max. weight to be lifted (maintenance):', 'unit': 'kg', 'answer': 'N/A'}, {'name': 'Dimension of largest part (maintenance):', 'unit': 'mxmxm', 'answer': 'N/A'}, {'name': 'Number of packages:', 'unit': '', 'answer': 'N/A'}, {'name': 'Factory Location:', 'unit': '', 'answer': 'N/A'}, {'name': 'Total Volume (m3)', 'unit': '', 'answer': 'N/A'}, {'name': 'Dimension of largest part (erection):', 'unit': 'mxmxm', 'answer': 'N/A'}, {'name': 'Total weight of equipment:', 'unit': 'kg', 'answer': 'N/A'}]}, {'name': 'Mechanical', 'items': [{'name': 'Cone Angle', 'unit': 'degree', 'answer': 'N/A'}, {'name': 'Capacity (design)', 'unit': 'tph', 'answer': 'N/A'}, {'name': 'Size of Apex', 'unit': 'mm', 'answer': 'N/A'}, {'name': 'No. of standby cyclones:', 'unit': '', 'answer': 'N/A'}, {'name': 'Size of Vortex Finder', 'unit': 'mm', 'answer': 'N/A'}, {'name': 'Diameter of Individual Cyclone:', 'unit': 'mm', 'answer': 'N/A'}, {'name': 'Operating Pressure', 'unit': 'kPa', 'answer': 'N/A'}, {'name': 'No. of Operating cyclones:', 'unit': '', 'answer': 'N/A'}]}, {'name': 'Liner', 'items': [{'name': 'Cyclone - lining thickness', 'unit': 'mm', 'answer': 'N/A'}, {'name': 'Overflow pipe- lining material', 'unit': '', 'answer': 'N/A'}, {'name': 'Underflow launder - lining thickness', 'unit': 'mm', 'answer': 'N/A'}, {'name': 'Feed distributor - lining material', 'unit': '', 'answer': 'N/A'}, {'name': 'Feed distributor - lining thickness', 'unit': 'mm', 'answer': 'N/A'}, {'name': 'Cyclone feed pipe - lining material', 'unit': '', 'answer': 'N/A'}, {'name': 'Underflow launder - lining material', 'unit': '', 'answer': 'N/A'}, {'name': 'Overflow launder - lining material', 'unit': '', 'answer': 'N/A'}, {'name': 'Cyclone - lining material', 'unit': '', 'answer': 'N/A'}, {'name': 'Cyclone feed pipe - lining thickness', 'unit': 'mm', 'answer': 'N/A'}, {'name': 'Overflow pipe- lining thickness', 'unit': 'mm', 'answer': 'N/A'}, {'name': 'Overflow launder - lining thickness', 'unit': 'mm', 'answer': 'N/A'}]}, {'name': 'Piping', 'items': [{'name': 'Underflow discharge pipe diameter ', 'unit': 'mm', 'answer': 'N/A'}, {'name': 'Knife Gate Valve - Diameter ', 'unit': 'mm', 'answer': 'N/A'}, {'name': 'Flange Specifications :', 'unit': '', 'answer': 'N/A'}, {'name': 'Type of piping connections', 'unit': '', 'answer': 'N/A'}, {'name': 'Pressure Gauge ', 'unit': '', 'answer': 'N/A'}, {'name': 'Overflow discharge pipe diameter ', 'unit': 'mm', 'answer': 'N/A'}, {'name': 'Feed distributor pipe diameter', 'unit': 'mm', 'answer': 'N/A'}, {'name': 'Knife Gate Valve - Model', 'unit': '', 'answer': 'N/A'}]}, {'name': 'Description of Equipment', 'items': [{'name': 'Quotation #', 'unit': '', 'answer': 'N/A'}, {'name': 'Vendor Name', 'unit': '', 'answer': 'N/A'}, {'name': 'Model', 'unit': '', 'answer': 'N/A'}, {'name': 'Service', 'unit': '', 'answer': 'N/A'}, {'name': '% of Components to be Assembled on Site', 'unit': '', 'answer': 'N/A'}, {'name': 'Number of cyclones', 'unit': '', 'answer': 'N/A'}, {'name': 'Number of unit(s)', 'unit': '', 'answer': 'N/A'}, {'name': 'Equipment type', 'unit': '', 'answer': 'N/A'}, {'name': 'Warranty (commissioning/shipping)', 'unit': '', 'answer': 'N/A'}]}]}], 'bid_id': '6aa67866-913e-42a8-97cc-b0b7d662241e', 'bidder_name': 'Tysen'}]}
    # Generate report
    generate_technical_adjudication(technical_data=data)

if __name__ == "__main__":
    main()