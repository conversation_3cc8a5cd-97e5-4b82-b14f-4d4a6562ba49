import pandas as pd
import re
from bs4 import BeautifulSoup
import json
from services.claude_ai_service import ClaudeService
from services.prompt_loader import PromptLoader

# Initialize clients
claude_client = ClaudeService(claude_keys=['CLAUDE_API_CBP', 'CLAUDE_API_CBP'],default_key="CLAUDE_API_CBP")

class ComparisonTableCreator:
    """
    Class to handle the creation of bidder comparison tables
    """
    def __init__(self, bidders_data, criteria_weights,scores_explanation_formula):
        """
        Initialize with bidders data and criteria weights
        
        Args:
            bidders_data (list): List of dictionaries containing bidder information
            criteria_weights (list): List of dictionaries mapping criteria to weights
        """
        self.bidders_data = bidders_data
        self.criteria_weights = criteria_weights
        self.table_data = []
        self.special_criteria_data = []
        self.scores_explanation_formula = scores_explanation_formula #explanation for special criteria that uses formula
        
    
    def create_initial_comparison_table(self):
        """Create a comparison table from bidder data and criteria weights"""
        # Process each bidder's data
        for bidder_data in self.bidders_data:
            bidder_name = bidder_data.get('bidder_name', 'Unknown Bidder')
            
            # Process each criteria chunk in the bidder's evaluate_summary_chunk
            for chunk in bidder_data.get('evaluate_summary_chunk', []):
                # Extract all criteria (main and sub) from the chunk
                all_criteria = self.extract_all_criteria(chunk)
                
                print(f"ALL CRITERIA : {all_criteria}")
                for criteria_info in all_criteria:
                    is_sub_criteria = 'parent_criteria' in criteria_info
                    criteria_name = criteria_info['criteria_name']
                    
                    # Skip criteria with empty or "Unknown" names
                    if criteria_name == "Unknown" or not criteria_name.strip():
                        continue
                        
                    # Skip "Sub-Criteria Analysis" entries, they're just headers
                    if "Analysis" in criteria_name:
                        continue
                    
                    # For display purposes, use just the criteria name without parent prefixing
                    display_name = criteria_name
                    
                    # Find matching criteria in the table or create a new row
                    criteria_row = next((row for row in self.table_data if row['criteria'] == display_name), None)
                    
                    if criteria_row is None:
                        # Get weight from criteria_weights if available
                        weight = next((item['weight'] for item in self.criteria_weights 
                                      if item['criteria'].lower() == criteria_name.lower()), 'N/A')
                        
                        criteria_row = {
                            'criteria': display_name,
                            'is_sub_criteria': is_sub_criteria,
                            'parent': criteria_info.get('parent_criteria', ''),
                            'weight': weight  # Use weight for both main and sub-criteria
                        }
                        # Initialize all bidder scores as 'N/A'
                        for bidder in [b['bidder_name'] for b in self.bidders_data]:
                            criteria_row[f'{bidder}_score'] = 'N/A'
                            criteria_row[f'{bidder}_weighted_score'] = 'N/A'
                            criteria_row[f'{bidder}_summary'] = ''
                        
                        self.table_data.append(criteria_row)
                    
                    # Add bidder's score to the row
                    if criteria_info['score'] is not None:
                        criteria_row[f'{bidder_name}_score'] = f"{criteria_info['weighted_score']}%"
                    else:
                        criteria_row[f'{bidder_name}_score'] = 'N/A'
                        
                    if criteria_info['weighted_score'] is not None:
                        criteria_row[f'{bidder_name}_weighted_score'] = f"{criteria_info['weighted_score']}%"
                    else:
                        criteria_row[f'{bidder_name}_weighted_score'] = 'N/A'
                    
                    # Add evaluation summary if available
                    if 'evaluation_summary' in criteria_info and criteria_info['evaluation_summary']:
                        criteria_row[f'{bidder_name}_summary'] = criteria_info['evaluation_summary']
                    else:
                        criteria_row[f'{bidder_name}_summary'] = ''
        
        # Organize data and create DataFrame
        return self._create_organized_dataframe()
    
    def _create_organized_dataframe(self):
        """Organize criteria in a logical way and create DataFrame"""
        organized_data = []
        
        # Find all main criteria
        main_criteria = [row for row in self.table_data if not row['is_sub_criteria']]
        
        # For each main criterion, add it and then its sub-criteria
        for main in main_criteria:
            organized_data.append(main)
            # Find and add all sub-criteria belonging to this main criterion
            for sub in [row for row in self.table_data if row['is_sub_criteria'] and row['parent'] == main['criteria']]:
                organized_data.append(sub)
        
        # Create DataFrame from organized data
        df = pd.DataFrame(self.table_data)
        
        # Organize columns in a logical order
        column_order = ['criteria', 'is_sub_criteria', 'parent', 'weight']
        
        # Add bidder-specific columns
        bidders = set()
        for column in df.columns:
            for bidder in set(bidder_data['bidder_name'] for bidder_data in self.bidders_data):
                if column.startswith(bidder):
                    bidders.add(bidder)
        
        for bidder in bidders:
            column_order.extend([f'{bidder}_score', f'{bidder}_weighted_score', f'{bidder}_summary'])
        
        # Reorder columns (only include columns that exist)
        valid_columns = [col for col in column_order if col in df.columns]
        if valid_columns:
            df = df[valid_columns]
        
        
        return df
    
    def update_table_with_special_criteria(self, df):
        """Update the comparison table with special criteria calculations"""
        # Calculate scores for special criteria
        calculator = SpecialCriteriaCalculator(self.special_criteria_data)
        calculated_criteria = calculator.calculate_all()

        if not calculated_criteria:
            return df
        
        # Create new rows for the calculated criteria
        new_rows = []
        
        for criteria_item in calculated_criteria:
            bidder_name = criteria_item['bidder_name']
            criteria_name = criteria_item['criteria_name']
            calculated_score = criteria_item.get('calculated_score', 0)
            weighted_score = criteria_item.get('weighted_score', 0)
            
            # Create a new row for this criteria
            new_row = {
                'criteria': criteria_name,
                'is_sub_criteria': False,  # These are main criteria
                'parent': '',
                'weight': criteria_item.get('weight', 'N/A')
            }
            
            # Add bidder-specific columns
            new_row[f'{bidder_name}_score'] = f"{calculated_score}"
            new_row[f'{bidder_name}_summary'] = f"{criteria_item['summary']}"
            new_row[f'{bidder_name}_weighted_score'] = f"{weighted_score}"
            
            new_rows.append(new_row)
        
        # Add the new rows to the DataFrame
        if new_rows:
            new_df = pd.DataFrame(new_rows)
            df = pd.concat([df, new_df], ignore_index=True)
        
        return df
    

    def generate_html_comparison_table(self,transformed_data):
        # Get all unique bidder names
        bidders = set()
        for item in transformed_data:
            for key in item.keys():
                if key != 'criteria' and key != 'Scores':
                    bidders.add(key)
        
        bidders = sorted(list(bidders))  # Sort bidders for consistent display
        
        # Start building HTML table
        html = ['<table border="1" style="border-collapse: collapse; width: 100%;">']
        
        # Create header row
        header = ['<tr>', '<th style="padding: 8px; text-align: left;">Criteria</th>']
        for bidder in bidders:
            header.append(f'<th style="padding: 8px; text-align: left;">{bidder}</th>')
            header.append('<th style="padding: 8px; text-align: left;">Scores</th>')
        header.append('</tr>')
        html.append(''.join(header))
        
        # Create data rows
        for item in transformed_data:
            row = ['<tr>', f'<td style="padding: 8px; text-align: left;">{item["criteria"]}</td>']
            
            for bidder in bidders:
                # Add bidder's summary
                bidder_summary = item.get(bidder, '')
                row.append(f'<td style="padding: 8px; text-align: left;">{bidder_summary}</td>')
                
                # Add bidder's score
                score = item['Scores'].get(bidder, 'N/A')
                row.append(f'<td style="padding: 8px; text-align: left;">{score}</td>')
            
            row.append('</tr>')
            html.append(''.join(row))
        
        html.append('</table>')
        return ''.join(html)


    def extract_criteria_data(self, html_content, is_main_criteria=True):
        """
        Extract criteria information from HTML content using BeautifulSoup
        Handles both main criteria and sub-criteria
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Extract criteria name
        criteria_name = "Unknown"
        
        if is_main_criteria:
            # For main criteria, look for h3 tags
            criteria_header = soup.find('h3')
            if criteria_header:
                if ': ' in criteria_header.text:
                    criteria_name = criteria_header.text.split(': ')[1]
                else:
                    criteria_name = criteria_header.text.strip()
        else:
            # For sub-criteria, we need to be more precise
            # First try to find the specific sub-criteria heading
            sub_criteria_h5 = soup.find('h5', string=lambda s: s and 'Sub-Criteria' in s)
            
            if sub_criteria_h5:
                # Extract from format like "Sub-Criteria 1: Equipment pricing"
                if ': ' in sub_criteria_h5.text:
                    criteria_name = sub_criteria_h5.text.split(': ')[1].strip()
                else:
                    criteria_name = sub_criteria_h5.text.strip()
            else:
                # Try alternative h4 format
                sub_criteria_h4 = soup.find('h4')
                if sub_criteria_h4:
                    if ': ' in sub_criteria_h4.text:
                        criteria_name = sub_criteria_h4.text.split(': ')[1].strip()
                    else:
                        # For cases like "Sub-Criteria 1: Ball Mill 2"
                        if 'Sub-Criteria' in sub_criteria_h4.text and ':' in sub_criteria_h4.text:
                            criteria_name = sub_criteria_h4.text.split(':')[1].strip()
                        else:
                            criteria_name = sub_criteria_h4.text.strip()
        
        # Extract score
        score_tag = soup.find('span', class_='score')
        score = None
        if score_tag:
            score_match = re.search(r'(\d+)%', score_tag.text)
            if score_match:
                score = int(score_match.group(1))
        
        # Extract weighted score
        weighted_score = None
        if is_main_criteria:
            # Try multiple patterns for main criteria weighted score
            weighted_score_tag = soup.find('Main_weighted_score')
            if weighted_score_tag:
                weighted_match = re.search(r'Weighted Score: (\d+(?:\.\d+)?)%', weighted_score_tag.text)
                if weighted_match:
                   
                    
                    weighted_score = str(weighted_match.group(1))
            
            # If not found with tag, try looking for h5 with "Weighted Score" text
            if weighted_score is None:
                weighted_h5 = soup.find(['h5', 'h6'], string=lambda s: s and 'Weighted Score:' in s)
                if weighted_h5:
                    weighted_match = re.search(r'Weighted Score: (\d+(?:\.\d+)?)%', weighted_h5.text)

                  

                    if weighted_match:
                        weighted_score = str(weighted_match.group(1))
        else:
            weighted_score_tag = soup.find('weighted_score')
            if weighted_score_tag:
                weighted_match = re.search(r'Weighted Score: (\d+(?:\.\d+)?)%', weighted_score_tag.text)
                if weighted_match:
                    weighted_score = str(weighted_match.group(1))
            
            # If no explicit weighted_score tag, look for h6 containing "Weighted Score"
            if weighted_score is None:
                weighted_h6 = soup.find('h6', string=lambda s: s and 'Weighted Score:' in s)
                if weighted_h6:
                    weighted_match = re.search(r'Weighted Score: (\d+(?:\.\d+)?)%', weighted_h6.text)
                    if weighted_match:
                        weighted_score = str(weighted_match.group(1))
        
        # Extract evaluation summary - UPDATED LOGIC
        evaluation_summary = None
        
        # Look for any h5/h6 with class containing 'evaluation_summary' (case-insensitive)
        evaluation_summary_tag = soup.find(['h5', 'h6'], class_=lambda c: c and 'evaluation_summary' in c.lower())
        if evaluation_summary_tag:
            evaluation_summary = evaluation_summary_tag.text.strip()
        
        # Extract sub-criteria if this is a main criteria
        sub_criteria = []
        if is_main_criteria:
            # Method 1: Find sub-criteria in dedicated section
            sub_criteria_sections = soup.find_all('li', class_='sub-criteria-section')
            if sub_criteria_sections:
                for section in sub_criteria_sections:
                    sub_items = section.find_all('li', class_='sub-criteria-item')
                    for item in sub_items:
                        sub_criteria_html = str(item)
                        sub_criteria_data = self.extract_criteria_data(sub_criteria_html, is_main_criteria=False)
                        sub_criteria.append(sub_criteria_data)
            else:
                # Method 2: Alternative structure with div and h4
                # First check if there's a sub-criteria analysis section
                sub_header = soup.find('h4', string=lambda s: s and 'Sub-Criteria Analysis' in s)
                if sub_header:
                    # Find all divs that might contain sub-criteria
                    for div in soup.find_all('div'):
                        sub_header = div.find('h4', string=lambda s: s and 'Sub-Criteria' in s)
                        if sub_header:
                            sub_criteria_data = self.extract_criteria_data(str(div), is_main_criteria=False)
                            if sub_criteria_data['criteria_name'] != "Unknown":
                                sub_criteria.append(sub_criteria_data)
                else:
                    # Method 3: Look for h4 elements that contain "Sub-Criteria" and process that section
                    for h4 in soup.find_all('h4'):
                        if 'Sub-Criteria' in h4.text:
                            # Get the next ul element which should contain the sub-criteria details
                            ul = h4.find_next('ul')
                            if ul:
                                sub_criteria_data = self.extract_criteria_data(str(ul), is_main_criteria=False)
                                if sub_criteria_data['criteria_name'] != "Unknown":
                                    # Update name from h4 if it wasn't found in the ul
                                    if sub_criteria_data['criteria_name'] == "Unknown":
                                        if ': ' in h4.text:
                                            sub_criteria_data['criteria_name'] = h4.text.split(': ')[1].strip()
                                        else:
                                            sub_criteria_data['criteria_name'] = h4.text.strip()
                                    sub_criteria.append(sub_criteria_data)
        
        return {
            'criteria_name': criteria_name,
            'score': score,
            'weighted_score': weighted_score,
            'evaluation_summary': evaluation_summary,
            'sub_criteria': sub_criteria if is_main_criteria else []
        }

    def extract_all_criteria(self, html_content):
        """Extract both main criteria and sub-criteria from HTML content"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # First extract the main criteria
        main_criteria = self.extract_criteria_data(html_content)
        
        # Return all criteria (main and sub) in a flat structure
        all_criteria = [main_criteria]
        
        # Process sub-criteria from the main criteria's sub_criteria list
        for sub in main_criteria['sub_criteria']:
            # Skip sub-criteria with empty or "Unknown" names
            if sub['criteria_name'] == "Unknown" or not sub['criteria_name'].strip():
                continue
                
            # Mark as sub-criteria by adding parent information
            sub['parent_criteria'] = main_criteria['criteria_name']
            all_criteria.append(sub)
        
        # Look for additional sub-criteria sections that might have been missed
        h4_elements = soup.find_all('h4')
        for h4 in h4_elements:
            if 'Sub-Criteria' in h4.text and ':' in h4.text:
                # Extract the sub-criteria name
                sub_name = h4.text.split(':')[1].strip()
                
                # Skip if this sub-criteria is already in the list
                if any(sub.get('criteria_name') == sub_name for sub in all_criteria):
                    continue
                
                # Get all elements following this h4 until the next h4 or end of container
                next_elements = []
                current = h4.next_sibling
                while current and (not hasattr(current, 'name') or current.name != 'h4'):
                    next_elements.append(str(current))
                    current = current.next_sibling
                
                # Create a section with all these elements
                section_html = f"<div>{str(h4)}{''.join(next_elements)}</div>"
                
                # Process this section
                sub_criteria_data = self.extract_criteria_data(section_html, is_main_criteria=False)
                
                # If we still don't have a name, extract it from h4
                if sub_criteria_data['criteria_name'] == "Unknown":
                    sub_criteria_data['criteria_name'] = sub_name
                
                # Add parent information
                sub_criteria_data['parent_criteria'] = main_criteria['criteria_name']
                
                # Extract evaluation summary specifically for this sub-criteria
                sub_soup = BeautifulSoup(section_html, 'html.parser')
                
                # Try to find evaluation summary in this section
                if not sub_criteria_data.get('evaluation_summary'):
                    # Look for colored text that might be a summary
                    colored_h6 = sub_soup.find(['h5', 'h6'], style=lambda s: s and 'color:' in s.lower())
                    if colored_h6:
                        sub_criteria_data['evaluation_summary'] = colored_h6.text.strip()
                    
                    # Also try class-based detection
                    eval_tag = sub_soup.find(['h5', 'h6'], class_=lambda c: c and 'evaluation_summary' in c.lower())
                    if eval_tag:
                        sub_criteria_data['evaluation_summary'] = eval_tag.text.strip()
                
                all_criteria.append(sub_criteria_data)
        
        # Double-check each sub-criteria for missing evaluation summaries
        for criteria in all_criteria:
            if 'parent_criteria' in criteria and not criteria.get('evaluation_summary'):
                # Try to find the corresponding section in the original HTML
                sub_name = criteria['criteria_name']
                section = None
                
                # Look for h4/h5/h6 that contains this sub-criteria name
                for h in soup.find_all(['h4', 'h5', 'h6']):
                    if sub_name in h.text:
                        section = h.parent
                        break
                
                if section:
                    # Look for colored text that might be a summary in this section
                    colored_text = section.find(['h5', 'h6'], style=lambda s: s and 'color:' in s.lower())
                    if colored_text:
                        criteria['evaluation_summary'] = colored_text.text.strip()
        
        return all_criteria

    def detect_special_criteria(self, criteria_name, temperature=0.001):
        """
        Classifies a criteria name into one of three categories: Price, Delivery, or Other.
        
        Args:
            criteria_name (str): The name of the criteria to classify
            temperature (float): Temperature setting for the AI model
        
        Returns:
            str: One of three category values: "Price", "Delivery", or "Other"
        """

        prompt = PromptLoader().get_prompt('detect_special_criteria', {"criteria_name": criteria_name})
        messages = [
            {
                "role": "user",
                "content": prompt
            }
        ]
        models = ["llama3-70b-8192", "llama-3.3-70b-versatile"]
        import random
        from services.fast_apis_service import FastAPIs
        fast_apis = FastAPIs()
        model = random.choice(models)  
        response = fast_apis.generate_completion([{"role": "user", "content": prompt}], model=model)
        #response = claude_client.generate_message_sync(messages, "", temperature, "Claude 3 Sonnet", 4096)
        return response.strip()

    def extract_special_criteria_value(self, docs, criteria, temperature=0.001):
        """
        Extracts specific values from criteria analysis based on its category.
        
        Args:
            docs (str): The HTML document containing the analysis
            criteria (str): The name of the criteria to extract values for
            temperature (float): Temperature setting for the AI model
        
        Returns:
            str: Extracted value in format specific to the criteria type:
                 - Price: number(USD)
                 - Delivery: number(WEEKS)
                 - Other: N/A
        """

        prompt = PromptLoader().get_prompt('extract_special_criteria_value', {"analysis": docs, "criteria_name": criteria})
        messages = [
            {
                "role": "user",
                "content": prompt
            }
        ]
        models = ["llama3-70b-8192", "llama-3.3-70b-versatile"]
        import random
        from services.fast_apis_service import FastAPIs
        fast_apis = FastAPIs()
        model = random.choice(models)  
        response = fast_apis.generate_completion([{"role": "user", "content": prompt}], model=model)
        #response = claude_client.generate_message_sync(messages, "", temperature, "Claude 3 Sonnet", 4096)
        return response.strip()


    def create_complete_html_table(self):
        df = self.create_initial_comparison_table()

        # Update with special criteria
        #df = self.update_table_with_special_criteria(df)


        # Ensure each criterion appears only once by grouping
        df = df.groupby('criteria', as_index=False).first()

        individual_bidder_summary = df.copy()
        # Move "Technical Criteria" to the end
        

        # Add comparison summary column
        bidder_names = list(set(bidder_data['bidder_name'] for bidder_data in self.bidders_data))


        # Apply generate_comparison_summary to each rowdder_summary =[{"bidder_name":,"summary":}...]
        for idx, row in df.iterrows():
            print(f"ROW : {row}")
            try:


                bidder_summaries = self.generate_comparison_summary(row, bidder_names)
                print(f"Bidder summaries for row {idx}: {bidder_summaries}")


                # Replace existing summary with new summary

                for bidder in bidder_names:
                    summary_col = f"{bidder}_summary"
                    if bidder_summaries.get(summary_col):
                        df.at[idx, summary_col] = bidder_summaries[summary_col]

                print(f"COLUMNS : {df.columns}")
                
            except Exception as e:
                print(f"Error generating comparison for row {idx}: {e}")


       
        # Create HTML table
        # Filter to only include the desired columns
        filtered_columns = ['criteria']
        
        # Add bidder score columns
        for bidder in bidder_names:
            filtered_columns.append(f'{bidder}_score')
            filtered_columns.append(f"{bidder}_summary")
            
        
        # Filter the DataFrame to only include these columns
        filtered_df = df[filtered_columns]

        individual_bidder_summary = individual_bidder_summary[filtered_columns]

        #filtered_df = transform_csv_headers(filtered_df)
        def transform_keys(data):
            result = []
            for item in data:
                new_item = {'criteria': item['criteria']}
                scores = {}
                
                for key in item:
                    if key == 'criteria':
                        continue
                        
                    if '_summary' in key:
                        # Change bidder_name_summary to bidder_name
                        bidder_name = key.replace('_summary', '')
                        new_item[bidder_name] = item[key]
                    elif '_score' in key:
                        # Extract bidder name and store score
                        bidder_name = key.replace('_score', '')
                        scores[bidder_name] = item[key]
                
                # Add scores as a separate entry
                new_item['Scores'] = scores
                result.append(new_item)
            return result

        #arranged in criteria order
        cirteria_order = [criteria["criteria"].strip() for criteria in self.criteria_weights]

        #arrange df in criteria order
        filtered_df = filtered_df.set_index('criteria').reindex(cirteria_order).reset_index()
        individual_bidder_summary = individual_bidder_summary.set_index('criteria').reindex(cirteria_order).reset_index()

        #drops rows with NAN values 
        df = filtered_df.dropna(axis=0, how='any')


        if "Technical Criteria" in df['criteria'].values:
            # Extract the Technical Criteria row
            tech_criteria_row = df[df['criteria'] == "Technical Criteria"]
            # Remove it from the original DataFrame
            df = df[df['criteria'] != "Technical Criteria"]
            # Append it to the end
            df = pd.concat([df, tech_criteria_row], ignore_index=True)

        dicts = df.to_dict(orient="records")

        individual_bidder_summary = individual_bidder_summary.to_dict(orient="records")

        print(f"Data before transform: {dicts}")
       
      
        data = transform_keys(dicts)

        print(f"Data after transform: {data}")

        html_table = self.generate_html_comparison_table(data)

        return html_table,data,individual_bidder_summary
    


    def generate_comparison_summary(self,row, bidder_names, temperature=0.001):
        """
        Generate a comparison summary for a specific criteria across all bidders using LLM.
        
        Args:
            row (Series): A row from the DataFrame representing a specific criteria
            bidder_names (list): List of bidder names
            temperature (float): Temperature setting for the AI model
            
        Returns:
            str: A comparison summary across all bidders for this criteria
        """
        criteria_name = row['criteria']
        is_sub_criteria = row.get('is_sub_criteria', False)
        
        if criteria_name  in self.scores_explanation_formula:
            additional_scoring_info = self.scores_explanation_formula[criteria_name]["explanation"]
        else:
            additional_scoring_info = ""

        # Collect bidder information for this criteria
        bidder_info = []
        
        for bidder in bidder_names:
            score_col = f"{bidder}_score"
            summary_col = f"{bidder}_summary"
            
            if score_col in row and summary_col in row:
                print(f"ROW : {row}")
                bidder_info.append({
                    "bidder_name": bidder,
                    "score": row.get(score_col, "N/A"),
                    "summary": row.get(summary_col, "")
                })
        

        # If we have less than 1 bidder, no comparison needed
        #if len(bidder_info) < 1:
            #eturn "Insufficient bidder data for comparison."
        
        # Create prompt for LLM directly instead of using PromptLoader
        prompt = PromptLoader().get_prompt('generate_comparison_summary_on_comparison_table', {
            "criteria_name": criteria_name,
            "is_sub_criteria": "Yes" if is_sub_criteria else "No",
            "bidder_info": bidder_info,
            "parent_criteria_name":row["parent"],
            "scores_explanation_formula":additional_scoring_info
        })
        
        # Call LLM to generate comparison
        messages = [
            {
                "role": "user",
                "content": prompt
            }
        ]
        
        try:

           # models = ["llama3-70b-8192", "llama-3.3-70b-versatile"]
            #import random
            #from services.fast_apis_service import FastAPIs
            #fast_apis = FastAPIs()
            #model = random.choice(models)  
            #response = fast_apis.generate_completion([{"role": "user", "content": prompt}], model=model)
            response = claude_client.generate_message_sync(messages, "", temperature, "Claude 3 Sonnet", 4096)
           
            bidder_summaries = json.loads(response)
            #replace existing summary with new summary
            '''for bidder in bidder_names:
                summary_col = f"{bidder}_summary"
                row[summary_col] = bidder_summaries[f"{bidder}_summary"]'''
            #return response.strip().replace("\n", "")
           
            return bidder_summaries
        except Exception as e:
            print(f"Error generating comparison: {e}")
            return ""


class SpecialCriteriaCalculator:
    """
    Class to handle calculations for special criteria types like Price and Delivery etc
    """
    def __init__(self, special_criteria_data):
        self.special_criteria_data = special_criteria_data
        self.calculated_data = []
        
    def calculate_all(self):
        """Calculate scores for all special criteria types"""
        self._calculate_price_scores()
        self._calculate_delivery_scores()
        return self.calculated_data
    
    def _extract_numeric_value(self, value_str):
        """Extract numeric value from strings like '400(USD)' or '48(WEEKS)'"""
        if not value_str or value_str == 'N/A':
            return None, None
            
        # Extract digits and decimal points, and the unit in parentheses
        import re
        numeric_match = re.search(r'(\d+\.?\d*)\s*\(([^)]+)\)', str(value_str))
        
        if numeric_match:
            value = float(numeric_match.group(1))
            unit = numeric_match.group(2).upper()
            return value, unit
            
        # Try just extracting a number if no parentheses format is found
        numeric_only = re.search(r'(\d+\.?\d*)', str(value_str))
        if numeric_only:
            return float(numeric_only.group(1)), None
            
        return None, None
    
    def _calculate_price_scores(self):
        """Calculate normalized price scores based on lowest bidder price"""
        # Get all price criteria
        print(f"Special criteria data: {self.special_criteria_data}")
        print(f"Calculating price scores...")
        price_criteria = [item for item in self.special_criteria_data if item['criteria_slug'] == 'Price']
        
        if not price_criteria:
            return
        
        
        # Extract numeric values from all price entries
        for item in price_criteria:
            item['numeric_value'], item['unit'] = self._extract_numeric_value(item['value'])
        
        # Get valid prices (non-None values)
        valid_prices = [item['numeric_value'] for item in price_criteria if item['numeric_value'] is not None]
        
        if not valid_prices:
            for item in price_criteria:
                calculated_item = item.copy()
                calculated_item['calculation_explanation'] = "No valid price provided, assigned minimum score"
                calculated_item['calculated_score'] = 0
                self.calculated_data.append(calculated_item)
            return
        
       
        # Find the lowest price among all bidders
        lowest_price = min(valid_prices)
        
        # Calculate normalized scores
        for item in price_criteria:
            calculated_item = item.copy()
            
            if item['numeric_value'] is not None:
                current_price = item['numeric_value']
                calculated_score = ((lowest_price / current_price) * 5) * 20
                calculated_score = f"{round(calculated_score, 2)}%"
                unit_str = f" {item['unit']}" if item['unit'] else ""
                calculated_item['calculated_score'] = calculated_score
                print(f"PRICE SCORE CALCULATION: Lowest price: {lowest_price}, Current price: {current_price}, Calculated score: {calculated_score}")
                calculated_item['calculation_explanation'] = f"Score calculated as (lowest price {lowest_price}{unit_str} / this bidder's price {current_price}{unit_str}) * 5"
            else:
                # Use the large value for N/A entries
                calculated_score = 0 # Will be very close to zero
                calculated_item['calculation_explanation'] = "No valid price provided, assigned minimum score"
                calculated_item['calculated_score'] = calculated_score
            
            self.calculated_data.append(calculated_item)

    
    def _calculate_delivery_scores(self):
        """Calculate normalized delivery scores based on shortest lead time"""
        # Get all delivery criteria
        delivery_criteria = [item for item in self.special_criteria_data if item['criteria_slug'] == 'Delivery']
        
        if not delivery_criteria:
            return
        
        # Extract numeric values from all delivery entries
        for item in delivery_criteria:
            item['numeric_value'], item['unit'] = self._extract_numeric_value(item['value'])
        
        # Get valid times (non-None values)
        valid_times = [item['numeric_value'] for item in delivery_criteria if item['numeric_value'] is not None]
        
        if not valid_times: 
            for item in delivery_criteria:
                calculated_item = item.copy()
                calculated_item['calculation_explanation'] = "No valid lead time provided, assigned minimum score"
                calculated_item['calculated_score'] = 0
                self.calculated_data.append(calculated_item)
            return
        
        # Calculate a very large value for N/A entries (sum of all valid times × 10)
        large_value = sum(valid_times) * 10
        
        # Find the shortest lead time among all bidders
        shortest_time = min(valid_times)
        
        # Calculate normalized scores
        for item in delivery_criteria:
            calculated_item = item.copy()
            
            if item['numeric_value'] is not None:
                current_time = item['numeric_value']
                calculated_score = ((shortest_time / current_time) * 5) * 20
                calculated_score = f"{round(calculated_score, 2)}%"
                print(f"DELIVERY SCORE CALCULATION: Shortest time: {shortest_time}, Current time: {current_time}, Calculated score: {calculated_score}")
                calculated_item['calculated_score'] = calculated_score
                calculated_item['calculation_explanation'] = f"Score calculated as (shortest lead time {shortest_time} WEEKS / this bidder's lead time {current_time} WEEKS) * 5"
            else:
                # Use the large value for N/A entries
                calculated_score = ((shortest_time / large_value) * 5) * 20 # Will be very close to zero
                calculated_score = f"{round(calculated_score, 2)}%"
                calculated_item['calculation_explanation'] = "No valid lead time provided, assigned minimum score"
                calculated_item['calculated_score'] = calculated_score
            
            self.calculated_data.append(calculated_item)


def generate_comparison_summary(row, bidder_names, temperature=0.001):
    """
    Generate a comparison summary for a specific criteria across all bidders using LLM.
    
    Args:
        row (Series): A row from the DataFrame representing a specific criteria
        bidder_names (list): List of bidder names
        temperature (float): Temperature setting for the AI model
        
    Returns:
        str: A comparison summary across all bidders for this criteria
    """
    criteria_name = row['criteria']
    is_sub_criteria = row.get('is_sub_criteria', False)
    
    
    # Collect bidder information for this criteria
    bidder_info = []
    
    for bidder in bidder_names:
        score_col = f"{bidder}_score"
        summary_col = f"{bidder}_summary"
        
        if score_col in row and summary_col in row:
            print(f"ROW : {row}")
            bidder_info.append({
                "bidder_name": bidder,
                "score": row.get(score_col, "N/A"),
                "summary": row.get(summary_col, "")
            })
    
    # If we have less than 1 bidder, no comparison needed
    if len(bidder_info) < 1:
        return "Insufficient bidder data for comparison."
    
    # Create prompt for LLM directly instead of using PromptLoader
    prompt = PromptLoader().get_prompt('generate_comparison_summary_on_comparison_table', {
        "criteria_name": criteria_name,
        "is_sub_criteria": "Yes" if is_sub_criteria else "No",
        "bidder_info": bidder_info,
        "parent_criteria_name":row["parent"]
    })
    
    # Call LLM to generate comparison
    messages = [
        {
            "role": "user",
            "content": prompt
        }
    ]
    
    try:

        models = ["llama3-70b-8192", "llama-3.3-70b-versatile"]
        import random
        from services.fast_apis_service import FastAPIs
        fast_apis = FastAPIs()
        model = random.choice(models)  
        #response = fast_apis.generate_completion([{"role": "user", "content": prompt}], model=model)
        response = claude_client.generate_message_sync(messages, "", temperature, "Claude 3 Sonnet", 4096)
        print(f"Bidder summaries RESPONSE : {response}")
        bidder_summaries = json.loads(response)
        #replace existing summary with new summary
        '''for bidder in bidder_names:
            summary_col = f"{bidder}_summary"
            row[summary_col] = bidder_summaries[f"{bidder}_summary"]'''
        #return response.strip().replace("\n", "")
        print(f"BIDDER SUMMARIES : {bidder_summaries}")
        return bidder_summaries
    except Exception as e:
        print(f"Error generating comparison: {e}")
        return ""





def main():
    # Example criteria weights
    criteria_weights = [
        {'criteria': 'Price', 'weight': 50},
        {'criteria': 'Delivery', 'weight': 30},
        {'criteria': 'Compliant bid', 'weight': 20},  # Sub-criteria weight
        {'criteria': 'Fulfilment of technical specification', 'weight': 100}         # Sub-criteria weight
    ]
    
    # Load bidder data from file or use sample data
    try:
            from test_keys import data
            bidders_data = data
    except FileNotFoundError:
        pass
        # Use sample data if file not found

    creator = ComparisonTableCreator(bidders_data, criteria_weights)
    html_table = creator.create_complete_html_table()
    
    # Save HTML to file
    with open('comparison_table.html', 'w') as f:
        f.write(html_table)
     
if __name__ == "__main__":
    main()

