"""Keep package_id nullable in bids table

Revision ID: keep_package_id_nullable
Revises: fd4af19b47eb
Create Date: 2024-03-21 17:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'keep_package_id_nullable'
down_revision = 'fd4af19b47eb'
branch_labels = None
depends_on = None

def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('bids', schema=None) as batch_op:
        batch_op.alter_column('package_id',
               existing_type=mysql.VARCHAR(length=36),
               nullable=True)
    # ### end Alembic commands ###

def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('bids', schema=None) as batch_op:
        batch_op.alter_column('package_id',
               existing_type=mysql.VARCHAR(length=36),
               nullable=False)
    # ### end Alembic commands ### 