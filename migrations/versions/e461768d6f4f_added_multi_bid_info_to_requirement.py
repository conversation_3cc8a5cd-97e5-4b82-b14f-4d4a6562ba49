"""added multi-bid info to Requirement

Revision ID: e461768d6f4f
Revises: keep_package_id_nullable
Create Date: 2025-03-24 21:37:25.911828

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e461768d6f4f'
down_revision = 'keep_package_id_nullable'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('bids', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_bids_package_id'), ['package_id'], unique=False)
        batch_op.create_foreign_key(None, 'project', ['package_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('requirement', schema=None) as batch_op:
        batch_op.add_column(sa.Column('is_multibid', sa.<PERSON>(), nullable=True))
        batch_op.add_column(sa.Column('multi_bids_info', sa.Text(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('requirement', schema=None) as batch_op:
        batch_op.drop_column('multi_bids_info')
        batch_op.drop_column('is_multibid')

    with op.batch_alter_table('bids', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_index(batch_op.f('ix_bids_package_id'))

    # ### end Alembic commands ###
