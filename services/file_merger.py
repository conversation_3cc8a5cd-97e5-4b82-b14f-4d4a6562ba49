import os
import uuid
from docx import Document
import fitz  # PyMuPDF
from werkzeug.utils import secure_filename
from services.convert_docx_to_pdf import DocxToPdf

class FileMerger:
    def __init__(self, output_folder):
        self.output_folder = output_folder
        self.docx_to_pdf = DocxToPdf()

    def merge_files(self, files, output_filename, remove_temp_files=True):
        temp_paths = []
        print('this is files: ', files)

        # Process each file
        for file in files:
            temp_path = os.path.join(self.output_folder, secure_filename(file.filename))
            file.save(temp_path)
            temp_paths.append(temp_path)

        pdf_paths = []

        # Convert DOCX to PDF and collect PDF paths
        for file_path in temp_paths:
            if file_path.endswith('.docx'):
                pdf_path = self._convert_docx_to_pdf(file_path)
                pdf_paths.append(pdf_path)
            elif file_path.endswith('.pdf'):
                pdf_paths.append(file_path)
            else:
                raise ValueError("Unsupported file type")
        

        print('pdf_paths: ', pdf_paths)
        print('output filename: ', output_filename)
        print(temp_paths)
        # Merge all PDFs
        merged_file_path = self._merge_pdf_files(pdf_paths, output_filename)

        # Remove temporary files
        if remove_temp_files:
            # self._remove_temp_files(temp_paths, pdf_paths)
            for temp_path in temp_paths:
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                else:
                    print(f"File not found: {temp_path}")

        for pdf_path in pdf_paths:
            if os.path.exists(pdf_path):
                os.remove(pdf_path)
            else:
                print(f"File not found: {pdf_path}")

        return merged_file_path

    def merge_test_files(self, file_names_and_objects, output_filename, remove_temp_files=True):
        temp_paths = []
        print('this is file_names_and_objects: ', file_names_and_objects)

        # Process each file
        # for file in file_names_and_objects:
        #     temp_path = os.path.join(self.output_folder, secure_filename(file[0]))
        #     file[1].save(temp_path)
        #     temp_paths.append(temp_path)
        
        # Process each file by writing contents manually
        for filename, file_obj in file_names_and_objects:
            temp_path = os.path.join(self.output_folder, secure_filename(filename))
            with open(temp_path, 'wb') as temp_file:
                temp_file.write(file_obj.read())  # Write contents of file_obj to temp_path
            temp_paths.append(temp_path)

        pdf_paths = []

        # Convert DOCX to PDF and collect PDF paths
        for file_path in temp_paths:
            if file_path.endswith('.docx'):
                pdf_path = self._convert_docx_to_pdf(file_path)
                pdf_paths.append(pdf_path)
            elif file_path.endswith('.pdf'):
                pdf_paths.append(file_path)
            else:
                raise ValueError("Unsupported file type")
        

        print('pdf_paths: ', pdf_paths)
        print('output filename: ', output_filename)
        print(temp_paths)
        # Merge all PDFs
        merged_file_path = self._merge_pdf_files(pdf_paths, output_filename)

        # Remove temporary files
        if remove_temp_files:
            # self._remove_temp_files(temp_paths, pdf_paths)
            for temp_path in temp_paths:
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                else:
                    print(f"File not found: {temp_path}")

        for pdf_path in pdf_paths:
            if os.path.exists(pdf_path):
                os.remove(pdf_path)
            else:
                print(f"File not found: {pdf_path}")

        return merged_file_path
    
    def _convert_docx_to_pdf(self, docx_path):
        pdf_path = docx_path.replace('.docx', '.pdf')
        self.docx_to_pdf.convert_docx_to_pdf(docx_path, os.path.dirname(pdf_path))
        return pdf_path

    def _merge_pdf_files(self, file_paths, output_filename):
        # sys.exit()
        merger = fitz.open()

        for file_path in file_paths:
            pdf_document = fitz.open(file_path)
            merger.insert_pdf(pdf_document)
            pdf_document.close()

        output_path = os.path.join(self.output_folder, output_filename)
        print('file merged: ', output_path)
        merger.save(output_path)
        merger.close()
        return output_path
