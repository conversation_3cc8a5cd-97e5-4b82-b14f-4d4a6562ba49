import os
import json
from uuid import uuid4
from typing import List, Dict, <PERSON><PERSON>
from langchain_cohere import CohereEmbeddings
import faiss
import numpy as np
from langchain_core.documents import Document
import asyncio
import time
import cohere
from concurrent.futures import ThreadPoolExecutor

class FaissEmbeddingGPU:
    def __init__(self, request_id, max_concurrent_tasks=10, dimension=1024, batch_size=50):
        # First assign all instance variables
        self.request_id = request_id  # Move this up
        print("Request ID :", self.request_id)
        self.model = "embed-english-v3.0"
        self.dimension = dimension
        self.batch_size = batch_size
        self.vector_name = "aienergy_vec_db"
        self.documents: Dict[int, Document] = {}
        self.next_id = 0
        self.storage_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'storage', 'indices')
        os.makedirs(self.storage_dir, exist_ok=True)  
        
        # Then perform initialization operations
        self._load_environment()
        self.co_client = cohere.AsyncClient(api_key=os.getenv("COHERE_API_KEY"))
        self._load_or_initialize_index()
        self.semaphore = asyncio.Semaphore(max_concurrent_tasks)
        self.executor = ThreadPoolExecutor(max_workers=4)
    

    def _load_environment(self):
        """Loads environment variables from a JSON file."""
        parent_dir = "/workspace/faiss-gpu/"
        env_file_path = os.path.join(parent_dir, 'env.json')
        with open(env_file_path, 'r') as f:
            env_data = json.load(f)
        os.environ["COHERE_API_KEY"] = env_data.get("COHERE_API_KEY", "")

    
    def _load_or_initialize_index(self):
        """Initialize new index or load existing one based on request_id."""
        index_path = os.path.join(self.storage_dir, f"{self.vector_name}_{self.request_id}.index")
        docs_path = os.path.join(self.storage_dir, f"{self.vector_name}_{self.request_id}.docs")
        
        try:
            if os.path.exists(index_path) and os.path.exists(docs_path):
                print(f"Loading existing index and documents")
                
                # Load the documents
                with open(docs_path, 'r') as f:
                    documents_data = json.load(f)
                    self.documents = {
                        int(k): Document(
                            page_content=v['page_content'],
                            metadata=v['metadata']
                        ) for k, v in documents_data.items()
                    }
                    self.next_id = max(map(int, documents_data.keys())) + 1 if documents_data else 0
                
                # Load the CPU index
                cpu_index = faiss.read_index(index_path)
                
                # Convert to GPU if possible
                try:
                    self.gpu_resource = faiss.StandardGpuResources()
                    gpu_config = faiss.GpuIndexFlatConfig()
                    gpu_config.useFloat16 = True
                    gpu_config.device = 0
                    self.index = faiss.index_cpu_to_gpu(
                        self.gpu_resource, 
                        0, 
                        cpu_index)
                    print("Successfully loaded and converted index to GPU")
                except Exception as e:
                    print(f"Failed to convert to GPU, using CPU index: {str(e)}")
                    self.index = cpu_index
            else:
                print(f"No existing index found, initializing new one")
                self._initialize_vector_store()
                
        except Exception as e:
            print(f"Error loading index, initializing new one: {str(e)}")
            self._initialize_vector_store()


    def _initialize_vector_store(self):
        """Initializes the FAISS vector store with GPU support."""
        try:
            # Initialize GPU resources
            self.gpu_resource = faiss.StandardGpuResources()
            
            # Configure GPU parameters
            gpu_config = faiss.GpuIndexFlatConfig()
            gpu_config.useFloat16 = True
            gpu_config.device = 0

            # Create a new CPU index
            cpu_index = faiss.IndexFlatL2(self.dimension)
            
            # Convert to GPU index
            self.index = faiss.index_cpu_to_gpu(
                self.gpu_resource, 
                0, 
                cpu_index
            )
            print("Successfully initialized GPU-enabled FAISS index")
            
        except Exception as e:
            print(f"Failed to initialize GPU, falling back to CPU: {str(e)}")
            self.index = faiss.IndexFlatL2(self.dimension)

    async def _get_embeddings_batch(self, texts: List[str]) -> np.ndarray:
        """Get embeddings for a batch of texts using Cohere's async client."""
        try:
            response = await self.co_client.embed(
                texts=texts,
                model=self.model,
                input_type="search_document"
            )
            return np.array(response.embeddings, dtype=np.float32)
        except Exception as e:
            print(f"Error getting embeddings: {str(e)}")
            raise

    async def _process_batch(self, batch_docs: List[Document]) -> Tuple[np.ndarray, List[int]]:
        """Process a batch of documents to get embeddings and IDs with retry logic."""
        max_retries = 3
        retry_delay = 1  # seconds
        
        for attempt in range(max_retries):
            try:
                texts = [doc.page_content for doc in batch_docs]
                embeddings = await self._get_embeddings_batch(texts)
                
                # Assign IDs and store documents
                ids = []
                for doc in batch_docs:
                    self.documents[self.next_id] = doc
                    ids.append(self.next_id)
                    self.next_id += 1
                    
                return embeddings, ids
            
            except Exception as e:
                if attempt == max_retries - 1:
                    raise
                print(f"Retry {attempt + 1}/{max_retries} after error: {str(e)}")
                await asyncio.sleep(retry_delay * (attempt + 1))

    async def insert_documents(self, content: List[str], file_name: str, request_id: str):
        """Inserts documents into the vector store asynchronously with improved batch handling."""
        documents = [
            Document(
                page_content=data,
                metadata={"source": file_name, "request_id": request_id}
            ) for data in content
        ]
        
        # Reduce batch size for large document sets
        #actual_batch_size = min(self.batch_size, 20)  # Limit to 20 documents per batch
        actual_batch_size = self.batch_size
        total_processed = 0
        
        try:
            for i in range(0, len(documents), actual_batch_size):
                batch = documents[i:i + actual_batch_size]
                
                # Process batch with retry logic
                try:
                    embeddings, ids = await self._process_batch(batch)
                    self.index.add(embeddings)
                    total_processed += len(batch)
                    
                    # Save index periodically
                    if total_processed % self.batch_size == 0:
                        self._save_index()
                        print(f"Progress: {total_processed}/{len(documents)} documents processed")
                    
                    # Add small delay between batches to avoid rate limits
                    #await asyncio.sleep(0.1)
                    
                except Exception as e:
                    print(f"Error processing batch {i//actual_batch_size + 1}: {str(e)}")
                    continue
            
            # Final save
            self._save_index()
            print(f"Successfully added {total_processed} out of {len(documents)} documents")
            
        except Exception as e:
            print(f"Error in adding documents: {str(e)}")
            raise

    def _save_index(self):
        """Save the FAISS index and documents to disk."""
        try:
            # Save FAISS index
            index_path = os.path.join(self.storage_dir, f"{self.vector_name}_{self.request_id}.index")
            index_cpu = faiss.index_gpu_to_cpu(self.index)
            faiss.write_index(index_cpu, index_path)
            
            # Save documents dictionary
            docs_path = os.path.join(self.storage_dir, f"{self.vector_name}_{self.request_id}.docs")
            documents_data = {
                str(k): {
                    'page_content': v.page_content,
                    'metadata': v.metadata
                } for k, v in self.documents.items()
            }
            with open(docs_path, 'w') as f:
                json.dump(documents_data, f)
                
            print(f"Index and documents saved to {self.storage_dir}")
        except Exception as e:
            print(f"Error saving index and documents: {str(e)}")


    async def search(self, query: str, k: int = 5) -> List[Tuple[Document, float]]:
        """Performs GPU-accelerated similarity search."""
        try:
            query_embedding = await self._get_embeddings_batch([query])
            
            print(f"Number of documents in store: {len(self.documents)}")
            print(f"Document IDs available: {list(self.documents.keys())[:10]}...")  # Print first 10 IDs
            
            distances, indices = self.index.search(query_embedding, k)
            
            results = []
            for idx, dist in zip(indices[0], distances[0]):
                if idx in self.documents:
                    results.append((self.documents[idx], float(dist)))
                else:
                    print(f"Warning: Index {idx} not found in documents dictionary")
                    
            return results
                
        except Exception as e:
            print(f"Error in search: {str(e)}")
            raise
            
async def main():
    # Initialize the GPU-enabled FAISS embedding system
    faiss_gpu = FaissEmbeddingGPU(request_id="11223344",batch_size=500)

    # Test Case 3: Large Batch Processing
    print("\nTest Case 3: Large Batch Processing")

    single_page = """
    Passage #4 – Internet Filtering in the Workplace
    Extended Response Stimulus Materials:
    Source Material #1
    Email
    From: Justine Timmons, CEO
    Sent: Monday, September 24
    To: Employees of Niagara Equipment Corp.
    Subject: Workplace Internet Use
    Beginning next month, we are instituting a new policy for all employees regarding
    Internet use at work. To limit access to inappropriate and social media websites, we are
    installing new filtering software that will block those sites on every company-owned
    computer.
    Although management recognizes that the Internet is a valuable tool, especially for our
    creative department and sales team, we are also aware of the potentially costly
    downsides to unlimited access. According to a survey by Salary.com, at least 64
    percent of employees nationwide admit to visiting non-work-related websites while on
    the job. This “cyberslacking,” as it is called, hurts productivity and wastes company
    resources. Workers who are distracted by checking their social media sites are clearly
    not giving their full attention to their jobs.
    The other important reason that we are installing filters is to promote a positive
    workplace for all of our valued employees. When staff members use their computers to
    access offensive or inappropriate material that can be seen by their fellow workers, this
    creates a hostile workplace environment, thereby exposing the company to expensive
    and demoralizing lawsuits. Even social networking sites can lead to legal jeopardy if
    they are used to bully fellow workers. Other businesses have already faced harassment
    suits for just such activities.
    A committee comprising representatives of each team will be formed to determine which
    sites to block. Please tell your team leader if you are interested in serving on this
    committee.
    Thank you all for your cooperation and for your commitment to making Niagara
    Equipment the best place to work in Buffalo!
    Best,
    Justine Timmons, CEO
    """
    # large_document_set = [
    #     f"Document {single_page} containing technical information about AI and GPU computing"
    #     for i in range(1000)
    # ]
    
    # start_time = time.time()
    # await faiss_gpu.insert_documents(
    #     content=large_document_set,
    #     file_name="batch_test.txt",
    #     request_id="11223344"
    # )
    # print(f"Large batch processing time: {time.time() - start_time:.2f} seconds")
    
    # Test Case 4: Multiple Concurrent Searches
    print("\nTest Case 4: Multiple Concurrent Searches")
    search_queries = [
        "GPU processing",
        "artificial intelligence",
        "vector databases",
        "machine learning",
        "parallel computing"
    ]
    
    start_time = time.time()
    search_results = await asyncio.gather(*[
        faiss_gpu.search(query, k=3) for query in search_queries
    ])
    print(f"Concurrent search time: {time.time() - start_time:.2f} seconds")
    
    for query, results in zip(search_queries, search_results):
        print(f"\nResults for query: '{query}'")
        for doc, score in results[:2]:  # Show top 2 results for each query
            print(f"Score: {score:.4f}, Content: {doc.page_content[:100]}...")

if __name__ == "__main__":
    asyncio.run(main())