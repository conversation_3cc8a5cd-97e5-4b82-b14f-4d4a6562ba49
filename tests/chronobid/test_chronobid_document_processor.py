import os
import json
import async<PERSON>
from .cbp_file_processor import CBPFileProcessor
from services.handle_new_chronobid_documents import ChronobidDocumentProcessor
from models import Project

class ChronobidProcessorManager:
    def __init__(self, json_data_path, files_directory):
        with open(json_data_path, 'r') as file:
            self.data = json.load(file)
        self.files_directory = files_directory

    def process_requests(self):
        request_ids = []
        # Loop through each item in the JSON data
        for item in self.data:
            package_id = item.get('package_id')
            criteria_str = item.get('criteria')
            file_names = item.get('file_name')

            # Verify file_names is a list
            if not isinstance(file_names, list):
                print(f"Invalid file list for project ID {package_id}")
                continue

            package = Project.get_single(package_id)
            if not package:
                return 'Invalid package_id', 400

            # Collect files that exist in the directory
            files_to_process = []
            file_names_with_objects = []  # Store tuples of (filename, file_object)
            for file_name in file_names:
                file_path = os.path.join(self.files_directory, file_name)
                if os.path.isfile(file_path):
                    file_object = open(file_path, 'rb')
                    files_to_process.append(file_object)
                    file_names_with_objects.append((file_name, file_object))
                else:
                    print(f"File not found: {file_name} for project ID {package_id}")


            # Only process if we have valid files
            if files_to_process:
                try:
                    # Initialize CBPFileProcessor and process the list of files
                    file_processor = CBPFileProcessor(
                        package_id=package_id,
                        package_name=package['name'],
                        criteria_str=criteria_str
                    )
                    request_id = file_processor.process_files(file_names_with_objects)  # Process files with CBPFileProcessor
                    print(f"Successfully processed files for project ID: {package_id}")
                    request_ids.append(request_id)
                except Exception as e:
                    print(f"Error processing files for project ID {package_id}: {e}")
                finally:
                    # Close each file after processing to free resources
                    for file in files_to_process:
                        file.close()

        return request_ids


    async def run(self):
        request_ids = self.process_requests()
        results = []
        tasks = []  # Create a list to hold the tasks

        for ids in request_ids:
            print('ids: ', ids)
            cbp_evaluator = ChronobidDocumentProcessor(is_test=True)
            # Create a task for each request
            task = cbp_evaluator.process_single_request(ids['request_id'])
            tasks.append(task)

        # Run all tasks concurrently and wait for them to finish
        results = await asyncio.gather(*tasks)  # Gather results from all tasks

        # Display output after all processing is done
        print("Processing Done:", results)  # Output the results

        

