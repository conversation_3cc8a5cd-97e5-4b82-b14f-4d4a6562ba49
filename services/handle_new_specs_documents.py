import gevent
from gevent import monkey
monkey.patch_all()

import os
import sys
import json
import time
import asyncio
import re
from models import Requirement, Project, Chunks
from services.reasoning_specs_comply import SpecsComplyReasoning
from services.process_document import FileProcessor
from services.data_handler import DataManager
from services.engr_doc_types_detection import EngrDocTypesDetection
from services.process_scp_dataset_1 import Dataset1Extractor
from services.process_scp_dataset_2 import Dataset2Extractor
from services.report_manager import ReportManager
from flask_socketio import emit
from services.parser_openai_parellel import OpenAIProcessor
from services.faiss_embedding import FaissEmbedding
from services.fast_apis_service import FastAPIs
from services.claude_ai_service import ClaudeService
# from flask import current_app


class SpecsDocumentProcessor:
    def __init__(self, socket_manager, is_test=False):
        print('i am now called upon to process scp...')
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        env_file_path = os.path.join(parent_dir, 'env.json')

        with open(env_file_path, 'r') as f:
            self.env_data = json.load(f)
        
        self.socket_manager = socket_manager
        self.specs_reasoning = SpecsComplyReasoning()
        self.data_manager = DataManager()
        self.file_processor = FileProcessor()
        self.engr_doc_detector = EngrDocTypesDetection()
        self.dataset2_extractor = Dataset2Extractor(self.socket_manager)
        self.report_manager = None
        
        self.file_processor = OpenAIProcessor()

    def process_scp(self, requirement_id):
        from flask import current_app
        asyncio.run(self.handle_single_new_document_v2(requirement_id))
        
    def process_scp_v2(self, requirement_id):
        from init import app
        def _run_async():
            try:
                # Ensure each greenlet has an application context
                with app.app_context():
                    return self.run_scp(requirement_id)
            except Exception as e:
                print(f"Error in process_scp : {e}")
                raise
            
        # Spawn and return the greenlet
        return gevent.spawn(_run_async)
    
    def run_scp(self, requirement_id):
        try:
            self.add_event(requirement_id , 'progress_message', {
                'event_type': 'SCP',
                'request_id': requirement_id ,
                'data': {'status': 'extracting_sources', 'message': f"Welcome to Specs Comply Assistant..."}
            })
            requirement = Requirement.get_single(requirement_id)
            
            if requirement is None:
                raise RuntimeError("Requirement not found")
            
            
            # Load vendor documents from gpu server
            # S2
            faiss_gpu = FaissEmbedding()
            vendor_documents = faiss_gpu.request_documents(requirement_id)
            
            # Find discipline and document type
            # first 10 chunks of vendor documents joined together
            # ENHANCEMENT: may have to go through all and fetch multiple disciplines and document types
            doc_type_text = " ".join([doc['content'] for doc in vendor_documents[:15]])
            discipline, document_type = self.guess_discipline_and_document_type_from_text(doc_type_text)
            
            print('discipline: ', discipline)
            print('document_type: ', document_type)
            
            # Pull project files by discipline and document type
            # TODO: need to filter by discipline and document type
            project_chunks = Chunks.get_by(project_id=requirement['project_id'])
            print(f'project_chunks: {len(project_chunks)}')
            
            # get document requirement from project chunks to build cheat sheet
            # S3
            cheat_sheet = {}
            for chunk in project_chunks[:10]:
                document_specification = self.get_document_specification_from_project_text(chunk['content'], discipline, document_type)
                if f'{document_type}|{discipline}' not in cheat_sheet:
                    cheat_sheet[f'{document_type}|{discipline}'] = document_specification
                else:
                    cheat_sheet[f'{document_type}|{discipline}'] += document_specification
            
            print('cheat sheet: ', cheat_sheet)
            
            
            
            # S4
            
            rough_review = []
            for count, section in enumerate(vendor_documents[:10]):
                # find the document specification from the cheat sheet based on the document type and discipline
                document_specification = cheat_sheet[f'{document_type}|{discipline}']
                review, reference = self.query_section_spec_compliance_check(section, document_specification, document_type, discipline)
                print(f'review: {review}')
                print(f'reference: {reference}')
                rough_review.append({
                    'review': review,
                    'reference': reference
                })
            print(f'rough_review: {len(rough_review)}')
           
            return
            
            # extract criteria based on the document type and discipline
            
            
            # await self._prepare_context(requirement)
            
            criteria = [
                "Project background",
                "Standards, building code, local regulation",
                "Design load - including live and dead loads: Structural self-weight (DS), Equipment load (DE), Floors Live Load (LL), Roof Live Load (LR), Live Load of Materials (LE), Live Vehicle Loads (LV), Vibrating Loads (V), 3.3 Soil Load (HL), Thermal Load (T), Wind Load (WL), Snow Load (S), Earthquake Load (EQ), 3.8 Conveyor Belts Tension Loads (BL) , Impact Loads (I)",
                "Load Combination, Load and Resistance Factor Design (LRFD)", 
                "Clearance",
                "Design basis",
                "Materials for structural steel",
                "Materials for concrete structures"
            ]
            
            
            
            cheat_sheet = {}
            for count, criterion in enumerate(criteria, start=1):
                self.add_event(requirement_id , 'progress_message', {
                    'event_type': 'SCP',
                    'request_id': requirement_id ,
                    'data': {'status': 'extracting_sources', 'message': f"Starting analysis for Query {count}. Extracting relevant tender sources.", 'query_idx': count}
                })
                
                source_document_text = self.data_manager.extract_data_specs_comply_v2(requirement['project_id'], criterion)
                cheat_sheet[criterion] = source_document_text
            
            print('cheat sheet: ', cheat_sheet)
            
            
            
        except Exception as e:
            print(f"Error in process_scp : {e}")
            raise
        
    def guess_discipline_and_document_type_from_text(self, text):
        
        document_types = SpecsComplyReasoning.load_document_types()
        disciplines = SpecsComplyReasoning.load_disciplines()
        
        type_text = "".join([f" CODE: {type['code']} - DESCRIPTION: {type['description']}  ," for type in document_types])
        discipline_text = "".join([f" CODE: {discipline['discipline']} - TITLE: {discipline['title']}  ," for discipline in disciplines])
        
        llm = FastAPIs()
        response = llm.generate_completion(
            [
                {
                    "role": "user",
                    "content": f"""
                    You are a, EPC ENGINEER, that can help me pick the right discipline and document type from a given text.
                    Here are the document types:
                    {type_text}
                    Here are the disciplines:
                    {discipline_text}
                    
                    The text is:
                    {text}
                    
                    ### IMPORTANT INSTRUCTION ###
                    Please return the discipline and document type strictly in the following xml format:
                    <RESULT>
                        <DISCIPLINE>
                            <DISCIPLINE_CODE>
                                AH
                            </DISCIPLINE_CODE>
                        </DISCIPLINE>
                        <DOCUMENT_TYPE>
                            <DOCUMENT_TYPE_CODE>
                                ADJ
                            </DOCUMENT_TYPE_CODE>
                        </DOCUMENT_TYPE>
                    </RESULT>
                    
                    """
                }
            ],
            model='llama-3.1-8b-instant'
        )
        # extract the discipline and document type from the response
        discipline = re.search(r'<DISCIPLINE_CODE>(.*?)</DISCIPLINE_CODE>', response, re.DOTALL)
        document_type = re.search(r'<DOCUMENT_TYPE_CODE>(.*?)</DOCUMENT_TYPE_CODE>', response, re.DOTALL)
        
        return discipline.group(1).strip(), document_type.group(1).strip()
        
    def get_document_specification_from_project_text(self, project_text, discipline, document_type):
        llm = FastAPIs()
        # TODO: need to use discipline and document type to pull key focus areas if any
        document_types = SpecsComplyReasoning.load_document_types()
        disciplines = SpecsComplyReasoning.load_disciplines()
        
        # find discipline title from discipline code
        discipline_title = next((d['title'] for d in disciplines if d['discipline'] == discipline), None)
        # find document type title from document type code
        document_type_description = next((d['description'] for d in document_types if d['code'] == document_type), None)
        
        print(f'discipline_title: {discipline_title}')
        print(f'document_type_description: {document_type_description}')
        
        response = llm.generate_completion(
            [
                {
                    "role": "user",
                    "content": f""" 
                    You are a EPC ENGINEER, that can help me identify the document specification from the given text.
                    Here is the document type:
                    {document_type_description}
                    Here is the discipline:
                    {discipline_title}
                    
                    The text is:
                    {project_text}
                    
                    ### IMPORTANT INSTRUCTION ###
                    Please return the document specification strictly in the following xml format:
                    <RESULT>
                        <DOCUMENT_SPECIFICATION>
                            Scope of work should contain the following: 
                             - Scope of work
                             - mechanical requirements
                        </DOCUMENT_SPECIFICATION>
                    </RESULT>
                    """
                }
            ],
            model='llama-3.1-8b-instant'
        )
        # extract the document specification from the response
        document_specification = re.search(r'<DOCUMENT_SPECIFICATION>(.*?)</DOCUMENT_SPECIFICATION>', response, re.DOTALL)
        return document_specification.group(1).strip()
        
    def query_section_spec_compliance_check(self, section, criteria, document_type, discipline):
        messages = [
            {
                "role": "user",
                "content": f'''
                    <REPORT>
                        <DocumentSpecification>{criteria}</DocumentSpecification>
                        <DocumentSection>{section}</DocumentSection>
                        <DocumentType>{document_type}</DocumentType>
                        <Discipline>{discipline}</Discipline>
                    
                    </REPORT>
                    <ROLE>
                        1.) You are an Industrial engineer helpful assistant to do detailed document review of the above document section based on the document specification.
                    </ROLE>
                    <RULE>
                         
                         The report should strictly follow the below format whereby we have the detailed review wrapped in DETAILED_REVIEW tag &  reference wrapped between REFERENCE tag :
                            <DETAILED_REVIEW>
                            </DETAILED_REVIEW>
                            <REFERENCE>
                            </REFERENCE>
                    </RULE>
                    <EXAMPLE>
                        <DETAILED_REVIEW>
                            1. section 4.2 page 16 design life: The submitted centrifugal pump design specification proposes a service life of 25 years, which falls short of the specified minimum design service life requirement of thirty (30) years outlined in the project specification. Additionally, the minimum continuous operation period without the need for shutdown is stated as three (3) years, rather than the required minimum of five (5) years. These deviations from the specified requirements raise concerns regarding the longevity and reliability of the equipment under operational conditions. Further documentation or justification is necessary to ensure compliance with the specified minimum design service life and continuous operation period requirements.
                        </DETAILED_REVIEW>
                        <REFERENCE>
                            Section 4.2 Page 16 - Design Life
                            Within the scope of our engineering and manufacturing excellence, the centrifugal pump has been engineered with a keen focus on durability and operational longevity, achieving a service life of 25 years. This specification underscores our commitment to providing high-quality, durable solutions that support the long-term objectives of our clients. Recognizing the challenges and demands of the operational environments these pumps are destined for, the design incorporates advanced materials and engineering techniques aimed at reducing wear and tear, thereby extending the operational life of the equipment. Additionally, the design facilitates a minimum continuous operation period of three (3) years, a feature meticulously developed to ensure that our clients benefit from reduced maintenance requirements and uninterrupted service. This operational characteristic is vital for ensuring that workflow efficiencies are maximized, and operational costs are minimized, reflecting our dedication to enhancing the value and reliability of our products within their intended application environments.
                        </REFERENCE>
                    </EXAMPLE>
                    <EXAMPLE>
                        <DETAILED_REVIEW>
                            2 Attachment 2 page 34 Environemental Data: The proposed material specifications do not align with the environmental data provided for Canada, especially concerning minimum temperature requirements. For instance, the specified environmental parameters for onshore engineering projects in Canada include a minimum recorded temperature of -35°C, while the proposed material specifications overlook this critical threshold, proposing a minimum temperature of 0°C. This discrepancy raises significant concerns regarding the materials' ability to withstand extreme conditions and maintain performance and integrity. It is imperative to revise the material specifications to align with the provided environmental data, ensuring suitability and durability for the intended application in Canada.  
                        </DETAILED_REVIEW>
                        <REFERENCE>
                            Attachment 2 page 34 - Material Specifications for Canadian Climate Conditions
                            Environmental Compatibility and Performance
                            Our centrifugal pump design has been meticulously engineered to excel in various operational environments, with a specific focus on the challenging climate conditions found in Canada. Understanding the critical nature of environmental adaptability, our specifications are tailored to ensure unparalleled durability and performance, even in extreme weather conditions.
                        </REFERENCE>
                    </EXAMPLE>
                '''
            }    
        ]
     
        llm = ClaudeService()
        response = llm.generate_message_agent_sonnet_sync(
            messages
        )
        
        response = response.content[0].text
        
        print(f'query_section_spec_compliance_check response: {response}')
        
        review = re.search(r'<DETAILED_REVIEW>(.*?)</DETAILED_REVIEW>', response, re.DOTALL)
        reference = re.search(r'<REFERENCE>(.*?)</REFERENCE>', response, re.DOTALL)
        
        print(f'review: {review}')
        print(f'reference: {reference}')



        return review.group(1).strip(), reference.group(1).strip()
        

    async def handle_single_new_document_v2(self, requirement_id):
        try:

            print('calling add event to send message to FE.')

            self.add_event(requirement_id , 'progress_message', {
                'event_type': 'SCP',
                'request_id': requirement_id ,
                'data': {'status': 'extracting_sources', 'message': f"Welcome to Specs Comply Assistant..."}
            })
            # return 

            print('i am now in scp class...')
            requirement = Requirement.get_single(requirement_id)
            
            # Check if the requirement status is already done
            if requirement['status'] == 'done':
                print(f"SCP Requirement with id: {requirement_id} is already done. Exiting...")
                # return  # Exit if the requirement is already done
        

            await self._prepare_context(requirement)

            if requirement is None:
                self.handle_failed_request('', '', '')
            
            print(requirement)
            print('passed requirement successfully...')
            Requirement.update(requirement_id, status='pending')


            print('detect document category successfully.......')
            print(self.document_data)
            

            print('criterion.......')
            print(self.criterion)

            self.report_manager = ReportManager(
                self.env_data.get('SPECS_DIR'), self.project_id, self.requirement_id
            )
            self.report_manager.create_initial_report(self.initialize_log_structure(len(self.criterion)))
            log = self.report_manager.load_report()

            
            for count, criteria in enumerate(self.criterion, start=1):
                await self.process_single_criterion(criteria, count, log)
            
            # tasks = [
            #     asyncio.create_task(self.process_single_criterion(criteria, count, log))
            #     for count, criteria in enumerate(self.criterion, start=1)
            # ]

            # await asyncio.gather(*tasks)

            log['project_title'] = self.project['name']
            await self.specs_reasoning.get_claude_to_summarize_evaluate_v2(log)

            # return 
            #Todo: need to loop through the array and concat all items into the evaluate_summary key
            log['evaluate_summary']  = ''.join(log['evaluation_report_section']['evaluation_report'])
            log['evaluate_report'] = self.create_engineering_comments_table(
                log['evaluation_report_section']['engr_reference'],
                log['evaluation_report_section']['engr_comment']
            )

            # Emit final completion event
            self.add_event(self.requirement_id, 'completed_event', {
                'event_type': 'SCP',
                'request_id': self.requirement_id,
                'data': log
            })

            # self.save_report(log)
            self.report_manager.update_log(log)
            Requirement.update(requirement['id'], status='done')

        except Exception as e:
            print(f"Error returned: {e}")
            Requirement.update(requirement['id'], status='idle', tried=requirement["tried"] + 1)

    async def process_single_criterion(self, criteria, count, log):
        try:
            print('criteria: ', criteria)
            print('count : ', count)
            self.criteria = criteria
            print('i am log: ', log['criteria_array'])
            print('i am criterion: ', self.criterion)
            
            log['criteria_array'][count - 1] = self.criteria
            print(f"now insingle criteria {count} {self.criterion[count - 1]}....")
            # return
            self.add_event(self.requirement_id , 'progress_message', {
                'event_type': 'SCP',
                'request_id': self.requirement_id ,
                'data': {'status': 'extracting_sources', 'message': f"Starting analysis for Query {count}. Extracting relevant tender sources.", 'query_idx': count}
            })

            source_document_text = await self._extract_dataset1(self.criterion[count - 1], count, log)
            if source_document_text is None:
                self.handle_failed_request(self.requirement, "", "")
                return
            
            print(f'source document done for criteria {count} {self.criterion[count - 1]}....')
            # print(source_document_text)

            self.add_event(self.requirement_id , 'progress_message', {
                'event_type': 'SCP',
                'request_id': self.requirement_id ,
                'data': {'status': 'extracting_sources', 'message': f"Continuing analysis for Query {count}. Refining insights from the uploaded bid.", 'query_idx': count}
            })

            data = await self._extract_dataset2(self.criterion[count - 1], source_document_text, count, log)
            if not data:
                raise RuntimeError("Dataset 2 extraction failed")
            
            self.add_event(self.requirement_id , 'progress_message', {
                'event_type': 'SCP',
                'request_id': self.requirement_id ,
                'data': {'status': 'extracting_sources', 'message': f"Insights for Query {count} extracted. Preparing to analyze document chunks.", 'query_idx': count}
            })

            chunks = await self._get_chunks_to_process(count, log)
            log['chunk_to_pass'][count-1] = chunks

            print(f'i am here for criteria {count} {criteria}...')

            evaluate_text = f"<CRITERIA>Criteria {count}<CRITERIA><br/>"
            evaluate_text_2 = ""
            evaluate_reference = ""
            for evaluate in chunks:
                evaluate_text += f"""
                    <Evaluation>{evaluate['evaluation_data']['Evaluation']}</Evaluation><br/>
                    <Score>{evaluate['evaluation_data']['Score']}</Score><br/>
                    <Reason>{evaluate['evaluation_data']['Reason']}</Reason><br/>
                    <Reference>{evaluate['evaluation_data']['Reference']}</Reference><br/>
                """
                evaluate_text_2 += f"""
                    <Reason>{evaluate['evaluation_data']['Reason']}</Reason><br/>
                    <Reference>{evaluate['evaluation_data']['Reference']}</Reference><br/>
                """
                evaluate_reference += f"""
                    <Reason>{evaluate['evaluation_data']['Reason']}</Reason><br/>
                """
            
            print('now here also......')
            
            log['evaluation_report_section']['engr_reference'][count-1] = evaluate_reference

            # print('Chunk to pass: ', chunk_to_pass)
            print(f'now passing the criteria {count} {self.criterion[count - 1]}')
            await self.specs_reasoning.get_claude_to_give_non_compliance_comment(evaluate_text_2, self.criterion[count - 1], count, log)
            
            log['evaluate_summary_processing'][count-1] = f"{evaluate_text}<br/>"

            log['evaluate_summary'] += log['evaluation_report_section']['evaluation_report'][count-1]

            self.add_event(self.requirement_id, 'in_progress_event', {
                'event_type': 'SCP',
                'request_id': self.requirement_id,
                'data': log
            })

            self.report_manager.update_log(log)
            # self.save_report(log)
          

        except Exception as e:
            print(f"Error in process_single_criterion: {e}")
            Requirement.update(self.requirement['id'], status='idle', tried=self.requirement["tried"] + 1)

    def handle_failed_request(self, requirement, data, responses):
        report = {
            "total_factors": "",
            "positive_factors": "",
            "details": data,
            "reasoning_summary": "",
            "source_map": responses,
            "evaluate_summary": "The Source Document does not return any data based on the criteria provided."
        }
        report_path = os.path.join(
            self.env_data.get('DATA_DIR'),
            requirement.get("project_id", ""),
            f"{requirement.get('id', requirement)}.json"
        )
        self.save_json(report_path, report)

        if requirement:
            Requirement.update(requirement['id'], status='idle', tried=requirement["tried"] + 1)

    async def _extract_dataset1(self, criteria, count, log):
        extractor = Dataset1Extractor(criteria)
        return await extractor.get_dataset1(self.project_id, count, log)
    
    async def _extract_dataset2(self, criteria, source_text, count, log):
        return await self.dataset2_extractor.process_new_file_SpecsComply(
            criteria, source_text, count, self.requirement_id, log
        )

    async def _get_chunks_to_process(self, count, log):
        for key in ['strength_chunk', 'weak_chunk', 'risk_chunk']:
            print(f"this is the key: {key} and log: {log['evaluation_report_section'][key]} ")
            chunk_list = log['evaluation_report_section'].get(key, [])
            print(f"condition check: {chunk_list}")
            if len(chunk_list) > count - 1 and chunk_list[count - 1]:
                print('the chunklist: ', chunk_list[count - 1])
                return chunk_list[count - 1]
        # Return an empty list if no valid chunk is found
        return []
    
    def create_engineering_comments_table(self, engr_references, engr_comments):
        # print(f"engr comment: {engr_comments}")
        # print(f"engr_references: {engr_references}")
        # Create the HTML table
        html_table = """
        <table border="1">
            <tr>
                <th>Comment #</th>
                <th>Engineering Comment</th>
                <th>Reference</th>
                <th>Contractor Feedback</th>
            </tr>
        """
        
        # Add rows for each comment and reference
        for idx, (comment, reference) in enumerate(zip(engr_comments, engr_references), start=1):
            contractor_feedback = "????"

            html_table += f"""
                <tr>
                    <td>Item #{idx}</td>
                    <td>{comment}</td>
                    <td>{reference}</td>
                    <td>{contractor_feedback}</td>
                </tr>
            """
        
        # Close the table
        html_table += "</table>"
        
        return html_table

    def save_report(self, log):
        report_path = os.path.join(
            self.env_data.get('SPECS_DIR'),
            self.project_id,
            f"{self.requirement_id}.json"
        )
        print(f"Writing report to {report_path}")
        self.save_json(report_path, log)

    def save_json(self, path, data):
        with open(path, 'w', encoding='utf8') as json_file:
            json.dump(data, json_file, ensure_ascii=False)

    async def _prepare_context(self, requirement):
        self.requirement = requirement
        self.requirement_id = requirement["id"]
        self.requirement_name = requirement["name"]
        self.tender_title = self._remove_extension(self.requirement_name)
        self.tender_number = requirement["id"]
        self.bidder_name = '[BIDDER NAME]'
        self.project = Project.get_single(requirement["project_id"])
        self.project_id = requirement['project_id']
        self.file_path = os.path.join(self.env_data.get('SPECS_DIR'), self.project_id, self.requirement_name)
        self.document_data = await self.engr_doc_detector.detect_document_category_from_document_pages(
            self.requirement_id
        )
        self.criterion = self.document_data.get("document_formatted_criteria")[:6]
        self.document_number = self.document_data.get("document_number")
        self.document_discipline = self.document_data.get("document_discipline")
        self.document_deliverable = self.document_data.get("document_deliverable")
        
        print('i am the criterion: ', self.criterion)
        # sys.exit()
        # self.criterion = self.convert_criteria_to_dict(
        #     self.document_data.get(self.requirement['category'], {}).get("criteria", [])
        # )[:3]

    
    def initialize_log_structure(self, criteria_count):
        return {
            "criteria": "",
            "criteria_array": [""] * criteria_count,
            "reasoning_summary": [""] * criteria_count,
            "evaluate_summary": "",
            "evaluate_summary_intro": "",
            "evaluate_summary_code": "",
            "evaluate_summary_array": [""] * criteria_count,
            "evaluate_summary_processing": [""] * criteria_count,
            "project_title": "",
            "document_number": [],
            "document_discipline": [],
            "document_deliverable": [],
            "document_title": self._remove_extension(self.requirement_name),
            "chunk_to_pass": [""] * criteria_count,
            "evaluation_report_section": {
                "introduction": "",
                "strength_chunk": [""] * criteria_count,
                "weak_chunk": [""] * criteria_count,
                "risk_chunk": [""] * criteria_count,
                "engr_comment": [""] * criteria_count,
                "engr_reference": [""] * criteria_count,
                "contractor_feedback": "????",
                "strength_text": "",
                "weak_text": "",
                "risk_text": "",
                "detailed_evaluate_text": "",
                "recommendation_text": "",
                "strength_weak_risk_chunk_intro": {},
                "evaluation_report": [""] * criteria_count,
                "evaluation_result": ""
            }
        }

    async def process_single_request(self, request_id):
        await self.handle_single_new_document_v2(request_id)

    def convert_criteria_to_dict(self, criteria):
        criteria_list = [item.strip() for item in criteria[0].split(',')]
        weight = round(100 / len(criteria_list), 2)

        return [
            {"name": item, "description": item, "weight": weight} for item in criteria_list
        ]

    def _remove_extension(self, file_name):
        base_name = os.path.splitext(file_name)[0]
        pattern = r'_(\d{6})_merged$'        
        modified_name = re.sub(pattern, '', base_name)
        
        return modified_name
    
    def add_event(self, request_id, event_name, data):
        # return
        print('now emitting event for the client via scp..', request_id, event_name, data)
        # self.socket_manager.emit_to_client(request_id, event_name, data, '/scp')
        
    async def load_request_vendor_files(self, request_id):
        docs = ["vendor_file.pdf"]
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        vendor_dir = parent_dir
        for doc in docs:
            file_path = os.path.join(vendor_dir, doc)
            file_type = doc.split('.')[-1]
            if file_type == 'pdf':
                data = await self.file_processor.extract_text_from_pdf(file_path)
            else:
                data = await self.file_processor.extract_text_from_docx(file_path)
                
        print('data: ', len(data[1]))
                
        # Return first 5 sections of data
        return data[1][20:25]
    
    async def query_section_compliance_check(self, request_id, criteria, discipline_summary):
        sections = await self.load_request_vendor_files(request_id)
        section_results = []
        for count, section in enumerate(sections):
            result = await self.specs_reasoning.query_section_compliance_check(section, criteria, discipline_summary)
            print('count: ', count)
            print('result: ', result)
            section_results.append({"section": count, "result": result})
            
        
        return section_results
    
    def load_project_files_by_key_area(self, project_id, key_area):
        pass
    

if __name__ == "__main__":
    
    # from socket_instance import socket_instance
    # socket_manager = socket_instance.get_instance()
        # Test emit
    # socket_manager.emit_to_client(
    #     req_id,
    #     'test_event',
    #     {'message': 'Test from SCP Celery'},
    #     namespace='/processing_files'
    # )
    # scp_loader = SpecsDocumentProcessor(socket_manager)
    from init import app
    with app.app_context():
        
        
        
        # 1e7c56ca-9fc6-49f3-8967-db5e815f5c0e
        processor = SpecsDocumentProcessor(None)
        processor.run_scp('fb0d8a8d-3ada-428e-a326-b3cadd6de049')
        # asyncio.run(processor.queue())
        # asyncio.run(processor.process_single_request('d6af81ab-e4df-433d-8bdd-0f11bdb6f8e9'))
        # asyncio.run(processor.query_section_compliance_check(
        #     'd6af81ab-e4df-433d-8bdd-0f11bdb6f8e9',
        #     'Comprehensive inventory of all electrical equipment\nSpecifications including manufacturer, model, and key parameters\nInstallation locations and associated systems\nMaintenance requirements', 
        #     f'''
        #     '''))
