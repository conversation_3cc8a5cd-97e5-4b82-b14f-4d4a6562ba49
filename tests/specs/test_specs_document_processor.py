import json
import uuid
from services.handle_new_specs_documents import SpecsDocumentProcessor
from models import Requirement

class SpecsComplyProcessorManager:
    def __init__(self, data):
        self.data = data
        self.processor = SpecsDocumentProcessor()

    def create_sample_request(self, file_name, project_id, category, discipline):
        request_id = str(uuid.uuid4())
        create_kwargs = {
            'id': request_id,
            'name': file_name,
            'project_id': project_id,
            'file_type': file_name.rsplit('.', 1)[1].lower(),
            'category': category,
            'discipline': discipline
        }
        Requirement.create(**create_kwargs)
        return request_id

    def process_requests(self, data):

        # Process each item in the JSON file
        for item in data:
            project_id = item.get('project_id')
            category = item.get('category')
            discipline = item.get('discipline')
            file_name = item.get('file_name')

            # Create a requirement record and get the request_id
            request_id = self.create_sample_request(file_name, project_id, category, discipline)

            # Process the requirement using the processor
            self.processor.process_single_request(request_id)

    def run(self):
        self.process_requests(self.data)


# # Usage
# if __name__ == "__main__":
    # processor_manager = SpecsComplyProcessorManager('specscomply_data.json')
    # processor_manager.run()
