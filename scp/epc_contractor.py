import sys
import os
import re
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models import File, Project, Chunks, Tags, Requirement
from sqlalchemy import or_
import uuid
import json
import anthropic
from typing import Dict, List, Any
from services.data_handler import DataManager
import pandas as pd
from services.pinecone_vector_db import CanopyAI
import requests

class EPCContractor:
    def __init__(self, req_id, socket_manager, package_id, vendor_id, vendor_file_id):
        self.req_id = req_id
        self.socket_manager = socket_manager
        self.package_id = package_id
        self.vendor_id = vendor_id
        self.vendor_file_id = vendor_file_id
        self.cheat_sheets = {}
        self.working_doc = {}
        self.comparison_summary = {}
        self.ai_comparison_results = {}
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.env_file_path = os.path.join(parent_dir, 'env.json')
        self.setup_agent()
        self.data_manager = DataManager()
        self.vec_db = CanopyAI()
        
        # Get package information
        package = Project.get_by(id=package_id, entity_type='tender')
        if not package:
            raise ValueError(f"Package with ID {package_id} not found")
        
        # Get files for package and specific vendor file
        self.package_files = File.get_by(project_id=package_id)
        self.vendor_files = File.get_by(project_id=vendor_id)
        self.vendor_file = File.get_by(id=vendor_file_id)[0]
        print(f"Vendor file: {self.vendor_file}")
        
        # Read the Area of Focus Excel file
        excel_path = os.path.join(os.path.dirname(__file__), 'Area of focus.xlsx')
        self.requirements_df = pd.read_excel(excel_path)

        print(f"Vendor file: {self.vendor_file['id']}")
        self.section_ids = [chunk['id'] for chunk in Chunks.get_by(file_id=self.vendor_file['id'])]
        print(f"Section IDs: {self.section_ids}")

    def load_env(self):
        with open(self.env_file_path, 'r') as f:
            return json.load(f)
        
    def setup_claude_client(self, api_key):
        self.client = anthropic.Anthropic(api_key=api_key)

    def setup_agent(self):
        env_data = self.load_env()
        self.setup_claude_client(env_data.get('CLAUDE_API_KEY', ''))

    def _extract_vdrl_code(self, file):
        """Extract VDRL code from file name or metadata"""
        file_name = file['name']
        match = re.search(r'-[A-Za-z]\d{2}-', file_name)
        print(f"Match: {match.group(0)[1:-1]}")
        if match:
            return match.group(0)[1:-1]
        return None

    def add_event(self, request_id, event_name, data):
        # return
        self.socket_manager.emit_to_client(request_id, event_name, data, '/epc')

    def get_areas_focus_by_vdrl(self, vdrl_code):
        """Get the areas of focus for a given VDRL code."""
        try:
            headers = {
                "x-project": "YWllbmVyZ3k6aHNxdHJ0cDdoc3dpamp6dWU3c2N3emV4ZDhodTRh",
                "Content-Type": "application/json", 
                "Authorization": ""
            }
            
            payload = {
                "email": "<EMAIL>",
                "password": "a123456",
                "role": "user"
            }

            url = "https://backend.aienergy-oilandgas.com/v3/api/custom/aienergy/area-focus/vendor-documents"
            
            response = requests.get(
                url,
                headers=headers,
                data=json.dumps(payload)
            )
            response.raise_for_status()
            data = response.json()
            
            # Search through the data to find matching VDRL code
            for item in data['list']['data']:
                if item['vdrl_code'] == vdrl_code:
                    return item['areas_focus']
            return None
        
        except Exception as e:
            print(f"Error occurred: {e}")
            return None
        
    def _get_requirements_for_vdrl(self, vdrl_code):
        """Get minimum requirements for a given VDRL code"""
        # Filter requirements dataframe for the given VDRL code

        areas_focus = self.get_areas_focus_by_vdrl(vdrl_code)

        if not areas_focus:
            vdrl_requirements = self.requirements_df[
                self.requirements_df['VDRL CODE'] == vdrl_code
            ]
            print(f"VDRL requirements: {vdrl_requirements}")
            # Convert minimum requirements to list and handle empty case
            minimum_requirements = vdrl_requirements['MINIMUM REQUIREMENTS'].tolist()
            print(f"Minimum requirements: {minimum_requirements}")
        else:
            minimum_requirements = [areas_focus]
        
        # Generate expanded queries using Claude
        expanded_queries = []
        if minimum_requirements:
            prompt = f"""
            Given these minimum requirements for technical documentation:
            {minimum_requirements[0]}

            Generate specific search queries that would help find relevant content in technical documents. 
            Focus on key technical terms and specifications.
            Format each query on a new line.
            Keep queries concise but descriptive.
            Do not include variations of technical terminology.
            """
            
            try:
                response = self.client.messages.create(
                    model="claude-3-haiku-20240307",
                    max_tokens=500,
                    temperature=0.15,
                    messages=[{
                        "role": "user",
                        "content": prompt
                    }]
                )
                
                if response.content:
                    # Split response into individual queries
                    expanded_queries = [q.strip() for q in response.content[0].text.split('\n') if q.strip()]
                    
            except Exception as e:
                print(f"Error generating expanded queries: {e}")
                # Fallback to original requirements
                expanded_queries = minimum_requirements

        # Combine original and expanded queries
        search_queries = expanded_queries
        print(f"Search queries: {search_queries}")
        
        return search_queries

    def compare_requirements(self):
        """Compare vendor responses against minimum requirements using vector search"""
        try:
            # Initialize results storage
            comparison_results = []
            
            # Extract VDRL code from file name or metadata
            vdrl_code = self._extract_vdrl_code(self.vendor_file)
            if not vdrl_code:
                return []
            
            # Get requirements for this VDRL code
            areas_of_focus = self._get_requirements_for_vdrl(vdrl_code)
            print(f"\n\n\nRequirements: {areas_of_focus}")
            
            # Process each requirement
            for area_of_focus in areas_of_focus:
                # Query tender documents
                tender_results = self.vec_db.get_data(self.package_id, [area_of_focus], limit=3)
                
                # Query vendor documents
                vendor_results = self.vec_db.get_data_by_section_ids(self.vendor_id, [area_of_focus], self.section_ids, limit=3)
                
                print(f"Tender results: {tender_results}")
                print(f"Vendor results: {vendor_results}")
                
                # Extract relevant content from results
                tender_content = []
                if tender_results and tender_results['metadatas']:
                    for metadata_list in tender_results['metadatas']:
                        for metadata in metadata_list:
                            if 'documents' in metadata:
                                tender_content.append(metadata['documents'])
                
                vendor_content = []
                if vendor_results and vendor_results['metadatas']:
                    for metadata_list in vendor_results['metadatas']:
                        for metadata in metadata_list:
                            if 'documents' in metadata:
                                vendor_content.append(metadata['documents'])
                
                # Prepare comparison result
                comparison = {
                    'area_of_focus': area_of_focus,
                    'file_name': self.vendor_file['name'],
                    'tender_content': tender_content,
                    'vendor_content': vendor_content,
                }
                
                comparison_results.append(comparison)
            
            return comparison_results
            
        except Exception as e:
            print(f"Error comparing requirements: {e}")
            return []

    def generate_evaluation_summary(self, comparison_results):
        """Generate a comparison analysis using Claude to evaluate the results"""
        try:
            # Prepare the prompt for Claude
            prompt = """
            You are an expert in analyzing technical requirements and vendor responses.
            Please analyze the following comparison data and provide a detailed evaluation of the vendor's response to the tender requirements.
            
            Format your response in HTML with the following structure:
            
            <div>
                <h3>Area of Focus 1: [Exact Area of Focus Name]</h3>
                <ul>
                    <li><h5>Score: <span class='score'><Score>X%</Score></span></h5></li>
                    <li><h5 class='reason highlight'>Reason: [Detailed analysis of vendor response vs tender requirements, including specific examples, gaps, and impact assessment]</h5></li>
                    <li><h5 style="font-size: 14px; color: #006699; margin: 0" class='criteria_<area_of_focus_name>_evaluation_summary'>[Concise summary of evaluation with key findings and recommendations]</h5></li>
                </ul>
            </div>

            <div>
                <h3>Overall Evaluation Summary</h3>
                <ul>
                    <li><h5>Total Score: <span class='score'><Score>X%</Score></span></h5></li>
                    <li><h5 class='reason highlight'>[Comprehensive summary of all areas of focus evaluations, highlighting key strengths, weaknesses, and critical recommendations]</h5></li>
                </ul>
            </div>

            IMPORTANT FORMATTING RULES:
            - Title should be size 18
            - Section headers should be size 15
            - Body text should be 11-12 size
            - No text should be bold
            - Maintain exact HTML structure and tag hierarchy
            - Use exact tag names as provided
            - Include % symbol for all scores
            - For partial information scenarios, use the 'highlight-red' class
            - Keep exact casing of area of focus names as provided

            The analysis must include:
            * Comprehensive breakdown of ALL requirements from bid and tender analysis
            * Exhaustive analysis of each equipment item, examining every relevant detail and component
            * Rigorous gap analysis comparing tender requirements vs bid submissions
            * Meticulous explanation of scoring rationale, supported by concrete examples
            * Extensive evidence and direct quotes from both tender and bid analysis
            * Detailed examples demonstrating compliance or non-compliance
            * In-depth assessment of how any gaps impact project success
            * Specific recommendations for addressing deficiencies

            Here is the comparison data to analyze:
            """
            
            # Add comparison data to prompt
            for result in comparison_results:
                prompt += f"\n\nArea of Focus: {result['area_of_focus']}"
                prompt += f"\nVendor File: {result['file_name']}"
                prompt += f"\nTender Content: {' '.join(result['tender_content'])}"
                prompt += f"\nVendor Content: {' '.join(result['vendor_content'])}"
                prompt += "\n---"
            
            # Get analysis from Claude
            response = self.client.messages.create(
                model="claude-3-haiku-20240307",
                max_tokens=4000,
                temperature=0.3,
                system="You are an expert in analyzing technical requirements, tender responses and vendor responses. Provide clear, detailed evaluations in a structured text format. Explain each point in detail.",
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )
            
            # Return the text response directly
            return response.content[0].text
                
        except Exception as e:
            print(f"Error generating evaluation: {e}")
            return f"Error generating evaluation: {str(e)}"

    def process_requirements_comparison(self):
        """Main method to process requirements comparison and generate analysis"""
        try:
            # Step 1: Compare requirements using vector search
            comparison_results = self.compare_requirements()

            if not comparison_results:
                return {
                    "error": "No comparison results generated"
                }
            
            
            analysis = self.generate_evaluation_summary(comparison_results)
            self.add_event(self.req_id, 'completed', {'message': analysis})
            print(f"Analysis: \n{analysis}")
            return {
                "analysis": analysis
            }
            
        except Exception as e:
            print(f"Error in process_requirements_comparison: {e}")
            return {
                "error": str(e)
            }

if __name__ == "__main__":
    contractor = EPCContractor("53dc535d-2325-4a5b-a01d-46e6dd3f53d5", None, "12345", "67890")
    results = contractor.process_requirements_comparison()
    print(json.dumps(results, indent=2))
    # Store results in a JSON file
    output_file = f"comparison_results.json"
    try:
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"Results saved to {output_file}")
    except Exception as e:
        print(f"Error saving results to file: {e}")