import json
import logging
from typing import Any, Dict, List, Optional, Union

import pydgraph
import grpc




class DgraphService:
    """
    A service class for interacting with Dgraph database.
    
    This service provides methods for:
    - Managing connections to Dgraph
    - Executing GraphQL and DQL queries
    - Performing mutations
    - Schema operations
    - Transaction management
    """

    def __init__(
        self,
        hosts: List[str] = None,
        port: int = 8080,
        credentials = None,  # Removed type hint for credentials
        secure: bool = False,
        timeout: int = 30,
    ):
        """
        Initialize the Dgraph service.
        
        Args:
            hosts: List of Dgraph Alpha addresses (default: ["localhost"])
            port: Dgraph Alpha port
            credentials: Optional credentials for secure connections
            secure: Whether to use a secure connection
            timeout: Connection timeout in seconds
        """
        self.hosts = hosts or ["localhost"]
        self.port = port
        self.credentials = credentials
        self.secure = secure
        self.timeout = timeout
        self.client = None
        self.stubs = []
        
        # Connect to Dgraph
        self.connect()
    
    def connect(self) -> None:
        """Establish connection to Dgraph server."""
        try:
            # Close existing connections if any
            self.close()
            
            # Create stubs
            for host in self.hosts:
                address = f"{host}:{self.port}"
                if self.secure:
                    if not self.credentials:
                        raise ValueError("Credentials required for secure connection")
                    # Use the credentials directly with the stub
                    stub = pydgraph.DgraphClientStub(address, self.credentials)
                else:
                    # For insecure connections
                    stub = pydgraph.DgraphClientStub(address)
                self.stubs.append(stub)
            
            # Create client
            self.client = pydgraph.DgraphClient(*self.stubs)
            print(f"Connected to Dgraph at {', '.join(self.hosts)}")
        except Exception as e:
            print(f"Failed to connect to Dgraph: {str(e)}")
            raise
    
    def close(self) -> None:
        """Close all connections to Dgraph."""
        for stub in self.stubs:
            try:
                stub.close()
            except Exception as e:
                print(f"Error closing Dgraph stub: {str(e)}")
        self.stubs = []
        self.client = None
    
    def set_schema(self, schema: str) -> Dict[str, Any]:
        """
        Set the Dgraph schema.
        
        Args:
            schema: Schema definition string
            
        Returns:
            Response from Dgraph
        """
        try:
            operation = pydgraph.Operation(schema=schema)
            return self.client.alter(operation)
        except Exception as e:
            print(f"Failed to set schema: {str(e)}")
            raise
    
    def drop_all(self) -> Dict[str, Any]:
        """
        Drop all data and schema from the Dgraph database.
        
        Returns:
            Response from Dgraph
        """
        try:
            operation = pydgraph.Operation(drop_all=True)
            return self.client.alter(operation)
        except Exception as e:
            print(f"Failed to drop all data: {str(e)}")
            raise
    
    def execute_query(
        self, 
        query: str, 
        variables: Optional[Dict[str, Any]] = None,
        read_only: bool = True
    ) -> Dict[str, Any]:
        """
        Execute a DQL query.
        
        Args:
            query: DQL query string
            variables: Optional variables for the query
            read_only: Whether the query is read-only
            
        Returns:
            Query results
        """
        try:
            txn = self.client.txn(read_only=read_only)
            try:
                if variables:
                    res = txn.query(query, variables=variables)
                else:
                    res = txn.query(query)
                
                if read_only:
                    # For read-only transactions, we can discard
                    txn.discard()
                else:
                    # For read-write transactions, we need to commit
                    txn.commit()
                
                return json.loads(res.json)
            except Exception as e:
                # If an error occurs, discard the transaction
                txn.discard()
                raise
            finally:
                # Always discard the transaction to be safe
                # This is a no-op if already committed or discarded
                txn.discard()
        except Exception as e:
            print(f"Query execution failed: {str(e)}")
            raise
    
    def execute_mutation(
        self, 
        mutation_data: Union[Dict[str, Any], str], 
        commit_now: bool = True,
        is_json: bool = True
    ) -> Dict[str, Any]:
        """
        Execute a mutation.
        
        Args:
            mutation_data: Mutation data (JSON object or RDF string)
            commit_now: Whether to commit the transaction immediately
            is_json: Whether the mutation data is in JSON format
            
        Returns:
            Mutation response
        """
        try:
            txn = self.client.txn()
            try:
                if is_json:
                    if isinstance(mutation_data, dict):
                        mutation_data = json.dumps(mutation_data)
                    # Create a mutation object with the JSON data
                    mu = pydgraph.Mutation(set_json=mutation_data.encode('utf8'))
                    response = txn.mutate(mu, commit_now=commit_now)
                else:
                    # RDF format
                    mu = pydgraph.Mutation(set_nquads=mutation_data)
                    response = txn.mutate(mu, commit_now=commit_now)
                
                if not commit_now:
                    txn.commit()
                
                return {
                    "uids": dict(response.uids),
                    "metrics": {
                        "latency": response.latency,
                    }
                }
            except Exception as e:
                # If an error occurs, discard the transaction
                txn.discard()
                raise
            finally:
                # Always discard the transaction to be safe
                # This is a no-op if already committed or discarded
                txn.discard()
        except Exception as e:
            print(f"Mutation failed: {str(e)}")
            raise
    
    def upsert(
        self, 
        query: str, 
        mutation_data: Union[Dict[str, Any], str],
        variables: Optional[Dict[str, Any]] = None,
        is_json: bool = True
    ) -> Dict[str, Any]:
        """
        Perform an upsert operation (conditional mutation).
        
        Args:
            query: DQL query to identify nodes
            mutation_data: Mutation data
            variables: Optional variables for the query
            is_json: Whether the mutation data is in JSON format
            
        Returns:
            Upsert response
        """
        try:
            txn = self.client.txn()
            try:
                if is_json:
                    if isinstance(mutation_data, dict):
                        mutation_data = json.dumps(mutation_data)
                    mutation = txn.create_mutation(set_json=mutation_data)
                else:
                    mutation = txn.create_mutation(set_nquads=mutation_data)
                
                request = txn.create_request(query=query, mutations=[mutation], commit_now=True)
                if variables:
                    request.vars.update(variables)
                
                response = txn.do_request(request)
                return json.loads(response.json)
            except Exception as e:
                # If an error occurs, discard the transaction
                txn.discard()
                raise
            finally:
                # Always discard the transaction to be safe
                # This is a no-op if already committed or discarded
                txn.discard()
        except Exception as e:
            print(f"Upsert operation failed: {str(e)}")
            raise
    
    def delete_nodes(self, uids: List[str], commit_now: bool = True) -> Dict[str, Any]:
        """
        Delete nodes by their UIDs.
        
        Args:
            uids: List of UIDs to delete
            commit_now: Whether to commit the transaction immediately
            
        Returns:
            Deletion response
        """
        try:
            txn = self.client.txn()
            try:
                # Create a deletion mutation
                deletion = [
                    {
                        "uid": uid,
                        "dgraph.type": None
                    } for uid in uids
                ]
                
                deletion_json = json.dumps({"delete": deletion})
                mu = pydgraph.Mutation(delete_json=deletion_json)
                response = txn.mutate(mu, commit_now=commit_now)
                
                if not commit_now:
                    txn.commit()
                
                return {
                    "uids": dict(response.uids),
                    "metrics": {
                        "latency": response.latency,
                    }
                }
            except Exception as e:
                # If an error occurs, discard the transaction
                txn.discard()
                raise
            finally:
                # Always discard the transaction to be safe
                # This is a no-op if already committed or discarded
                txn.discard()
        except Exception as e:
            print(f"Node deletion failed: {str(e)}")
            raise
    
    def execute_graphql_query(
        self, 
        query: str, 
        variables: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Execute a GraphQL query.
        
        Args:
            query: GraphQL query string
            variables: Optional variables for the query
            
        Returns:
            GraphQL query results
        """
        try:
            # Convert to DQL and execute
            # Note: This is a simplified approach. In a real implementation,
            # you might want to use the /graphql endpoint directly.
            
            # Replace double quotes with escaped double quotes, but avoid using backslashes in f-strings
            escaped_query = query.replace('"', '\\"')
            dql_query = f"""
            query {{
                graphql(query: "{escaped_query}")
                {f", variables: {json.dumps(variables)}" if variables else ""}
            }}
            """
            
            result = self.execute_query(dql_query)
            return result.get("graphql", {})
        except Exception as e:
            print(f"GraphQL query execution failed: {str(e)}")
            raise
    
    def check_health(self) -> bool:
        """
        Check if the Dgraph connection is healthy.
        
        Returns:
            True if healthy, False otherwise
        """
        try:
            # Simple health check query
            result = self.execute_query("{ health { status } }")
            status = result.get("health", {}).get("status")
            return status == "healthy"
        except Exception as e:
            print(f"Health check failed: {str(e)}")
            return False
    
    def __enter__(self):
        """Support for context manager."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Clean up resources when exiting context."""
        self.close()


# Example usage
if __name__ == "__main__":
    # Example schema
    EXAMPLE_SCHEMA = """
    type Person {
        name: string @index(exact) .
        age: int .
        friend: [Person] @reverse .
    }
    """
    
    # Initialize service
    dgraph = DgraphService()
    
    # Set schema
    dgraph.set_schema(EXAMPLE_SCHEMA)
    
    # Add data
    mutation = {
        "set": [
            {
                "dgraph.type": "Person",
                "name": "Alice",
                "age": 30,
                "friend": [
                    {
                        "dgraph.type": "Person",
                        "name": "Bob",
                        "age": 32
                    }
                ]
            }
        ]
    }
    
    result = dgraph.execute_mutation(mutation)
    print(f"Mutation result: {result}")
    
    # Query data
    query = """
    {
        people(func: type(Person)) {
            uid
            name
            age
            friend {
                name
                age
            }
        }
    }
    """
    
    result = dgraph.execute_query(query)
    print(f"Query result: {json.dumps(result, indent=2)}")
    
    # Close connection
    dgraph.close()
    
    
    
    
"""  


# Install dgraph

curl https://get.dgraph.io -sSf | bash

# Start up dgraph

dgraph zero

dgraph alpha --zero localhost:5080

# Start up dgraph-ratel

docker run --rm -it -p 8000:8000 dgraph/ratel:latest

"""
