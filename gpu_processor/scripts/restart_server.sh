#!/bin/bash

# Define variables
PROJECT_DIR="/workspace/gpu_processor"
VENV_DIR="$PROJECT_DIR/venv/bin"
LOG_DIR="$PROJECT_DIR/logs"
GUNICORN_CMD="$VENV_DIR/gunicorn --bind 0.0.0.0:5120 app:app --worker-class eventlet --timeout 300 --workers 2 --access-logfile $LOG_DIR/server.log --error-logfile $LOG_DIR/server.log --capture-output --log-level debug --max-requests 200 --max-requests-jitter 30 --daemon"

echo "Checking for existing Gunicorn processes on port 5120..."
PIDS=$(ss -tulnp | grep ":5120" | grep -oP '(?<=pid=)\d+')

if [ -n "$PIDS" ]; then
    echo "Stopping Gunicorn processes..."
    echo "$PIDS" | xargs -r kill -9
    echo "Gunicorn processes stopped."
else
    echo "No Gunicorn processes found on port 5120."
fi

sleep 2

# Navigate to project directory
echo "Activating virtual environment..."
source "$VENV_DIR/activate"

cd "$PROJECT_DIR"

# Restart Gunicorn
echo "Starting Gunicorn..."
eval "$GUNICORN_CMD"

echo "✅ Gunicorn restarted successfully!"
