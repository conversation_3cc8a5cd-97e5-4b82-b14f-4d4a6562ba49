'''
TODO: This script job is to be called as a cron job with following steps:
- the argv parameter --env env.json path pass in to read. Inside env.json there are keys: userID, companyID, Password, ProjectNo, OneTimePassword, Application
- we will have a main() function called to handle the logic of the script
- We will call session api to get the session key with this curl command:
curl -X POST --header 'Content-Type: application/json' --header 'Accept: application/json' -d '{ \ 
   "UserID": "AIESVC", \ 
   "CompanyID": "DRA", \ 
   "Password": "St%40c^JHp^ZATt5#Q&#33;%40", \ 
   "ProjectNo": "DRATRG001", \ 
   "oneTimePassword": "", \ 
   "Application": "test" \ 
 }' 'https://eu1.doc.ineight.com/TBReportingAPI/Session'

 this returns 201 http code and session key in the response

- then we call the directory api to get all the files metadata 
curl -X GET --header 'Accept: application/json' --header 'Authorization: basic <session_key>' 'https://eu1.doc.ineight.com/TBReportingAPI/Report/04.001a'

- The response format is like this:
[
  {
    "Int_Key": "2550",
    "CheckedOutValue": "No",
    "CheckedOut": "0",
    "DocumentNo": "00002                                    ",
    "Rev": "5    ",
    "Sts": "DFT  ",
    "Title": "Agenda 07.02.2023 - Test versioning - 0.03",
    "Discipline": "CO        ",
    "Category": "DOC       ",
    "Type": "AGA       ",
    "AvlDwgFmts": "1",
    "Int_Apprvd": "-1",
    "RvwStatus": "Un-Restrained",
    "FromId": "LCONWAY   ",
    "Approved": "2024-04-24T08:29:13.21",
    "FirstName": "Lee",
    "LastName": "Conway",
    "Company": "DRA Global",
    "FromUser": "Lee Conway (DRA Global)",
    "DocumentSender": "",
    "CheckedOutBy": "",
    "Dt_Checkd": "1900-01-01T00:00:00",
    "fcDocHst_VWFILECNT": "  Building 33 / The Woodlands Office Park 20 Woodlands Drive / Woodlands Sandton / South Africa / 2080 draglobal.com A0 ",
    "Latest": "2550",
    "Superseded": "N",
    "RowNum": 1,
    "ChkOutAcc": "1",
    "DocAccess": "15",
    "DwgFormat": "1",
    "Format1Access": "5",
    "Format2Access": "0",
    "Format4Access": "0",
    "Format8Access": "0",
    "FormatCaption1": "PDF",
    "FormatCaption2": "MSO",
    "FormatCaption3": "DWG",
    "FormatCaption4": "Other",
    "FormatAccess1": "5",
    "FormatAccess2": "0",
    "FormatAccess3": "0",
    "FormatAccess4": "0"
  }
]

- Save this response to a json with naming convention: <ineight>_dra_<timestamp>.json
'''

import argparse
import json
import requests
from datetime import datetime
import sys
import os

def read_env_file(env_path):
    """Read and parse the environment JSON file"""
    try:
        with open(env_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error reading environment file: {e}")
        sys.exit(1)

def get_session_key(env_config):
    """Get session key from iEight API"""
    # session_url = 'https://eu1.doc.ineight.com/TBReportingAPI/Session'
    session_url = f"{env_config['base_url']}/TBReportingAPI/Session"
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
    payload = {
        'UserID': env_config['userID'],
        'CompanyID': env_config['companyID'],
        'Password': env_config['Password'],
        'ProjectNo': env_config['ProjectNo'],
        'oneTimePassword': env_config.get('OneTimePassword', 'string'),
        'Application': env_config['Application']
    }
    try:
        response = requests.post(session_url, headers=headers, json=payload)
        if response.status_code == 201:
            return response.text.strip()  # Session key is returned in response body
        else:
            print(f"Failed to get session key. Status code: {response.status_code}")
            print(f"Response: {response.text}")
            sys.exit(1)
    except Exception as e:
        print(f"Error getting session key: {e}")
        sys.exit(1)

def get_directory_data(session_key, env_config):
    """Get directory data using the session key"""
    # directory_url = 'https://eu1.doc.ineight.com/TBReportingAPI/Report/05.001a'
    # directory_url = f"{env_config['base_url']}/TBReportingAPI/Report/05.001a"
    directory_url = f"{env_config['base_url']}/TBReportingAPI/Report/05.001"
    headers = {
        'Accept': 'application/json',
        'Authorization': f'basic {session_key}'
    }
    print("directory_url >> ", directory_url, headers)

    try:
        response = requests.get(directory_url, headers=headers)
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Failed to get directory data. Status code: {response.status_code}")
            print(f"Response: {response.text}")
            sys.exit(1)
    except Exception as e:
        print(f"Error getting directory data: {e}")
        sys.exit(1)

def save_directory_data(data):
    """Save directory data to JSON file"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    # filename = f"transmittal_{timestamp}.json"
    filename = f"transmittal_report.json"
    
    try:
        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)
        print(f"Directory data saved to {filename}")
    except Exception as e:
        print(f"Error saving directory data: {e}")
        sys.exit(1)



def main():
    # Set up argument parser
    parser = argparse.ArgumentParser(description='iEight Directory Ingestion Script')
    parser.add_argument('--env', required=True, help='Path to environment JSON file')
    args = parser.parse_args()

    # Read environment configuration
    env_config = read_env_file(args.env)

    # Get session key
    session_key = get_session_key(env_config)
    # print("session_key >> ", session_key)

    # Get directory data
    directory_data = get_directory_data(session_key.strip('"'), env_config)
    # directory_data = get_directory_data("74c3aa55-3563-4cc9-942d-5cf3b2644572", env_config)
    # print("directory_data >> ", directory_data)
    # Save directory data
    save_directory_data(directory_data)

if __name__ == "__main__":
    main()
    

# after downloading files, we need to read the file and find document number. and redownload those files. then we ingest to vector db and delete all files.

# if download file was process, save in db. so we don't processes again. this script everydays work.