"""make shortcode field nullable

Revision ID: c4491c3d6ab4
Revises: 3e28f331b3fb
Create Date: 2024-08-21 23:45:32.937031

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'c4491c3d6ab4'
down_revision = '3e28f331b3fb'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('disciplines', schema=None) as batch_op:
        batch_op.alter_column('shortcode',
               existing_type=mysql.VARCHAR(length=2),
               nullable=True)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('disciplines', schema=None) as batch_op:
        batch_op.alter_column('shortcode',
               existing_type=mysql.VARCHAR(length=2),
               nullable=False)

    # ### end Alembic commands ###
