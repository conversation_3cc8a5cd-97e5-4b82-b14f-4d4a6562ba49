from models import DocumentNumberingTemplate
import uuid

class DocumentNumbering:
    def __init__(self):
        pass

    def create(self, client, template):
        create_kwargs = {
            'id': str(uuid.uuid4()),  # Generate a new unique ID
            'client': client,
            'template': template
        }
        # Call the create method of DocumentNumberingTemplate
        return DocumentNumberingTemplate.create(**create_kwargs)

    def update(self, id, template=None):
        update_kwargs = {}
        if template:
            update_kwargs['template'] = template
        
        # Call the update method of DocumentNumberingTemplate
        return DocumentNumberingTemplate.update(id, **update_kwargs)

    def delete(self, id):
        # Call the delete method of DocumentNumberingTemplate
        return DocumentNumberingTemplate.delete_by(id=id)

    def list(self, **filters):
        # Call the get_by method of DocumentNumberingTemplate
        return DocumentNumberingTemplate.get_by(**filters)

    def view(self, id):
        # Call the get_single method of DocumentNumberingTemplate
        return DocumentNumberingTemplate.get_single(id)
