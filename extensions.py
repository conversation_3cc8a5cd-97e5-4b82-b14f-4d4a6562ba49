# extensions.py
#import eventlet
#eventlet.monkey_patch()

import gevent
from gevent import monkey
monkey.patch_all(thread=False)

import os
import json
from flask_sqlalchemy import SQLAlchemy
from flask_socketio import Socket<PERSON>, emit, Namespace
from flask_migrate import Migrate
from rq import Queue
from redis import Redis
from celery import Celery
from services.socket_manager import SocketManager

env_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'env.json')
with open(env_file_path, 'r') as f:
    env_data = json.load(f)

db = SQLAlchemy()
migrate = Migrate()

allowed_origins = [
        '*',
        'null',
        'http://localhost:*',  # Allow all localhost ports
        'http://127.0.0.1:*',  # Allow all localhost IP ports
        'http://127.0.0.1:5500',
        'https://backend.aienergy-oilandgas.com',
        'https://app.aienergy-oilandgas.com',
        'https://ainrg.draglobal.com',
        'https://ainrgds.draglobal.com'
    ]

socketio = SocketIO(cors_allowed_origins=allowed_origins, async_mode="gevent", logger=True, engineio_logger=True)
socket_manager = SocketManager(socketio)

REDIS_URL = env_data.get("REDIS_URL") or "redis://localhost:6379/0"
RABBITMQ_URL = env_data.get("RABBITMQ_URL") or "guest:guest@localhost:5672/"
BROKER_URL = 'amqp://' + RABBITMQ_URL
BACKEND_URL = 'rpc://' + RABBITMQ_URL
celery = Celery(__name__, broker=BROKER_URL, backend=BACKEND_URL)
redis_conn = Redis.from_url(REDIS_URL)

# Prefixes for queue names
queue_prefixes = ["cbp_", "scp_", "cbp_report_", "file_upload_"]

# Dictionary to hold queue instances
# Create 5 connections for each prefix
queues = {}
for prefix in queue_prefixes:
    queues[prefix] = [Queue(f"{prefix}{i}_queue", connection=redis_conn) for i in range(5)]


class MyCustomNamespace(Namespace):
    def __init__(self, namespace):
        super().__init__(namespace)
        print('connected to test namespace....')
        # self.socket_manager.add_client(sid, request_id)

    def on_connect(self):
        emit('welcome', {})
        pass

    def on_disconnect(self, reason):
        pass

    def on_my_event(self, data):
        emit('my_response', data)

# socketio.on_namespace(MyCustomNamespace('/test'))
