# /home/<USER>/file_processor_tasks.py
from extensions import celery
# from socket_instance import socket_instance
from services.zip_file_processor import ZIPFileProcessor
import json
import os
import uuid
from models import BidFile  # Adjust the import to match your project
from paddleocr import PaddleOCR
from socket_instance import socket_instance

@celery.task(bind=True, max_retries=1, name="task.file_processor_tasks_bid.process_files_bid_task")
def process_files_bid_task(self, bid_id, file_path):
    try:
        try:
            # socket_manager = socket_instance.get_instance()            
            # Test emit
            # socket_manager.emit_to_client(
            #     req_id,
            #     'test_event',
            #     {'message': 'Test from Celery'},
            #     namespace='/processing_files'
            # )
            print('starting to process bid files...')
            file_processor = ZIPFileProcessor(bid_id, file_path)
            file_processor.process_files()
        except Exception as e:
            print(f"Error during zip file processing: {e}")

        return "ZIP File processing completed successfully."
    except Exception as e:
        print(f"Error during file processing: {e}")
        return str(e)

@celery.task(bind=True, max_retries=1, name="task.file_processor_tasks_bid.process_files_bid_bulk_task")
def process_files_bid_bulk_task(self, bid_id, file_paths, req_id=None):
    """
    Process multiple files in bulk for a given bid.
    Args:
        bid_id (str): ID of the bid.
        file_paths (list): List of file paths to process.
        req_id (str, optional): Request ID for socket communication.
    Returns:
        str: Status message.
    """
    try:
        # Get socket manager instance
        socket_manager = socket_instance.get_instance()
        total_files = len(file_paths)
        
        # If request ID is provided, emit a test event
        if req_id and socket_manager:
            socket_manager.emit_to_client(
                req_id,
                'test_event',
                {'message': f'Starting bulk bid processing for {total_files} files'},
                namespace='/bid_processing'
            )
            
            # Emit processing started event
            socket_manager.emit_to_client(
                req_id,
                'processing_started',
                {
                    'event_type': 'bid_processing',
                    'request_id': req_id,
                    'data': {
                        'message': f"Starting to process {total_files} bid files in bulk",
                        'bid_id': bid_id,
                        'total_files': total_files
                    }
                },
                namespace='/bid_processing'
            )
        
        print(f'Starting to process {len(file_paths)} files in bulk...')
        processed_count = 0
        failed_count = 0
        successful_files = []
        failed_files = []
        print(f"Files to process are : {file_paths}")
        
        for index, file in enumerate(file_paths):
            bid_file_id = file["file_id"]
            file_path = file["file_path"]
            file_name = os.path.basename(file_path)
            file_extension = os.path.splitext(file_path)[1].lstrip('.')
            current_file = index + 1
            
            # Emit progress update for current file
            if req_id and socket_manager:
                socket_manager.emit_to_client(
                    req_id,
                    'progress_message',
                    {
                        'event_type': 'bid_processing',
                        'request_id': req_id,
                        'data': {
                            'status': 'processing',
                            'message': f"Processing file {current_file} of {total_files}: {file_name}",
                            'progress': (current_file - 0.5) / total_files * 100,
                            'bid_id': bid_id,
                            'file_name': file_name,
                            'current_file': current_file,
                            'total_files': total_files
                        }
                    },
                    namespace='/bid_processing'
                )
            
            # Set status to pending before processing
            try:
                print(f"Updating file {file_name} status: pending")
                file_data = {
                    'name': file_name,
                    'bid_id': bid_id,
                    'status': "pending",
                    'file_type': file_extension,
                }
                BidFile.update(bid_file_id, **file_data)
                
                # Process the file
                if os.path.exists(file_path):
                    file_processor = ZIPFileProcessor(bid_id, file_path)
                    file_processor.process_files()
                    processed_count += 1
                    status = 'done'
                    successful_files.append({
                        'file_id': bid_file_id,
                        'file_name': file_name
                    })
                else:
                    print(f"File not found: {file_path}")
                    failed_count += 1
                    status = 'failed'
                    failed_files.append({
                        'file_id': bid_file_id,
                        'file_name': file_name,
                        'error': 'File not found'
                    })
            except Exception as e:
                print(f"Error processing file {file_path}: {e}")
                failed_count += 1
                status = 'failed'
                failed_files.append({
                    'file_id': bid_file_id,
                    'file_name': file_name,
                    'error': str(e)
                })
                
            # Update status after processing (success or failure)
            try:
                print(f"Updating file {file_name} status: {status}")
                file_data = {
                    'name': file_name,
                    'bid_id': bid_id,
                    'status': status,
                    'file_type': file_extension,
                }
                BidFile.update(bid_file_id, **file_data)
            except Exception as db_err:
                print(f"Error saving BidFile for {file_name}: {db_err}")
            
            # Emit progress update after processing current file
            if req_id and socket_manager:
                file_status = 'success' if status == 'done' else 'error'
                file_message = f"Successfully processed" if status == 'done' else f"Failed to process"
                
                socket_manager.emit_to_client(
                    req_id,
                    'file_complete',
                    {
                        'event_type': 'bid_processing',
                        'request_id': req_id,
                        'data': {
                            'status': file_status,
                            'message': f"{file_message} file {current_file} of {total_files}: {file_name}",
                            'progress': (current_file / total_files * 100),
                            'bid_id': bid_id,
                            'file_name': file_name,
                            'current_file': current_file,
                            'total_files': total_files
                        }
                    },
                    namespace='/bid_processing'
                )
        
        # Prepare summary message based on results
        if failed_count == 0:
            summary_msg = f"All {total_files} files were successfully processed."
            summary_status = "success"
        elif processed_count == 0:
            summary_msg = f"We couldn't process any of your {total_files} files. They may be corrupted or in unsupported formats."
            summary_status = "error"
        else:
            summary_msg = f"{processed_count} files were successfully processed and {failed_count} files couldn't be processed."
            summary_status = "partial"
            
        print(f"Bulk processing completed. Successfully processed: {processed_count}, Failed: {failed_count}")
        
        # Emit completion event
        if req_id and socket_manager:
            # Create a more user-friendly list of failed files if any
            failed_files_messages = []
            if failed_count > 0:
                for failed_file in failed_files:
                    failed_files_messages.append(f"• '{failed_file['file_name']}' - Unable to process")
            
            socket_manager.emit_to_client(
                req_id,
                'completed_event',
                {
                    'event_type': 'bid_processing',
                    'request_id': req_id,
                    'data': {
                        'status': summary_status,
                        'message': summary_msg,
                        'total_files': total_files,
                        'successful_files': processed_count,
                        'failed_files': failed_count,
                        'failed_files_messages': failed_files_messages,
                        'success_details': successful_files,
                        'failure_details': failed_files,
                        'bid_id': bid_id
                    }
                },
                namespace='/bid_processing'
            )
            
        return f"Bulk processing completed. Successfully processed: {processed_count}, Failed: {failed_count}"
    except Exception as e:
        print(f"Error during bulk file processing: {e}")
        
        # Emit error event
        if req_id and socket_manager:
            socket_manager.emit_to_client(
                req_id,
                'error_event',
                {
                    'event_type': 'bid_processing',
                    'request_id': req_id,
                    'data': {
                        'status': 'error',
                        'message': "We encountered an unexpected problem while processing your files.",
                        'error': str(e),
                        'bid_id': bid_id
                    }
                },
                namespace='/bid_processing'
            )
            
        return str(e)
