import os
import time
import openai
from openai import OpenAI
import json
from services.document_loader import load_single_document
from services.serverless import ServerlessRunpodAPIClient
from services.open_ai_service import get_response
import json
class ParserParellel:
    def __init__(self):
        # Get the path to the parent directory
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # Load environment variables from env.json file in the parent directory
        env_file_path = os.path.join(parent_dir, 'env.json')
        with open(env_file_path, 'r') as f:
            env_data = json.load(f)
        
        # Set up OpenAI client
        self.client = OpenAI(api_key=env_data.get('OPENAI_API_KEY', ''))
        
        # Set the OpenAI API key globally
        openai.api_key = env_data.get('OPENAI_API_KEY', '')
        self.serverless_init = ServerlessRunpodAPIClient()

    def put_flag_template(self, text):
        prompt_template='''
            ### System Prompt
            Follow these rules only:
            Identify Sections and Subsections: 
            First, clearly define what constitutes a 'Section' and 'Subsection' in your text. This might be based on formatting, like headings or titles, or specific keywords.

            Insert Symbols:
            Add "###" before each 'Section' title.
            Add "####" before each 'Subsection' title.
            Add "$$" just after each 'Section' and 'Subsection' title.

            Clean Up Whitespace: Ensure there are no extra spaces or line breaks around the symbols or titles.

            Preserve Original Text: Make sure that the body of the sections and subsections remains unchanged.
            ### User Message
            4.10 Reporting 
            4.11 Progress Reporting 
            Refer to “PROJECT CONTROL” section of EXHIBIT - P – Administration  Instructions & Project Control. 
            4.12 Interface Management 
            Refer to the “PROJECT CONTROL” section of EXHIBIT - P – Administration  Instructions & Project Control. 
            CONTRACTOR shall acknowledge the COMPANY Interface Management  Procedure (Project1 INTERFACE CONTROL PROCEDURE – Ref. Project1-Company-PMT 
            0000-PM-PRO-0029), the Interface Register (INTERFACE REGISTER – Ref. NA 
            company-PMT-0812-PM-REG-0006-1), the Interface Alignment sheets (Project1  INTERFACE ALIGNMENT SHEETS – Ref. MUL-company-Project1 -0812-MD-IAS-0001 ) and  shall actively participate in the interface alignment process as required by  COMPANY.
            ### Assistant
            ### 4.10 Reporting $$  
            ### 4.11 Progress Reporting $$
            Refer to “PROJECT CONTROL” section of EXHIBIT - P – Administration  Instructions & Project Control. 
            ### 4.12 Interface Management $$ 
            Refer to “PROJECT CONTROL” section of EXHIBIT - P – Administration  Instructions & Project Control. 
            CONTRACTOR shall acknowledge the COMPANY Interface Management  Procedure (Project1 INTERFACE CONTROL PROCEDURE – Ref. Project1-Company-PMT 
            0000-PM-PRO-0029), the Interface Register (INTERFACE REGISTER – Ref. NA 
            company-PMT-0812-PM-REG-0006-1), the Interface Alignment sheets (Project1  INTERFACE ALIGNMENT SHEETS – Ref. MUL-company-Project1 -0812-MD-IAS-0001 ) and  shall actively participate in the interface alignment process as required by  COMPANY.
        '''
                    
        prompt_template += f'''
            ### User Message
            {text}
            ### Assistant
        '''
        return prompt_template

    def parser(self, text):
        sections = text.split('##')
        ret = []
        for section in sections:
            tmp = section.replace("#", '').split('$$')
            if len(tmp) != 2:
                if len(tmp) == 1:
                    if len(ret) != 0:
                        ret[-1]["content"] += tmp[0]
                continue
            ret.append({
                "title": tmp[0],
                "content": tmp[1]
            })
        return ret

    def parser_chronobid(self, text):
        sections = text.split('###')
        ret = []
        for section in sections:
            if '$$' not in section or '#' not in section:
                lines = section.strip().split('\n')
                if len(lines) >= 2:
                    title = lines[0].strip()
                    content = '\n'.join(lines[1:]).strip()
                    ret.append({
                        "title": title,
                        "content": content
                    })
                continue
            tmp = section.replace("#", '').split('$$')
            if len(tmp) != 2:
                if len(tmp) == 1:
                    if len(ret) != 0:
                        ret[-1]["content"] += tmp[0]
                else:
                    continue
            ret.append({
                "title": tmp[0],
                "content": tmp[1]
            })
        return ret

    def get_flagged_string(self, text, split_length=300):
        words = text.split()
        queries = []
        chunks = []
        i = 0
        num = 0
        total = 0
        while i < len(words):
            start = time.time()
            chunk = ' '.join(words[i:i + split_length])
            print("processing chunk-->", num)
            queries.append(self.put_flag_template(chunk))
            i += split_length
            num += 1

        chunks2 = self.serverless_init.parallel_request_handler(queries)
        flagged_text = ' '.join(chunks2)
        return flagged_text

    def get_serialized_data_serverless(self, file_path, log):
        text = load_single_document(file_path)
        flagged_text = self.get_flagged_string(text)
        log['flagged'] = flagged_text
        data = self.parser(flagged_text)
        return data

    def get_serialized_data_serverless_chronobid(self, file_path, log):
        text = load_single_document(file_path)
        flagged_text = self.get_flagged_string(text)
        log['flagged'] = flagged_text
        data = self.parser_chronobid(flagged_text)
        return data
