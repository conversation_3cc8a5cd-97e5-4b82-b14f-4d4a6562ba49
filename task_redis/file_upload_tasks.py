# file_upload_tasks.py
from services.file_uploader import Fileuploader
from init import app, socketio, socket_manager

# Redis connection
def upload_files_task(dirtry, project_id, file_ids, file_names):
    try:
        with app.app_context():
            print('accepted files for uploading....', dirtry, project_id, file_ids, file_names)
            _loader = Fileuploader(dirtry, project_id, file_ids, file_names)
            _loader.uploader()
            print('Files uploaded, background processing started...')
    except Exception as e:
        print(f"Error during file upload: {e}")
        return str(e)
