import json
import threading

class ClientManagerService:
    def __init__(self, file_path="clients_data.json"):
        self.file_path = file_path
        self.file_lock = threading.Lock()
        self.data = self._load_from_file()

    def _load_from_file(self):
        """Load clients and client_joined data from the file."""
        with self.file_lock:
            try:
                with open(self.file_path, "r") as f:
                    return json.load(f)
            except (FileNotFoundError, json.JSONDecodeError):
                return {"clients": {}, "client_joined": {}}

    def _save_to_file(self):
        """Save the current state of clients and client_joined to the file."""
        with self.file_lock:
            with open(self.file_path, "w") as f:
                json.dump(self.data, f, indent=4)

    def add_client_event(self, request_id, event_name, data):
        """Add an event to the client's queue."""
        if request_id not in self.data["clients"]:
            self.data["clients"][request_id] = []
        self.data["clients"][request_id].append({"event_name": event_name, "data": data})
        self._save_to_file()

    def get_client_events(self, request_id):
        """Get all queued events for a client."""
        return self.data["clients"].get(request_id, [])

    def clear_client_events(self, request_id):
        """Clear all events for a client."""
        if request_id in self.data["clients"]:
            self.data["clients"][request_id] = []
            self._save_to_file()

    def mark_client_joined(self, request_id):
        """Mark a client as joined."""
        self.data["client_joined"][request_id] = True
        self._save_to_file()

    def is_client_joined(self, request_id):
        """Check if a client has joined."""
        return self.data["client_joined"].get(request_id, False)

    def remove_client(self, request_id):
        """Remove a client from the system."""
        if request_id in self.data["clients"]:
            del self.data["clients"][request_id]
        if request_id in self.data["client_joined"]:
            del self.data["client_joined"][request_id]
        self._save_to_file()
