import os
import time
import json
from models import Requirement
from concurrent.futures import ThreadPoolExecutor
from services.parser_openai_parellel import OpenAIProcessor
from services.faiss_embedding import FaissEmbeddingGPU
import asyncio
import redis


class ZIPFileProcessor:
    def __init__(self, bid_id, file_path):
        self.file_path = file_path
        self.file_type = file_path.rsplit('.', 1)[1].lower()
        self.bid_id = bid_id
        self.file_processor = OpenAIProcessor()
        self.faiss_embedding = FaissEmbeddingGPU(request_id=bid_id,batch_size=50)

    def process_files(self):
        asyncio.run(self._async_process_files(self.file_type, self.file_path))
    
    async def _async_process_files(self, file_type, file_path):
            
        print('Processing files efficiently...')        
        try:
            print('this is filetype also: ', file_type)
            if file_type == 'pdf':
                data = await self.file_processor.extract_text_from_pdf(file_path)
            elif file_type in ["docx", "doc"]:
                data = await self.file_processor.extract_text_from_docx(file_path)
            elif file_type in ["csv", "xls", "xlsx"]:
                data = await self.file_processor.extract_text_from_excel_or_csv_batch(file_path)
            else:
                raise ValueError(f"Unsupported file type: {file_type}")
            
            # print('data received for above file.......')
            await self.seed_upload_data(os.path.basename(file_path), data[1], file_type)
            print('done processing the above file.... \n')
            
        except Exception as e:
            print('failed to retrieve data for above file...')
            print(e)
    
    async def seed_upload_data(self, file_name, data, file_type):        
        print("uploaded data below before send to vec db....")
        print('file_type: ', file_type)
        # Add texts to the FAISS index
        start_time = time.time()
        if file_type in ["csv", "xls", "xlsx"]:
            print('using v2...')
            result = await self.faiss_embedding.insert_documents_v2(data, file_name, self.request_id)
        else:
            result = await self.faiss_embedding.insert_documents(data, file_name, self.request_id)
        
        elapsed_time = time.time() - start_time
        print(f"Time taken to insert Faiss document '{file_name}': {elapsed_time:.2f} seconds")
        return result
