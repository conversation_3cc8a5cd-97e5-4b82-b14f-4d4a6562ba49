from transformers import AutoModelForCausalLM, AutoTokenizer, pipeline
# from auto_gptq import exllama_set_max_input_length
import torch
import time
import os



from auto_gptq import exllama_set_max_input_length

from openai import OpenAI

client = OpenAI(
    api_key="***************************************************",
)


# 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

from services.document_loader import load_single_document
# model_name_or_path = "TheBloke/CodeLlama-34B-GPTQ"
# To use a different branch, change revision
# For example: revision="gptq-4bit-32g-actorder_True"

# model_name_or_path = "TheBloke/CodeLlama-7B-Instruct-GPTQ"


model_name_or_path = 'TheBloke/Phind-CodeLlama-34B-v2-GPTQ'
# model_name_or_path = 'TheBloke/Mistral-7B-Instruct-v0.1-GPTQ'
# model_name_or_path = 'TheBloke/Mistral-7B-v0.1-GPTQ'
# model_name_or_path = 'Kowshik24/mistral-7b-finetuned-v1'
# model_name_or_path = 'TheBloke/Mistral-7B-Instruct-v0.1-GPTQ'
# model = AutoModelForCausalLM.from_pretrained(model_name_or_path,
#                                              torch_dtype=torch.float16,
#                                              device_map="cuda:0",
#                                              revision="main")

# os.environ['CUDA_LAUNCH_BLOCKING'] = '1'



# model = exllama_set_max_input_length(model, 4096)

def putFlag(text, model, tokenizer):
#     prompt_template='''
# [INST] Follow this rules only:
# Add this "$$" just after each section and subsection's title from the given text: 
# Add this "###" before starting each subsection from the given text:

# Text:
# 4.10 Reporting 
# 4.11 Progress Reporting 
# Refer to “PROJECT CONTROL” section of EXHIBIT - P – Administration  Instructions & Project Control. 
# 4.12 Interface Management 
# Refer to the “PROJECT CONTROL” section of EXHIBIT - P – Administration  Instructions & Project Control. 
# CONTRACTOR shall acknowledge the COMPANY Interface Management  Procedure (Project1 INTERFACE CONTROL PROCEDURE – Ref. Project1-Company-PMT 
# 0000-PM-PRO-0029), the Interface Register (INTERFACE REGISTER – Ref. NA 
# company-PMT-0812-PM-REG-0006-1), the Interface Alignment sheets (Project1  INTERFACE ALIGNMENT SHEETS – Ref. MUL-company-Project1 -0812-MD-IAS-0001 ) and  shall actively participate in the interface alignment process as required by  COMPANY.

# Marked Test:
# ### 4.10 Reporting $$  
# ### 4.11 Progress Reporting $$
# Refer to “PROJECT CONTROL” section of EXHIBIT - P – Administration  Instructions & Project Control. 
# ### 4.12 Interface Management $$ 
# Refer to “PROJECT CONTROL” section of EXHIBIT - P – Administration  Instructions & Project Control. 
# CONTRACTOR shall acknowledge the COMPANY Interface Management  Procedure (Project1 INTERFACE CONTROL PROCEDURE – Ref. Project1-Company-PMT 
# 0000-PM-PRO-0029), the Interface Register (INTERFACE REGISTER – Ref. NA 
# company-PMT-0812-PM-REG-0006-1), the Interface Alignment sheets (Project1  INTERFACE ALIGNMENT SHEETS – Ref. MUL-company-Project1 -0812-MD-IAS-0001 ) and  shall actively participate in the interface alignment process as required by  COMPANY.
#     '''
    
    
#     # print(prompt_template)
#     # print(tokenizer.eos_token)
#     # prompt_template='Query: The wind speed should be 30.\nAnswer:{"name": "wind_speed", "condition": "=", value:"30"}</s>\nQuery: The temparature should be more than 50.\nAnswer:{"name": "temperature", "condition": ">", value:"50"}</s>'
#     prompt_template += f'''
# Text:
# {text}
# Marked Text:
#     '''

#     prompt_template = f'''
#     ### System Prompt
#     Follow this rules only:
# Add this "$$" just after each section and subsection's title from the user message
# Add this "###" before starting each subsection from the user message.
# No input from user should be lost. Meaning, you will only mark the text based on the above instructions, but the output should not loose any text from the inut.

# ### User Message: 
# 4.10 Reporting 
# 4.11 Progress Reporting 
# Refer to “PROJECT CONTROL” section of EXHIBIT - P – Administration  Instructions & Project Control. 
# 4.12 Interface Management 
# Refer to the “PROJECT CONTROL” section of EXHIBIT - P – Administration  Instructions & Project Control. 
# CONTRACTOR shall acknowledge the COMPANY Interface Management  Procedure (Project1 INTERFACE CONTROL PROCEDURE – Ref. Project1-Company-PMT 
# 0000-PM-PRO-0029), the Interface Register (INTERFACE REGISTER – Ref. NA 
# company-PMT-0812-PM-REG-0006-1), the Interface Alignment sheets (Project1  INTERFACE ALIGNMENT SHEETS – Ref. MUL-company-Project1 -0812-MD-IAS-0001 ) and  shall actively participate in the interface alignment process as required by  COMPANY.

# ### Assistant:
# ### 4.10 Reporting $$  
# ### 4.11 Progress Reporting $$
# Refer to “PROJECT CONTROL” section of EXHIBIT - P – Administration  Instructions & Project Control. 
# ### 4.12 Interface Management $$ 
# Refer to “PROJECT CONTROL” section of EXHIBIT - P – Administration  Instructions & Project Control. 
# CONTRACTOR shall acknowledge the COMPANY Interface Management  Procedure (Project1 INTERFACE CONTROL PROCEDURE – Ref. Project1-Company-PMT 
# 0000-PM-PRO-0029), the Interface Register (INTERFACE REGISTER – Ref. NA 
# company-PMT-0812-PM-REG-0006-1), the Interface Alignment sheets (Project1  INTERFACE ALIGNMENT SHEETS – Ref. MUL-company-Project1 -0812-MD-IAS-0001 ) and  shall actively participate in the interface alignment process as required by  COMPANY.
#     '''

    prompt_template = f'''
    ### System Prompt
Follow these rules only:
Identify Sections and Subsections: 
First, clearly define what constitutes a 'Section' and 'Subsection' in your text. This might be based on formatting, like headings or titles, or specific keywords.

Insert Symbols:
Add "###" before each 'Section' title.
Add "####" before each 'Subsection' title.
Add "$$" just after each 'Section' and 'Subsection' title.

Clean Up Whitespace: Ensure there are no extra spaces or line breaks around the symbols or titles.

Preserve Original Text: Make sure that the body of the sections and subsections remains unchanged.

### User Message: 
4.10 Reporting 
4.11 Progress Reporting 
Refer to “PROJECT CONTROL” section of EXHIBIT - P – Administration  Instructions & Project Control. 
4.12 Interface Management 
Refer to the “PROJECT CONTROL” section of EXHIBIT - P – Administration  Instructions & Project Control. 
CONTRACTOR shall acknowledge the COMPANY Interface Management  Procedure (Project1 INTERFACE CONTROL PROCEDURE – Ref. Project1-Company-PMT 
0000-PM-PRO-0029), the Interface Register (INTERFACE REGISTER – Ref. NA 
company-PMT-0812-PM-REG-0006-1), the Interface Alignment sheets (Project1  INTERFACE ALIGNMENT SHEETS – Ref. MUL-company-Project1 -0812-MD-IAS-0001 ) and  shall actively participate in the interface alignment process as required by  COMPANY.

### Assistant:
### 4.10 Reporting $$  
### 4.11 Progress Reporting $$
Refer to “PROJECT CONTROL” section of EXHIBIT - P – Administration  Instructions & Project Control. 
### 4.12 Interface Management $$ 
Refer to “PROJECT CONTROL” section of EXHIBIT - P – Administration  Instructions & Project Control. 
CONTRACTOR shall acknowledge the COMPANY Interface Management  Procedure (Project1 INTERFACE CONTROL PROCEDURE – Ref. Project1-Company-PMT 
0000-PM-PRO-0029), the Interface Register (INTERFACE REGISTER – Ref. NA 
company-PMT-0812-PM-REG-0006-1), the Interface Alignment sheets (Project1  INTERFACE ALIGNMENT SHEETS – Ref. MUL-company-Project1 -0812-MD-IAS-0001 ) and  shall actively participate in the interface alignment process as required by  COMPANY.
    '''

    prompt_template += f'''
### User Message:
{text}
### Assistant:'''
    
    # print(prompt_template)
    # Now proceed with your inference
    input_ids = tokenizer(prompt_template, return_tensors='pt').input_ids.cuda()
    output = model.generate(inputs=input_ids, temperature=0, max_new_tokens=512)
    # print(tokenizer.decode(output[0]))
    
    # Inference can also be done using transformers' pipeline
    
    
    pipe = pipeline(
        "text-generation",
        model=model,
        tokenizer=tokenizer,
        max_new_tokens=4096,
        temperature=0,
        top_p=0.95,
        repetition_penalty=1.15,
        pad_token_id = tokenizer.eos_token_id,
        return_full_text=False
    )
    
    return pipe(prompt_template)[0]['generated_text']


def putFlagGPT(text):
    completion = client.chat.completions.create(
  model="gpt-3.5-turbo",
  temperature=0,
  messages=[
    {"role": "system", "content": f'''You are an assistant to put flags in a text.
Add this "$$" just after each section and subsection's title from the given text: 
Add this "###" before starting each subsection from the given text.'''},
    {"role": "user", "content": f'''4.10 Reporting 
4.11 Progress Reporting 
Refer to “PROJECT CONTROL” section of EXHIBIT - P – Administration  Instructions & Project Control. 
4.12 Interface Management 
Refer to the “PROJECT CONTROL” section of EXHIBIT - P – Administration  Instructions & Project Control. 
CONTRACTOR shall acknowledge the COMPANY Interface Management  Procedure (Project1 INTERFACE CONTROL PROCEDURE – Ref. Project1-Company-PMT 
0000-PM-PRO-0029), the Interface Register (INTERFACE REGISTER – Ref. NA 
company-PMT-0812-PM-REG-0006-1), the Interface Alignment sheets (Project1  INTERFACE ALIGNMENT SHEETS – Ref. MUL-company-Project1 -0812-MD-IAS-0001 ) and  shall actively participate in the interface alignment process as required by  COMPANY.'''},
 {"role": "assistant", "content": f'''### 4.10 Reporting $$  
### 4.11 Progress Reporting $$
Refer to “PROJECT CONTROL” section of EXHIBIT - P – Administration  Instructions & Project Control. 
### 4.12 Interface Management $$ 
Refer to “PROJECT CONTROL” section of EXHIBIT - P – Administration  Instructions & Project Control. 
CONTRACTOR shall acknowledge the COMPANY Interface Management  Procedure (Project1 INTERFACE CONTROL PROCEDURE – Ref. Project1-Company-PMT 
0000-PM-PRO-0029), the Interface Register (INTERFACE REGISTER – Ref. NA 
company-PMT-0812-PM-REG-0006-1), the Interface Alignment sheets (Project1  INTERFACE ALIGNMENT SHEETS – Ref. MUL-company-Project1 -0812-MD-IAS-0001 ) and  shall actively participate in the interface alignment process as required by  COMPANY.'''},
{"role": "user", "content": f'''{text}'''}
]
)
    return completion.choices[0].message.content


def parser(text):
    sections = text.split('##')
    ret = []
    for section in sections:
        tmp = section.replace("#", '').split('$$')
        if len(tmp) != 2:
            if len(tmp) == 1:
                if len(ret) != 0:
                    ret[-1]["content"] += tmp[0]
                else:
                    ret.append({
                    "title": tmp[0],
                    "content": tmp[0]
                    })
            continue
        # if len(tmp[1]) <=5:
        #     continue
        ret.append({
            "title": tmp[0],
            "content": tmp[1]
        })
    return ret




def get_flagged_string(text, split_length=300):
    model = AutoModelForCausalLM.from_pretrained(model_name_or_path,
                                             device_map="cuda:0",
                                             trust_remote_code=True,
                                             revision="main",
                                             token='*************************************'
                                             )


    tokenizer = AutoTokenizer.from_pretrained(model_name_or_path, use_fast=True)

    words = text.split()  # Split the string into a list of words
    chunks = []
    i = 0
    num = 0
    total = 0
    while i < len(words):
        start = time.time()
        chunk = ' '.join(words[i:i + split_length])
        print("processing chunk-->", num)
        chunk = putFlag(chunk, model, tokenizer)
        # chunk = putFlagGPT(chunk)
        chunks.append(chunk)
        i += split_length  
        print("Done! Time:", time.time() - start)
        total += time.time() - start
        num+=1
    print("Total Time ->", total)
    flagged_text = ' '.join(chunks)
    return flagged_text
    

def get_serielized_data(file_path):
    text = load_single_document(file_path)

    flagged_text = get_flagged_string(text)
    
    data = parser(flagged_text)

    return data