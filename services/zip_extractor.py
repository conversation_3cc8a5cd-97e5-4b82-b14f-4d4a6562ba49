import os
import uuid
import json
import time
import shutil
import tempfile
from zipfile import ZipFile, BadZipFile
from werkzeug.utils import secure_filename
from models import BidFile, Bids, BidFileMetadata
from flask import current_app
import traceback
from socket_instance import socket_instance


class ZipExtractorForUpload:
    def __init__(self, base_upload_path, project_id, project_type=None):
        self.base_upload_path = base_upload_path
        self.project_id = project_id
        self.project_type = project_type
        self.ALLOWED_EXTENSIONS = {'doc', 'docx', 'pdf', 'txt', "csv", "xls", "xlsx", "xlsm"}

    def extract_zip_to_project(self, zip_file, project_folder):
        """
        Extract zip file to project folder and return list of extracted files.
        
        Args:
            zip_file: FileStorage object containing the zip file
            project_folder: Path to the project folder
            
        Returns:
            tuple: (list of extracted file info, list of unsupported files)
        """
        extracted_files = []
        unsupported_files = []
        
        try:
            # Create a temporary directory for processing
            with tempfile.TemporaryDirectory() as temp_dir:
                # Save the zip file to temp directory
                zip_path = os.path.join(temp_dir, secure_filename(zip_file.filename))
                zip_file.save(zip_path)

                # Create extraction directory
                extract_path = os.path.join(temp_dir, 'extracted')
                os.makedirs(extract_path, exist_ok=True)

                # Extract the zip file
                with ZipFile(zip_path, 'r') as zip_ref:
                    zip_ref.extractall(extract_path)

                # Process extracted files recursively
                self._process_directory_recursive(
                    extract_path, 
                    project_folder, 
                    extracted_files, 
                    unsupported_files
                )

                return extracted_files, unsupported_files

        except (IOError, BadZipFile) as e:
            print(f"An error occurred while processing the zip file: {e}")
            return [], []

    def _is_system_file(self, file_path, filename):
        """
        Check if a file is a system file that should be ignored.
        
        Args:
            file_path: Full path to the file
            filename: Name of the file
            
        Returns:
            bool: True if it's a system file to ignore
        """
        # System files and directories to ignore
        ignore_patterns = [
            '__MACOSX',      # macOS metadata folder
            '.DS_Store',     # macOS metadata file
            'Thumbs.db',     # Windows thumbnail cache
            'desktop.ini',   # Windows folder settings
            '._.DS_Store',   # macOS resource fork
        ]
        
        # Check if filename starts with ._ (macOS resource fork files)
        if filename.startswith('._'):
            return True
            
        # Check if path contains any ignore patterns
        path_parts = file_path.split(os.sep)
        for part in path_parts:
            if part in ignore_patterns:
                return True
                
        # Check if filename matches ignore patterns
        if filename in ignore_patterns:
            return True
            
        return False

    def _process_directory_recursive(self, source_dir, dest_dir, extracted_files, unsupported_files):
        """
        Recursively process directory, extracting nested zips and copying supported files.
        Args:
            source_dir: Source directory to process
            dest_dir: Destination directory to copy files to
            extracted_files: List to store extracted file information
            unsupported_files: List to store unsupported file names
        """
        for item in os.listdir(source_dir):
            source_path = os.path.join(source_dir, item)
            
            # Skip system files and directories
            if self._is_system_file(source_path, item):
                print(f"Skipping system file/directory: {item}")
                continue
            
            if os.path.isdir(source_path):
                # Recursively process subdirectory without prefix
                self._process_directory_recursive(
                    source_path, 
                    dest_dir, 
                    extracted_files, 
                    unsupported_files
                )
            elif item.lower().endswith('.zip'):
                # Extract nested zip
                try:
                    nested_extract_path = os.path.join(source_dir, f"{item[:-4]}_extracted")
                    os.makedirs(nested_extract_path, exist_ok=True)
                    
                    with ZipFile(source_path, 'r') as nested_zip:
                        nested_zip.extractall(nested_extract_path)
                    
                    # Recursively process the nested zip contents without prefix
                    self._process_directory_recursive(
                        nested_extract_path, 
                        dest_dir, 
                        extracted_files, 
                        unsupported_files
                    )
                except (IOError, BadZipFile) as e:
                    print(f"Error extracting nested zip {item}: {e}")
                    unsupported_files.append(f"{item} (corrupted zip)")
            else:
                # Process regular file
                file_extension = item.rsplit('.', 1)[-1].lower() if '.' in item else ""
                
                # Always track files for debugging
                print(f"Processing file: {item}, extension: {file_extension}")
                
                if file_extension in self.ALLOWED_EXTENSIONS:
                    # Validate that it's actually a valid file (not zero bytes, etc.)
                    try:
                        file_size = os.path.getsize(source_path)
                        if file_size == 0:
                            print(f"Skipping empty file: {item}")
                            unsupported_files.append(f"{item} (empty file)")
                            continue
                    except OSError as e:
                        print(f"Error checking file size for {item}: {e}")
                        unsupported_files.append(f"{item} (access error)")
                        continue
                    
                    # Use the original file name
                    unique_filename = item
                    dest_path = os.path.join(dest_dir, unique_filename)
                    
                    # Ensure the destination directory exists
                    os.makedirs(os.path.dirname(dest_path), exist_ok=True)
                    
                    # Copy file to destination
                    try:
                        shutil.copy2(source_path, dest_path)
                        
                        # Verify the copied file exists and has content
                        if os.path.exists(dest_path) and os.path.getsize(dest_path) > 0:
                            # Add to extracted files list
                            extracted_files.append({
                                'name': unique_filename,
                                'original_name': item,
                                'extension': file_extension,
                                'path': dest_path
                            })
                            print(f"Extracted supported file: {unique_filename}")
                        else:
                            print(f"File copy verification failed for: {item}")
                            unsupported_files.append(f"{item} (copy verification failed)")
                    except Exception as e:
                        print(f"Error copying file {item}: {e}")
                        unsupported_files.append(f"{item} (copy error)")
                else:
                    # File extension not supported - add to unsupported list
                    unsupported_file_name = item
                    unsupported_files.append(unsupported_file_name)
                    print(f"Skipped unsupported file: {unsupported_file_name} (extension: {file_extension})")