import fitz 
import json
import os
import anthropic
from docx import Document
import mimetypes
import uuid
from datetime import datetime
from models import Project, File

class NumberingRuleGenerator:
    def __init__(self, document_numbering_file_path, project_id):
        self.document_numbering_file_path = os.path.abspath(document_numbering_file_path)
        self.project_id = project_id
        
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.env_file_path = os.path.join(parent_dir, 'env.json')
        self.setup_agent()
        self.model = "claude-3-5-sonnet-20241022"

    def get_file_type(self, file_path):
        mime_type, _ = mimetypes.guess_type(file_path)
        if mime_type == 'application/pdf':
            return 'pdf'
        elif mime_type in ['application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/msword']:
            return 'word'
        else:
            raise ValueError(f"Unsupported file type: {mime_type}")

    def extract_pdf_text(self, file_path):
        doc = fitz.open(file_path)
        text = "\n".join(page.get_text() for page in doc)
        doc.close()
        return text

    def extract_word_text(self, file_path):
        doc = Document(file_path)
        text = "\n".join(paragraph.text for paragraph in doc.paragraphs)
        return text

    def extract_full_text(self):
        file_type = self.get_file_type(self.document_numbering_file_path)
        
        if file_type == 'pdf':
            return self.extract_pdf_text(self.document_numbering_file_path)
        elif file_type == 'word':
            return self.extract_word_text(self.document_numbering_file_path)
        else:
            raise ValueError(f"Unsupported file type: {file_type}")  

    def load_env(self):
        with open(self.env_file_path, 'r') as f:
            return json.load(f)

    def setup_claude_client(self, api_key):
        self.client = anthropic.Anthropic(api_key=api_key)

    def setup_agent(self):
        env_data = self.load_env()
        self.setup_claude_client(env_data.get('CLAUDE_API_KEY', ''))
    
    def generate_numbering_rule(self, full_text):
        prompt = (
            f"""
            Analyze this document numbering procedure and create a concise rule that explains how to determine file type and discipline from document numbers.
            The rule should be a single, clear text that can be used to classify documents based on their names.
            Focus on the patterns, positions, and codes that indicate file type and discipline.

            Example format:
            "Document numbers follow the pattern: [Discipline Code]-[Type Code]-[Number]. 
            Discipline codes: EL=Electrical, ME=Mechanical. 
            Type codes: DR=Drawing, SP=Specification."

            {full_text}
            Return only the rule text, without any additional formatting or explanation.
            Be as detailed as possible and be explicit.
            """
        )

        response = self.client.messages.create(
            model=self.model,
            max_tokens=1024,
            messages=[{"role": "user", "content": prompt}],
        )
        return response
    
    def store_rule(self, rule_text):
        """Store the numbering rule in the project's numbering_rule column"""
        try:
            # Get the project
            project = Project.get_one_by(id=self.project_id)
            if not project:
                raise ValueError(f"Project with ID {self.project_id} not found")
            
            # Update the project with the new rule
            Project.update(self.project_id, numbering_rule=rule_text)
            return self.project_id
        except Exception as e:
            print(f"Error storing rule: {e}")
            raise

    def get_unclassified_files(self):
        """Get files that don't have document type or discipline set"""
        try:
            # Get all files for the project
            files = File.get_by(project_id=self.project_id)
            
            # Filter files that don't have document_type or discipline
            unclassified_files = [
                file for file in files 
                if not file.get('document_type') or not file.get('document_discipline')
            ]
            
            return unclassified_files
        except Exception as e:
            print(f"Error getting unclassified files: {e}")
            return []

    def classify_file(self, filename, rule_text):
        """Classify a single file using the numbering rule"""
        prompt = (
            f"""
            You are a document classification expert. Your task is to classify a document name based on the provided numbering rule.

            Numbering Rule:
            {rule_text}

            Document Name to Classify:
            {filename}

            Instructions:
            1. Analyze the document name using the numbering rule
            2. Determine the file type and discipline
            3. Return a JSON object with exactly these fields: file_type, discipline
            4. The explanation should be brief but clear

            Example Response Format:
            {{
                "file_type": "Procedure",
                "discipline": "Drafting"
            }}

            Return ONLY the JSON object, with no additional text or explanation.
            """
        )
        
        try:
            response = self.client.messages.create(
                model=self.model,
                max_tokens=1024,
                messages=[{"role": "user", "content": prompt}],
            )
            
            # Get the response text and clean it
            response_text = response.content[0].text.strip()
            
            # Try to find JSON object in the response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx == -1 or end_idx == 0:
                raise ValueError("No JSON object found in response")
                
            json_text = response_text[start_idx:end_idx]
            
            # Parse the response as JSON
            classification = json.loads(json_text)
            
            # Validate the response structure
            required_fields = ['file_type', 'discipline']
            if not all(field in classification for field in required_fields):
                raise ValueError(f"Classification missing required fields: {required_fields}")
                
            return classification
            
        except Exception as e:
            print(f"Error classifying file {filename}: {e}")
            return None

    def update_unclassified_files(self):
        """Update files that don't have document type or discipline"""
        try:
            # Get the project's rule
            project = Project.get_one_by(id=self.project_id)
            if not project or not project.numbering_rule:
                raise ValueError("No numbering rule found for this project")
            
            # Get unclassified files
            unclassified_files = self.get_unclassified_files()
            if not unclassified_files:
                print("No unclassified files found")
                return []
            
            updated_files = []
            for file in unclassified_files:
                # Classify the file
                classification = self.classify_file(file['name'], project.numbering_rule)
                if classification:
                    # Update the file in the database
                    File.update(file['id'], 
                              document_type=classification['file_type'],
                              document_discipline=classification['discipline'])
                    updated_files.append({
                        'file_id': file['id'],
                        'name': file['name'],
                        'classification': classification
                    })
            
            return updated_files
        except Exception as e:
            print(f"Error updating unclassified files: {e}")
            return []
    
    def parse(self):
        full_text = self.extract_full_text()
        result = self.generate_numbering_rule(full_text)
        # Extract the rule text from the response and clean it
        rule_text = result.content[0].text.strip()
        
        # Store rule in the database
        project_id = self.store_rule(rule_text)
        
        # Update unclassified files with the new rule
        updated_files = self.update_unclassified_files()
            
        return {
            'project_id': project_id,
            'rule_text': rule_text,
            'updated_files': updated_files
        }

class DocumentClassifier:
    def __init__(self, project_id):
        self.project_id = project_id
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.env_file_path = os.path.join(parent_dir, 'env.json')
        self.setup_agent()
        self.model = "claude-3-5-sonnet-20241022"

    def load_env(self):
        with open(self.env_file_path, 'r') as f:
            return json.load(f)

    def setup_claude_client(self, api_key):
        self.client = anthropic.Anthropic(api_key=api_key)

    def setup_agent(self):
        env_data = self.load_env()
        self.setup_claude_client(env_data.get('CLAUDE_API_KEY', ''))

    def get_project_rule(self):
        """Get the numbering rule from the project"""
        project = Project.get_by(id=self.project_id)
        if not project:
            raise ValueError(f"Project with ID {self.project_id} not found")
            
        if not project[0]["numbering_rule"]:
            raise ValueError("No numbering rule found for this project. Please generate rules first.")
            
        return project[0]["numbering_rule"]

    def classify_documents(self, filenames):
        """
        Classify multiple documents based on their filenames using the stored rules and LLM.
        
        Args:
            filenames (list): List of document names to classify
            
        Returns:
            dict: A dictionary with filenames as keys and their classifications as values
        """
        if not isinstance(filenames, list):
            filenames = [filenames]  # Convert single filename to list
            
        # Get the project's rule
        project_rule = self.get_project_rule()
        
        # Create a list to store filenames for the prompt
        filenames_text = "\n".join([f"- {filename}" for filename in filenames])
        
        prompt = (
            f"""
            You are a document classification expert. Your task is to classify document names based on the provided numbering rule.

            Numbering Rule:
            {project_rule}

            Document Names to Classify:
            {filenames_text}

            Instructions:
            1. Analyze each document name using the numbering rule
            2. Determine the file type and discipline for each document
            3. Return a JSON object where:
               - Keys are the original filenames
               - Values are objects containing file_type and discipline
            4. Each classification object must have exactly these fields: file_type, discipline
            5. The explanation should be brief but clear

            Example Response Format:
            {{
                "DRA-03-PRO-DC-002_1": {{
                    "file_type": "Procedure",
                    "discipline": "Drafting"
                }},
                "EL-02-SP-001": {{
                    "file_type": "Specification",
                    "discipline": "Electrical"
                }}
            }}

            Return ONLY the JSON object, with no additional text or explanation.
            """
        )
        
        try:
            response = self.client.messages.create(
                model=self.model,
                max_tokens=1024,
                messages=[{"role": "user", "content": prompt}],
            )
            
            # Get the response text and clean it
            response_text = response.content[0].text.strip()
            
            # Try to find JSON object in the response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx == -1 or end_idx == 0:
                print("Debug - Raw response:", response_text)
                raise ValueError("No JSON object found in response")
                
            json_text = response_text[start_idx:end_idx]
            
            # Parse the response as JSON
            classifications = json.loads(json_text)
            
            # Validate the response structure
            if not isinstance(classifications, dict):
                print("Debug - Parsed response:", classifications)
                raise ValueError("LLM response is not a dictionary of classifications")
                
            # Validate each classification has the required fields
            required_fields = ['file_type', 'discipline']
            for filename, classification in classifications.items():
                if not all(field in classification for field in required_fields):
                    print("Debug - Invalid classification:", classification)
                    raise ValueError(f"Classification for {filename} missing required fields: {required_fields}")
                    
            return classifications
            
        except json.JSONDecodeError as e:
            print("Debug - JSON Parse Error:", str(e))
            print("Debug - Response text:", response_text)
            raise ValueError(f"Failed to parse LLM response as JSON: {str(e)}")
        except Exception as e:
            print("Debug - Unexpected error:", str(e))
            raise

# === Example usage ===
if __name__ == "__main__":
    # Example 1: Generate a new rule and update unclassified files
    # file_path = "/home/<USER>/Downloads/MKD related/DRA-03-PRO-DC-002_1 Document Numbering Procedure.pdf"
    # project_id = "your-project-id"  # Replace with actual project ID
    # rule_generator = NumberingRuleGenerator(file_path, project_id)
    # result = rule_generator.parse()
    # print("✅ Document parsed and rule generated.")
    # print(f"Project ID: {result['project_id']}")
    # print("Rule:", result['rule_text'])
    # print("\nUpdated files:")
    # for file in result['updated_files']:
    #     print(f"\nFile: {file['name']}")
    #     print(f"Type: {file['classification']['file_type']}")
    #     print(f"Discipline: {file['classification']['discipline']}")
    pass