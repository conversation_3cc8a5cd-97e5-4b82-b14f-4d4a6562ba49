import asyncio
import json
from services.data_handler import DataManager


data_manager = DataManager()
async def runner():
    sources = await data_manager.extract_data_v2('fb28b6e6-41ae-4bc1-85f6-b910a465ceb0', ['Please kindly confirm whether the air clutch is a must when the VFD is provided?'], [], 5)
    return sources

# Execute the async function and dump the result to JSON
result = asyncio.run(runner())
print(json.dumps(result, indent=2))














Refrerence Documents:  P3803-000-ME-SPC-001 & P3803-000-ME-SOW-001_1



Criteria: 

Price (30)   Which of the Bidder satisfy the Price (30)
Delivery (30)   Which bidder has Delivery (30) 
Compliant (10)  Which bidder has Technical Compliant Bid (10)
Query Responses (10)   Which bidder has technical Query Responses (10)
Prior Experience (10)   Which bidder has Technical Prior Experience (10) 
Local Service (10)     Which bidder has Local Service (10) 



BidFile 1

BidFile 2

Pick from TEAR Files



{
    "file1": {
        "fileA": {}
        "fileB": {},
        "fileC": {
            "fileCA": {
                
            }
        }
    }
}