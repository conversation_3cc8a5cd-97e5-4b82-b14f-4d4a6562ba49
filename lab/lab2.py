import re 
def extract_document_info_v2(document_string, log, count):
    # Regular expressions to match document title and number, with optional whitespace
    number_match = re.search(r'<DOCUMENT_NUMBER>\s*(.*?)\s*</DOCUMENT_NUMBER>', document_string, re.DOTALL)
    discipline_match = re.search(r'<DISCIPLINE>\s*(.*?)\s*</DISCIPLINE>', document_string, re.DOTALL)
    deliverable_match = re.search(r'<DELIVERABLE>\s*(.*?)\s*</DELIVERABLE>', document_string, re.DOTALL)
    
    # Extracting the values and stripping whitespace
    doc_number = number_match.group(1).strip() if number_match else None
    doc_discipline = discipline_match.group(1).strip() if discipline_match else None
    doc_deliverable = deliverable_match.group(1).strip() if deliverable_match else None

    print([doc_number, doc_discipline, doc_deliverable])
    



doc_str = """
document info:  Based on the provided content, I will analyze the document and suggest the right discipline and deliverable under that discipline.

<DOCUMENT_NUMBER>  
    P4258-000-PHL-PR-001  
</DOCUMENT_NUMBER>  
<DISCIPLINE>  
    Process  
</DISCIPLINE>  
<DELIVERABLE>  
    ESD (Emergency Shut Down) Philosophy  
</DELIVERABLE>

The document falls under the "Process" discipline, and the deliverable is "ESD (Emergency Shut Down) Philosophy".
"""

extract_document_info_v2(doc_str, [], 0)