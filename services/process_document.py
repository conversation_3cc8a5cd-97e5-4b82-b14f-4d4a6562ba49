import json
import re
import time
import asyncio
import os,sys,re
from services.parser_openai_parellel import OpenAIProcessor
from services.pull_reference import find_reference
from services.summarization import SummarizationAssistant
from services.parser_openai import AIProcessor
from services.synonym_expansion import SynonymGenerator
from services.process_cbp_evaluate_chunks import AIChunckProcessor
from services.cohere_embedding import CohereService
from services.engr_doc_types_detection import EngrDocTypesDetection
from services.fast_apis_service import FastAPIs
from services.evaluation_templates import EvaluationTemplates

class FileProcessor:
    def __init__(self):
        self.open_ai_processor = OpenAIProcessor()
        self.summarization = SummarizationAssistant()
        self.find_reference = find_reference
        self.parser_openai = AIProcessor()
        self.synonym_service = SynonymGenerator()
        self.parser_claude = AIChunckProcessor()
        self.engr_doc_detector = EngrDocTypesDetection()
        self.type = ""


    async def process_file(self, file_path):
        result = await self.open_ai_processor.get_serialized_data_openai_parser(file_path)
        filename = os.path.basename(file_path)
        data = self.convert_to_objects(result[1], filename)
        idx = 0
        for x in data:
            print("Processing Section -> ", idx)
            idx+=1
            if x["content"] == "":
                x["content"] = x["title"]
                x["title"] = "No Title"

            print('Summarizing')
            try:
                x["summary"] = self.summarization.openai_summarization(x["title"]+ '\n' + x["content"])
            except Exception as E:
                print('Summarization not done')
                x["summary"] = ""
            x["tags"] = "[]"
            x["source"] = file_path
            print('Pulling Reference')
            referenceInTitle = self.find_reference(x["title"])
            referenceInContent = self.find_reference(x["content"])
            totalReference = referenceInTitle + referenceInContent
            x["reference"] = list(set(totalReference))
            x['reference_content'] = []

        pattern = r'Ref\s+(\d+(?:\.\d+)*)'
        for x in data:
            for y in x['reference']:
                ref_num = ''.join(re.findall(pattern, y))
                for z in data:
                    if ref_num in z['title'] or ref_num in z['content']:
                        x['reference_content'].append(z)


        return data

    async def process_file_for_seeding(self, file_path):
        from .process_pdf_ocr import process_df_with_ocr_support
        # result = await self.open_ai_processor.get_serialized_data_openai_parser(file_path)
        file_type = os.path.splitext(file_path)[-1].lower().lstrip('.')
        if file_type == 'pdf':
            #text_single_string, chunks = await self.open_ai_processor.extract_text_from_pdf(file_path)
            #result = chunks
            result = await process_df_with_ocr_support(file_path=file_path)
            if "error" in result:
                  raise Exception("Error in Loading PDF")
            else:
                result = result["text"]
                print(f"FIRST FEW TEXTS FROM PDF : {result[0][:50]}")
        elif file_type in ["docx", "doc"]:
            chunks = await self.open_ai_processor.extract_text_from_docx(file_path)
            result = chunks[1]
        elif file_type in ["csv", "xls", "xlsx", "xlsm"]:
            chunks = await self.open_ai_processor.extract_text_from_excel_or_csv_batch(file_path)
            result = chunks[1]
        else:
            raise ValueError(f"Unsupported file type: {file_type}")

        filename = os.path.basename(file_path)
        print('result: ', result)
        data = self.convert_to_objects(result, filename)
        return data
    
    def process_file_for_seeding_sync(self, file_path):
        from .process_pdf_ocr import process_pdf_with_ocr_support
        # result = await self.open_ai_processor.get_serialized_data_openai_parser(file_path)
        file_type = os.path.splitext(file_path)[-1].lower().lstrip('.')
        if file_type == 'pdf':
            #text_single_string, chunks = await self.open_ai_processor.extract_text_from_pdf(file_path)
            #result = chunks
            result = process_pdf_with_ocr_support(file_path=file_path)
            if "error" in result:
                  raise Exception("Error in Loading PDF")
            else:
                result = result["text"]
                print(f"FIRST FEW TEXTS FROM PDF : {result[0][:50]}")
        elif file_type in ["docx", "doc"]:
            chunks = self.open_ai_processor.extract_text_from_docx_sync(file_path)
            result = chunks[1]
        elif file_type in ["csv", "xls", "xlsx", "xlsm"]:
            chunks = self.open_ai_processor.extract_text_from_excel_or_csv_batch_sync(file_path)
            result = chunks[1]
        else:
            raise ValueError(f"Unsupported file type: {file_type}")

        filename = os.path.basename(file_path)
        print('result: ', result)
        data = self.convert_to_objects(result, filename)
        return data

    def convert_to_objects(self, text_array, filename):

        # Initialize the result list
        result = []
        page_number = 1        

        # Iterate over the array of text
        for count, text in enumerate(text_array):
            
            # Create the object with better metadata
            obj = {
                'title': "",
                'content': text,
                'page_number': page_number,
                'document_name': filename,
                'section_number': count
            }
            page_number += 1

            # Append the object to the result list
            result.append(obj)

        return result

    async def process_new_file(self, file_path, log={}):
        print(f"Entering process new file {file_path}")
        t = time.time()
        data = await self.open_ai_processor.get_serialized_data_openai_parser(file_path)
        log["serialization_time"] = time.time() - t
        print(f"print get_serialized_data_serverless finished!!!")
        # print(data)
        t = time.time()
        print(f"Entering generate_questions_and_fields_parellel with data above:")
        log["question_generation_time"] = time.time() - t
        print(f"print generate_questions_and_fields_parellel finished!!!")
        # print(data)
        return data

    async def process_new_file_SpecsComply(self, file_path, criteria, source_document_text, count, log={}):
        print(f"Entering process new scp file {file_path}")
        #-- the below chunk the data, parse by appending sign & also group into title & contents
        result = await self.open_ai_processor.get_serialized_data_openai_parser(file_path)

        #--- detect document title & number
        await self.engr_doc_detector.get_claude_to_detect_document_details(result[1][0:3], log)

        data = result[1] #chunkarray
        print(f"get_serialized_data_serverless finished SCP!!!")

        new_satisfied_data = []
        evaluate_data, token_in, token_out = await self.master_evaluation_v2(data, source_document_text, criteria, count, 3)

        # resetting the array values
        log['evaluation_report_section']['strength_chunk'] = []
        log['evaluation_report_section']['weak_chunk'] = []
        log['evaluation_report_section']['risk_chunk'] = []

        # good_response = ['Non-compliant or no response','Full Compliance', 'Inadequate Response', 'Minimal Compliance', 'Higher Partial Compliance', 'Full Compliance']
        # Check if there's at least one good response
        # has_good_response = any(section['evaluation_data'][0] in good_response for section in evaluate_data)
        # if has_good_response:
        #     # Add only good responses
        #     for section in evaluate_data:
        #         # if section['evaluation_data'][0] not in bad_response:
        #         new_satisfied_data.append(section)
        # else:
        #     # Add all responses if no good responses found
        #     new_satisfied_data.extend(evaluate_data)

        new_satisfied_data = []
        for section in evaluate_data:
            new_satisfied_data.append(section)


        # Evaluate each section and append to the appropriate chunk in the log
        for section in evaluate_data:
            score_str = section['evaluation_data'][1]
            # Remove the percentage sign if it exists
            if score_str.endswith('%'):
                score_str = score_str[:-1]
            score = int(score_str)
            if 20 < score < 81:
                log['evaluation_report_section']['weak_chunk'].append(section)
            elif score >= 81:
                log['evaluation_report_section']['strength_chunk'].append(section)
            elif score <= 20:
                log['evaluation_report_section']['risk_chunk'].append(section)

        print("Done with sections evaluation SCP....")
        return new_satisfied_data, token_in, token_out

    async def  process_new_file_Chronobid(self, file_path, criteria, source_document_text, count, log={}):
        print(f"Entering process new chronobid file {file_path}")
        t1 = time.time()
        #-- the below chunk the data, parse by appending sign & also group into title & contents ---#
        result = await self.open_ai_processor.get_serialized_data_openai_parser(file_path)

        # Calculate time taken for dataset1
        t2 = time.time()
        dataset1_duration = (t2 - t1) / 60  # Convert seconds to minutes
        print(f"serialize text done in {dataset1_duration:.2f} minutes.....\n\n")

        data = result[1] #chunkarray

        # Start counting time for dataset2 retrieval
        t3 = time.time()
        evaluate_data, token_in, token_out = await self.master_evaluation_v2(data, source_document_text, criteria, count, 3)

        # log['upload_potential_data_chunks'] = section_texts
        # Calculate time taken for dataset2
        t4 = time.time()
        dataset2_duration = (t4 - t3) / 60  # Convert seconds to minutes
        print(f"master evaluation done in {dataset2_duration:.2f} minutes.....\n\n")

        # resetting the array values
        log['evaluation_report_section']['strength_chunk'] = []
        log['evaluation_report_section']['weak_chunk'] = []
        log['evaluation_report_section']['risk_chunk'] = []

        # Evaluate each section and append to the appropriate chunk in the log
        for section in evaluate_data:
            score_str = section['evaluation_data'][1]
            # Remove the percentage sign if it exists
            if score_str.endswith('%'):
                score_str = score_str[:-1]

            score = int(score_str)
            if 20 < score < 81:
                log['evaluation_report_section']['weak_chunk'].append(section)
            elif score >= 81:
                log['evaluation_report_section']['strength_chunk'].append(section)
            elif score <= 20:
                log['evaluation_report_section']['risk_chunk'].append(section)


        print("Done with sections evaluation CBP....")
        return evaluate_data, token_in, token_out

    async def process_source_file_specs(self, chunks_array, criteria, count, log={}):
        synonyms = await self.synonym_service.generate_synonym(f"{criteria['name']}\n Description: {criteria['description']}")
        query = f"""

            Question {count}
            Title: {criteria['name']}
            Description: {criteria['description']}

            {synonyms}

        """
        print('Query from source document SCP...')

        self.cohere_embedding = CohereService()
        retrieved_texts = self.cohere_embedding.search_similar(query, chunks_array)
        return retrieved_texts[0] #return first text assumed to be best text

    async def master_evaluation_v2(self, chunked_text, source_document_text, criteria, count, limit=3):

        synonyms = await self.synonym_service.generate_synonym(f"{criteria['name']}\n Description: {criteria['description']}")
        token_count_in = 0
        token_count_out = 0

        # First run: for the question title & description
        query_title_desc = f"""
            Question {count}
            Title: {criteria['name']}
            Description: {criteria['description']}
        """

        # Capture both name and description together
        name_descriptions = re.findall(r"Name:\s*(.+?)\n\s*Description:\s*(.+?)(?:\n|</SYNONYMS>)", synonyms, re.DOTALL)

        # Combine each name and description into a single string
        name_description_pairs = [f"{name.strip()} - {description.strip()}" for name, description in name_descriptions]


        t1 = time.time()
        self.cohere_embedding = CohereService()
        retrieved_master_texts = self.cohere_embedding.search_similar(query_title_desc, chunked_text)
        synonym_searches = [
            self.cohere_embedding.search_similar(pair, chunked_text, 2)
            for pair in name_description_pairs
        ]
        synonym_results = [result for results in synonym_searches for result in results]

        # Calculate time taken for dataset1
        t2 = time.time()
        dataset1_duration = (t2 - t1) / 60  # Convert seconds to minutes
        print(f"Cohere searching text done in {dataset1_duration:.2f} minutes.....\n\n")

        # Merging the two arrays
        processing_lists = [retrieved_master_texts, synonym_results]
        # Start counting time for dataset2 retrieval
        t3 = time.time()
        unified_responses = []
        for current_list in processing_lists:
            for section in current_list:
                section_text = section
                try:
                    task = self.parser_claude.get_claude_to_evaluate_chunks_based_on_criteria(section_text, source_document_text, criteria, count)
                    self.type = "claude"
                    if asyncio.iscoroutine(task):
                        evaluation_data, token_in, token_out, response_agent_1, response_agent_2 = await task
                    else:
                        raise RuntimeError("Task from Claude API is not a coroutine")
                except Exception as e:
                    print(f"Error with Claude API: {e}. Falling back to OpenAI API.")

                evaluation_result = await self.extract_data_from_string(evaluation_data)
                unified_responses.append({
                    "text_content": section_text,
                    "evaluation_data": evaluation_result
                })
                token_count_in += token_in
                token_count_out += token_out

                # Check if the score exceeds 60
                score = float(evaluation_result[1]) if evaluation_result[1].isdigit() else 0
                if score >= 60:
                    print(f"Score {score} exceeds 60, stopping further processing.")
                    return unified_responses, token_count_in, token_count_out

        # Calculate time taken for dataset2
        t4 = time.time()
        dataset2_duration = (t4 - t3) / 60  # Convert seconds to minutes
        print(f"Claude evaluation chunk extraction done in {dataset2_duration:.2f} minutes.....\n\n")
        return unified_responses, token_count_in, token_count_out

    async def extract_data_from_string(self, data):
        # print("i am data string:", data)
        if self.type == "gpt":
            data_string = data.choices[0].message.content
        elif self.type == "claude":
            data_string = data

        # Define regex patterns for different tags
        evaluation_pattern = re.compile(r'<Evaluation>(.*?)</Evaluation>', re.IGNORECASE | re.DOTALL)
        score_pattern = re.compile(r'<Score>(.*?)</Score>', re.IGNORECASE | re.DOTALL)
        content_pattern = re.compile(r'<Content>(.*?)</Content>', re.IGNORECASE | re.DOTALL)
        reason_pattern = re.compile(r'<Reason>(.*?)</Reason>', re.IGNORECASE | re.DOTALL)
        reference_pattern = re.compile(r'<Reference>(.*?)</Reference>', re.IGNORECASE | re.DOTALL)

        # Extract data using each pattern
        evaluation_sections = evaluation_pattern.findall(data_string)
        score_sections = score_pattern.findall(data_string)
        content_sections = content_pattern.findall(data_string)
        reason_sections = reason_pattern.findall(data_string)
        reference_sections = reference_pattern.findall(data_string)

        # Check if lists are empty before accessing their elements
        data1 = evaluation_sections[0] if evaluation_sections else ''
        data2 = score_sections[0] if score_sections else ''
        data3 = content_sections[0] if content_sections else ''
        data4 = reason_sections[0] if reason_sections else ''
        data5 = reference_sections[0] if reference_sections else ''

        # Merge extracted data into a list
        merge = [data1, data2, data3, data4, data5]

        return merge

    async def evaluate_source_document(self,text, criteria, count):
        """
        Perform evaluation of text against given criteria using text embeddings and evaluation tasks.

        Args:
            text (str): The text to be evaluated.
            criterias (list): List of criteria for evaluation.

        Returns:
            list: List of evaluation results for each section of the text.
        """

        source_document_unified_responses = []
        for section_text in text:
            try:
                task = self.parser_claude.get_claude_to_evaluate_chunks_based_on_criteria(section_text, criteria, count)
                self.type = "claude"
                if asyncio.iscoroutine(task):
                    evaluation_data = await task
                else:
                    raise RuntimeError("Task from Claude API is not a coroutine")
            except Exception as e:
                return (f"Error with Claude API: {e}. Falling back to OpenAI API.")

            formatted_response = await self.extract_data_from_string(evaluation_data)
            if len(formatted_response) > 0 and formatted_response['evaluation_data'][0] != "Non-Compliance":
                source_document_unified_responses.append({
                    "text_content": section_text,
                    "source_document_evaluation_data": formatted_response
                })

        return source_document_unified_responses


if __name__ == "__main__":
    client = FileProcessor()
    asyncio.run(client.process_file_for_seeding("vendor_file.pdf"))

