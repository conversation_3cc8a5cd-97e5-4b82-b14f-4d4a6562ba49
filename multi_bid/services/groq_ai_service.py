import random
from groq import <PERSON>roq
from services.groq_api_key_provider import GroqAPIKeyProvider
from pydantic import BaseModel
import instructor
import os, json
class GroqService:
    def __init__(self):

        self.api_key_provider = GroqAPIKeyProvider()
        self.parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.env_data = self._load_env()
        # Map deployments to models
        self.deployments_map = {
            "Llama 3 8B": "llama3-8b-8192",
            "Llama 3 70B": "llama3-70b-8192",
            "Mixtral 7B": "mixtral-8x7b-32768"
        }

        self.model_ids = ["llama3-8b-8192", "llama3-70b-8192", "mixtral-8x7b-32768"]

        # Select a default model
        self.selected_model = self.deployments_map["Llama 3 8B"]

    def _load_env(self):
        env_path = os.path.join(self.parent_dir, 'env.json')
        with open(env_path, 'r') as f:
            return json.load(f)
        
    def generate_completion(self, messages, temperature=0.01, max_tokens=1024, model=None, top_p=1.0, stream=False, stop=None):
        random.shuffle(self.model_ids)
        api_key = self.api_key_provider.get_ready_api_key()
        self.client = Groq(api_key=api_key)
        if api_key:
            models_to_try = [model] if model else self.model_ids
            print('i am model to try: ', models_to_try)
            for model_id in models_to_try:
                print(model_id)
                try:
                    # Create a chat completion request
                    completion = self.client.chat.completions.create(
                        model=model_id,
                        messages=messages,
                        temperature=temperature,
                        max_tokens=max_tokens,
                        top_p=top_p,
                        stream=stream,
                        stop=stop,
                    )
                    if stream:
                        response = ""
                        for chunk in completion:
                            response += chunk.choices[0].delta.content or ""
                        return response
                    else:
                        return completion.choices[0].message.content
                except Exception as e:
                    print(f"Error with model {model}: {e}")
            return None
    

    def extract_data(self, schema: BaseModel, messages, model=None):
        """
        Extract data using Groq with the specified schema and prompts.

        :param schema: Pydantic model specifying the response schema
        :param system_prompt: The system-level instruction for the Groq model
        :param user_prompt: The user-level input text to extract data from
        :return: An instance of the schema populated with extracted data
        """
        try:
            random.shuffle(self.model_ids)
            api_key = self.api_key_provider.get_ready_api_key()

            self.client = instructor.from_groq(Groq(api_key=self.env_data.get(api_key, '')), mode=instructor.Mode.JSON)
            models_to_try = [model] if model else self.model_ids
            for model_id in models_to_try:
                print(model_id)
                try:
                    completion = self.client.chat.completions.create(
                        model=model_id,
                        response_model=schema,
                        messages=messages,
                        temperature=0.01,
                    )
                    return completion
                except Exception as e:
                    print(f"Error with model {model}: {e}")
        except Exception as e:
            print(f"Error generating completion: {e}")
