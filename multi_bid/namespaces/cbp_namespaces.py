from datetime import datetime
from flask_socketio import emit, Namespace
from init import create_app
from rq import Retry
from flask import request
from services.background_task_processor import BackgroundTaskProcessor
from extensions import socketio, queues
import random

class CBPNamespace(Namespace):
    def __init__(self, namespace, socket_manager):
        super().__init__(namespace)
        self.socket_manager = socket_manager
        print('initialized cbp namespace....')

    def on_connect(self):
        """Handle connection to /cbp namespace."""
        try:
            sid = request.sid
            print(f"[{self._get_timestamp()}] New client connected: {sid}")
            emit("progress_message", {
                "message": "Test emit from server, congratulations on joining CBP Namespace on CPU",
                "timestamp": self._get_timestamp()
            })
            print("Client connected to /cbp namespace")
        except Exception as e:
            print(f"Error in on_connect: {str(e)}")

    def on_disconnect(self):
        """Handle disconnection from /cbp namespace."""
        try:
            sid = request.sid
            print(f"[{self._get_timestamp()}] Client {sid} disconnecting from /cbp namespace")
            self.socket_manager.remove_client(sid)
            print(f"Client {sid} disconnected and removed from room management")
        except Exception as e:
            print(f"Error in on_disconnect: {str(e)}")

    def on_leave(self, data):
        """Handle client leaving a room."""
        try:
            sid = request.sid
            request_id = data.get('request_id')
            if request_id:
                print(f"[{self._get_timestamp()}] Client {sid} leaving room {request_id}")
                self.socket_manager.remove_client(sid)
                emit("progress_message", {
                    "message": f"Client left room {request_id}",
                    "timestamp": self._get_timestamp()
                }, room=request_id)
                print(f"Client left room {request_id} in /cbp namespace")
        except Exception as e:
            print(f"Error in on_leave: {str(e)}")

    def on_join(self, data):
        """Handle client joining the room."""
        try:
            sid = request.sid
            request_id = data.get('request_id')
            is_reconnect = data.get("is_reconnect", False)

            if not request_id:
                print(f"[{self._get_timestamp()}] Error: No request_id provided for client {sid}")
                emit("error", {
                    "message": "No request_id provided",
                    "timestamp": self._get_timestamp()
                }, room=sid)
                return

            print(f"[{self._get_timestamp()}] Client {sid} joining room {request_id} (reconnect: {is_reconnect})")

            if is_reconnect:
                self.socket_manager.handle_reconnect(sid, request_id)
            else:
                self.socket_manager.add_client(sid, request_id)

                # Emit join confirmation
                join_message = {
                    "message": f"Client joined room {request_id} on CPU",
                    "timestamp": self._get_timestamp(),
                    "request_id": request_id,
                    "is_reconnect": is_reconnect
                }
                
                # Emit to the specific client
                emit("progress_message", join_message, room=sid)
                
                # Emit to all clients in the room
                emit("client_joined", {
                    "message": f"New client joined the room",
                    "timestamp": self._get_timestamp()
                }, room=request_id)

                # Log room members after join
                room_members = self.socket_manager.get_room_members(request_id)
                
                # For CBP tasks
                cbp_processor = BackgroundTaskProcessor(task_type="celery", process_type="evaluate_cbp")
                cbp_processor.process_task(request_id=request_id)

                # # with app.app_context():
                # q = random.choice(queues["cbp_"])              
                # q.enqueue(evaluate_files_cbp_task, request_id, retry=Retry(max=2))
               
                print(f"Current room {request_id} members: {room_members}")

        except Exception as e:
            print(f"Error in on_join: {str(e)}")
            emit("error", {
                "message": f"Error joining room: {str(e)}",
                "timestamp": self._get_timestamp()
            }, room=sid)

    def _get_timestamp(self):
        """Helper method to get formatted timestamp."""
        return datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')

