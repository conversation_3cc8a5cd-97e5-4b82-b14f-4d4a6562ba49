import os
import uuid
import json
import time
import random
import numpy as np
import pandas as pd
import itertools
from tqdm.auto import tqdm
from pinecone import Pinecone, ServerlessSpec
from langchain.text_splitter import RecursiveCharacterTextSplitter
from openai import AzureOpenAI
from services.cohere_embedding import CohereService
from concurrent.futures import ThreadPoolExecutor
import asyncio
import ssl


class CanopyAI:
    def __init__(self):
        # Get the path to the parent directory
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # Load environment variables from env.json file in the parent directory
        env_file_path = os.path.join(parent_dir, 'env.json')
        with open(env_file_path, 'r') as f:
            env_data = json.load(f)
        
        
        # Set up Pinecone client
        print(f"Pinecone api key: {env_data.get('PINECONE_API_KEY', '')}")
        self.pc = Pinecone(api_key=env_data.get("PINECONE_API_KEY", ""))

        # self.index = self.pc.Index("canopy-ai-energy-v3")
        self.index = self.pc.Index("aienergy-v1")
        
        # Set up Azure OpenAI client
        self.azure_client = AzureOpenAI(
            azure_endpoint=env_data.get("AZURE_ENDPOINT", ""),
            api_key=env_data.get("AZURE_API_KEY", ""),
            api_version=env_data.get("AZURE_API_VERSION", "")
        )
        self.deployments_map = {
            "gpt-3.5-turbo-16k": ["ai_energy-3-16k-1"],
            "gpt-4-turbo": ["ai_energy-4-turbo-1"],
            "gpt-3.5-turbo": ["ai_energy-3-turbo"],
            "text-embedding-ada-002": ["ai_energy_ada_2"]
        }
        
        self.cohere_service = CohereService()

    def create_index(self, index_name, dimension, metric):
        index_list = self.pc.list_indexes()
        indexes = [x["name"] for x in index_list]
        if index_name in indexes:
            return "exist"
        else:
            try:
                self.pc.create_index(
                    name=index_name,
                    dimension=dimension,
                    metric=metric,
                    spec=ServerlessSpec(
                        cloud="aws",
                        region="us-west-2"
                    )
                )
                while not self.pc.describe_index(index_name).status['ready']:
                    time.sleep(1)
                return "done"
            except Exception as e:
                return "error"

    def index_status(self, index_name):
        index = self.pc.Index(index_name)
        return index.describe_index_stats()

    def delete_index(self, index_name):
        return self.pc.delete_index(index_name)

    def get_metadata(self, data):
        return [{"title": x["title"], "content": x["content"], "source": x["source"], "documents": x["summary"]} for x in data]

    def get_metadata_v2(self, data):
        return [{"documents": x["content"], "section_id": x["section_id"]} for x in data]

    def create_embedding_v2(self, text, model='sentence-transformers/all-MiniLM-L12-v2'):
        pass
        # print("creating embedding... \n")
        # model = SentenceTransformer(model)
        # embedding = model.encode(text)
        # embedding = np.array(embedding).tolist()
        # return embedding
    
    def create_embedding_v3(self, texts, input_type="search_query"):
        embeddings = self.cohere_service.embed_texts_as_list(texts, input_type=input_type, model="embed-english-v3.0")
        return embeddings

    def batch_embed(self, texts, batch_size=50):
        with ThreadPoolExecutor(max_workers=4) as executor:  # Adjust max_workers based on hardware
            futures = [executor.submit(self.cohere_service.embed_texts_as_list, texts[i:i + batch_size], input_type="search_document", model="embed-english-v3.0") for i in range(0, len(texts), batch_size)]
            results = [future.result() for future in futures]
        return [item for sublist in results for item in sublist]

    def chunks(self, iterable, batch_size=200):
        """A helper function to break an iterable into chunks of size batch_size."""
        it = iter(iterable)
        chunk = tuple(itertools.islice(it, batch_size))
        while chunk:
            yield chunk
            chunk = tuple(itertools.islice(it, batch_size))
    
    def fetch_all_ids(self, index_name, namespace):
        print(index_name)
        index = self.pc.Index(index_name)
        resp = index.list(namespace=namespace)
        res = []
        for ids in resp:
            res += ids
        return res
    
    def fetch_all_data(self, index_name, namespace):
        index = self.pc.Index(index_name)
        ids = self.fetch_all_ids(index_name, namespace)
        l = 200
        res = []
        for i in range(0, len(ids), l):
            resp = index.fetch(ids = ids[i:i+l], namespace=namespace)
            for id in resp.vectors:
                data = resp.vectors[id]
                res.append({
                    "content": data['metadata']['documents'],
                    "section_id": data['metadata'].get('section_id', '')
                })
        return res
    
    def fecth_all_namespaces(self, index_name):
        index = self.pc.Index(index_name)
        
        return index.describe_index_stats()
        

    def save_to_db(self, project_id, data):
        index = self.index
        metadatas = self.get_metadata_v2(data)
        ids = [str(uuid.uuid4()) for x in range(0, len(data))]
        print("ids generation done...")
        contents = [x['content'] for x in data]
        values = self.create_embedding_v3(contents, "search_document") 
        df = pd.DataFrame({
            "id": ids,
            "metadata": metadatas,
            "values": values
        })
        print("done creating embedding for all text content...")
        try:
            print("inserting data...")
            data_to_upsert = [{"id": row['id'], "values": row['values'], "metadata": row['metadata']} for _, row in df.iterrows()]
            for batch in tqdm(self.chunks(data_to_upsert, batch_size=500), total=len(data_to_upsert) // 500):
                index.upsert(vectors=batch, namespace=project_id)
            return ids
        except Exception as e:
            return "error saving to pinecone db !!!"
        
    
    async def save_uploaded_doc_to_db(self, project_id, file_name, data):
        # Initialize the index once at setup time
        index = self.index

        ids = [str(uuid.uuid4()) for _ in range(len(data))]

        # Batch embedding generation
        embeddings = self.batch_embed(data, batch_size=50)
        print("Done creating embeddings...")

        data_to_upsert = [{"id": id, "values": value, "metadata": {"content": content}} for id, value, content in zip(ids, embeddings, data)]

        try:
            # Async batch upserts
            print("Inserting data....")

            await self.async_upsert(project_id, index, data_to_upsert, batch_size=500)

            print('done with insertion....')
            # return ids
        except Exception as e:
            print(f"Error: {e}")
            return "error saving to Pinecone DB!"
    
    async def async_upsert(self, project_id, index, vectors, batch_size=500):
        tasks = [
            asyncio.create_task(self.async_upsert_batch(project_id, index, batch))
            for batch in self.chunks(vectors, batch_size=batch_size)
        ]
        await asyncio.gather(*tasks)
    
    async def async_upsert_batch(self, project_id, index, batch):
        print('upserting batch...')
        try:
            result = index.upsert(batch, namespace=f"temp_{project_id}")  # Direct call without `to_thread`
            return result
        except Exception as e:
            print(f"Batch upsert failed: {e}")
            return None



    def get_data(self, project_id, queries, limit=3):
        # index = self.pc.Index("canopy-ai-energy-v3")
        index = self.pc.Index("aienergy-v1")
        query_vectors = self.create_embedding_v3(queries, "search_query")
        results = []
        # print("Queries: ",queries)
        for query in query_vectors:
            response = index.query(
                namespace=project_id,
                vector=query,
                top_k=limit
            )
            results.append(response)
        ids = []
        # print("ids: ",ids)
        for i in range(len(results)):
            temp = []
            for j in range(len(results[i]["matches"])):
                temp.append(results[i]["matches"][j]["id"])
            ids.append(temp)
            
        ids = [[match["id"] for match in result["matches"]] for result in results]
        scores = [[match["score"] for match in result["matches"]] for result in results]
        metadata = [[item['metadata'] for item in index.fetch(ids=ids[i], namespace=project_id)['vectors'].values()] for i in range(len(ids))]
        final_results = {
            "ids": ids,
            "distances": scores,
            "metadatas": metadata,
            "uris": None,
            "data": None
        }
        # print(final_results)
        return final_results
    
    def get_uploaded_data(self, project_id, queries, limit=3):
        # Connect to the Pinecone index
        # index = self.pc.Index("canopy-ai-energy-v3")
        index = self.pc.Index("aienergy-v1")
        
        # Generate embeddings for the queries
        query_vectors = self.create_embedding_v3(queries, input_type="search_query")
        
        # Prepare the results
        final_results = {"ids": [], "distances": [], "metadatas": []}

        # Query Pinecone for each query vector
        for query_vector in query_vectors:
            response = index.query(
                namespace=project_id,
                vector=query_vector,
                top_k=limit,
                include_metadata=True
            )

            # print('i am response: ', response)
            
            # Process the response
            ids = [match["id"] for match in response["matches"]]
            scores = [match["score"] for match in response["matches"]]
            metadata = [match["metadata"] for match in response["matches"]]
            
            # Append results
            final_results["ids"].append(ids)
            final_results["distances"].append(scores)
            final_results["metadatas"].append(metadata)
        
        # Return the compiled results
        return final_results


    def delete_namespace(self, project_id):
        # index = self.pc.Index("canopy-ai-energy-v3")
        index = self.pc.Index("aienergy-v1")
        return index.delete(delete_all=True, namespace=project_id)

    def chunk_text(self, text, chunk_size=512):
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=20
        )
        docs = text_splitter.create_documents([text])
        return docs

    def create_batches(self, data, batch_size=200):
        return [data[i:i + batch_size] for i in range(0, len(data), batch_size)]

    def create_embedding(self, text):
        pass
    
    def move_embeddings(self, from_index_name, to_index_name):
        from_index = self.pc.Index(from_index_name)
        to_index = self.pc.Index(to_index_name)
        
        index_stats = from_index.describe_index_stats()
        for namespace in index_stats.namespaces:
            print("Moving namespace: ", namespace)
            try:
                to_index.delete(namespace)
            except Exception as e:
                print("Namespace already empty", e)
            data = self.fetch_all_data(from_index_name, namespace)
            self.save_to_db(namespace, data)
        pass

if __name__ == "__main__":
    pinecone_service = CanopyAI()
    res = pinecone_service.move_embeddings('canopy-ai-energy-v2', 'canopy-ai-energy-v3')
    print(res)
    
    