import asyncio
from services.data_handler import DataManager
from services.cohere_embedding import CohereService
from services.synonym_expansion import SynonymGenerator


# New class for extracting dataset1
class Dataset1Extractor:
    def __init__(self, criteria):
        self.data_manager = DataManager()
        self.synonym_service = SynonymGenerator()
        self.criteria = criteria


    async def get_dataset1(self, project_id, count, log):
        try:
            # Here we extract data source1 from Pinecone based on criteria

            print('project id: ', project_id)
            print('criteria: ', self.criteria)

            responses = await self.data_manager.extract_data_specs_comply(project_id, self.criteria)

            if not responses['ids'] and len(responses['metadatas']) == 0:
                raise RuntimeError("Unable to find Source document")
            
            pinecone_data = []
            chunk_detailed_content = []
            source_document_text = ""
            for resp in range(len(responses['metadatas'])):
                metadata = responses['metadatas'][resp]

                pinecone_data.append(metadata.get("documents", ""))
                detailed_chunk = metadata.get("detailed_chunk", "")
                if detailed_chunk:
                    chunk_detailed_content.append(detailed_chunk)
                else:
                    source_document_text += metadata.get("documents", "") + "\n"
            
            if source_document_text != "":
                # source_document_text = await self.process_source_file_Chronobid([source_document_text], self.criteria, count, log)
                print('comes in whole..')
                return source_document_text
            else:
                # print('this is chunk_detailed: ', chunk_detailed_content)
                source_document_text = await self.process_source_file_specs(chunk_detailed_content, self.criteria, count, log)
                print('comes in chunk..')
                return source_document_text
        except Exception as e:
            print('failed to get Dataset1: ', e)
            return None

    async def process_source_file_specs(self, chunks_array, criteria, count, log={}):
        
        synonyms = await self.synonym_service.generate_synonym(f"{criteria}\n Description: {criteria}")
        query = f"""

            Question {count} 
            Title: {criteria}
            Description: {criteria}

            {synonyms}
            
        """
        # print('Query:', query)
        # print('Chunks array:', chunks_array)
        print('Query from source document CBP...')

        self.cohere_embedding = CohereService()
        retrieved_texts = self.cohere_embedding.search_similar(query, chunks_array)
        return retrieved_texts

async def get_dataset1():
    criteria = {"name": "Foriegn Investments Prohibition", "description": "Where is foreign investments prohibited", "weight": "100"}
    extractor = Dataset1Extractor(criteria)
    return await extractor.get_dataset1('2b6849da-34e1-4bc3-a47b-76a87831803c', 1, {"input_token": 0, "output_token": 0})

# New function to run the async method
def run_get_dataset1():
    result = asyncio.run(get_dataset1())  # Run the async function
    print(result)  # Print the result

if __name__ == "__main__":
    # Call the function to execute
    run_get_dataset1()