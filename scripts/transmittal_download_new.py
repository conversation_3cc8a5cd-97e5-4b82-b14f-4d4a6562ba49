"""
TODO: This script job is to be called as a cron job with following steps:
- the argv parameter --dir directory.json path pass in to read. Inside will have same keys eneight_test_directory_list.json
- we will have a main() function called to handle the logic of the script
- create a folder called ineight_download_files if not exist otherwise use existing folder
- Create a function called download_file_from_ineight(directory_list) loop through the directory_list and download the file from ineight and save to ineight_download_files folder
- Create a function called ingest_download_files(directory_list) loop through the directory_list and return the list, add a 1 second sleep between each download
"""

import argparse
import json
import os
import time
from pathlib import Path
import requests
import xml.etree.ElementTree as ET
import sys

from pinecone import Pinecone, ServerlessSpec
from langchain.embeddings.openai import OpenAIEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter
from concurrent.futures import ThreadPoolExecutor
import os
from datetime import datetime
from dateutil import parser
from cryptography.fernet import Fernet
import uuid
import traceback
import threading

# Get the parent directory (one level up)
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from services.cohere_embedding import CohereService


parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
# Load environment variables from env.json file in the parent directory
env_file_path = os.path.join(parent_dir, 'env.json')
with open(env_file_path, 'r') as f:
    env_data = json.load(f)
    
# pinecone.init(api_key=env_data.get("PINECONE_API_KEY", ""), environment=PINECONE_ENV)
pc = Pinecone(api_key=env_data.get("PINECONE_API_KEY", ""), environment="us-east-1")
# openai.api_key = env_data.get("OPENAI_API_KEY", "")
# embed_model = OpenAIEmbeddings(openai_api_key=env_data.get("OPENAI_API_KEY", ""))
embed_model = CohereService()

print("openai API key >>>>>> ", env_data.get("OPENAI_API_KEY", ""))

# Create or connect to Pinecone index
# pc.delete_index("transmitall-document")
index_name = "transmitall-document"
if index_name not in pc.list_indexes().names():
    pc.create_index(
        name=index_name,
        dimension=1024,  # Adjust as needed
        metric="cosine",
        spec=ServerlessSpec(cloud="aws", region="us-east-1")  # Change region accordingly
    )

index = pc.Index(index_name)

text_splitter = RecursiveCharacterTextSplitter(
    chunk_size=500,
    chunk_overlap=50,
    length_function=len,
    separators=["\n\n", "\n", " ", ""]
)

ALLOWED_EXTENSIONS = {'doc', 'docx', 'pdf', 'txt', "csv", "xls", "xlsx", "xlsm"}
def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def read_env_file(env_path):
    """Read and parse the environment JSON file"""
    try:
        with open(env_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error reading environment file: {e}")
        sys.exit(1)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Download and ingest files from InEight')
    parser.add_argument('--dir', required=True, help='Path to directory.json file')
    parser.add_argument('--env', required=True, help='Path to environment JSON file')
    return parser.parse_args()

def load_directory_list(file_path):
    """Load directory list from JSON file."""
    with open(file_path, 'r') as f:
        return json.load(f)

def create_download_folder(project_id):
    script_dir = os.path.dirname(os.path.abspath(__file__))
    base_dir = Path(os.path.join(script_dir, 'transmittal_files'))
    base_dir.mkdir(exist_ok=True)

    download_dir = base_dir / str(project_id)
    download_dir.mkdir(exist_ok=True)

    print(f"Created download folder: {download_dir}")
    return download_dir


def get_session_key(env_config):
    base_url = "https://sa1.doc.ineight.com/tbws/session.asmx/LogonTFAWithApplication"
    
    url = (
    f"https://sa1.doc.ineight.com/tbws/session.asmx/LogonTFAWithApplication"
    f"?UserID={env_config['userID']}"
    f"&CompanyID={env_config['companyID']}"
    f"&TfaToken={env_config.get('TfaToken', '.')}"
    f"&ProjNo={env_config['ProjectNo']}"
    f"&connectingProduct={env_config.get('connectingProduct', '.')}"
    f"&Password={env_config['Password']}"
    )
    try:
        response = requests.get(url)
        
        if response.text.strip().startswith('<!DOCTYPE HTML') or '<html' in response.text.lower():
            print(f"Received HTML error response: {response.text[:200]}...")
            return f"ERROR - Server returned HTML error response (status code: {response.status_code})"
        
        if response.status_code == 200:
            try:
                
                import xml.etree.ElementTree as ET
                root = ET.fromstring(response.text)
                session_key = root.text.strip() if root.text else None
                
                if session_key and "ERROR" in session_key:
                    print(f"Session key contains error: {session_key}")
                    return session_key
                
                if session_key:
                    return session_key
                else:
                    return "ERROR - No session key found in response"
            except ET.ParseError:
                print(f"Failed to parse XML response: {response.text[:200]}...")
                return f"ERROR - Invalid XML response from server"
        else:
            print(f"Failed to get session key. Status code: {response.status_code}")
            print(f"Response: {response.text}")
            return f"ERROR - Authentication failed (status code: {response.status_code})"
    except requests.RequestException as e:
        print(f"Request error getting session key: {e}")
        return f"ERROR - Network error: {str(e)}"
    except Exception as e:
        print(f"Unexpected error getting session key: {e}")
        return f"ERROR - Unexpected error: {str(e)}"

def get_download_url(session_key, int_key, env_config):
    
    print("int_key >> ", int_key)
    print("session_key >> ", session_key)
    # url = 'https://eu1.doc.ineight.com/tbws/document.asmx/GetDownloadLink'
    url = f"{env_config['base_url']}/tbws/document.asmx/GetDownloadLink"
    
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    
    data = {
        'sessionKey': session_key,
        'int_viewFile': str(int_key)
    }
    
    try:
        response = requests.post(url, headers=headers, data=data)
        response.raise_for_status()
        
        # Parse XML response
        root = ET.fromstring(response.text)
        
        # Extract download URL from XML
        download_url = root.text
        
        if not download_url:
            raise ValueError("No download URL found in response")
            
        return download_url
        
    except requests.RequestException as e:
        print(f"Failed to get download URL: {str(e)}")
        raise
    except (ET.ParseError, ValueError) as e:
        print(f"Failed to parse response: {str(e)}")
        raise

def get_file_extension(content_disposition, default_ext=".bin"):
    """
    Extract the file extension from the Content-Disposition header.
    Defaults to .bin if unknown.
    """
    if content_disposition:
        parts = content_disposition.split(";")
        for part in parts:
            if "filename=" in part:
                filename = part.split("=")[-1].strip().strip('"')
                return Path(filename).suffix if '.' in filename else default_ext
    return default_ext

def download_file_from_ineight(directory_list, download_dir, session_key, env_config):
    print("directory_list >> ")
    transmittal_run_id = 1
    last_time= 'Thu, 27 Mar 2025 18:17:56 GMT'
    if transmittal_run_id:
        # Fetch the last_time of the transmittal run using the API
        print("transmittal_run_id >> ", transmittal_run_id)
        response = requests.get(f'http://127.0.0.1:5120/transmittal_run/{transmittal_run_id}/last_time')
        print("response >> done")
        if response.status_code == 200:
            last_time_data = response.json()
            last_time = last_time_data.get('data', {}).get('last_time')
            print(f"Last time for transmittal run {transmittal_run_id}: {last_time}")
        else:
            print(f"Failed to fetch last_time for transmittal run {transmittal_run_id}: {response.text}")
    else:
        print("No TransmittalRunId found in item.")
    
    for item in directory_list:
        try:
            print("item >> ", item['SentOn'])
            last_time_dt = datetime.strptime(last_time, "%a, %d %b %Y %H:%M:%S %Z")
            sent_on_str = item['SentOn']
            
            if "." in sent_on_str:
                fraction_part = sent_on_str.split(".")[-1]
                if len(fraction_part) == 2:  # If only 2 fractional digits, add a zero
                    sent_on_str += "0"
             
            sent_on_dt = parser.isoparse(sent_on_str.replace("Z", "+00:00"))
            print("sent_on_dt >>", sent_on_dt)
            print("last_time_dt >>", last_time_dt)
            if sent_on_dt < last_time_dt:
                print(f"Item {item['DocumentNo']} has already been processed. Skipping download.")
                continue
            
            # Get download URL
            try:
                download_url = get_download_url(session_key, item['Int_Key'], env_config)
                print(f"Download URL: {download_url}")
            except Exception as e:
                print(f"Error getting download URL for {item.get('DocumentNo', 'Unknown')}: {str(e)}")
                continue
            
            doc_no = item['DocumentNo'].strip()
            Int_Key = item['Int_Key']
            
            # Download the file with streaming to handle large files
            response = requests.get(download_url, stream=True)
            response.raise_for_status()
            
            # Determine file extension from response headers
            content_disposition = response.headers.get('Content-Disposition', '')
            file_ext = get_file_extension(content_disposition, default_ext=".pdf")
            file_path = download_dir / f"{doc_no}_{Int_Key}{file_ext}"
            
            # Get total file size if available
            total_size = int(response.headers.get('content-length', 0))
            
            # Open file in binary write mode
            with open(file_path, 'wb') as f:
                if total_size == 0:
                    # No content length header - just download
                    f.write(response.content)
                else:
                    # Download with progress tracking for larger files
                    downloaded = 0
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            downloaded += len(chunk)
                            # Calculate progress percentage
                            progress = int((downloaded / total_size) * 100)
                            print(f"\rDownloading {doc_no}: {progress}%", end='')
                    print()  # New line after progress
            
            print(f"Successfully downloaded {doc_no} to {file_path}")
            
        except Exception as e:
            print(f"Error downloading {item['DocumentNo']}: {str(e)}")
            continue
            
        time.sleep(1)  # 1 second delay between downloads


def process_files_from_directory(project, File, BackgroundTaskProcessor, app):
    try:
        UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data')
        project_id = str(project['id'])
        print("project_id >> ", project_id)
        if not project_id:
            print("Error: Project ID is required")
            return {"success": False, "message": "Project ID is required"}

        # Define the source directory
        # base_dir = os.getcwd()
        base_dir = os.path.dirname(os.path.abspath(__file__))
        folder_path = os.path.join(base_dir, "transmittal_files", project_id)

        if not os.path.exists(folder_path):
            print(f"Error: Directory {folder_path} does not exist")
            return {"success": False, "message": f"Directory {folder_path} does not exist"}

        files = [
            f for f in os.listdir(folder_path)
            if os.path.isfile(os.path.join(folder_path, f)) and allowed_file(f)
        ]

        if not files:
            print("Error: No allowed files found in the directory")
            return {"success": False, "message": "No allowed files found in the directory"}

        result = []
        file_ids = []
        file_names = []
        all_success = True
        all_failed = True
        lock = threading.Lock()
        
        print("files >> ", files)

        def process_file(file_name):
            with app.app_context():
                nonlocal all_success, all_failed
                file_path = os.path.join(folder_path, file_name)
                try:
                    file_id = str(uuid.uuid4())
                    file_extension = file_name.rsplit('.', 1)[-1].lower()
                    pdf_conversion_status = 'done' if file_extension == 'pdf' else 'idle'

                    file_data = {
                        'id': file_id,
                        'name': file_name,
                        'project_id': project_id,
                        'file_dirtry': UPLOAD_FOLDER,
                        'file_type': file_extension,
                        'pdf_conversion_status': pdf_conversion_status
                    }

                    File.create(**file_data)

                    with lock:
                        all_failed = False
                        file_ids.append(file_id)
                        file_names.append(file_name)
                        result.append({"success": True, "file": file_name})

                    print(f"Successfully processed file: {file_name}")
                except Exception as e:
                    with lock:
                        all_success = False
                        result.append({"success": False, "file": file_name, "error": str(e)})
                    print(f"Failed to process file {file_name}: {str(e)}")

        with ThreadPoolExecutor() as executor:
            executor.map(process_file, files)

        message = (
            "All files successfully processed" if all_success else
            "All files failed to process" if all_failed else
            "One or more files failed to process"
        )

        print(f"Processing complete: {message}")
        print(f"folder_path: {folder_path}")
        print(f"UPLOAD_FOLDER: {UPLOAD_FOLDER}")

        BackgroundTaskProcessor.send(
            UPLOAD_FOLDER,
            project_id,
            file_ids,
            file_names,
            None
        )

        return {
            "message":message, 
            "result":result
        }

    except Exception as e:
        traceback.print_exc()
        print(f"Error in process_files_from_directory: {str(e)}")
        return {"success": False, "message": f"System error: {str(e)}"}


def ineight_download_handle(FERNET_KEY, Project, File, BackgroundTaskProcessor, app):
    print("Starting ineight_download_handle...")
    results = []
    
    try:
        
        cipher = Fernet(FERNET_KEY.encode())
        
        ineight_info_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ineight_info')
        
        if not os.path.exists(ineight_info_dir):
            print(f"Directory {ineight_info_dir} does not exist")
            return [{'status': 'error', 'error': f'Directory {ineight_info_dir} does not exist'}]
        
        # Get all JSON files in the directory
        json_files = [f for f in os.listdir(ineight_info_dir) if f.endswith('.json') and f.startswith('transmittal_')]
        
        if not json_files:
            print(f"No transmittal JSON files found in {ineight_info_dir}")
            return [{'status': 'error', 'error': 'No transmittal JSON files found'}]
        
        print(f"Found {len(json_files)} transmittal files")
        
        # Process each JSON file
        for json_file in json_files:
            print("json_file >> ", json_file)
            try:
                # Extract project ID from filename (format: transmittal_PROJECT_ID_TIMESTAMP.json)
                parts = json_file.split('_')
                if len(parts) >= 2:
                    project_id = parts[1]
                    
                    # Fetch project details from database
                    project = Project.get_single(project_id)
                    
                    if not project:
                        print(f"Project with ID {project_id} not found in database")
                        results.append({
                            'file': json_file,
                            'project_id': project_id,
                            'status': 'error',
                            'error': 'Project not found in database'
                        })
                        continue
                    
                    # Read the JSON file
                    file_path = os.path.join(ineight_info_dir, json_file)
                    with open(file_path, 'r') as f:
                        directory_data = json.load(f)
                    
                    # Decrypt credentials for this project
                    def decrypt(val):
                        return cipher.decrypt(val.encode()).decode() if val else None
                    
                    ineight_project_id = decrypt(project.get('ineight_project_id'))
                    ineight_user_id = decrypt(project.get('ineight_user_id'))
                    ineight_company_id = decrypt(project.get('ineight_company_id'))
                    ineight_password = decrypt(project.get('ineight_password'))
                    
                    # Skip if any credential is missing
                    if not all([ineight_project_id, ineight_user_id, ineight_company_id, ineight_password]):
                        print(f"Skipping project {project['name']} due to missing credentials")
                        results.append({
                            'file': json_file,
                            'project_id': project_id,
                            'project_name': project.get('name', 'Unknown'),
                            'status': 'error',
                            'error': 'Missing credentials'
                        })
                        continue
                    
                    # Create environment config for this project
                    env_config = {
                        'base_url': 'https://sa1.doc.ineight.com',
                        'userID': ineight_user_id,
                        'companyID': ineight_company_id,
                        'Password': ineight_password,
                        'ProjectNo': ineight_project_id,
                        'TfaToken': '.',
                        'connectingProduct': '.',
                        'Application': 'InEight Document'
                    }
                    
                    # Get session key for this project
                    session_key = get_session_key(env_config)
                    # print("session_key >>", session_key)
                    # Check if session key contains an error message
                    if isinstance(session_key, str) and "ERROR" in session_key:
                        print(f"Session key error for project {project['name']}: {session_key}")
                        results.append({
                            'file': json_file,
                            'project_id': project_id,
                            'project_name': project.get('name', 'Unknown'),
                            'status': 'error',
                            'error': f'Authentication failed: {session_key}'
                        })
                        continue
                    
                    # Create download folder
                    download_dir = create_download_folder(project_id)
                    
                    # Download files
                    print(f"Starting file downloads for project {project['name']}...")
                    download_file_from_ineight(directory_data, download_dir, session_key.strip('"'), env_config)
                    
                    # Ingest files
                    print(f"Starting file ingestion for project {project['name']}...")
                    # process_result = process_files_from_directory(project, File, BackgroundTaskProcessor, app)
                    # ingest_download_files(directory_data, download_dir)
                    
                    results.append({
                        'file': json_file,
                        'project_id': project_id,
                        'project_name': project.get('name', 'Unknown'),
                        'status': 'success',
                        'message': f'Processed {len(directory_data)} documents',
                        "process_result": process_result
                    })
                    
                else:
                    print(f"Invalid filename format: {json_file}")
                    results.append({
                        'file': json_file,
                        'status': 'error',
                        'error': 'Invalid filename format'
                    })
            
            except Exception as e:
                print(f"Error processing file {json_file}: {e}")
                results.append({
                    'file': json_file,
                    'status': 'error',
                    'error': str(e)
                })
        
        return results
    
    except Exception as e:
        print(f"Error in ineight_download_handle: {e}")
        return [{'status': 'error', 'error': f'System error: {str(e)}'}]
    
if __name__ == "__main__":
    from init import app
    from models import Project, File
    with app.app_context():
        ineight_download_handle("R8337urkBHZraE-DxfGobPJzTW58f50SAS4hPulD_8s=", Project, File, {}, app)


# def main():
#     """Main function to orchestrate the download and ingestion process."""
#     # Parse command line arguments
#     print("start parse")
#     args = parse_args()
#     print("end parse")
#     # with open('eneight_env.json') as f:
#     #     env_config = json.load(f)
#     env_config = read_env_file(args.env)
#     # Get session key
#     try:
#         session_key = get_session_key(env_config)
#         print(f"Successfully obtained session key", session_key)
#     except Exception as e:
#         print(f"Failed to get session key: {str(e)}")
#         return
        
#     # Load directory list
#     directory_list = load_directory_list(args.dir)
    
#     # Create download folder 
#     download_dir = create_download_folder()
    
#     # Download files
#     # print("download_dir >> ", download_dir)
#     print("Starting file downloads...")
#     download_file_from_ineight(directory_list, download_dir, session_key.strip('"'), env_config)
#     # download_file_from_ineight(directory_list, download_dir, "74c3aa55-3563-4cc9-942d-5cf3b2644572", env_config)
#     transmittal_run_id=1
#     last_time_update_url = f'http://localhost:5130/transmittal_run/{transmittal_run_id}/last_time'
#     update_payload = {'last_time': datetime.now().isoformat()}
    
#     try:
#         update_response = requests.put(last_time_update_url, json=update_payload)
#         update_response.raise_for_status()
#         print(f"Successfully updated last_time for transmittal run {transmittal_run_id}.")
#     except requests.RequestException as e:
#         print(f"Failed to update last_time for transmittal run {transmittal_run_id}: {str(e)}")
#     # Ingest files
#     print("\nStarting file ingestion...")
#     ingest_download_files(directory_list, download_dir)
#     try:
#         os.remove('transmittal_report.json')
#         print("Successfully removed transmittal_report.json")
#     except FileNotFoundError:
#         print("Warning: transmittal_report.json not found")
#     except Exception as e:
#         print(f"Error removing transmittal_report.json: {str(e)}")
#     print("\nProcess completed!")

# if __name__ == "__main__":
#     main()
