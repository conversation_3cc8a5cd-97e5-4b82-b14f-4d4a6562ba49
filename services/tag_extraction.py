from transformers import AutoModelForCausalLM, AutoTokenizer, pipeline
from auto_gptq import exllama_set_max_input_length
import torch

model_name_or_path = 'TheBloke/Mistral-7B-Instruct-v0.1-GPTQ'

model = AutoModelForCausalLM.from_pretrained(model_name_or_path,
                                             torch_dtype=torch.float16,
                                             device_map="cuda:0",
                                             revision="main")

tokenizer = AutoTokenizer.from_pretrained(model_name_or_path, use_fast=True)

model = exllama_set_max_input_length(model, 4096)


def tag_extraction(text):
    import time

    prompt_template='''
        ### System Prompt
        Analyze the provided content and generate a list of relevant tag words. Extract key terms, phrases, and concepts that accurately represent the central themes, topics, and subject matter of the content. Ensure the tag words are comprehensive, allowing users to understand the essence and scope of the content quickly.
        ### User Message: 4.12 Interface Management 
        Refer to the “PROJECT CONTROL” section of EXHIBIT - P – Administration  Instructions & Project Control. 
        CONTRACTOR shall acknowledge the COMPANY Interface Management  Procedure (Project1 INTERFACE CONTROL PROCEDURE – Ref. Project1-Company-PMT 
        0000-PM-PRO-0029), the Interface Register (INTERFACE REGISTER – Ref. NA 
        company-PMT-0812-PM-REG-0006-1), the Interface Alignment sheets (Project1  INTERFACE ALIGNMENT SHEETS – Ref. MUL-company-Project1 -0812-MD-IAS-0001 ) and  shall actively participate in the interface alignment process as required by  COMPANY.
        ### Assistant: Interface Management, Project Control Exhibit - P, Administration Instructions, CONTRACTOR, COMPANY Interface Management Procedure, Interface Control Procedure, Interface Register, Interface Alignment Sheets, Active Participation, Alignment Process, COMPANY, Project1, Administration, Procedure, Register, Instructions, Reference, Compliance, Coordination
    '''
    prompt_template += f'''
        ### User Message
        {text}
        ### Assistant
    '''
    
    input_ids = tokenizer(prompt_template, return_tensors='pt').input_ids.cuda()
    output = model.generate(inputs=input_ids, temperature=0, max_new_tokens=512)
    
    pipe = pipeline(
        "text-generation",
        model=model,
        tokenizer=tokenizer,
        max_new_tokens=4096,
        temperature=0,
        top_p=0.95,
        repetition_penalty=1.15,
        pad_token_id = tokenizer.eos_token_id,
        return_full_text=False
    )
    
    return pipe(prompt_template)[0]['generated_text']

