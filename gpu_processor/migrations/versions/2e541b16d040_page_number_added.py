"""Page Number Added

Revision ID: 2e541b16d040
Revises: d1520463393c
Create Date: 2024-08-03 03:21:17.302528

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.engine.reflection import Inspector


# revision identifiers, used by Alembic.
revision = '2e541b16d040'
down_revision = 'd1520463393c'
branch_labels = None
depends_on = None


def upgrade():
    # Get the current database connection
    conn = op.get_bind()
    inspector = Inspector.from_engine(conn)
    # Check if the 'page_number' column already exists
    columns = inspector.get_columns('chunks')
    if 'page_number' not in [column['name'] for column in columns]:
        # ### commands auto generated by Alembic - please adjust! ###
        with op.batch_alter_table('chunks', schema=None) as batch_op:
            batch_op.add_column(sa.Column('page_number', sa.String(length=36), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('chunks', schema=None) as batch_op:
        batch_op.drop_column('page_number')