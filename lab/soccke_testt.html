<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>WebSocket Test</title>
  <script src="https://cdn.socket.io/4.5.4/socket.io.min.js"></script>
</head>
<body>
  <h1>WebSocket Test</h1>
  <button id="connect">Connect</button>
  <button id="progress_message">progess</button>
  <button id="send">Send Message</button>
  <button id="joinMultiBid">Join MultiBid</button>
  <pre id="output"></pre>

  <script>
    let socket;
    const output = document.getElementById('output');

    document.getElementById('connect').addEventListener('click', () => {
      // Replace with your actual WebSocket server URL
      const serverUrl = "localhost:5130"; 
      socket = io(serverUrl + "/cbp", {
        transports: ["websocket"],
        reconnection: true,
        reconnectionAttempts: Infinity
      });
      socket.on("connect", () => {
        output.textContent += "Connected to /cbp namespace\n";
        socket.emit("join", { 
          request_id: "d6cd067b-62a5-4a13-a140-1cb672bfcd7f",
          is_multi_bid: false 
        });
      });

      socket.on("progress_message", (data) => {
        output.textContent += "Received: " + JSON.stringify(data) + "\n";
      });

      socket.on("error", (error) => {
        output.textContent += "WebSocket Error: " + JSON.stringify(error) + "\n";
      });

      socket.on("disconnect", () => {
        output.textContent += "WebSocket connection closed\n";
      });
    });

    document.getElementById('joinMultiBid').addEventListener('click', () => {
      if (socket && socket.connected) {
        socket.emit("join", { 
          request_id: "0b4238ea-84d9-4771-8cd6-14bba6573d8f",
          is_multi_bid: true 
        }); 
        output.textContent += "Joining with MultiBid enabled\n";
      } else {
        output.textContent += "Socket not connected! Please connect first.\n";
      }
    });
    document.getElementById('send').addEventListener('click', () => {
      if (socket && socket.connected) {
        socket.emit("progress_message", { message: "Hello from client!" });
        output.textContent += "Sent: Hello from client!\n";
      } else {
        output.textContent += "Socket not connected!\n";
      }
    });
  </script>
</body>
</html>
