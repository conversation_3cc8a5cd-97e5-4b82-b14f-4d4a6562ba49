"""
TODO: This script job is to be called as a cron job with following steps:
- the argv parameter --dir directory.json path pass in to read. Inside will have same keys eneight_test_directory_list.json
- we will have a main() function called to handle the logic of the script
- create a folder called ineight_download_files if not exist otherwise use existing folder
- Create a function called download_file_from_ineight(directory_list) loop through the directory_list and download the file from ineight and save to ineight_download_files folder
- Create a function called ingest_download_files(directory_list) loop through the directory_list and return the list, add a 1 second sleep between each download
"""

import argparse
import json
import os
import time
from pathlib import Path
import requests
import xml.etree.ElementTree as ET
import sys

import pandas as pd
import docx2txt
import pdfminer
from pdfminer.high_level import extract_text
from pptx import Presentation
import ifcopenshell
import ezdxf

from pinecone import Pinecone, ServerlessSpec
import openai
from langchain.embeddings.openai import OpenAIEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter
from concurrent.futures import ThreadPoolExecutor
import os
from datetime import datetime
from dateutil import parser

# Get the parent directory (one level up)
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from services.cohere_embedding import CohereService


parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
# Load environment variables from env.json file in the parent directory
env_file_path = os.path.join(parent_dir, 'env.json')
with open(env_file_path, 'r') as f:
    env_data = json.load(f)
    
# pinecone.init(api_key=env_data.get("PINECONE_API_KEY", ""), environment=PINECONE_ENV)
pc = Pinecone(api_key=env_data.get("PINECONE_API_KEY", ""), environment="us-east-1")
# openai.api_key = env_data.get("OPENAI_API_KEY", "")
# embed_model = OpenAIEmbeddings(openai_api_key=env_data.get("OPENAI_API_KEY", ""))
embed_model = CohereService()

print("openai API key >>>>>> ", env_data.get("OPENAI_API_KEY", ""))

# Create or connect to Pinecone index
# pc.delete_index("transmitall-document")
index_name = "transmitall-document"
if index_name not in pc.list_indexes().names():
    pc.create_index(
        name=index_name,
        dimension=1024,  # Adjust as needed
        metric="cosine",
        spec=ServerlessSpec(cloud="aws", region="us-east-1")  # Change region accordingly
    )

index = pc.Index(index_name)

text_splitter = RecursiveCharacterTextSplitter(
    chunk_size=500,
    chunk_overlap=50,
    length_function=len,
    separators=["\n\n", "\n", " ", ""]
)

def read_env_file(env_path):
    """Read and parse the environment JSON file"""
    try:
        with open(env_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error reading environment file: {e}")
        sys.exit(1)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Download and ingest files from InEight')
    parser.add_argument('--dir', required=True, help='Path to directory.json file')
    parser.add_argument('--env', required=True, help='Path to environment JSON file')
    return parser.parse_args()

def load_directory_list(file_path):
    """Load directory list from JSON file."""
    with open(file_path, 'r') as f:
        return json.load(f)

def create_download_folder():
    """Create ineight_download_files folder if it doesn't exist."""
    download_dir = Path('transmittal_files')
    download_dir.mkdir(exist_ok=True)
    return download_dir

# def get_session_key(env_config):
#     """
#     Get session key from InEight API using credentials from env_config.
    
#     Args:
#         env_config (dict): Configuration containing API credentials and settings
        
#     Returns:
#         str: Session key from InEight API
        
#     Raises:
#         requests.RequestException: If API call fails
#         ValueError: If response is not in expected format
#     """
#     # Build API URL with parameters from env_config
#     base_url = env_config.get('base_url', 'https://eu1.doc.ineight.com')
#     api_endpoint = f"{base_url}/tbws/session.asmx/LogonTFAWithApplication"
    
#     params = {
#         'userID': env_config.get('user_id'),
#         'companyID': env_config.get('company_id'),
#         'Password': env_config.get('password'),
#         'TfaToken': env_config.get('tfa_token', ' '),  # Default empty space if not provided
#         'ProjNo': env_config.get('project_no'),
#         'connectingProduct': env_config.get('connecting_product', 'ABC')
#     }

#     try:
#         # Make GET request to API
#         response = requests.get(api_endpoint, params=params)
#         response.raise_for_status()  # Raise exception for non-200 status codes
        
#         # Parse XML response
#         root = ET.fromstring(response.text)
        
#         # Extract session key from XML
#         # XML format: <string xmlns="http://www.qa-software.com/">session-key-here</string>
#         session_key = root.text
        
#         if not session_key:
#             raise ValueError("No session key found in response")
            
#         return session_key
        
#     except requests.RequestException as e:
#         print(f"Failed to get session key: {str(e)}")
#         raise
#     except (ET.ParseError, ValueError) as e:
#         print(f"Failed to parse response: {str(e)}")
#         raise

# def get_session_key(env_config):
#     """Get session key from iEight API"""
#     # session_url = 'https://eu1.doc.ineight.com/TBReportingAPI/Session'
#     session_url = f"{env_config['base_url']}/TBReportingAPI/Session"
#     headers = {
#         'Content-Type': 'application/json',
#         'Accept': 'application/json'
#     }
#     payload = {
#         'UserID': env_config['userID'],
#         'CompanyID': env_config['companyID'],
#         'Password': env_config['Password'],
#         'ProjectNo': env_config['ProjectNo'],
#         'oneTimePassword': env_config.get('OneTimePassword', 'string'),
#         'Application': env_config['Application']
#     }
#     try:
#         response = requests.post(session_url, headers=headers, json=payload)
#         if response.status_code == 201:
#             return response.text.strip()  # Session key is returned in response body
#         else:
#             print(f"Failed to get session key. Status code: {response.status_code}")
#             print(f"Response: {response.text}")
#             sys.exit(1)
#     except Exception as e:
#         print(f"Error getting session key: {e}")
#         sys.exit(1)

def get_session_key(env_config):
    base_url = "https://sa1.doc.ineight.com/tbws/session.asmx/LogonTFAWithApplication"
    
    url = (
    f"https://sa1.doc.ineight.com/tbws/session.asmx/LogonTFAWithApplication"
    f"?UserID={env_config['userID']}"
    f"&CompanyID={env_config['companyID']}"
    f"&TfaToken={env_config.get('TfaToken', '.')}"
    f"&ProjNo={env_config['ProjectNo']}"
    f"&connectingProduct={env_config.get('connectingProduct', '.')}"
    f"&Password={env_config['Password']}"
    )
    try:
        response = requests.get(url)
        
        if response.text.strip().startswith('<!DOCTYPE HTML') or '<html' in response.text.lower():
            print(f"Received HTML error response: {response.text[:200]}...")
            return f"ERROR - Server returned HTML error response (status code: {response.status_code})"
        
        if response.status_code == 200:
            try:
                
                import xml.etree.ElementTree as ET
                root = ET.fromstring(response.text)
                session_key = root.text.strip() if root.text else None
                
                if session_key and "ERROR" in session_key:
                    print(f"Session key contains error: {session_key}")
                    return session_key
                
                if session_key:
                    return session_key
                else:
                    return "ERROR - No session key found in response"
            except ET.ParseError:
                print(f"Failed to parse XML response: {response.text[:200]}...")
                return f"ERROR - Invalid XML response from server"
        else:
            print(f"Failed to get session key. Status code: {response.status_code}")
            print(f"Response: {response.text}")
            return f"ERROR - Authentication failed (status code: {response.status_code})"
    except requests.RequestException as e:
        print(f"Request error getting session key: {e}")
        return f"ERROR - Network error: {str(e)}"
    except Exception as e:
        print(f"Unexpected error getting session key: {e}")
        return f"ERROR - Unexpected error: {str(e)}"

def get_download_url(session_key, int_key, env_config):
    
    print("int_key >> ", int_key)
    print("session_key >> ", session_key)
    """
    Call the InEight API to get the download URL for the file.
    
    Args:
        session_key (str): The session key for authentication
        int_key (str): The internal key of the file to download
        
    Returns:
        str: The download URL for the file
        
    Raises:
        requests.RequestException: If the API call fails
        ValueError: If response is not in expected format
    """
    # url = 'https://eu1.doc.ineight.com/tbws/document.asmx/GetDownloadLink'
    url = f"{env_config['base_url']}/tbws/document.asmx/GetDownloadLink"
    
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    
    data = {
        'sessionKey': session_key,
        'int_viewFile': str(int_key)
    }
    
    try:
        response = requests.post(url, headers=headers, data=data)
        response.raise_for_status()
        
        # Parse XML response
        root = ET.fromstring(response.text)
        
        # Extract download URL from XML
        download_url = root.text
        
        if not download_url:
            raise ValueError("No download URL found in response")
            
        return download_url
        
    except requests.RequestException as e:
        print(f"Failed to get download URL: {str(e)}")
        raise
    except (ET.ParseError, ValueError) as e:
        print(f"Failed to parse response: {str(e)}")
        raise

def get_file_extension(content_disposition, default_ext=".bin"):
    """
    Extract the file extension from the Content-Disposition header.
    Defaults to .bin if unknown.
    """
    if content_disposition:
        parts = content_disposition.split(";")
        for part in parts:
            if "filename=" in part:
                filename = part.split("=")[-1].strip().strip('"')
                return Path(filename).suffix if '.' in filename else default_ext
    return default_ext

def download_file_from_ineight(directory_list, download_dir, session_key, env_config):
    print("directory_list >> ")
    """
    Download files from InEight and save to download folder.
    
    Args:
        directory_list (list): List of directory items to download
        download_dir (Path): Path to download directory
        session_key (str): Session key for authentication
    """
    transmittal_run_id = 1 # Assuming the item contains a TransmittalRunId
    if transmittal_run_id:
        # Fetch the last_time of the transmittal run using the API
        response = requests.get(f'http://localhost:5130/transmittal_run/{transmittal_run_id}/last_time')
        if response.status_code == 200:
            last_time_data = response.json()
            last_time = last_time_data.get('data', {}).get('last_time')
            print(f"Last time for transmittal run {transmittal_run_id}: {last_time}")
        else:
            print(f"Failed to fetch last_time for transmittal run {transmittal_run_id}: {response.text}")
    else:
        print("No TransmittalRunId found in item.")
    
    print("last_time >> ", last_time)
    for item in directory_list:
        try:
            print("item >> ", item['SentOn'])
            last_time_dt = datetime.strptime(last_time, "%a, %d %b %Y %H:%M:%S %Z")
            sent_on_str = item['SentOn']
            
            if "." in sent_on_str:
                fraction_part = sent_on_str.split(".")[-1]
                if len(fraction_part) == 2:  # If only 2 fractional digits, add a zero
                    sent_on_str += "0"
             
            sent_on_dt = parser.isoparse(sent_on_str.replace("Z", "+00:00"))
            print("sent_on_dt >>", sent_on_dt)
            print("last_time_dt >>", last_time_dt)
            if sent_on_dt < last_time_dt:
                print(f"Item {item['DocumentNo']} has already been processed. Skipping download.")
                continue
            
            download_url = get_download_url(session_key, item['Int_Key'], env_config)
            print(f"Download URL: {download_url}")
            
            doc_no = item['DocumentNo'].strip()
            
            # Download the file with streaming to handle large files
            response = requests.get(download_url, stream=True)
            response.raise_for_status()
            
            # Determine file extension from response headers
            content_disposition = response.headers.get('Content-Disposition', '')
            file_ext = get_file_extension(content_disposition, default_ext=".pdf")
            file_path = download_dir / f"{doc_no}{file_ext}"
            
            # Get total file size if available
            total_size = int(response.headers.get('content-length', 0))
            
            # Open file in binary write mode
            with open(file_path, 'wb') as f:
                if total_size == 0:
                    # No content length header - just download
                    f.write(response.content)
                else:
                    # Download with progress tracking for larger files
                    downloaded = 0
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            downloaded += len(chunk)
                            # Calculate progress percentage
                            progress = int((downloaded / total_size) * 100)
                            print(f"\rDownloading {doc_no}: {progress}%", end='')
                    print()  # New line after progress
            
            print(f"Successfully downloaded {doc_no} to {file_path}")
            
        except Exception as e:
            print(f"Error downloading {item['DocumentNo']}: {str(e)}")
            continue
            
        time.sleep(1)  # 1 second delay between downloads



def extract_text_from_pptx(filepath):
    """Extract text from PowerPoint (.pptx) files."""
    ppt = Presentation(filepath)
    text = []
    for slide in ppt.slides:
        for shape in slide.shapes:
            if hasattr(shape, "text"):
                text.append(shape.text)
    return "\n".join(text)

def extract_text_from_smlx(filepath):
    """Extract text from SMLX files with proper encoding handling."""
    try:
        with open(filepath, "r", encoding="utf-8") as f:
            content = f.read()
    except UnicodeDecodeError:
        print(f"⚠️ UTF-8 decoding failed for {filepath}, trying ISO-8859-1...")
        with open(filepath, "r", encoding="ISO-8859-1") as f:
            content = f.read()

    return content

def extract_text_from_ifc(filepath):
    """Extracts text data from an IFC file."""
    try:
        model = ifcopenshell.open(filepath)
        elements = model.by_type("IfcElement")  # Get all elements
        text_data = [el.Name for el in elements if el.Name]  # Extract names
        return "\n".join(text_data)
    except Exception as e:
        return f"Error parsing IFC file: {str(e)}"

def extract_text_from_dwg(filepath):
    """Extract text from DXF files using ezdxf."""
    try:
        doc = ezdxf.readfile(filepath)
        text_data = []
        
        # Iterate through all text entities in the DXF file
        for entity in doc.modelspace().query('TEXT'):
            text_data.append(entity.text)
        
        return "\n".join(text_data)
    
    except Exception as e:
        print(f"Error reading DXF file {filepath}: {e}")
        return None

def convert_dwg_to_dxf(dwg_filepath, dxf_filepath):
    """Converts DWG to DXF format using a conversion tool (e.g., ODA or dwg2dxf)."""
    os.system(f"dwg2dxf {dwg_filepath} {dxf_filepath}")  # Example command-line conversion
    
def extract_text_from_file(filepath):
    """Extract text from various file formats."""
    ext = os.path.splitext(filepath)[-1].lower()

    # Handling text files
    if ext == ".txt":
        with open(filepath, "r", encoding="utf-8") as f:
            return f.read()

    # Handling PDF files
    elif ext == ".pdf":
        return extract_text(filepath)  # Using pdfminer.six

    # Handling DOCX files
    elif ext == ".docx":
        return docx2txt.process(filepath)  # Using docx2txt

    # Handling Excel files (XLSX, XLS)
    elif ext == ".xls" or ext == ".xlsx":
        df = pd.read_excel(filepath, engine="openpyxl")
        return df.to_string()  # Convert DataFrame to string

    # Handling CSV files
    elif ext == ".csv":
        df = pd.read_csv(filepath)
        return df.to_string()  # Convert CSV DataFrame to string

    # Handling PowerPoint files
    elif ext == ".pptx":
        return extract_text_from_pptx(filepath)  # Using python-pptx

    # Handling SMLX files (assuming XML-based)
    elif ext == ".smlx":
        return extract_text_from_smlx(filepath)  # Custom XML parsing

    # Handling IFC files (Building data format)
    elif ext == ".ifc":
        return extract_text_from_ifc(filepath)  # Using ifc-parser

    # For DXF files
    elif ext == ".dxf":
        return extract_text_from_dwg(filepath)
    
    # For DWG files, convert to DXF first, then extract text
    elif ext == ".dwg":
        dxf_filepath = str(filepath).replace(".dwg", ".dxf")  # Convert Path to string before using replace
        convert_dwg_to_dxf(str(filepath), dxf_filepath)
        return extract_text_from_dwg(Path(dxf_filepath))  # Convert back to Path for ezdxf

    else:
        return None  # Unsupported file type
def process_embedding(embedding):
    if isinstance(embedding, list) and len(embedding) > 0 and isinstance(embedding[0], list):
        return embedding[0]  # Extract inner list if it's a nested list
    return embedding  # Already a flat list

def embed_chunk(chunk, doc_id, embed_model, index):
    # Generate embedding
    embedding = embed_model.embed_texts_as_list(
        texts=[chunk], 
        input_type="search_document", 
        model="embed-english-v3.0"
    )
    # Process embedding (whatever this function does)
    embedding = process_embedding(embedding)
    # Upsert to Pinecone
    index.upsert([(doc_id, embedding, {"text": chunk, "file": doc_id})])
    return chunk

def ingest_to_pinecone(filepath):
    """Extracts text, creates embeddings, and ingests into Pinecone."""
    text = extract_text_from_file(filepath)
    if text:
        print(" text >> ", text)
        print(" text end --------------------------------------- ")
        
        # Split text into chunks
        chunks = text_splitter.split_text(text)
        print("chunks >> ", chunks)
        
        doc_id = os.path.basename(filepath)
        
        # Process chunks in parallel with ThreadPoolExecutor
        with ThreadPoolExecutor(max_workers=20) as executor:
            # Submit all embedding tasks
            futures = [
                executor.submit(embed_chunk, chunk, doc_id, embed_model, index) 
                for chunk in chunks
            ]
            
            # Wait for all to complete
            for future in futures:
                future.result()
                
        return f"File '{doc_id}' ingested successfully!"
    else:
        return f"Unsupported or empty file: {filepath}"
    
def ingest_download_files(directory_list, download_dir):
    """
    Ingest the downloaded files with different file types.
    
    Args:
        directory_list (list): List of directory items to process.
        download_dir (Path): Path to download directory.
    """
    for item in directory_list:
        doc_no = item['DocumentNo'].strip()

        # Find the correct file by checking multiple extensions
        possible_extensions = [".pdf", ".docx", ".csv", ".pptx", ".txt", ".xlsx", ".ifc", ".smlx", ".dwg"]
        file_path = None

        for ext in possible_extensions:
            temp_path = download_dir / f"{doc_no}{ext}"
            if temp_path.exists():
                file_path = temp_path
                break

        if file_path:
            print(f"Ingesting {doc_no} from {file_path}")
            ingest_to_pinecone(file_path)
            os.remove(file_path)  # Delete the file after ingestion
            time.sleep(1)  # 1 second delay between ingestion
        else:
            print(f"Warning: File not found for {doc_no}")

def main():
    """Main function to orchestrate the download and ingestion process."""
    # Parse command line arguments
    print("start parse")
    args = parse_args()
    print("end parse")
    # with open('eneight_env.json') as f:
    #     env_config = json.load(f)
    env_config = read_env_file(args.env)
    # Get session key
    try:
        session_key = get_session_key(env_config)
        print(f"Successfully obtained session key", session_key)
    except Exception as e:
        print(f"Failed to get session key: {str(e)}")
        return
        
    # Load directory list
    directory_list = load_directory_list(args.dir)
    
    # Create download folder 
    download_dir = create_download_folder()
    
    # Download files
    # print("download_dir >> ", download_dir)
    print("Starting file downloads...")
    download_file_from_ineight(directory_list, download_dir, session_key.strip('"'), env_config)
    # download_file_from_ineight(directory_list, download_dir, "74c3aa55-3563-4cc9-942d-5cf3b2644572", env_config)
    transmittal_run_id=1
    last_time_update_url = f'http://localhost:5130/transmittal_run/{transmittal_run_id}/last_time'
    update_payload = {'last_time': datetime.now().isoformat()}
    
    try:
        update_response = requests.put(last_time_update_url, json=update_payload)
        update_response.raise_for_status()
        print(f"Successfully updated last_time for transmittal run {transmittal_run_id}.")
    except requests.RequestException as e:
        print(f"Failed to update last_time for transmittal run {transmittal_run_id}: {str(e)}")
    # Ingest files
    print("\nStarting file ingestion...")
    ingest_download_files(directory_list, download_dir)
    try:
        os.remove('transmittal_report.json')
        print("Successfully removed transmittal_report.json")
    except FileNotFoundError:
        print("Warning: transmittal_report.json not found")
    except Exception as e:
        print(f"Error removing transmittal_report.json: {str(e)}")
    print("\nProcess completed!")

if __name__ == "__main__":
    main()