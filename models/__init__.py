from extensions import db
import uuid
from sqlalchemy import or_,Index
import json
from datetime import datetime

# # Create tables if they do not exist
# with app.app_context():
#     db.create_all()
class Project(db.Model):
    __tablename__ = 'project'
    id = db.Column(db.String(36), primary_key=True)
    name = db.Column(db.String(255), nullable=False, index=True)
    entity_type = db.Column(db.String(255), nullable=True, index=True)  # "package" or "project" or "vendor" or "engineer"
    assistant_id = db.Column(db.String(255), nullable=True)
    has_deleted_file = db.Column(db.<PERSON>, nullable=False, default=False)
    discipline_code = db.Column(db.String(255), nullable=True)
    
     # New fields
    user_id = db.Column(db.String(255), nullable=True)
    ineight_project_id = db.Column(db.String(255), nullable=True)
    ineight_user_id = db.Column(db.String(255), nullable=True)
    ineight_company_id = db.Column(db.String(255), nullable=True)
    ineight_password = db.Column(db.String(255), nullable=True)
    is_ineight = db.Column(db.Integer, nullable=False, default=0)  
    upload_status = db.Column(db.String(255), nullable=True) # queued, processing, completed, failed

    auto_criteria_technical = db.Column(db.Text, nullable=True)

    numbering_rule = db.Column(db.Text, nullable=True)
    
    # Self-referential relationship for packages under projects
    parent_id = db.Column(db.String(36), db.ForeignKey('project.id'), nullable=True)
    parent = db.relationship('Project', remote_side=[id], backref=db.backref('packages', lazy='dynamic'), foreign_keys=[parent_id])
    
    # Relationship with bids - only for packages
    
    bids = db.relationship('Bids', back_populates='package', cascade='all, delete-orphan')
    
    files = db.relationship('File', back_populates='project', cascade='all, delete-orphan')
    merged_files = db.relationship('Merged_file', back_populates='project', cascade='all, delete-orphan')
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    @property
    def serialize(self):
        return {
            "id": self.id,
            "name": self.name,
            "entity_type": self.entity_type,
            "files": [file.serialize for file in self.files],
            "discipline_code": self.discipline_code,
            "merged_files": [merged_file.serialize for merged_file in self.merged_files],
            "assistant_id": self.assistant_id,
            "has_deleted_file": self.has_deleted_file,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "parent_id": self.parent_id,
            "packages": [package.serialize for package in self.packages] if self.entity_type == "project" else [],
            "bids": [bid.serialize for bid in self.bids] if self.entity_type == "package" else [],
            "user_id": self.user_id,
            "ineight_project_id": self.ineight_project_id,
            "ineight_user_id": self.ineight_user_id,
            "ineight_company_id": self.ineight_company_id,
            "ineight_password": self.ineight_password,
            "auto_criteria_technical": self.auto_criteria_technical,
            "is_ineight": self.is_ineight ,
            "upload_status": self.upload_status,
            "numbering_rule": self.numbering_rule
        }

    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize
    
    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [project.serialize for project in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []
    
    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def get_one_by(cls, **kwargs):
        try:
            return db.session.query(cls).filter_by(**kwargs).first()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def create(cls, **kwargs):
        try:
            project = cls(**kwargs)
            db.session.add(project)
            db.session.commit()
            return project.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def update(cls, id, **kwargs):
        try:
            project = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(project, key, value)
            db.session.commit()
            return project.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
        
    @classmethod
    def get_packages_by_project(cls, project_id):
        try:
            project = db.session.query(cls).filter_by(id=project_id, entity_type="project").first()
            if not project:
                print("Project not found or not of type 'project'")
                return []
                
            packages = db.session.query(cls).filter_by(parent_id=project_id, entity_type="tender").all()
            return [package.serialize for package in packages]
        except Exception as e:
            print(e)
            return []
        
    # Revised method to link an existing package to a project
    @classmethod
    def add_package_to_project(cls, project_id, package_id):
        try:
            # Check if the project exists
            project = db.session.query(cls).filter_by(id=project_id, entity_type="project").first()
            if not project:
                print("Project not found or not of type 'project'")
                return None
                
            # Check if the package exists
            package = db.session.query(cls).filter_by(id=package_id, entity_type="tender").first()
            if not package:
                print("Package not found or not of type 'package'")
                return None
            
            # Set the parent_id to establish the relationship
            package.parent_id = project_id
            db.session.commit()
            return package.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    

    @classmethod
    def add_bid_to_package(cls, package_id, bid_id):
        try:
            # Check if the package exists
            package = db.session.query(cls).filter_by(id=package_id, entity_type="tender").first()
            if not package:
                print("Package not found or not of type 'package'")
                return None
                
            # Check if the bid exists
            bid = db.session.query(Bids).filter_by(id=bid_id).first()
            if not bid:
                print("Bid not found")
                return None
            
            # Set the package_id to establish the relationship
            bid.package_id = package_id
            db.session.commit()
            return bid.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def get_bids_by_package(cls, package_id):
        try:
            package = db.session.query(cls).filter_by(id=package_id, entity_type="tender").first()
            if not package:
                print("Package not found or not of type 'package'")
                return []
                
            return [bid.serialize for bid in package.bids]
        except Exception as e:
            print(e)
            return []
        

class DocumentNumberingTemplate(db.Model):
    __tablename__ = 'document_numbering_template'
    id = db.Column(db.String(36), primary_key=True)
    client = db.Column(db.String(255), nullable=False, index=True)
    template = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)

    @property
    def serialize(self):
        return {
            "id": self.id,
            "client": self.client,
            "template": self.template,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize
    
    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [project.serialize for project in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []
    
    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def get_one_by(cls, **kwargs):
        try:
            return db.session.query(cls).filter_by(**kwargs).first()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def create(cls, **kwargs):
        try:
            project = cls(**kwargs)
            db.session.add(project)
            db.session.commit()
            return project.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def update(cls, id, **kwargs):
        try:
            project = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(project, key, value)
            db.session.commit()
            return project.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None


class ReportTemplate(db.Model):
    __tablename__ = 'report_template'
    id = db.Column(db.String(36), primary_key=True)
    client = db.Column(db.String(255), nullable=False, index=True)
    intro_template = db.Column(db.Text, nullable=True)
    body_template = db.Column(db.Text, nullable=True)
    conclusion_template = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)

    @property
    def serialize(self):
        return {
            "id": self.id,
            "client": self.client,
            "intro_template": self.intro_template,
            "body_template": self.body_template,
            "conclusion_template": self.conclusion_template,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize
    
    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [project.serialize for project in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []
    
    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def get_one_by(cls, **kwargs):
        try:
            return db.session.query(cls).filter_by(**kwargs).first()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def create(cls, **kwargs):
        try:
            project = cls(**kwargs)
            db.session.add(project)
            db.session.commit()
            return project.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def update(cls, id, **kwargs):
        try:
            project = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(project, key, value)
            db.session.commit()
            return project.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

class Users(db.Model):
    __tablename__ = 'users'
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(255), nullable=False, index=True)
    status = db.Column(db.String(255), nullable=False, default='active', index=True)
    role = db.Column(db.String(255), nullable=False, default='customer')  # manager, customer, admin
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)

    @property
    def serialize(self):
        return {
            "id": self.id,
            "name": self.name,
            "status": self.status,
            "role": self.role,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

    @classmethod
    def get_single(cls, id):
        try:
            return db.session.query(cls).filter_by(id=id).first().serialize
        except Exception as e:
            print(e)
            return None

    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [user.serialize for user in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []

    @classmethod
    def get_one_by(cls, **kwargs):
        try:
            return db.session.query(cls).filter_by(**kwargs).first()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def create(cls, **kwargs):
        try:
            user = cls(**kwargs)
            db.session.add(user)
            db.session.commit()
            return user.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def update(cls, id, **kwargs):
        try:
            user = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(user, key, value)
            db.session.commit()
            return user.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None


class Bids(db.Model):
    __tablename__ = 'bids'
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    package_id = db.Column(db.String(36), db.ForeignKey('project.id', ondelete='CASCADE'), nullable=True, index=True)  # Temporarily allow NULL # Bids can exist without a package
    name = db.Column(db.String(255), nullable=False, index=True)
    user_id = db.Column(db.String(36), db.ForeignKey('users.id', ondelete='CASCADE'), nullable=False, index=True)
    status = db.Column(db.String(255), nullable=False, default='idle', index=True)
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Define relationships
    user = db.relationship('Users', backref=db.backref('bids', lazy=True, cascade='all, delete-orphan'))
    package = db.relationship('Project', back_populates='bids')

    @property
    def serialize(self):
        return {
            "id": self.id,
            "name": self.name,
            "status": self.status,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

    @classmethod
    def get_single(cls, id):
        try:
            return db.session.query(cls).filter_by(id=id).first().serialize
        except Exception as e:
            print(e)
            return None

    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [bid.serialize for bid in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []

    @classmethod
    def get_one_by(cls, **kwargs):
        try:
            return db.session.query(cls).filter_by(**kwargs).first()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def create(cls, **kwargs):
        try:
            bid = cls(**kwargs)
            db.session.add(bid)
            db.session.commit()
            return bid.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def update(cls, id, **kwargs):
        try:
            bid = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(bid, key, value)
            db.session.commit()
            return bid.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None

class BidFile(db.Model):
    __tablename__ = 'bid_files'
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    bid_id = db.Column(db.String(36), db.ForeignKey('bids.id', ondelete='CASCADE'), nullable=False, index=True)
    name = db.Column(db.String(255), nullable=False)
    status = db.Column(db.String(255), nullable=False, default='idle', index=True)
    file_type = db.Column(db.String(50), nullable=False)
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationship with Bids
    bid = db.relationship('Bids', backref=db.backref('files', lazy=True, cascade='all, delete-orphan'))

    @property
    def serialize(self):
        return {
            "id": self.id,
            "bid_id": self.bid_id,
            "name": self.name,
            "file_type": self.file_type,
            "status": self.status,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

    @classmethod
    def get_single(cls, id):
        try:
            return db.session.query(cls).filter_by(id=id).first().serialize
        except Exception as e:
            print(e)
            return None

    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [file.serialize for file in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []

    @classmethod
    def get_one_by(cls, **kwargs):
        try:
            return db.session.query(cls).filter_by(**kwargs).first()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def create(cls, **kwargs):
        try:
            bid_file = cls(**kwargs)
            db.session.add(bid_file)
            db.session.commit()
            return bid_file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def update(cls, id, **kwargs):
        try:
            bid_file = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(bid_file, key, value)
            db.session.commit()
            return bid_file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None


class BidFileMetadata(db.Model):
    __tablename__ = 'bid_file_metadata'
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    bid_id = db.Column(db.String(36), db.ForeignKey('bids.id', ondelete='CASCADE'), nullable=False, index=True)
    bid_metadata = db.Column(db.Text, nullable=False)
    status = db.Column(db.String(255), nullable=False, default='idle', index=True)
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)

    bid = db.relationship('Bids', backref=db.backref('metadata_records', lazy=True, cascade='all, delete-orphan'))

    @property
    def serialize(self):
        return {
            "id": self.id,
            "bid_id": self.bid_id,
            "bid_metadata": self.bid_metadata,
            "status": self.status,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

    @classmethod
    def get_single(cls, id):
        try:
            return db.session.query(cls).filter_by(id=id).first().serialize
        except Exception as e:
            print(e)
            return None

    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [bid.serialize for bid in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []

    @classmethod
    def get_one_by(cls, **kwargs):
        try:
            return db.session.query(cls).filter_by(**kwargs).first()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def create(cls, **kwargs):
        try:
            bid = cls(**kwargs)
            db.session.add(bid)
            db.session.commit()
            return bid.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def update(cls, id, **kwargs):
        try:
            bid = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(bid, key, value)
            db.session.commit()
            return bid.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None
class File_merged_file_association(db.Model):
    __tablename__ = 'file_merged_file_association' 
    id = db.Column(db.String(36), primary_key=True)  # Assuming UUIDs are used, hence the length 36
    file_id = db.Column(db.String(36), db.ForeignKey('file.id'), nullable=False)  # Assuming UUIDs are used, hence the length 36
    merged_file_id = db.Column(db.String(36), db.ForeignKey('merged_file.id'), nullable=False)  # Assuming UUIDs are used, hence the length 36
    association_type = db.Column(db.String(255), nullable=False)  # Adjusted to 255
    status = db.Column(db.String(255), nullable=False)  # Adjusted to 255

    # Define relationships with File and Merged_file tables if necessary
    # file = db.relationship('File', back_populates='file_associations')
    # merged_file = db.relationship('Merged_file', back_populates='merged_file_associations')

    @property
    def serialize(self):
        return {
            "id": self.id,
            "file_id": self.file_id,
            "merged_file_id": self.merged_file_id,
            "merge_start_index": self.merge_start_index,
            "merge_end_index": self.merge_end_index
        }

    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [x.serialize for x in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []

    @classmethod
    def create(cls, file_id, merged_file_id):
        try:
            id = str(uuid.uuid4())
            association = cls(id=id, file_id=file_id, merged_file_id=merged_file_id)
            db.session.add(association)
            db.session.commit()
            return association
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def update(cls, file_id, merged_file_id, **kwargs):
        try:
            association = cls.query.filter_by(file_id=file_id, merged_file_id=merged_file_id).first()
            if association:
                for key, value in kwargs.items():
                    setattr(association, key, value)
                db.session.commit()
                return association
            else:
                return None
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def delete_by(cls, **kwargs):
        try:
            association = cls.query.filter_by(**kwargs).first()
            if association:
                db.session.delete(association)
                db.session.commit()
                return True
            else:
                return False
        except Exception as e:
            print(e)
            db.session.rollback()
            return False

    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [file.serialize for file in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []

class Merged_file(db.Model):
    id = db.Column(db.String(36), primary_key=True)  # Assuming UUIDs are used, hence the length 36
    project_id = db.Column(db.String(36), db.ForeignKey('project.id'), nullable=False)  # Assuming UUIDs are used, hence the length 36
    assistant_file_id = db.Column(db.String(255))
    status = db.Column(db.String(255))  # Adjusted to 255
    
    project = db.relationship('Project', back_populates='merged_files')
    files = db.relationship('File', secondary='file_merged_file_association', back_populates='merged_files')

    @property
    def serialize(self):
        return {
            "id": self.id,
            "project_id": self.project_id,
            "assistant_file_id": self.assistant_file_id,
            "status": self.status,
            "files": [file.serialize_basic for file in self.files]
        }
    @property
    def serialize_basic(self):
        return {
            "id": self.id,
            "project_id": self.project_id,
            "assistant_file_id": self.assistant_file_id,
            "status": self.status,
        }

    @classmethod
    def get_single(cls, id):
        try:
            return db.session.query(cls).filter_by(id=id).first().serialize
        except Exception as e:
            print(e)
            return None

    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [merged_file.serialize for merged_file in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []
    
    @classmethod
    def get_by_prefix(cls, prefix):
        try:
            records = db.session.query(cls).filter(cls.id.like(f'{prefix}%')).all()
            return [record.serialize for record in records]
        except Exception as e:
            print(e)
            return []

    @classmethod
    def delete_all(cls):
        try:
            db.session.query(cls).delete()
            db.session.commit()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def get_one_by(cls, **kwargs):
        try:
            return db.session.query(cls).filter_by(**kwargs).first()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def create(cls, **kwargs):
        try:
            merged_file = cls(**kwargs)
            db.session.add(merged_file)
            db.session.commit()
            return merged_file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def update(cls, id, **kwargs):
        try:
            merged_file = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(merged_file, key, value)
            db.session.commit()
            return merged_file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

class File(db.Model):
    id = db.Column(db.String(36), primary_key=True)  # Assuming UUIDs are used, hence the length 36
    name = db.Column(db.String(255), nullable=False, index=True)
    project_id = db.Column(db.String(36), db.ForeignKey('project.id'), nullable=False, index=True)  
    file_type = db.Column(db.String(255), nullable=False, index=True)  # Adjusted to 255
    status = db.Column(db.String(255), nullable=True, default='idle', index=True)  # Adjusted to 255
    merge_status = db.Column(db.String(255), default='not_merged')  # Adjusted to 255
    tried = db.Column(db.Integer, default=0, index=True)
    category = db.Column(db.String(255), nullable=True)  # Adjusted to 255
    file_dirtry = db.Column(db.String(255), nullable=False, default='qmp', index=True)  # Adjusted to 255
    pdf_conversion_status = db.Column(db.String(36), nullable=False, default='idle', index=True)
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)

    project = db.relationship('Project', back_populates='files')
    # file_associations = db.relationship('File_merged_file_association', back_populates='file')
    merged_files = db.relationship('Merged_file', secondary='file_merged_file_association', back_populates ='files')

    # File type and discipline
    document_type = db.Column(db.String(36), nullable=True)
    document_discipline = db.Column(db.String(36), nullable=True)
    
    revision = db.Column(db.String(10), nullable=True)
    
    @property
    def serialize(self):
        return {
           "id": self.id,
           "name": self.name,
           "project_id": self.project_id,
           "file_type": self.file_type,
           "file_dirtry": self.file_dirtry,
           "category": self.category,
           "status": self.status,
           "pdf_conversion_status": self.pdf_conversion_status,
           "merged_files": [merged_file.serialize_basic for merged_file in self.merged_files],
           "tried": self.tried,
           "merge_status": self.merge_status,
           "revision": self.revision,
           "created_at": self.created_at.isoformat() if self.created_at else None,
           "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
    @property
    def serialize_basic(self):
        return {
           "id": self.id,
           "name": self.name,
           "project_id": self.project_id,
           "file_type": self.file_type,
           "file_dirtry": self.file_dirtry,
           "category": self.category,
           "status": self.status,
           "pdf_conversion_status": self.pdf_conversion_status,
           "tried": self.tried,
           "merge_status": self.merge_status,
           "created_at": self.created_at.isoformat() if self.created_at else None,
           "updated_at": self.updated_at.isoformat() if self.updated_at else None,
           "revision": self.revision
        }
    
    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize
    
    @classmethod
    def get_by(cls, **kwargs):
        try:
            # print(kwargs)
            return [file.serialize for file in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []
    
    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def create(cls, **kwargs):
        try:
            file = cls(**kwargs)
            db.session.add(file)
            db.session.commit()
            return file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def update(cls, id, **kwargs):
        try:
            file = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(file, key, value)
            db.session.commit()
            return file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    @classmethod
    def get_paginated_ordered(cls, page=1, per_page=10, order_by='created_at', order_direction='desc', filters=None):
        try:
            offset = (page - 1) * per_page
            filters = filters or {}

            # Validate the order_by field
            valid_fields = ['id', 'provider', 'model', 'input_tokens', 'output_tokens', 'created_at']
            if order_by not in valid_fields:
                order_by = 'created_at'

            # Get the attribute to order by
            order_attr = getattr(cls, order_by)

            # Start the query with filters
            query = db.session.query(cls).filter_by(**filters)

            # Apply ordering
            if order_direction.lower() == 'asc':
                query = query.order_by(order_attr.asc())
            else:
                query = query.order_by(order_attr.desc())

            # Apply pagination
            query = query.offset(offset).limit(per_page)

            # Return serialized results
            return [item.serialize for item in query.all()]
        except Exception as e:
            print(f"Error getting paginated and filtered results: {e}")
            return []
        
class Prompt(db.Model):
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))  # UUID as a string
    name = db.Column(db.String(255), nullable=False)
    value = db.Column(db.Text, nullable=False) 
    type = db.Column(db.String(255), nullable=False)
    date_created = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    date_updated = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    @property
    def serialize(self):
        return {
           "id": self.id,
           "name": self.name,
           "value": self.value,
           "type": self.type,
           "date_created": self.date_created.isoformat(),
           "date_updated": self.date_updated.isoformat()
        }
    
    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize
    
    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [prompt.serialize for prompt in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []

    @classmethod
    def create(cls, **kwargs):
        try:
            prompt = cls(**kwargs)
            db.session.add(prompt)
            db.session.commit()
            return prompt.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def update(cls, id, **kwargs):
        try:
            prompt = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(prompt, key, value)
            db.session.commit()
            return prompt.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None
        
class Discipline(db.Model):
    __tablename__ = 'disciplines'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    shortcode = db.Column(db.String(2), unique=True, nullable=True)
    name = db.Column(db.String(255), nullable=False)
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)

    categories = db.relationship('Category', back_populates='discipline')

    @property
    def serialize(self):
        return {
           "id": self.id,
           "shortcode": self.shortcode,
           "name": self.name,
           "created_at": self.created_at.isoformat() if self.created_at else None,
           "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize
    
    @classmethod
    def get_by(cls, **kwargs):
        try:
            # print(kwargs)
            return [file.serialize for file in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []
    
    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def create(cls, **kwargs):
        try:
            file = cls(**kwargs)
            db.session.add(file)
            db.session.commit()
            return file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def update(cls, id, **kwargs):
        try:
            file = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(file, key, value)
            db.session.commit()
            return file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

class Category(db.Model):
    __tablename__ = 'categories'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    name = db.Column(db.String(255), nullable=False)
    feed = db.Column(db.Boolean, default=False)
    detail = db.Column(db.Boolean, default=False)
    vendor = db.Column(db.Boolean, default=False)
    similar = db.Column(db.Text, nullable=True)
    criteria = db.Column(db.Text, nullable=True)
    discipline_id = db.Column(db.Integer, db.ForeignKey('disciplines.id'), nullable=False)
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)

    discipline = db.relationship('Discipline', back_populates='categories')

    @property
    def serialize(self):
        return {
           "id": self.id,
           "name": self.name,
           "feed": self.feed,
           "detail": self.detail,
           "vendor": self.vendor,
           "discipline_id": self.discipline_id,
           "similar": json.loads(self.similar) if self.similar else [],
           "criteria": json.loads(self.criteria) if self.criteria else [],
           "created_at": self.created_at.isoformat() if self.created_at else None,
           "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize
    
    @classmethod
    def get_by(cls, **kwargs):
        try:
            # print(kwargs)
            return [file.serialize for file in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []
    
    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def create(cls, **kwargs):
        try:
            file = cls(**kwargs)
            db.session.add(file)
            db.session.commit()
            return file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def update(cls, id, **kwargs):
        try:
            file = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(file, key, value)
            db.session.commit()
            return file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None


class Chunks(db.Model):
    id = db.Column(db.String(36), primary_key=True)  
    title = db.Column(db.String(255), nullable=False, index=True)
    content = db.Column(db.Text, nullable=False)
    source = db.Column(db.String(255))
    summary = db.Column(db.Text)
    project_id = db.Column(db.String(36), nullable=False, index=True)  
    file_id = db.Column(db.String(36), nullable=False, index=True)
    page_number = db.Column(db.String(36), nullable=True)
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    document_type = db.Column(db.String(255), nullable=True)
    discipline = db.Column(db.String(255), nullable=True)
    document_number = db.Column(db.String(36), nullable=True)
    document_references = db.Column(db.String(255), nullable=True)

    
    tags = db.relationship('Tags', back_populates='chunks')

    @property
    def serialize(self):
        return {
            "id": self.id,
            "title": self.title,
            "content": self.content,
            "source": self.source,
            "summary": self.summary,
            "tags": [tag.serialize for tag in self.tags],
            "project_id": self.project_id,
            "file_id": self.file_id,
            "page_number": self.page_number,
            "created_at": self.created_at.isoformat() if self.created_at else None,
           "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize

    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [chunk.serialize for chunk in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []


    @classmethod
    def create(cls, **kwargs):
        try:
            chunk = cls(**kwargs)
            db.session.add(chunk)
            db.session.commit()
            return chunk.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def update(cls, id, **kwargs):
        try:
            chunk = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(chunk, key, value)
            db.session.commit()
            return chunk.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None

class Tags(db.Model):
    id = db.Column(db.String(36), primary_key=True)  # Assuming UUIDs are used, hence the length 36
    name = db.Column(db.String(255), nullable=False)
    chunk_id = db.Column(db.String(36), db.ForeignKey('chunks.id'), nullable=False) 

    chunks = db.relationship('Chunks', back_populates='tags')

    def __init__(self, name, chunk_id):
        self.name = name
        self.chunk_id = chunk_id

    @property
    def serialize(self):
        return {
            "id": self.id,
            "name": self.name,
            "chunk_id": self.chunk_id
        }

    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize

    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [tag.serialize for tag in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []

    @classmethod
    def create(cls, **kwargs):
        try:
            tag = cls(**kwargs)
            db.session.add(tag)
            db.session.commit()
            return tag.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def update(cls, id, **kwargs):
        try:
            tag = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(tag, key, value)
            db.session.commit()
            return tag.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
        
        
class Requirement(db.Model):
    __tablename__ = 'requirement'
    
    id = db.Column(db.String(36), primary_key=True)  # Assuming UUIDs are used, hence the length 36
    name = db.Column(db.Text, nullable=False, index=True)
    
    is_multibid = db.Column(db.Integer, default=0)
    multi_bids_info = db.Column(db.Text, nullable=True)
    
    package_id = db.Column(db.String(36), nullable=True)
    engineer_id = db.Column(db.String(36), nullable=True)
    vendor_id = db.Column(db.String(36), nullable=True)

    project_id = db.Column(db.String(36), db.ForeignKey('project.id'), nullable=False)  # Assuming UUIDs are used, hence the length 36
    file_type = db.Column(db.String(255), nullable=False)  # Adjusted to 255
    category = db.Column(db.String(255), nullable=True)  # Adjusted to 255
    discipline = db.Column(db.String(255), nullable=True)  # Adjusted to 255
    status = db.Column(db.String(255), nullable=True, default='idle', index=True)  # Adjusted to 255
    tried = db.Column(db.Integer, default=0, index=True)
    is_test = db.Column(db.Integer, default=0)
    exist_bid = db.Column(db.Integer, default=0)
    score = db.Column(db.Integer, nullable=True)
    criteria = db.Column(db.String(4096), nullable=True)  # Adjusted to 255
    type = db.Column(db.String(255), nullable=True, default='scp', index=True)  # Added type field with default value
    is_report_generated = db.Column(db.Integer, default=0, nullable=False)
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    #fields for reporting
    report_bid_ids  = db.Column(db.Text, nullable=True) #bids ida for reporting '["x","y"..]'
    report_equipment_technical = db.Column(db.Text, nullable=True) #equipment list for technical report '["x","y"..]'
    req_metadata = db.Column(db.Text, nullable=True)
    data_aggregrate_report = db.Column(db.Text, nullable=True)  # Long text field for aggregated data for report

    __table_args__ = (
        Index('ix_requirement_name', 'name', mysql_length=191),
    )
    
    @property
    def serialize(self):
        return {
           "id": self.id,
           "name": self.name,
           "project_id": self.project_id,
           "file_type": self.file_type,
           "discipline": self.discipline,
           "category": self.category,
           "is_test": self.is_test,
           "exist_bid": self.exist_bid,
           "status": self.status,
           "tried": self.tried,
           "score": self.score,
           "type": self.type,
           "criteria": self.criteria,
           "is_report_generated": self.is_report_generated,
           "created_at": self.created_at.isoformat() if self.created_at else None,
           "updated_at": self.updated_at.isoformat() if self.updated_at else None,
           "is_multibid": self.is_multibid,
           "multi_bids_info": self.multi_bids_info,
           'req_metadata':self.req_metadata,
           'package_id':self.package_id,
           'engineer_id':self.engineer_id,
           'vendor_id':self.vendor_id,
           'data_aggregrate_report':self.data_aggregrate_report
       }

    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize
    
    @classmethod
    def get_by(cls, **kwargs):
        try:
            # print(kwargs)
            return [file.serialize for file in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []
    
    @classmethod
    def get_scp_by(cls, **kwargs):
        try:
            query = db.session.query(cls).filter_by(**kwargs)
            # Add an additional filter to exclude records with an empty category field
            query = query.filter(or_(cls.criteria.is_(None), cls.criteria == ''))
            return [file.serialize for file in query.all()]
        except Exception as e:
            print(e)
            return []
    
    @classmethod
    def get_by_not_null_criteria(cls, **kwargs):
        try:
            query = db.session.query(cls)
    
            # Apply filter criteria where 'criteria' is not null or empty
            query = query.filter(or_(cls.criteria != None, cls.criteria != ''))
    
            # Apply additional filter criteria provided in kwargs
            if kwargs:
                query = query.filter_by(**kwargs)
    
            # Retrieve records and return serialized objects
            return [file.serialize for file in query.all()]
    
        except Exception as e:
            print(e)
            return []

    @classmethod
    def get_by_null_criteria(cls, **kwargs):
        try:
            # Query all records where 'criteria' is null or empty
            return [file.serialize for file in db.session.query(cls).filter(or_(cls.criteria == None, cls.criteria == '')).all()]
        
        except Exception as e:
            print(e)
            return []

    @classmethod
    def create(cls, **kwargs):
        try:
            file = cls(**kwargs)
            db.session.add(file)
            db.session.commit()
            return file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def update(cls, id, **kwargs):
        try:
            file = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(file, key, value)
            db.session.commit()
            return file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None



class CbpRequirementTechnicalReport(db.Model):
    __tablename__ = 'cbp_requirement_technical_report'
    
    id = db.Column(db.String(36), primary_key=True)  # UUID
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)
    report_bid_ids = db.Column(db.Text, nullable=True)  # JSON string like '["x", "y"]'
    report_equipment = db.Column(db.Text, nullable=True)  # JSON string like '["x", "y"]'
    status = db.Column(db.String(50), nullable=True)  # Added status field
    user_id = db.Column(db.String(36), nullable=False)  # Added user_id field
    data_aggregrate_report = db.Column(db.Text, nullable=True) 

    @property
    def serialize(self):
        return {
            "id": self.id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "report_bid_ids": self.report_bid_ids,
            "report_equipment": self.report_equipment,
            "status": self.status,
            "user_id": self.user_id,
            "data_aggregrate_report":self.data_aggregrate_report
              # Include user_id in serialization
        }

    @classmethod
    def get_single(cls, id):
        record = db.session.query(cls).filter_by(id=id).first()
        return record.serialize if record else None

    @classmethod
    def create(cls, **kwargs):
        try:
            record = cls(**kwargs)
            db.session.add(record)
            db.session.commit()
            return record.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def update(cls, id, **kwargs):
        try:
            record = db.session.query(cls).filter_by(id=id).first()
            if not record:
                return None
            for key, value in kwargs.items():
                setattr(record, key, value)
            db.session.commit()
            return record.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def delete_by(cls, **kwargs):
        try:
            record = db.session.query(cls).filter_by(**kwargs).first()
            if not record:
                return None
            db.session.delete(record)
            db.session.commit()
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

           
class Tender(db.Model):
    id = db.Column(db.String(36), primary_key=True)  # Assuming UUIDs are used, hence the length 36
    name = db.Column(db.String(255), nullable=False)
    project_id = db.Column(db.String(36), nullable=False, index=True)  # Assuming UUIDs are used, hence the length 36
    file_type = db.Column(db.String(255), nullable=False)  # Adjusted to 255
    status = db.Column(db.String(255), nullable=True, default='idle', index=True)  # Adjusted to 255
    tried = db.Column(db.Integer, default=0)
    score = db.Column(db.Integer, nullable=True)
    criteria = db.Column(db.Text, nullable=True)

    
    
    @property
    def serialize(self):
       return {
           "id": self.id,
           "name": self.name,
           "project_id": self.project_id,
           "file_type": self.file_type,
           "status": self.status,
           "tried": self.tried,
           "score": self.score,
           "criteria": self.criteria
       }
    
    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize
    
    @classmethod
    def get_by(cls, **kwargs):
        try:
            # print(kwargs)
            return [file.serialize for file in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []

    @classmethod
    def create(cls, **kwargs):
        try:
            file = cls(**kwargs)
            db.session.add(file)
            db.session.commit()
            return file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def update(cls, id, **kwargs):
        try:
            file = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(file, key, value)
            db.session.commit()
            return file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None

class EquipmentPackage(db.Model):
    __tablename__ = 'equipment_package'
    
    id = db.Column(db.String(36), primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)
    user_id = db.Column(db.String(36), nullable=True)
    
    # Relationship with EquipmentCategory
    equipment_categories = db.relationship('EquipmentCategory', back_populates='equipment_package')

    @property
    def serialize(self):
        return {
            "id": self.id,
            "name": self.name,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "user_id": self.user_id,
            "equipment_categories": [cat.serialize for cat in self.equipment_categories]
        }

    @classmethod
    def get_single(cls, id, user_id=None):
        try:
            return db.session.query(cls).filter_by(id=id, user_id=user_id).first().serialize
        except:
            return None

    @classmethod
    def get_by(cls, user_id=None, **kwargs):
        try:
            # Always include user_id in filters
            kwargs['user_id'] = user_id
            return [item.serialize for item in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []

    @classmethod
    def create(cls, user_id=None, **kwargs):
        try:
            # Ensure user_id is set
            kwargs['user_id'] = user_id
            item = cls(**kwargs)
            db.session.add(item)
            db.session.commit()
            return item.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def update(cls, id, user_id=None, **kwargs):
        try:
            # First find the item belonging to this user
            item = db.session.query(cls).filter_by(id=id, user_id=user_id).first()
            if not item:
                return None
                
            # Don't allow changing user_id
            if 'user_id' in kwargs:
                del kwargs['user_id']
                
            for key, value in kwargs.items():
                setattr(item, key, value)
            db.session.commit()
            return item.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def delete_by(cls, user_id=None, **kwargs):
        try:
            # Always include user_id in filters
            kwargs['user_id'] = user_id
            item = db.session.query(cls).filter_by(**kwargs).first()
            if item:
                db.session.delete(item)
                db.session.commit()
                return True
            return False
        except Exception as e:
            print(e)
            db.session.rollback()
            return False
    @classmethod
    def get_equipment_with_specs(cls, equipment_package_ids, user_id=None):
        """
        Get equipment packages with categories and specs in a hierarchical format.
        Only returns packages owned by the specified user.
        """
        try:
            # Filter by both IDs and user_id
            equipment_packages = db.session.query(cls).filter(
                cls.id.in_(equipment_package_ids)
            ).all()
            print(f"Equipment packages found: {equipment_packages}")
            if not equipment_packages:
                return []
                
            results = []
            
            for equipment_package in equipment_packages:
                result = {
                    "equipment_name": equipment_package.name,
                    "id": equipment_package.id,
                    "categories": []
                }
                
                # Get categories and their specs
                if equipment_package.equipment_categories is not None:
                    for category in equipment_package.equipment_categories:
                        category_data = {
                            "name": category.name,
                            "items": []
                        }
                        
                        # Add specs for this category
                        if category.category_specs is not None:
                            for spec in category.category_specs:
                                spec_data = {
                                    "name": spec.name,
                                    "unit": spec.unit or "",
                                    "discipline": spec.discipline or ""
                                }
                                category_data["items"].append(spec_data)
                        
                        # Add category even if it has no specs
                        result["categories"].append(category_data)
                
                # Add equipment package even if it has no categories
                results.append(result)
                
            return results
            
        except Exception as e:
            print(e)
            return []

    @classmethod
    def get_all_equipment_packages(cls):
        """
        Get all equipment packages with basic information (id and name) regardless of user_id.
        Returns a list of dictionaries containing id and name of all equipment packages.
        """
        try:
            equipment_packages = db.session.query(cls).all()
            return [{
                "id": package.id,
                "name": package.name,
                "user_id":package.user_id
            } for package in equipment_packages]
        except Exception as e:
            print(e)
            return []

    @classmethod
    def get_equipment_list_format(cls, equipment_ids):
        """
        Get equipment packages in a list format with equipment_id and equipment_name.
        """
        try:
            equipment_packages = db.session.query(cls).filter(cls.id.in_(equipment_ids)).all()
            return [{
                "equipment_id": package.id,
                "equipment_name": package.name
            } for package in equipment_packages]
        except Exception as e:
            print(e)
            return []


class EquipmentCategory(db.Model):
    __tablename__ = 'equipment_category'
    
    id = db.Column(db.String(36), primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)
    equipment_package_id = db.Column(db.String(36), db.ForeignKey('equipment_package.id'), nullable=False)

    # Relationships
    equipment_package = db.relationship('EquipmentPackage', back_populates='equipment_categories')
    category_specs = db.relationship('EquipmentCategorySpecs', back_populates='equipment_category')

    @property
    def serialize(self):
        return {
            "id": self.id,
            "name": self.name,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "equipment_package_id": self.equipment_package_id,
            "category_specs": [spec.serialize for spec in self.category_specs]
        }

    @classmethod
    def get_single(cls, id, user_id=None):
        try:
            # Join with equipment_package to check user_id
            item = db.session.query(cls).join(
                EquipmentPackage,
                cls.equipment_package_id == EquipmentPackage.id
            ).filter(
                cls.id == id,
                EquipmentPackage.user_id == user_id
            ).first()
            
            return item.serialize if item else None
        except:
            return None

    @classmethod
    def get_by(cls, user_id=None, **kwargs):
        try:
            # Join with equipment_package to check user_id
            items = db.session.query(cls).join(
                EquipmentPackage,
                cls.equipment_package_id == EquipmentPackage.id
            ).filter(
                EquipmentPackage.user_id == user_id,
                *[getattr(cls, key) == value for key, value in kwargs.items()]
            ).all()
            
            return [item.serialize for item in items]
        except Exception as e:
            print(e)
            return []

    @classmethod
    def create(cls, user_id=None, **kwargs):
        try:
            # First check if the equipment_package belongs to the user
            equipment_package_id = kwargs.get('equipment_package_id')
            if not equipment_package_id:
                return None
                
            package = db.session.query(EquipmentPackage).filter_by(
                id=equipment_package_id, 
                user_id=user_id
            ).first()
            
            if not package:
                return None
                
            item = cls(**kwargs)
            db.session.add(item)
            db.session.commit()
            return item.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def update(cls, id, user_id=None, **kwargs):
        try:
            # First find the category and check if its package belongs to the user
            item = db.session.query(cls).join(
                EquipmentPackage,
                cls.equipment_package_id == EquipmentPackage.id
            ).filter(
                cls.id == id,
                EquipmentPackage.user_id == user_id
            ).first()
            
            if not item:
                return None
                
            # If equipment_package_id is being changed, verify user owns the new package too
            if 'equipment_package_id' in kwargs:
                new_package = db.session.query(EquipmentPackage).filter_by(
                    id=kwargs['equipment_package_id'], 
                    user_id=user_id
                ).first()
                if not new_package:
                    return None
            
            for key, value in kwargs.items():
                setattr(item, key, value)
            db.session.commit()
            return item.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def delete_by(cls, user_id=None, **kwargs):
        try:
            # Find the category and check if its package belongs to the user
            item = db.session.query(cls).join(
                EquipmentPackage,
                cls.equipment_package_id == EquipmentPackage.id
            ).filter(
                EquipmentPackage.user_id == user_id,
                *[getattr(cls, key) == value for key, value in kwargs.items()]
            ).first()
            
            if item:
                db.session.delete(item)
                db.session.commit()
                return True
            return False
        except Exception as e:
            print(e)
            db.session.rollback()
            return False


class EquipmentCategorySpecs(db.Model):
    __tablename__ = 'equipment_category_specs'
    
    id = db.Column(db.String(36), primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    unit = db.Column(db.String(255), nullable=True)
    discipline = db.Column(db.String(255), nullable=True)
    equipment_category_id = db.Column(db.String(36), db.ForeignKey('equipment_category.id'), nullable=False)

    # Relationship
    equipment_category = db.relationship('EquipmentCategory', back_populates='category_specs')

    @property
    def serialize(self):
        return {
            "id": self.id,
            "name": self.name,
            "unit": self.unit,
            "discipline": self.discipline,
            "equipment_category_id": self.equipment_category_id
        }

    @classmethod
    def get_single(cls, id, user_id=None):
        try:
            # Join with category and package to check user ownership
            item = db.session.query(cls).join(
                EquipmentCategory,
                cls.equipment_category_id == EquipmentCategory.id
            ).join(
                EquipmentPackage,
                EquipmentCategory.equipment_package_id == EquipmentPackage.id
            ).filter(
                cls.id == id,
                EquipmentPackage.user_id == user_id
            ).first()
            
            return item.serialize if item else None
        except:
            return None

    @classmethod
    def get_by(cls, user_id=None, **kwargs):
        try:
            # Join with category and package to check user ownership
            items = db.session.query(cls).join(
                EquipmentCategory,
                cls.equipment_category_id == EquipmentCategory.id
            ).join(
                EquipmentPackage,
                EquipmentCategory.equipment_package_id == EquipmentPackage.id
            ).filter(
                EquipmentPackage.user_id == user_id,
                *[getattr(cls, key) == value for key, value in kwargs.items()]
            ).all()
            
            return [item.serialize for item in items]
        except Exception as e:
            print(e)
            return []

    @classmethod
    def create(cls, user_id=None, **kwargs):
        try:
            # Verify the category belongs to the user's package
            category_id = kwargs.get('equipment_category_id')
            if not category_id:
                return None
                
            category = db.session.query(EquipmentCategory).join(
                EquipmentPackage,
                EquipmentCategory.equipment_package_id == EquipmentPackage.id
            ).filter(
                EquipmentCategory.id == category_id,
                EquipmentPackage.user_id == user_id
            ).first()
            
            if not category:
                return None
                
            item = cls(**kwargs)
            db.session.add(item)
            db.session.commit()
            return item.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def update(cls, id, user_id=None, **kwargs):
        try:
            # Verify the spec belongs to the user's package
            item = db.session.query(cls).join(
                EquipmentCategory,
                cls.equipment_category_id == EquipmentCategory.id
            ).join(
                EquipmentPackage,
                EquipmentCategory.equipment_package_id == EquipmentPackage.id
            ).filter(
                cls.id == id,
                EquipmentPackage.user_id == user_id
            ).first()
            
            if not item:
                return None
                
            # If changing category, verify the new category belongs to the user too
            if 'equipment_category_id' in kwargs:
                new_category = db.session.query(EquipmentCategory).join(
                    EquipmentPackage,
                    EquipmentCategory.equipment_package_id == EquipmentPackage.id
                ).filter(
                    EquipmentCategory.id == kwargs['equipment_category_id'],
                    EquipmentPackage.user_id == user_id
                ).first()
                
                if not new_category:
                    return None
            
            for key, value in kwargs.items():
                setattr(item, key, value)
            db.session.commit()
            return item.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def delete_by(cls, user_id=None, **kwargs):
        try:
            # Verify the spec belongs to the user's package
            item = db.session.query(cls).join(
                EquipmentCategory,
                cls.equipment_category_id == EquipmentCategory.id
            ).join(
                EquipmentPackage,
                EquipmentCategory.equipment_package_id == EquipmentPackage.id
            ).filter(
                EquipmentPackage.user_id == user_id,
                *[getattr(cls, key) == value for key, value in kwargs.items()]
            ).first()
            
            if item:
                db.session.delete(item)
                db.session.commit()
                return True
            return False
        except Exception as e:
            print(e)
            db.session.rollback()
            return False
              

class RequestFileChunks(db.Model):
    id = db.Column(db.String(36), primary_key=True)  
    title = db.Column(db.String(255), nullable=False, index=True)
    content = db.Column(db.Text, nullable=True)
    source = db.Column(db.String(255), nullable=True)
    summary = db.Column(db.Text, nullable=True)
    request_id = db.Column(db.String(36), nullable=False, index=True)  
    file_id = db.Column(db.String(36), nullable=True)
    file_name = db.Column(db.String(100), nullable=True)
    page_number = db.Column(db.String(36), nullable=True)
    page_number = db.Column(db.String(36), nullable=True)
    section_id = db.Column(db.String(36), nullable=True)
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)

    

    @property
    def serialize(self):
        return {
            "id": self.id,
            "title": self.title,
            "content": self.content,
            "source": self.source,
            "summary": self.summary,
            "tags": [tag.serialize for tag in self.tags],
            "project_id": self.project_id,
            "file_id": self.file_id,
            "page_number": self.page_number,
            "created_at": self.created_at.isoformat() if self.created_at else None,
           "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize

    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [chunk.serialize for chunk in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []


    @classmethod
    def create(cls, **kwargs):
        try:
            chunk = cls(**kwargs)
            db.session.add(chunk)
            db.session.commit()
            return chunk.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def update(cls, id, **kwargs):
        try:
            chunk = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(chunk, key, value)
            db.session.commit()
            return chunk.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None
class Transmittal_run(db.Model):
    id = db.Column(db.String(36), primary_key=True)  # Specify length for VARCHAR
    last_time = db.Column(db.Text, nullable=True)  # Text does not need length
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)  # Assuming created_at is a timestamp
    updated_at =  db.Column(db.DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)  # Assuming updated_at is a timestamp

    @property
    def serialize(self):
        return {
            "id": self.id,
            "last_time": self.last_time,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
        }

    @classmethod
    def get_single(cls, id):
        result = db.session.query(cls).filter_by(id=id).first()
        return result.serialize if result else None

    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [chunk.serialize for chunk in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []

    @classmethod
    def create(cls, **kwargs):
        chunk = cls(**kwargs)
        db.session.add(chunk)
        db.session.commit()
        return chunk.serialize

    @classmethod
    def update(cls, id, **kwargs):
        chunk = db.session.query(cls).filter_by(id=id).first()
        if chunk:
            for key, value in kwargs.items():
                setattr(chunk, key, value)
            db.session.commit()
            return chunk.serialize
        return None

    @classmethod
    def delete_by(cls, **kwargs):
        try:
            item = db.session.query(cls).filter_by(**kwargs).first()
            if item:
                db.session.delete(item)
                db.session.commit()
            return True
        except Exception as e:
            print(e)
            db.session.rollback()
            return False

class LLMUsage(db.Model):
    __tablename__ = 'llm_usage'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    provider = db.Column(db.String(255), nullable=False, index=True)  # e.g., "claude", "groq"
    model = db.Column(db.String(255), nullable=False, index=True)
    input_tokens = db.Column(db.Integer, nullable=False)
    output_tokens = db.Column(db.Integer, nullable=False)
    created_at = db.Column(db.DateTime, nullable=True, default=datetime.utcnow)

    @property
    def serialize(self):
        return {
            "id": self.id,
            "provider": self.provider,
            "model": self.model,
            "input_tokens": self.input_tokens,
            "output_tokens": self.output_tokens,
            "total_tokens": self.input_tokens + self.output_tokens,  # Calculate on demand
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

    @classmethod
    def get_single(cls, id):
        try:
            return db.session.query(cls).filter_by(id=id).first().serialize
        except Exception as e:
            print(f"Error getting single LLM usage record: {e}")
            return None

    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [usage.serialize for usage in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(f"Error getting LLM usage records: {e}")
            return []

    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
            return True
        except Exception as e:
            print(f"Error deleting LLM usage record: {e}")
            db.session.rollback()
            return False

    @classmethod
    def create(cls, **kwargs):
        try:
            usage = cls(**kwargs)
            db.session.add(usage)
            db.session.commit()
            return usage.serialize
        except Exception as e:
            print(f"Error creating LLM usage record: {e}")
            db.session.rollback()
            return None
    @classmethod
    def count_all(cls):
        """Count all LLM usage records"""
        try:
            return db.session.query(db.func.count(cls.id)).scalar()
        except Exception as e:
            print(f"Error counting LLM usage records: {e}")
            return 0

    @classmethod
    def get_paginated(cls, page=1, per_page=10):
        """Get LLM usage records with pagination"""
        try:
            # Apply pagination
            offset = (page - 1) * per_page
            query = db.session.query(cls).order_by(cls.created_at.desc())  # Sort by creation date, newest first
            query = query.offset(offset).limit(per_page)
            
            # Return serialized results
            return [item.serialize for item in query.all()]
        except Exception as e:
            print(f"Error getting paginated LLM usage records: {e}")
            return []

    @classmethod
    def get_paginated_ordered(cls, page=1, per_page=10, order_by='created_at', order_direction='desc'):
        """
        Get LLM usage records with pagination and ordering
        
        Args:
            page: Page number (starting from 1)
            per_page: Number of records per page
            order_by: Field to order by
            order_direction: Direction of ordering ('asc' or 'desc')
        
        Returns:
            List of serialized LLM usage records
        """
        try:
            # Apply pagination
            offset = (page - 1) * per_page
            
            # Validate the order_by field to prevent SQL injection
            valid_fields = ['id', 'provider', 'model', 'input_tokens', 'output_tokens', 'created_at']
            if order_by not in valid_fields:
                order_by = 'created_at'  # Default to created_at if invalid field
            
            # Get the model attribute to order by
            order_attr = getattr(cls, order_by)
            
            # Apply ordering
            if order_direction.lower() == 'asc':
                query = db.session.query(cls).order_by(order_attr.asc())
            else:
                query = db.session.query(cls).order_by(order_attr.desc())
            
            # Apply pagination
            query = query.offset(offset).limit(per_page)
            
            # Return serialized results
            return [item.serialize for item in query.all()]
        except Exception as e:
            print(f"Error getting paginated LLM usage records: {e}")
            return []
