import asyncio
from services.claude_ai_service import Claude<PERSON>ervice
from services.fast_apis_service import FastAPIs
from services.prompt_loader import Prompt<PERSON>oader
import sys

class SynonymGenerator:
    def __init__(self):
        # Initialize the ClaudeService client
        self.claude_client = ClaudeService()
        self.fast_apis_client = FastAPIs()
        self.prompt_loader = PromptLoader()

    async def generate_synonym(self, word, temperature=0.001):

        prompt = self.prompt_loader.get_prompt('generate_synonym', {"word": word})
        messages = [
            {
                "role": "user",
                "content":  prompt
            }
        ]
        return self.fast_apis_client.generate_completion(messages)

        
        


# if __name__ == '__main__':
#     synonym_service = SynonymGenerator()
#     print(asyncio.run(synonym_service.generate_synonym('Name: Evacuation Plan \n Description: How would an injured person be evacuated to the nearest hospital?')))