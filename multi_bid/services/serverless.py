import requests
import json
from time import sleep
import traceback

class ServerlessRunpodAPIClient:
    def __init__(self, base_url="https://api.runpod.ai/v2/tds015hdehzy34", api_key="412UV29606V9AWZT4EXAQG5B5DGAVQY6MT4T1NF2"):
        self.base_url = base_url
        self.api_key = api_key

    def make_post_request(self, prompt):
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        data = {
            "input": {
                "query": prompt
            }
        }
        response = requests.post(f"{self.base_url}/run", headers=headers, data=json.dumps(data))
        ret = response.json()
        return ret

    def make_get_request(self, url):
        headers = {
            'Authorization': f'Bearer {self.api_key}',
        }
        response = requests.get(url, headers=headers)
        return response.json()

    def parallel_request_handler(self, queries, wait_time=10):
        track_ids = []
        results = []
        for x in queries:
            resp = self.make_post_request(x)
            print("Chunk ", len(track_ids), " ", resp)
            track_ids.append(resp['id'])
            results.append(None)

        total_queries = len(track_ids)
        print(total_queries)
        total_completed = 0
        step = 1
        while 1:
            print("Parallel Processing Step->", step)
            step += 1
            for i in range(total_queries):
                if track_ids[i] is None:
                    continue
                status = self.make_get_request(f"{self.base_url}/status/{track_ids[i]}")
                print("Status ", i, " ", status)
                try:
                    if status['status'] == 'COMPLETED':
                        results[i] = status['output']['response']
                        track_ids[i] = None
                        total_completed += 1
                except Exception as e:
                    traceback.print_exc()
                    results[i] = ""
                    track_ids[i] = None
                    total_completed += 1

            print("Total Completed->", total_completed)
            if total_completed == total_queries:
                break

            sleep(wait_time)

        return results

# Example usage:
# base_url = "https://api.runpod.ai/v2/tds015hdehzy34"
# api_key = '412UV29606V9AWZT4EXAQG5B5DGAVQY6MT4T1NF2'
# client = ServerlessRunpodAPIClient(base_url, api_key)
# queries = ['Write a python script to sum all of natural numbers up to n', 'Write a poetry on the topic of love', 'Write a python script to find the sum of all even numbers up to n']
# print(client.parallel_request_handler(queries))
