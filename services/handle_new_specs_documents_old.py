import os,sys
import json
import time
import asyncio
from models import Requirement,Project
from services.reasoning_specs_comply import SpecsComplyReasoning
from services.process_document import FileProcessor
from services.data_handler import DataManager
import re
from services.engr_doc_types_detection import EngrDocTypesDetection
from services.process_scp_dataset_1 import Dataset1Extractor
from services.process_scp_dataset_2 import Dataset2Extractor

class SpecsDocumentProcessor:
    def __init__(self):
        # Get the path to the parent directory
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # Load environment variables from env.json file in the parent directory
        env_file_path = os.path.join(parent_dir, 'env.json')
        with open(env_file_path, 'r') as f:
            self.env_data = json.load(f)

        self.specs_reasoning = SpecsComplyReasoning()
        self.data_manager = DataManager()
        self.file_processor = FileProcessor()
        self.engr_doc_detector = EngrDocTypesDetection()

    async def handle_single_new_document_v2(self, requirement_id):
        try:
            # print("i am requirement:", requirement)
            requirement = Requirement.get_single(requirement_id)

            if requirement is None:
                self.handle_failed_request('', '', '')
            # print('i am requirement: ', requirement)
            Requirement.update(requirement_id, status='pending')
            project = Project.get_single(requirement['project_id'])

            document_data = await self.engr_doc_detector.detect_document_category(requirement["name"], requirement["discipline"], requirement["category"])
            # print('document data: ', document_data)
            self.requirement = requirement
            criterion = self.convert_criteria_to_dict(document_data.get(requirement['category']).get("criteria", []))
            criterion = criterion[0:2] #to limit criteria to 2
            print("criteria below...")
            print({'criterion': criterion})
            # sys.exit()
            
            log = {
                "flagged_text": "",
                "total_factors": "",
                "positive_factors": "",
                "serialization_time": "",
                "question_generation_time": "",
                "document_pull_time": 0,
                "reasoing_time": 0,
                "reasoning_prompt_tokens": 0,
                "reasoning_response_tokens": 0,
                "section_details": [],
                "details": [],
                "criteria": "",
                "reasoning_summary": [],
                "source_map": [],
                "evaluate_summary": "",
                "evaluate_summary_processing": "",
                "evaluation_report": "",
                "input_token": 0,
                "output_token": 0,
                "response_agent_1": "",
                "response_agent_2": "",
                "project_title": "",
                "document_number": "",
                "document_title": "",
                "evaluation_report_section": {
                    "introduction":"",
                    "strength_chunk":[], "weak_chunk":[], "risk_chunk":[],
                    "engr_comment":[], "engr_reference":[], "contractor_feedback": "????",
                    "strength_text":"","weak_text":"","risk_text":"",
                    "detailed_evaluate_text":"","recommendation_text":"",
                    "strength_weak_risk_chunk_intro":{},
                    "evaluation_report":"", "evaluation_result":""
                }
            }
    
            # Use asyncio to run criteria evaluations in parallel
            tasks = []
            count = 0
            for criteria in criterion:
                count += 1
                log['criteria'] += f"""
                    Criteria {count} 
                    Name: {criteria['name']}
                    Description: {criteria['description']}
                    Weight: {criteria['weight']}
                    <br/>
                """
                criteria['weight'] = int(criteria['weight']) / 100 #convert to decimal
                file_path = os.path.join(self.env_data.get('SPECS_DIR'), requirement["project_id"], requirement["name"])
                tasks.append(self.process_single_criterion(requirement, criteria, file_path, count, log))
    
            # Run all tasks concurrently
            await asyncio.gather(*tasks)

            log['project_title'] = project['name']
            await self.specs_reasoning.get_claude_to_summarize_evaluate_v2(log)
            log['evaluate_summary'] += log['evaluation_report_section']['evaluation_report']
            log['evaluate_report'] = self.create_engineering_comments_table(log['evaluation_report_section']['engr_reference'], log['evaluation_report_section']['engr_comment'])
                    
            report_path = os.path.join(self.env_data.get('SPECS_DIR'), requirement["project_id"], requirement['id'] + ".json")
            print(f"writing report to {report_path}")
            with open(report_path, 'w', encoding='utf8') as json_file:
                json.dump(log, json_file, ensure_ascii=False)

            Requirement.update(requirement['id'], status='done')
            print("Final Bustop scp")

        except Exception as e:
            print("Error returned: ", e)
            Requirement.update(requirement['id'], status='idle', tried=(requirement["tried"] + 1))

    async def process_single_criterion(self, requirement, criteria, file_path, count, log):
        try:
            self.criteria = criteria
            t2 = time.time()
 
            source_document_text = await self.get_dataset1(requirement['project_id'], self.criteria, count, log)
            if source_document_text is None:
                self.handle_failed_request(self.requirement, data, '')
            
            # print('source document text: ', source_document_text)
            
            # Here we parse the data & also extract only sections matching the criteria from the uploaded document
            data, token_in_1, token_out_1 = await self.process_file(file_path, count, source_document_text, log)
        
            print("this is the length SCP: ", len(data))
            if len(data) == 0:
                self.handle_failed_request(requirement, data)
                raise RuntimeError("Unable to find uploaded document")
    
            log["section_details"].append(data)
        
            # Determine which chunk to pass to get_detailed_evaluation based on availability
            # Here we process the risk, weak & strength section of the document
            if log['evaluation_report_section'].get('strength_chunk', []):
                chunk_to_pass = log['evaluation_report_section'].get('strength_chunk', [])
            elif log['evaluation_report_section'].get('weak_chunk', []):
                chunk_to_pass = log['evaluation_report_section'].get('weak_chunk', [])
            elif log['evaluation_report_section'].get('risk_chunk', []):
                chunk_to_pass = log['evaluation_report_section'].get('risk_chunk', [])
            
    
            final_data = []
            evaluation_data = []            
            for datum in chunk_to_pass:
                # splitted_text = await self.split_by_table_tags(datum['text_content'])
                reason_data = {
                    "reasoning": datum['text_content'],
                    "evaluation_result": datum
                }

                final_data.append(reason_data)
                evaluation_data.append(reason_data["evaluation_result"])
    
            evaluate_text = f"<CRITERIA>Criteria {count}<CRITERIA><br/>"
            evaluate_text_2 = ""
            evaluate_reference = ""
            for evaluate in evaluation_data:
                evaluate_text += f"""
                    <Evaluation>{evaluate['evaluation_data'][0]}</Evaluation><br/>
                    <Score>{evaluate['evaluation_data'][1]}</Score><br/>
                    <Reason>{evaluate['evaluation_data'][3]}</Reason><br/>
                    <Reference>{evaluate['evaluation_data'][4]}</Reference><br/>
                """
                evaluate_text_2 += f"""
                    <Reason>{evaluate['evaluation_data'][3]}</Reason><br/>
                    <Reference>{evaluate['evaluation_data'][4]}</Reference><br/>
                """
                evaluate_reference += f"""
                    <Reason>{evaluate['evaluation_data'][3]}</Reason><br/>
                """
            
            log['evaluation_report_section']['engr_reference'].append(evaluate_reference)

            # print('Chunk to pass: ', chunk_to_pass)
            await self.specs_reasoning.get_claude_to_give_non_compliance_comment(evaluate_text_2, self.criteria, count, log)
            
            log['evaluate_summary_processing'] += f"{evaluate_text}<br/>"
            log['input_token'] += token_in_1
            log['output_token'] += token_out_1

            return log
        except Exception as e:
            print(f"Error in process_single_criterion: {e}")
            Requirement.update(self.requirement['id'], status='idle', tried=(self.requirement["tried"] + 1))

    def handle_failed_request(self, requirement="", data="", responses=""):
        report = {
            "total_factors": "",
            "positive_factors": "",
            "details": data,
            "reasoning_summary": "",
            "source_map": responses,
            "evaluate_summary": "The Source Document does not return any data based on the criteria provided, hereby comparison with an empty document is not possible at this time."
        }
        report_path = os.path.join(self.env_data.get('DATA_DIR'), requirement.get("project_id",""), requirement.get("id", requirement) + ".json")
        with open(report_path, 'w', encoding='utf8') as json_file:
            json.dump(report, json_file, ensure_ascii=False)
        
        if requirement is not None:
            Requirement.update(requirement['id'], status='idle', tried=(requirement["tried"] + 1))
    
    async def get_dataset1(self, project_id, criteria, count, log):
        try:
            # Here we extract data source1 from Pinecone based on criteria
            responses = await self.data_manager.extract_data_specs_comply(project_id, criteria)
            print('response of dataset1: ', responses)

            if not responses['ids'] and len(responses['metadatas']) == 0:
                raise RuntimeError("Unable to find Source document")
            
            pinecone_data = []
            chunk_detailed_content = []
            source_document_text = ""
            for resp in range(len(responses['metadatas'])):
                metadata = responses['metadatas'][resp]

                pinecone_data.append(metadata.get("documents", ""))
                
                detailed_chunk = metadata.get("detailed_chunk", "")
                if detailed_chunk:
                    chunk_detailed_content.append(detailed_chunk)
                else:
                    source_document_text += metadata.get("documents", "") + "\n"
            
            if source_document_text != "":
                print('comes in whole: ', source_document_text)
                return source_document_text
            else:
                source_document_text = await self.file_processor.process_source_file_specs(chunk_detailed_content, self.criteria, count, log)
                print('comes in chunk: ', source_document_text)
                return source_document_text
        except Exception as e:
            print('failed to get Dataset1: ', e)
            return None
    
    def remove_extension(self, file_name):
        base_name = os.path.splitext(file_name)[0]

        pattern = r'_(\d{6})_merged$'        
        modified_name = re.sub(pattern, '', base_name)
        
        return modified_name
    
    def replace_placeholder(self, evaluation_text, weighted_score):
        placeholder = "[Overall weighted score]"
        return evaluation_text.replace(placeholder, str(weighted_score))
    
    async def extract_all_weighted_scores(self, text):
        # Define the regex pattern to match all <WEIGHTED_SCORE> tags and their content
        pattern = r'<WEIGHTED_SCORE>(.*?)<\/WEIGHTED_SCORE>'        
        matches = re.findall(pattern, text)
        
        # Join all matches into a single string, separated by new lines
        result = '\n'.join(matches) if matches else None
        return f"\n<h2>{result}</h2>"
    
    async def manual_calculate_total_weighted_score(self, text):
        # Clean up the input text to ensure uniformity
        text = text.replace('\n', '').replace('\r', '').strip()

        # Define the regex pattern to match the weighted scores inside the <h5> tags
        pattern = r'Weighted Score:\s*(\d+(\.\d+)?)%'

        matches = re.findall(pattern, text)   

        scores = [float(match[0]) for match in matches]        
        total_weighted_score = sum(scores)
        
        average_weighted_score = total_weighted_score / len(scores) if scores else 0
        
        return total_weighted_score
    
    def convert_criteria_to_dict(self, criteria):

        criteria_list = [item.strip() for item in criteria[0].split(',')]
        weight = round(100 / len(criteria_list), 2)

        result = [{"name": item, "description": item, 'weight': weight} for item in criteria_list]

        return result

    async def split_by_table_tags(self, html_text):
        # Define the pattern to match <table>...</table> tags
        pattern = re.compile(r'(</?table.*?>)', re.IGNORECASE | re.DOTALL)
        
        # Split the text by the pattern
        split_sections = pattern.split(html_text)
        
        # Filter out empty strings and standalone tags
        split_sections = [section for section in split_sections if section.strip() and not re.match(r'</?table.*?>', section)]
        
        return split_sections
    
    def create_engineering_comments_table(self, engr_references, engr_comments):
        print(f"engr comment: {engr_comments}")
        print(f"engr_references: {engr_references}")
        # Create the HTML table
        html_table = """
        <table border="1">
            <tr>
                <th>Comment #</th>
                <th>Engineering Comment</th>
                <th>Reference</th>
                <th>Contractor Feedback</th>
            </tr>
        """
        
        # Add rows for each comment and reference
        for idx, (comment, reference) in enumerate(zip(engr_comments, engr_references), start=1):
            contractor_feedback = "????"

            html_table += f"""
                <tr>
                    <td>Item #{idx}</td>
                    <td>{comment}</td>
                    <td>{reference}</td>
                    <td>{contractor_feedback}</td>
                </tr>
            """
        
        # Close the table
        html_table += "</table>"
        
        return html_table
    
    async def queue(self):
        print('this started SCP ...')

        print('killing scp process now...')
        return
    
        pending = Requirement.get_scp_by(status='pending')

        if len(pending) >= 5:
            print('exiting...')
            return
        
        files = sorted(Requirement.get_scp_by(status='idle'), key=lambda x: x['tried'])
        # print(files)
        if len(files):
            if files[0]['tried'] < 1:
                await self.handle_single_new_document_v2(files[0]['id'])
        else:
            print("No Files In Queue!")

        return

    async def process_single_request(self, request_id):
        await self.handle_single_new_document_v2(request_id)

    async def process_file(self, file_path,count, source_document_text, log):
        data, token_in, token_out = await self.file_processor.process_new_file_SpecsComply(file_path, self.criteria, source_document_text, count, log)
        return data, token_in, token_out

if __name__ == "__main__":
        
    processor = SpecsDocumentProcessor()
    asyncio.run(processor.queue())
