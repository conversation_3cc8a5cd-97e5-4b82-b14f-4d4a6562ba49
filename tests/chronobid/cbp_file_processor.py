import os
import uuid
import random
from werkzeug.utils import secure_filename
import json
from services.file_merger import FileMerger
from services.docx_to_pdf_v2 import DocxToPdf
from models import Requirement

class CBPFileProcessor:
    def __init__(self, package_id, package_name, criteria_str):
        parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))
        env_file_path = os.path.join(parent_dir, 'env.json')
        with open(env_file_path, 'r') as f:
            env_data = json.load(f)

        self.package_id = package_id
        self.package_name = package_name
        self.criteria_str = json.dumps(criteria_str)
        CHRONOBID_FOLDER = env_data.get("CHRONOBID_DIR")
        self.folder_path = os.path.join(CHRONOBID_FOLDER, package_id)
        os.makedirs(self.folder_path, mode=0o775, exist_ok=True)

    def process_files(self, files):
        print('coming from cbp_file_processor...')
        # Generate request ID and a random 6-digit code for file naming
        request_id = str(uuid.uuid4())
        random_code = random.randint(100000, 999999)

        if len(files) > 1:
            file_merger = FileMerger(self.folder_path)
            file_name = f"{self.package_name}_{random_code}_merged.pdf"
            # print('merged_file_name:', file_name)
            file_names_with_objects = files
            file_path = file_merger.merge_test_files(file_names_with_objects, file_name)

        else:
            # For a single file
            file_name = secure_filename(files[0][0])
            temp_file_path = os.path.join(self.folder_path, file_name)
            print(temp_file_path)
            if file_name.rsplit('.', 1)[1].lower() == 'pdf':
                # If it's already a PDF, just save it
                file_path = temp_file_path
                with open(file_path, 'wb') as temp_file:
                    temp_file.write(files[0][1].read())  # Write contents of file_obj to temp_path
            else:
                # Otherwise, convert to PDF
                with open(temp_file_path, 'wb') as temp_file:
                    temp_file.write(files[0][1].read())  # Write contents of file_obj to temp_path
                file_converter = DocxToPdf()
                print('here no worries...')
                file_path = file_converter.convert_docx_to_pdf_file_to_file(temp_file_path, self.folder_path)
                # print('converted_file_path:', file_path)
                file_name = file_path.split('/')[-1]

        if file_name and file_path:
            # Prepare and save file metadata
            create_kwargs = {
                'id': request_id,
                'name': file_name,
                'project_id': self.package_id,
                'file_type': file_name.rsplit('.', 1)[1].lower(),
                'criteria': self.criteria_str,
                'type' : 'cbp',
                'is_test': 1
            }
            # print('this is kwags: ', create_kwargs)
            Requirement.create(**create_kwargs)
            return {"request_id": request_id}
        else:
            raise ValueError('Allowed file types are .pdf and .docx')
    
