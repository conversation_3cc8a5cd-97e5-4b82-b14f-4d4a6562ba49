def clean_price(price):
    """Cleans price string by removing commas and converting to float/int, handles None."""
    if price is None:
        return None
    try:
        # Remove commas and convert to float, then to int if it's a whole number
        cleaned = float(str(price).replace(',', ''))
        return int(cleaned) if cleaned == int(cleaned) else cleaned
    except ValueError:
        return None # Return None if conversion fails

def format_currency(price, currency="USD"):
    """Formats a number as currency string."""
    if price is None:
        return ""
    # Check if the price is a float with decimal places, then format appropriately
    if isinstance(price, float) and price != int(price):
        return f"{currency} {price:,.2f}"
    return f"{currency} {int(price):,}"

def generate_executive_summary_html(data):
    bidders = set()
    equipment_prices = {}
    other_items = {}

    for eq_id, eq_data in data['results'].items():
        equipment_name = eq_data['equipment_name']
        equipment_prices[equipment_name] = {}
        for item in eq_data['data']:
            if item['item_name'] == 'main_equipment_price':
                for price_info in item['prices']:
                    bidder = price_info['bidder']
                    unit_price = clean_price(price_info['unit_price'])
                    currency = price_info['currency']
                    bidders.add(bidder)
                    if bidder not in equipment_prices[equipment_name]:
                        equipment_prices[equipment_name][bidder] = 0
                    equipment_prices[equipment_name][bidder] = unit_price
            else:
                # Only collect items where other field is "YES"
                for price_info in item['prices']:
                    if price_info.get('other') == "YES":
                        item_display_name = item['item_name'].replace('_', ' ').title()
                        if item_display_name not in other_items:
                            other_items[item_display_name] = {}
                        bidder = price_info['bidder']
                        unit_price = clean_price(price_info['unit_price'])
                        currency = price_info['currency']
                        bidders.add(bidder)
                        other_items[item_display_name][bidder] = unit_price

    sorted_bidders = sorted(list(bidders))
    
    html_output = f"""<div align="center" style="margin: 0 auto; max-width: 1200px;">
        <h2 align="center" style="color: #2c5aa0; border-bottom: 2px solid #2c5aa0; padding-bottom: 10px;">
            EXECUTIVE SUMMARY
        </h2>
        <div style="background: #f8f9fa; padding: 20px; margin: 20px 0; border-left: 4px solid #2c5aa0;">
            <p align="center">
                The bids received in response to the request for tender (RFT) for the Cyclones are adjudicated in this document.<br>
                This document is focused on the technical and commercial adjudications on the cyclone packages for compliance with scope, completeness of bid, abidance with specification, cost and other commercial considerations, vendor experience, effect on project layout, and cost implications for maintenance and operational considerations.<br>
                {len(sorted_bidders)} tender offers were received as tabled below and shown in more details in Attachment 2:
            </p>
        </div>
        <table border="1" cellpadding="4" cellspacing="0" style="margin-top: 20px; border-collapse: collapse; border: 1px solid #ddd; width: auto; min-width: 800px;">
            <thead>
                <tr>
                    <th bgcolor="#f2f2f2" style="border: 1px solid #ddd; padding: 8px; width: 200px; max-width: 200px;">Tenderer</th>"""

    for bidder in sorted_bidders:
        html_output += f'<th bgcolor="#f2f2f2" style="border: 1px solid #ddd; padding: 8px; width: 120px;">{bidder}</th>\n'
    html_output += """
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px; width: 200px; max-width: 200px;">Reference</td>
    """
    for bidder in sorted_bidders:
        html_output += '<td style="border: 1px solid #ddd; padding: 8px; width: 120px;"></td>\n'
    html_output += """
                </tr>
                <tr><td bgcolor="#e0e0e0" colspan="{colspan}" style="border: 1px solid #ddd; padding: 8px;"><b>EQUIPMENT</b></td></tr>
    """.format(colspan=len(sorted_bidders) + 1)

    total_equipment_prices = {bidder: 0 for bidder in sorted_bidders}

    for eq_name in sorted(equipment_prices.keys()):
        html_output += f"""
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px; width: 200px; max-width: 200px;">{eq_name}</td>
        """
        for bidder in sorted_bidders:
            price = equipment_prices[eq_name].get(bidder)
            formatted_price = format_currency(price)
            html_output += f'<td style="border: 1px solid #ddd; padding: 8px; width: 120px;">{formatted_price}</td>\n'
            if price is not None:
                total_equipment_prices[bidder] += price
        html_output += "</tr>\n"

    html_output += """
                <tr>
                    <td bgcolor="#f8f8f8" style="border: 1px solid #ddd; padding: 8px; width: 200px; max-width: 200px;"><b>TOTAL EQUIPMENT</b></td>
    """
    for bidder in sorted_bidders:
        formatted_total = format_currency(total_equipment_prices[bidder])
        html_output += f'<td bgcolor="#f8f8f8" style="border: 1px solid #ddd; padding: 8px; width: 120px;"><b>{formatted_total}</b></td>\n'
    html_output += """
                </tr>
                <tr><td bgcolor="#e0e0e0" colspan="{colspan}" style="border: 1px solid #ddd; padding: 8px;"><b>OTHER</b></td></tr>
    """.format(colspan=len(sorted_bidders) + 1)

    total_other_prices = {bidder: 0 for bidder in sorted_bidders}

    for item_name in sorted(other_items.keys()):
        html_output += f"""
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px; width: 200px; max-width: 200px;">{item_name}</td>
        """
        for bidder in sorted_bidders:
            price = other_items[item_name].get(bidder)
            formatted_price = format_currency(price)
            html_output += f'<td style="border: 1px solid #ddd; padding: 8px; width: 120px;">{formatted_price}</td>\n'
            if price is not None:
                total_other_prices[bidder] += price
        html_output += "</tr>\n"

    html_output += """
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px; width: 200px; max-width: 200px;">*Notes</td>
    """
    for bidder in sorted_bidders:
        html_output += '<td style="border: 1px solid #ddd; padding: 8px; width: 120px;"></td>\n'
    html_output += """
                </tr>
                <tr>
                    <td bgcolor="#f8f8f8" style="border: 1px solid #ddd; padding: 8px; width: 200px; max-width: 200px;"><b>TOTAL OTHER</b></td>
    """
    for bidder in sorted_bidders:
        formatted_total = format_currency(total_other_prices[bidder])
        html_output += f'<td bgcolor="#f8f8f8" style="border: 1px solid #ddd; padding: 8px; width: 120px;"><b>{formatted_total}</b></td>\n'
    html_output += """
                </tr>
                <tr>
                    <td bgcolor="#f8f8f8" style="border: 1px solid #ddd; padding: 8px; width: 200px; max-width: 200px;"><b>TOTAL PACKAGE2</b></td>
    """
    for bidder in sorted_bidders:
        total = total_equipment_prices[bidder] + total_other_prices[bidder]
        formatted_total = format_currency(total)
        html_output += f'<td bgcolor="#f8f8f8" style="border: 1px solid #ddd; padding: 8px; width: 120px;"><b>{formatted_total}</b></td>\n'
    html_output += """
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px; width: 200px; max-width: 200px;">Lead Time</td>
    """
    for bidder in sorted_bidders:
        html_output += '<td style="border: 1px solid #ddd; padding: 8px; width: 120px;"></td>\n'
    html_output += """
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px; width: 200px; max-width: 200px;">Technically Acceptable</td>
    """
    for bidder in sorted_bidders:
        html_output += '<td style="border: 1px solid #ddd; padding: 8px; width: 120px;">Yes/No</td>\n'
    html_output += """
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px; width: 200px; max-width: 200px;">Commercially Acceptable</td>
    """
    for bidder in sorted_bidders:
        html_output += '<td style="border: 1px solid #ddd; padding: 8px; width: 120px;">Yes/No</td>\n'
    html_output += """
                </tr>
            </tbody>
        </table>
        <div style="margin-top: 30px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7;">
            <h4 style="color: #856404; margin-top: 0;">NOTES</h4>
            <p style="margin-bottom: 0; color: #856404;">
                Final contract award is contingent upon successful completion of clarifications and negotiations with the recommended tenderer(s).
            </p>
        </div>
    </div>"""
    return html_output


if __name__ == "__main__":
    import json
    with open('report.json', 'r') as f:
        data = json.load(f)
    # Generate the HTML content
    html_content = generate_executive_summary_html(data)

    # Save to a file
    with open("executive_summary.html", "w") as f:
        f.write(html_content)

    print("Generated executive_summary.html successfully!")