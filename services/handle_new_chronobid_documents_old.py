import os,sys
import json
import time
import asyncio
from models import Requirement,Project
from services.reasoning_chronobid import ChronobidReasoning
from services.data_handler import DataManager
from services.report_manager import ReportManager
from services.update_backend_status import BackendStatusUpdater
from services.process_cbp_dataset_1 import Dataset1Extractor
from services.process_cbp_dataset_2 import Dataset2Extractor
from services.process_document import FileProcessor

import re

class ChronobidDocumentProcessor:
    def __init__(self, is_test=False):
        # Get the path to the parent directory
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.is_test = is_test
        

        # Load environment variables from env.json file in the parent directory
        env_file_path = os.path.join(parent_dir, 'env.json')
        with open(env_file_path, 'r') as f:
            self.env_data = json.load(f)

        self.huggingface_token = self.env_data.get('HUGGINGFACE_TOKEN')
        self.chronobid_reasoning = ChronobidReasoning()
        self.data_manager = DataManager()
        self.file_processor = FileProcessor()
        self.cbp_file_processor = Dataset2Extractor()
        self.backend_status_updater = BackendStatusUpdater()
        self.risk_text_intro = '''
                Overall, [BIDDER NAME] presents certain risks in relation to criteria [#], with potential challenges identified in meeting criteria [#] and criteria [#], which may impact the project's successful completion.
            '''
        self.strength_text_intro = '''
                Overall, [BIDDER NAME] has demonstrated a good understanding and evidence of criteria [#] , a perceived commitment to achieve criteria [#] etc
            '''
        self.weak_text_intro = '''
                Overall, [BIDDER NAME] has demonstrated notable weaknesses in addressing criteria [#], with a lack of sufficient evidence and understanding in meeting the requirements of criteria [#] and criteria [#].
            '''
   

    async def handle_single_new_document_v2(self, requirement_id):
        try:
            requirement = Requirement.get_single(requirement_id)
            
            print('this is req: ', requirement)
            Requirement.update(requirement_id, status='pending')
            
            self.requirement = requirement
            self.criterion = json.loads(requirement["criteria"])
            self.tender_title = requirement["name"]
            self.tender_number = requirement["id"]
            self.bidder_name = '[BIDDER NAME]'
            self.project = Project.get_single(requirement["project_id"])

            top_text = f'''
                <div>
                    <h2>Evaluation Result</h2>
                    <p>Following a detailed evaluation of {self.remove_extension(requirement['name'])}'s proposal, the offer achieved an overall score of <Overall_Score>0.0</Overall_Score>% . This score reflects the performance across all assessed criteria. The summary of the evaluation per criterion is as follows:</p>
                </div>
            '''

            evaluation_result_text = f'''
                Following the thorough evaluation of [BIDDER NAME]’s proposal, an overall weighted score of <Overall_Score>0.0</Overall_Score>% was achieved, reflecting performance across all assessed criteria. 
                The detailed scoring for each criterion is as follows:
            '''

            initial_log = {
                "flagged_text": "",
                "total_factors": "",
                "positive_factors": "",
                "serialization_time": "",
                "question_generation_time": "",
                "document_pull_time": 0,
                "reasoing_time": 0,
                "reasoning_prompt_tokens": 0,
                "reasoning_response_tokens": 0,
                "section_details": [],
                "details": [],
                "criteria": "",
                "criteria_list": "",
                "query_synonyms": "",
                "reasoning_summary": [],
                "source_map": [],
                "evaluate_summary": top_text,
                "evaluate_summary_processing": [],
                "evaluate_summary_intro" : top_text,
                "evaluate_summary_chunk": [], 
                "evaluate_summary_score": "",
                "input_token": 0,
                "output_token": 0,
                "response_agent_1": "",
                "response_agent_2": "",
                "upload_potential_data_chunks": "",
                "source_potential_data_chunks": "",
                "chunk_to_pass": [],
                "evaluation_report_section": {
                    "cover_page":"",
                    "introduction":"",
                    "evaluation_result": evaluation_result_text,
                    "detailed_evaluation":"",
                    "recommendation":"",
                    "strength_chunk":[], "weak_chunk":[], "risk_chunk":[],
                    "strength_text":"","weak_text":"","risk_text":"",
                    "strength_weak_risk_chunk_intro":{},
                    "evaluation_report":""
                }
            }
            self.report_manager = ReportManager(self.env_data.get('CHRONOBID_DIR'), requirement["project_id"], requirement_id)
            self.report_manager.create_initial_report(initial_log)

            print('created initial report...')

            # Use asyncio to run criteria evaluations in parallel
            tasks = []
            count = 0
            for criteria in self.criterion:
                count += 1
                criteria['weight'] = int(criteria['weight']) / 100 #convert to decimal
                file_path = os.path.join(self.env_data.get('CHRONOBID_DIR'), requirement["project_id"], requirement["name"])
                tasks.append(self.process_single_criterion(requirement, criteria, file_path, count))
                
    
            # Run all tasks concurrently
            await asyncio.gather(*tasks)
            log = self.report_manager.load_report()

            print('generating full report.....')
            await self.update_log_and_status(log, requirement_id, 2, True)

            Requirement.update(requirement_id, status='done')
            
            print("Final Bustop")

        except Exception as e:
            print("Error returned: ", e)
            Requirement.update(requirement_id, status='idle', tried=(requirement["tried"] + 1))
    
    
    async def process_single_criterion(self, requirement, criteria, file_path, count):
        try:
            log = self.report_manager.load_report()
            criteria_str = f"""
                Criteria {count} 
                Name: {criteria['name']}
                Description: {criteria['description']}
                Weight: {criteria['weight']}
                <br/>
            """
            log['criteria'] += criteria_str
            
            print(f"\n\n Starting Criteria: {count} \n  {criteria_str} \n\n")
            self.criteria = criteria

            # Start counting the total time for dataset1 retrieval
            t1 = time.time()

            source_document_text = await self.get_dataset1(requirement['project_id'], self.criteria, count, log)
            if source_document_text is None:
                raise RuntimeError("Unable to process source document")
            
            # Calculate time taken for dataset1
            t2 = time.time()
            dataset1_duration = (t2 - t1) / 60  # Convert seconds to minutes
            print(f"Datasource1 text done in {dataset1_duration:.2f} minutes.....\n\n")


            # Start counting time for dataset2 retrieval
            t3 = time.time()
            # Here we parse the data & also extract only sections matching the criteria from the uploaded document
            data, token_in_1, token_out_1 = await self.get_dataset2(file_path, count, source_document_text, log)
            if len(data) == 0:
                raise RuntimeError("Unable to find uploaded document")
    
            log["section_details"].append(data)

            # Calculate time taken for dataset2
            t4 = time.time()
            dataset2_duration = (t4 - t3) / 60  # Convert seconds to minutes
            print(f"Datasource2 extraction done in {dataset2_duration:.2f} minutes.....\n\n")

            
            # Determine which chunk to pass to get_detailed_evaluation based on availability
            if log['evaluation_report_section'].get('strength_chunk', []):
                
                chunk_to_pass = log['evaluation_report_section'].get('strength_chunk', [])
                log['chunk_to_pass'].append(chunk_to_pass)
                
            elif log['evaluation_report_section'].get('weak_chunk', []):
                
                chunk_to_pass = log['evaluation_report_section'].get('weak_chunk', [])
                log['chunk_to_pass'].append(chunk_to_pass)

            elif log['evaluation_report_section'].get('risk_chunk', []):
                
                chunk_to_pass = log['evaluation_report_section'].get('risk_chunk', [])
                log['chunk_to_pass'].append(chunk_to_pass)
            
    
            final_data = []
            evaluation_data = []            
            for datum in chunk_to_pass:
                splitted_text = await self.split_by_table_tags(datum['text_content'])

                reason_data = {
                    "reasoning": splitted_text,
                    "evaluation_result": datum
                }

                final_data.append(reason_data)
                evaluation_data.append(datum)
            
            evaluate_text = f"<CRITERIA>Criteria {count}<CRITERIA><br/>"
            for evaluate in evaluation_data:
                evaluate_text += f"""
                    <Evaluation>{evaluate['evaluation_data'][0]}</Evaluation><br/>
                    <Score>{evaluate['evaluation_data'][1]}</Score><br/>
                    <Reason>{evaluate['evaluation_data'][3]}</Reason><br/>
                """
            log['evaluate_summary_processing'].append(f"{evaluate_text}")
            
            log['input_token'] += token_in_1
            log['output_token'] += token_out_1            

            print('getting claude summarize for response...')
            await self.chronobid_reasoning.get_claude_to_summarize_evaluate_v2(log, self.criteria, count)

            await self.update_log_and_status(log, requirement['id'], 1, True)
            Requirement.update(requirement['id'], status='response_started')

            print(f'completed {count} criteria')
        except Exception as e: 
            print(f"Error in process_single_criterion: {e}")
            return None
            # Requirement.update(self.requirement['id'], status='idle', tried=(self.requirement["tried"] + 1))

    async def update_log_and_status(self, log, requirement_id, status="", should_update_status=False):
        print('\n\n now updating log with new log data...\n\n')

        # Update the log with new data
        self.report_manager.update_log(log)
        
        # Check if the status should be updated based on should_update_status parameter
        if should_update_status and status != "":
            if not self.is_test:
                self.backend_status_updater.update_history_status(requirement_id, status)
    
    async def get_dataset1(self, project_id, criteria, count, log):
        extractor = Dataset1Extractor(criteria)
        response = await extractor.get_dataset1(project_id, count, log)
        log['source_potential_data_chunks'] = response
        return response

    
    def handle_failed_request(self, requirement, data="", responses=""):
        report = {
            "total_factors": "",
            "positive_factors": "",
            "details": data,
            "reasoning_summary": "",
            "source_map": responses,
            "evaluate_summary": "The Source Document does not return any data based on the criteria provided, hereby comparison with an empty document is not possible at this time."
        }
        report_path = os.path.join(self.env_data.get('CHRONOBID_DIR'), requirement["project_id"], requirement["id"] + ".json")
        with open(report_path, 'w', encoding='utf8') as json_file:
            json.dump(report, json_file, ensure_ascii=False)
        Requirement.update(requirement['id'], status='idle', tried=(requirement["tried"] + 1))

    async def extract_all_weighted_scores(self, text):
        # Define the regex pattern to match all <WEIGHTED_SCORE> tags and their content
        pattern = r'<WEIGHTED_SCORE>(.*?)<\/WEIGHTED_SCORE>'        
        matches = re.findall(pattern, text)
        
        # Join all matches into a single string, separated by new lines
        result = '\n'.join(matches) if matches else None
        return f"\n<h2>{result}</h2>"
    
    def remove_extension(self, file_name):
        base_name = os.path.splitext(file_name)[0]
        pattern = r'_(\d{6})_merged$'        
        modified_name = re.sub(pattern, '', base_name)
        
        return modified_name

    async def split_by_table_tags(self, html_text):
        # Define the pattern to match <table>...</table> tags
        pattern = re.compile(r'(</?table.*?>)', re.IGNORECASE | re.DOTALL)
        
        # Split the text by the pattern
        split_sections = pattern.split(html_text)
        
        # Filter out empty strings and standalone tags
        split_sections = [section for section in split_sections if section.strip() and not re.match(r'</?table.*?>', section)]
        
        return split_sections
    
    async def queue(self):
        print('this started CBP...')

        # Requirement.update("28eb3649-b2b2-4d3a-b106-bded60423353", status="done", tried=2)
        # Requirement.update("eabcb3c8-c35c-4ee4-8c7c-2fccb9760159", status="idle", tried=0)
        # return await self.handle_single_new_document_v2('0ac4f209-5bf0-453d-92db-c29841016da4')
        # return await self.handle_single_new_document_v2('30f184b2-78ed-495e-9a08-03d4322bd77d')
        # return await self.handle_single_new_document_v2('1ce04c6b-3286-4d0f-8547-366a7851cd14') # ISO CERT
        # return await self.handle_single_new_document_v2('9215409b-bb98-4bd0-b7aa-e6abe08d8ea5') # Temp Range
        # return await self.handle_single_new_document_v2('6464b465-2916-4db0-a2ea-142db3385a50') # material type
        # return await self.handle_single_new_document_v2('73a987c1-00c2-4c1a-9eab-bddbc8fc4aee') # combo5
        # return await self.handle_single_new_document_v2('41a05f31-dff7-4102-996b-038d67604ac5') #combo2 test to sync api changes to prod db
        #mariem latest #a3a8265b-1bff-4b95-af1f-b91dd91d9ae6
        pending = Requirement.get_by(status='pending', type='cbp', is_test=0)
        if len(pending) >= 5:
            print('exiting...')
            return

        # files = sorted(Requirement.get_by_not_null_criteria(status='idle', type='cbp'), key=lambda x: x['tried'])
        files = sorted([item for item in Requirement.get_by(status='idle', type='cbp', is_test=0) if item['tried'] < 2], key=lambda x: x['tried'])
        if len(files):
            if files[0]['tried'] < 2:
                await self.handle_single_new_document_v2(files[0]['id'])
        else:
            print("No Files In Queue!")

        return
    
    async def process_single_request(self, request_id):
        await self.handle_single_new_document_v2(request_id)


    async def get_dataset2(self, file_path, count, source_document_text, log):
        return await self.cbp_file_processor.process_new_file_Chronobid(file_path, self.criteria, source_document_text, count, log)

if __name__ == "__main__":
    processor = ChronobidDocumentProcessor(False)
    asyncio.run(processor.queue())
