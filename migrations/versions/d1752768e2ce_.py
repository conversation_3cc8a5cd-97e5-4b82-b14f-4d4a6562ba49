"""empty message

Revision ID: d1752768e2ce
Revises: 0e7efda7c2d3
Create Date: 2025-03-21 16:15:43.330980

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd1752768e2ce'
down_revision = '0e7efda7c2d3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('bids', schema=None) as batch_op:
        batch_op.add_column(sa.Column('package_id', sa.String(length=255), nullable=True))

    with op.batch_alter_table('project', schema=None) as batch_op:
        batch_op.add_column(sa.Column('parent_id', sa.String(length=36), nullable=True))
        batch_op.create_foreign_key(None, 'project', ['parent_id'], ['id'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('project', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_column('parent_id')

    with op.batch_alter_table('bids', schema=None) as batch_op:
        batch_op.drop_column('package_id')

    # ### end Alembic commands ###
