import os, re
import json
import asyncio
from multi_bid.models import Requirement, Project
from services.reasoning_chronobid import ChronobidReasoning
from services.data_handler import DataManager
from services.report_manager import ReportManager
from services.process_cbp_dataset_1 import Dataset1Extractor
from services.process_cbp_dataset_2 import Dataset2Extractor
from services.update_backend_status import BackendStatusUpdater
from flask_socketio import emit, join_room
import time
from flask import request
import redis

class ChronobidDocumentProcessor:
    def __init__(self, socket_manager, is_test=False):
        self.is_test = is_test
        self.socket_manager = socket_manager
        self.parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.env_data = self._load_env()
        self.chronobid_reasoning = ChronobidReasoning()
        self.data_manager = DataManager()
        self.dataset2_extractor = Dataset2Extractor(self.socket_manager)
        self.report_manager = None
        self.backend_status_updater = BackendStatusUpdater()
        self.redis_client = redis.StrictRedis(host='localhost', port=6379, db=0, decode_responses=True)
        
    
    def get_current_socketio(self):
        """Fetch the current socketio instance."""
        return self.get_socketio_instance()

    def _load_env(self):
        env_path = os.path.join(self.parent_dir, 'env.json')
        with open(env_path, 'r') as f:
            return json.load(f)
        
    async def process_cbp(self, requirement_id, bid_info ={}):
        # Store bid information for reference during processing
        self.multi_bid_info = bid_info  # Store the complete bid_info dictionary
        
        try:
            loop = asyncio.get_running_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
        return loop.run_until_complete(self.handle_multiple_bids(requirement_id))
    

    async def handle_multiple_bids(self, requirement_id):
        try:
            # Get the original requirement
            original_requirement = Requirement.get_single(requirement_id)
            
            # Check if the requirement has bids
            if not original_requirement.get('bids'):
                # If no bids, just process the single requirement as before
                return await self.handle_single_new_document_v2(requirement_id)
            
            # Parse the bids from the requirement
            bids = json.loads(original_requirement.get('bids')) if isinstance(original_requirement.get('bids'), str) else original_requirement.get('bids')
            
            if not bids or len(bids) == 0:
                return await self.handle_single_new_document_v2(requirement_id)
            
            
            # Notify the client that we're starting to process multiple bids
            self.add_event(requirement_id, 'progress_message', {
                'event_type': 'CBP',
                'request_id': requirement_id,
                'data': {'status': 'extracting_sources', 'message': f"Welcome to Chronobid Assistant... Processing {len(bids)} bids in parallel"}
            })
            
            # Create duplicated requirements for each bid
            duplicated_requirements = []
            for bid_index, bid in enumerate(bids):
                # Get the bid ID from the bid data
                bid_id = bid.get('id')  # Assuming each bid has an 'id' field
                
                # Get bid info from self.multi_bid_info using the bid_id
                bid_info = self.multi_bid_info.get(bid_id, {})
                
                # Duplicate the requirement for this bid
                dup_requirement = original_requirement.copy()
                
                # Generate a new unique ID for this duplicated requirement
                bid_requirement_id = f"{requirement_id}___{bid_id}"
                dup_requirement['id'] = bid_requirement_id
                dup_requirement['original_id'] = requirement_id
                dup_requirement['bid_data'] = bid
                dup_requirement['bid_index'] = bid_info.get('bid_index', bid_index)
                dup_requirement['bidder_name'] = bid_info.get('bidder_name', f'Bidder {bid_index+1}')
                
                duplicated_requirements.append(dup_requirement)
            
            # Process all bids in parallel using asyncio.gather
            tasks = [self.handle_single_new_document_v2(req['id']) for req in duplicated_requirements]
            results = await asyncio.gather(*tasks)
            
            # Initialize a list to store all logs
            full_log = []
            
            # Collect results for each bid
            for req, result in zip(duplicated_requirements, results):
                bid_requirement_id = req['id']
                bid_id = req['bid_data'].get('id')
                bid_info = self.multi_bid_info.get(bid_id, {})
                
                try:
                    if result:  # If the bid processing was successful
                        # Add bid-specific info to the log
                        result['bid_index'] = bid_info.get('bid_index')
                        result['bid_requirement_id'] = bid_requirement_id
                        result['bidder_name'] = bid_info.get('bidder_name')
                        result['bid_id'] = bid_id
                        result['status'] = 'completed'
                        
                        full_log.append(result)
                    else:
                        full_log.append({
                            "bid_index": bid_info.get('bid_index'),
                            "bid_requirement_id": bid_requirement_id,
                            "bid_id": bid_id,
                            "bidder_name": bid_info.get('bidder_name'),
                            "error": "Processing failed",
                            "status": "failed"
                        })
                    
                except Exception as e:
                    print(f"Error collecting results for bid {bid_id}: {e}")
                    full_log.append({
                        "bid_index": bid_info.get('bid_index'),
                        "bid_requirement_id": bid_requirement_id,
                        "bid_id": bid_id,
                        "bidder_name": bid_info.get('bidder_name'),
                        "error": str(e),
                        "status": "failed"
                    })
            
            # Update the original requirement with the full log
            Requirement.update(requirement_id, status='done', full_log=json.dumps(full_log))
            
            # Emit completion event with full results
            self.add_event(requirement_id, 'completed_event', {
                'event_type': 'CBP',
                'request_id': requirement_id,
                'data': {
                    'full_log': full_log,
                    'bid_count': len(bids)
                }
            })
            
            return full_log
            
        except Exception as e:
            print(f"Error in handle_multiple_bids: {e}")
            Requirement.update(requirement_id, status='idle', tried=(original_requirement.get("tried", 0) + 1))
            return None
    

    async def handle_single_new_document_v2(self, requirement_bid_ids):
        try:
            # Get the requirement to check if it's a bid
            requirement_id = requirement_bid_ids.split("___")[0]
            requirement = Requirement.get_single(requirement_id)
            self.exist_bid = self.requirement_bid_ids.split("___")[1]
            self.requirement_metadata = {"exist_bid":True,"bid_id":self.exist_bid}
            # Check if the requirement status is already done
            if requirement['status'] == 'done':
                print(f"CBP Requirement with id: {requirement_id} is already done. Exiting...")
                return  # Exit if the requirement is already done

            # Get bid-specific information if this is a bid
            
            bid_info = {
                    'bid_index': self.multi_bid_info[self.exist_bid]['bid_index'],
                    'bidder_name': self.multi_bid_info[self.exist_bid]['bidder_name'],
                    
                }

            self.add_event(requirement_id, 'progress_message', {
                'event_type': 'CBP',
                'request_id': requirement_id,
                'data': {'status': 'extracting_sources', 'message': f"Welcome to Chronobid Assistant..."}
            })
 
            self._prepare_context(requirement)
            self.report_manager = ReportManager(
                self.env_data.get('CHRONOBID_DIR'), self.requirement["project_id"], requirement_id
            )
        
            # Initialize log with bid information if available
            initial_log = self._initialize_log(len(self.criteria))
            if bid_info:
                initial_log.update(bid_info)
            
            self.report_manager.create_initial_report(initial_log)
            log = self.report_manager.load_report()

            for idx, criteria in enumerate(self.criteria, start=1):
                await self._async_process_single_criterion(criteria, idx, log)

            # Add bid information to the final log if this is a bid
            if bid_info:
                log.update(bid_info)

            self.add_event(requirement_id, 'completed_event', {
                'event_type': 'CBP',
                'request_id': requirement_id,
                'data': log
            })

            log = self.report_manager.load_report()
            await self._finalize_log_and_status(log)

        except Exception as e:
            print(f"Error: {e}")
    

    def _prepare_context(self, requirement):
        self.requirement = requirement
        self.requirement_id = requirement["id"]
        
        self.requirement_metadata = json.loads(self.redis_client.get(f"{self.requirement_id}_metadata"))

        print(f"loaded metadata for req_id {self.requirement_id} : {json.dumps(self.requirement_metadata)}")

        self.criteria = json.loads(requirement["criteria"])
        self.requirement_name = json.loads(requirement["name"])
        self.tender_number = requirement["id"]
        self.bidder_name = '[BIDDER NAME]'
        self.project = Project.get_single(requirement["project_id"])
        self.project_id = requirement['project_id']
        # self.tender_title = self.tender_title = " & ".join(self._remove_extension(os.path.basename(file_name)) for file_name in self.requirement_name)
        self.tender_title = self.bidder_name
        self.file_base_path = os.path.join(self.env_data.get('CHRONOBID_DIR'), self.project_id)
        self.intro_texts = {
            "risk": "Overall, [BIDDER NAME] presents certain risks in relation to criteria [#], which may impact the project's successful completion.",
            "strength": "Overall, [BIDDER NAME] has demonstrated a good understanding and evidence of criteria [#].",
            "weakness": "Overall, [BIDDER NAME] has notable weaknesses in addressing criteria [#], lacking sufficient evidence."
        }
        self.top_text = f'''
            <div>
                <h2>Evaluation Result</h2>
                <p>Following a detailed evaluation of <strong>{self.bidder_name}</strong>'s proposal, the offer achieved an overall score of <span class='score'><Overall_Score>0.0</Overall_Score>%</span> . This score reflects the performance across all assessed criteria. The summary of the evaluation per criterion is as follows:</p>
            </div>
        '''
        self.evaluation_result_text = f'''
            Following the thorough evaluation of [BIDDER NAME]'s proposal, an overall weighted score of <Overall_Score>0.0</Overall_Score>% was achieved, reflecting performance across all assessed criteria. 
            The detailed scoring for each criterion is as follows:
        '''

    def _initialize_log(self, criteria_count):
        return {
            "criteria": "",
            "input_token": 0,
            "output_token": 0,
            "requirement_id": self.requirement_id,
            "project_id": self.project_id,
            "chunk_to_pass": [""] * criteria_count,
            "evaluate_summaries": [""] * criteria_count,
            "top_text": self.top_text,
            "uploaded_text": "",
            "evaluate_summary": "",
            "evaluate_summary_processing": [""] * criteria_count,
            "evaluate_summary_intro" : self.top_text,
            "evaluate_summary_chunk": [""] * criteria_count, 
            "evaluate_summary_score": "",
            "source_potential_data_chunks": "",
            "evaluation_report_section": {
                "cover_page":"",
                "introduction":"",
                "evaluation_result": self.evaluation_result_text,
                "detailed_evaluation":"",
                "recommendation":"",
                "strength_chunk":[""] * criteria_count, "weak_chunk":[""] * criteria_count, "risk_chunk":[""] * criteria_count,
                "strength_text":"","weak_text":"","risk_text":"",
                "strength_weak_risk_chunk_intro":{},
                "evaluation_report":""
            }
        }
    
    def _remove_extension(self, file_name):
        base_name = os.path.splitext(file_name)[0]
        pattern = r'_(\d{6})_merged$'        
        modified_name = re.sub(pattern, '', base_name)
        
        return modified_name
    
    async def _sync_process_single_criterion(self, criteria, count, log): 
        try:
            start_time = time.time()  # Start time for the whole processing

            chunks = await self._get_chunks_to_process(count, log)
            log['chunk_to_pass'][count-1] = chunks
            print(f"Criterian {count} chunks: {chunks}")


            evaluate_text = f"<CRITERIA>Criteria {count}<CRITERIA><br/>"
            for chunk in chunks:
                evaluate_text += f"""
                    <Evaluation>{chunk['evaluation_data']['Evaluation']}</Evaluation><br/>
                    <Score>{chunk['evaluation_data']['Score']}</Score><br/>
                    <Reason>{chunk['evaluation_data']['Reason']}</Reason><br/>
                """
            log['evaluate_summary_processing'][count-1] = f"{evaluate_text}"

            print(f"Evaluation summary for criteria {count}: {evaluate_text}")

            print(f'processing evaluation summary for criteria {count}...')
            self.add_event(self.requirement_id , 'progress_message', {
                'event_type': 'CBP',
                'request_id': self.requirement_id ,
                'data': {'status': 'extracting_sources', 'message': f"Compiling the evaluation summary for Query {count}. Finalizing results.", 'query_idx': count}
            })
            
            await self.chronobid_reasoning.get_claude_to_summarize_evaluate_v2(log, criteria, count)

            print('came back from claude evaluate summary...')        
            
            self.add_event(self.requirement_id, 'in_progress_event', {
                'event_type': 'CBP',
                'request_id': self.requirement_id,
                'data': log
            })

            # Time taken for _sync_process_single_criterion
            processing_time = time.time() - start_time
            print(f"Time taken for _sync_process_single_criterion: {processing_time:.2f} seconds")
    
        except Exception as e:
            print(f"Error in processing criterion {count}: {e}")


    async def _async_process_single_criterion(self, criteria, count, log):
        try:

            print(f'now processing dataset1 for criteria {count}....')
            start_time = time.time()  # Start time for dataset1 processing

            self.add_event(self.requirement_id , 'progress_message', {
                'event_type': 'CBP',
                'request_id': self.requirement_id ,
                'data': {'status': 'extracting_sources', 'message': f"Starting analysis for Query {count}. Extracting relevant tender sources.", 'query_idx': count}
            })
            print('finished adding event...')

            source_text = await self._extract_dataset1(criteria, count)
            if not source_text:
                raise RuntimeError("Dataset 1 extraction failed")
            
            log['source_potential_data_chunks'] = source_text
            
            # Time taken for dataset1
            dataset1_time = time.time() - start_time
            print(f"Time taken for Dataset1 for criteria {count} extraction: {dataset1_time:.2f} seconds")

            print(f'now processing dataset2.... for criteria {count}.')
            start_time = time.time()  # Start time for dataset2 processing

            self.add_event(self.requirement_id , 'progress_message', {
                'event_type': 'CBP',
                'request_id': self.requirement_id ,
                'data': {'status': 'extracting_sources', 'message': f"Continuing analysis for Query {count}. Refining insights from the uploaded bid.", 'query_idx': count}
            })
            
            data = await self._extract_dataset2(criteria, source_text, count, log)
            if not data:
                raise RuntimeError("Dataset 2 extraction failed")

            # Time taken for dataset2
            dataset2_time = time.time() - start_time
            print(f"Time taken for Dataset2 extraction: {dataset2_time:.2f} seconds")
            
            print(f'completed dataset2 for criteria {count}...')
            self.add_event(self.requirement_id , 'progress_message', {
                'event_type': 'CBP',
                'request_id': self.requirement_id ,
                'data': {'status': 'extracting_sources', 'message': f"Insights for Query {count} extracted. Preparing to analyze document chunks.", 'query_idx': count}
            })

            await self._sync_process_single_criterion(criteria, count, log)
            self.report_manager.update_log(log)
        

        except Exception as e:
            print(f"Error in criterion {count}: {e}")

    async def _extract_dataset1(self, criteria, count):
        print('i enter_extract_dataset1 method...')
        extractor = Dataset1Extractor(criteria)
        return await extractor.get_dataset1(self.project_id, count)

    async def _extract_dataset2(self, criteria, source_text, count, log):
        return await self.dataset2_extractor.process_new_file_Chronobid(
            criteria, source_text, self.requirement_metadata, count, log
        )

    async def _get_chunks_to_process(self, count, log):
        for key in ['strength_chunk', 'weak_chunk', 'risk_chunk']:
            print(f"this is the key: {key} and log: {log['evaluation_report_section'][key]} ")
            chunk_list = log['evaluation_report_section'].get(key, [])
            print(f"condition check: {chunk_list}")
            if len(chunk_list) > count - 1 and chunk_list[count - 1]:
                print('the chunklist: ', chunk_list[count - 1])
                return chunk_list[count - 1]
        # Return an empty list if no valid chunk is found
        return []


    async def _finalize_log_and_status(self, log):
        self.report_manager.update_log(log)
        if not self.is_test:
            self.backend_status_updater.update_history_status(self.requirement_id, 2)
    
    def add_event(self, request_id, event_name, data):
        print(f"Adding event {event_name} to room {request_id}")
        # return
        self.socket_manager.emit_to_client(request_id, event_name, data)



# Example usage of process_cbp
async def example_process_cbp():
    # Initialize the processor
    socket_manager = None  # Replace with actual socket manager if needed
    processor = ChronobidDocumentProcessor(socket_manager)
    
    # Example requirement ID
    req_id = "example-requirement-id"
    
    # Example bid info
    bid_info = {
        "bid1": {
            "id": "bid1",
            "bid_index": 0,
            "bidder_name": "Company A"
        },
        "bid2": {
            "id": "bid2",
            "bid_index": 1,
            "bidder_name": "Company B"
        }
    }
    
    # Process the CBP documents
    result = await processor.process_cbp(req_id, bid_info)
    
    # Save results to a text file
    if result:
        output_file = f"cbp_results_{req_id}.txt"
        with open(output_file, 'w') as f:
            f.write("Chronobid Processing Results\n")
            f.write("==========================\n\n")
            
            for bid_result in result:
                f.write(f"Bidder: {bid_result.get('bidder_name', 'Unknown')}\n")
                f.write(f"Bid Index: {bid_result.get('bid_index', 'N/A')}\n")
                f.write(f"Status: {bid_result.get('status', 'Unknown')}\n")
                f.write("-" * 50 + "\n")
                
                # Write evaluation details if available
                if 'evaluation_report_section' in bid_result:
                    eval_section = bid_result['evaluation_report_section']
                    f.write("\nEvaluation Report:\n")
                    f.write(eval_section.get('evaluation_report', 'No evaluation report available\n'))
                
                f.write("\n" + "=" * 50 + "\n\n")
        
        print(f"Results saved to {output_file}")
    else:
        print("No results to save")

# Run the example
if __name__ == "__main__":
    asyncio.run(example_process_cbp())
    