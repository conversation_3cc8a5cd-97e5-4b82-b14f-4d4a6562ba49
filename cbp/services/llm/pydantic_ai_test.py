from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Union
from pydantic import BaseModel, Field

# Import the necessary PydanticAI components
from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.models.anthropic import AnthropicModel
from pydantic_ai.models.gemini import GeminiModel
from pydantic_ai.models.groq import GroqModel
from pydantic_ai.models.mistral import MistralModel
from pydantic_ai.models.cohere import CohereModel
from pydantic_ai.models.bedrock import BedrockConverseModel
from pydantic_ai.models.fallback import FallbackModel

# Import providers
from pydantic_ai.providers.openai import OpenAIProvider
from pydantic_ai.providers.anthropic import AnthropicProvider
from pydantic_ai.providers.google_gla import GoogleGLAProvider
from pydantic_ai.providers.google_vertex import GoogleVertexProvider
from pydantic_ai.providers.groq import GroqProvider
from pydantic_ai.providers.mistral import MistralProvider
from pydantic_ai.providers.cohere import CohereProvider
from pydantic_ai.providers.bedrock import BedrockProvider
from pydantic_ai.providers.azure import AzureProvider

T = TypeVar('T', bound=BaseModel)

class ModelResponse(Generic[T]):
    """Response wrapper for model responses."""
    
    def __init__(self, response, data=None):
        self.response = response
        self.data = data
        
    @property
    def raw_response(self):
        return self.response
    
    @property
    def structured_data(self) -> Optional[T]:
        return self.data
    
    def usage(self):
        return self.response.usage()
    
    def all_messages(self):
        return self.response.all_messages()

class UnifiedAIModel:
    """
    A unified interface for various AI providers supported by PydanticAI.
    
    This class provides a consistent way to interact with different LLM providers
    while abstracting away the specific implementation details of each.
    """
    
    SUPPORTED_PROVIDERS = {
        "openai": OpenAIModel,
        "anthropic": AnthropicModel,
        "google-gla": GeminiModel,
        "google-vertex": GeminiModel,
        "groq": GroqModel,
        "mistral": MistralModel,
        "cohere": CohereModel,
        "bedrock": BedrockConverseModel,
        "azure": OpenAIModel,
        "ollama": OpenAIModel,
    }
    
    def __init__(
        self,
        provider: str,
        model_name: str,
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        **kwargs
    ):
        """
        Initialize the unified AI model.
        
        Args:
            provider: The provider to use (e.g., "openai", "anthropic", "google-gla")
            model_name: The specific model to use (e.g., "gpt-4o", "claude-3-5-sonnet-latest")
            system_prompt: Optional system prompt to set context for the model
            temperature: Controls randomness in the model output (0.0 to 1.0)
            **kwargs: Additional provider-specific arguments
        """
        self.provider_name = provider
        self.model_name = model_name
        self.system_prompt = system_prompt
        self.temperature = temperature
        self.kwargs = kwargs
        
        # Initialize the model based on the provider
        self._initialize_model()
        
    def _initialize_model(self):
        """Initialize the appropriate model based on the provider."""
        if self.provider_name not in self.SUPPORTED_PROVIDERS:
            raise ValueError(f"Unsupported provider: {self.provider_name}. Supported providers are: {', '.join(self.SUPPORTED_PROVIDERS.keys())}")
        
        model_class = self.SUPPORTED_PROVIDERS[self.provider_name]
        provider_instance = self._get_provider_instance()
        
        # Special case for Ollama (which uses OpenAI's interface but needs a specific base_url)
        if self.provider_name == "ollama":
            base_url = self.kwargs.get("base_url", "http://localhost:11434/v1")
            provider_instance = OpenAIProvider(base_url=base_url)
        
        # Initialize the model with the appropriate provider
        if provider_instance:
            self.model = model_class(self.model_name, provider=provider_instance)
        else:
            # If no specific provider instance was created, use default initialization
            self.model = model_class(self.model_name)
        
        # Create the agent with the model
        self.agent = Agent(
            model=self.model,
            system_prompt=self.system_prompt,
            temperature=self.temperature
        )
    
    def _get_provider_instance(self):
        """Create an appropriate provider instance based on the provider name and kwargs."""
        provider_map = {
            "openai": self._create_openai_provider,
            "anthropic": self._create_anthropic_provider,
            "google-gla": self._create_google_gla_provider,
            "google-vertex": self._create_google_vertex_provider,
            "groq": self._create_groq_provider,
            "mistral": self._create_mistral_provider,
            "cohere": self._create_cohere_provider,
            "bedrock": self._create_bedrock_provider,
            "azure": self._create_azure_provider,
        }
        
        if self.provider_name in provider_map:
            return provider_map[self.provider_name]()
        return None
    
    def _create_openai_provider(self):
        """Create an OpenAI provider instance."""
        api_key = self.kwargs.get("api_key")
        base_url = self.kwargs.get("base_url")
        openai_client = self.kwargs.get("openai_client")
        
        if openai_client:
            return OpenAIProvider(openai_client=openai_client)
        elif api_key and base_url:
            return OpenAIProvider(api_key=api_key, base_url=base_url)
        elif api_key:
            return OpenAIProvider(api_key=api_key)
        elif base_url:
            return OpenAIProvider(base_url=base_url)
        return None
    
    def _create_anthropic_provider(self):
        """Create an Anthropic provider instance."""
        api_key = self.kwargs.get("api_key")
        http_client = self.kwargs.get("http_client")
        
        if api_key and http_client:
            return AnthropicProvider(api_key=api_key, http_client=http_client)
        elif api_key:
            return AnthropicProvider(api_key=api_key)
        return None
    
    def _create_google_gla_provider(self):
        """Create a Google Generative Language API provider instance."""
        api_key = self.kwargs.get("api_key")
        http_client = self.kwargs.get("http_client")
        
        if api_key and http_client:
            return GoogleGLAProvider(api_key=api_key, http_client=http_client)
        elif api_key:
            return GoogleGLAProvider(api_key=api_key)
        return None
    
    def _create_google_vertex_provider(self):
        """Create a Google Vertex AI provider instance."""
        service_account_file = self.kwargs.get("service_account_file")
        service_account_info = self.kwargs.get("service_account_info")
        region = self.kwargs.get("region")
        http_client = self.kwargs.get("http_client")
        project_id = self.kwargs.get("project_id")
        
        provider_kwargs = {}
        if service_account_file:
            provider_kwargs["service_account_file"] = service_account_file
        if service_account_info:
            provider_kwargs["service_account_info"] = service_account_info
        if region:
            provider_kwargs["region"] = region
        if http_client:
            provider_kwargs["http_client"] = http_client
        if project_id:
            provider_kwargs["project_id"] = project_id
            
        if provider_kwargs:
            return GoogleVertexProvider(**provider_kwargs)
        return None
    
    def _create_groq_provider(self):
        """Create a Groq provider instance."""
        api_key = self.kwargs.get("api_key")
        http_client = self.kwargs.get("http_client")
        
        if api_key and http_client:
            return GroqProvider(api_key=api_key, http_client=http_client)
        elif api_key:
            return GroqProvider(api_key=api_key)
        return None
    
    def _create_mistral_provider(self):
        """Create a Mistral provider instance."""
        api_key = self.kwargs.get("api_key")
        http_client = self.kwargs.get("http_client")
        
        if api_key and http_client:
            return MistralProvider(api_key=api_key, http_client=http_client)
        elif api_key:
            return MistralProvider(api_key=api_key)
        return None
    
    def _create_cohere_provider(self):
        """Create a Cohere provider instance."""
        api_key = self.kwargs.get("api_key")
        http_client = self.kwargs.get("http_client")
        
        if api_key and http_client:
            return CohereProvider(api_key=api_key, http_client=http_client)
        elif api_key:
            return CohereProvider(api_key=api_key)
        return None
    
    def _create_bedrock_provider(self):
        """Create an AWS Bedrock provider instance."""
        region_name = self.kwargs.get("region_name")
        aws_access_key_id = self.kwargs.get("aws_access_key_id")
        aws_secret_access_key = self.kwargs.get("aws_secret_access_key")
        bedrock_client = self.kwargs.get("bedrock_client")
        
        if bedrock_client:
            return BedrockProvider(bedrock_client=bedrock_client)
        elif region_name and aws_access_key_id and aws_secret_access_key:
            return BedrockProvider(
                region_name=region_name,
                aws_access_key_id=aws_access_key_id,
                aws_secret_access_key=aws_secret_access_key
            )
        return None
    
    def _create_azure_provider(self):
        """Create an Azure provider instance."""
        azure_endpoint = self.kwargs.get("azure_endpoint")
        api_version = self.kwargs.get("api_version")
        api_key = self.kwargs.get("api_key")
        
        if azure_endpoint and api_version and api_key:
            return AzureProvider(
                azure_endpoint=azure_endpoint,
                api_version=api_version,
                api_key=api_key
            )
        return None
    
    def query(self, question: str, result_type: Optional[Type[T]] = None) -> ModelResponse:
        """
        Query the model with a question and optionally specify a Pydantic model for structured output.
        
        Args:
            question: The question to ask the model
            result_type: Optional Pydantic model class to structure the response
            
        Returns:
            ModelResponse: A wrapper around the model's response
        """
        if result_type:
            # Update the agent with the specified result type
            self.agent = Agent(
                model=self.model,
                system_prompt=self.system_prompt,
                temperature=self.temperature,
                result_type=result_type
            )
            response = self.agent.run_sync(question)
            return ModelResponse(response, response.data)
        else:
            response = self.agent.run_sync(question)
            return ModelResponse(response)
    
    async def query_async(self, question: str, result_type: Optional[Type[T]] = None) -> ModelResponse:
        """
        Asynchronously query the model with a question and optionally specify a Pydantic model for structured output.
        
        Args:
            question: The question to ask the model
            result_type: Optional Pydantic model class to structure the response
            
        Returns:
            ModelResponse: A wrapper around the model's response
        """
        if result_type:
            # Update the agent with the specified result type
            self.agent = Agent(
                model=self.model,
                system_prompt=self.system_prompt,
                temperature=self.temperature,
                result_type=result_type
            )
            response = await self.agent.run(question)
            return ModelResponse(response, response.data)
        else:
            response = await self.agent.run(question)
            return ModelResponse(response)
    
    def create_fallback_chain(self, *additional_models: 'UnifiedAIModel') -> 'UnifiedAIModel':
        """
        Create a fallback chain with this model as the primary and the provided models as backups.
        
        Args:
            *additional_models: One or more UnifiedAIModel instances to use as fallbacks
            
        Returns:
            UnifiedAIModel: A new UnifiedAIModel instance configured with a FallbackModel
        """
        models = [self.model] + [model.model for model in additional_models]
        fallback_model = FallbackModel(*models)
        
        result = UnifiedAIModel(
            provider="fallback",
            model_name="fallback",
            system_prompt=self.system_prompt,
            temperature=self.temperature
        )
        result.model = fallback_model
        result.agent = Agent(
            model=fallback_model,
            system_prompt=self.system_prompt,
            temperature=self.temperature
        )
        return result