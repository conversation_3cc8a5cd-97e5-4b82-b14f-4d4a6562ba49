from datetime import datetime
from flask_socketio import emit, Namespace, Socket<PERSON>
from rq import Retry
from flask import request
from extensions import queues
from services.background_task_processor import BackgroundTaskProcessor
import random

class SCPNamespace(Namespace):
    def __init__(self, namespace, socket_manager):
        super().__init__(namespace)
        self.socket_manager = socket_manager
        print('initialized scp namespace....')

    def on_connect(self):
        """Handle connection to /scp namespace."""
        try:
            sid = request.sid
            print(f"[{self._get_timestamp()}] New client connected: {sid}")
            emit("progress_message", {
                "message": "Test emit from server, congratulations on joining SCP namespace on CPU",
                "timestamp": self._get_timestamp()
            })
            print("Client connected to /scp namespace")
        except Exception as e:
            print(f"Error in on_connect: {str(e)}")

    def on_disconnect(self):
        """Handle disconnection from /scp namespace."""
        try:
            sid = request.sid
            print(f"[{self._get_timestamp()}] Client {sid} disconnecting from /scp namespace")
            self.socket_manager.remove_client(sid)
            print(f"Client {sid} disconnected and removed from room management")
        except Exception as e:
            print(f"Error in on_disconnect: {str(e)}")

    def on_leave(self, data):
        """Handle client leaving a room."""
        try:
            sid = request.sid
            request_id = data.get('request_id')
            if request_id:
                print(f"[{self._get_timestamp()}] Client {sid} leaving room {request_id}")
                self.socket_manager.remove_client(sid)
                emit("progress_message", {
                    "message": f"Client left room {request_id}",
                    "timestamp": self._get_timestamp()
                }, room=request_id)
                print(f"Client left room {request_id} in /scp namespace")
        except Exception as e:
            print(f"Error in on_leave: {str(e)}")

    def on_join(self, data):
        """Handle client joining the room."""
        try:
            sid = request.sid
            request_id = data.get('request_id')
            is_reconnect = data.get("is_reconnect", False)

            if not request_id:
                print(f"[{self._get_timestamp()}] Error: No request_id provided for client {sid}")
                emit("error", {
                    "message": "No request_id provided",
                    "timestamp": self._get_timestamp()
                }, room=sid)
                return

            print(f"[{self._get_timestamp()}] Client {sid} joining room {request_id} (reconnect: {is_reconnect})")

            if is_reconnect:
                self.socket_manager.handle_reconnect(sid, request_id)
            else:
                self.socket_manager.add_client(sid, request_id)

                # Emit join confirmation
                join_message = {
                    "message": f"Client joined SCP room {request_id} on CPU",
                    "timestamp": self._get_timestamp(),
                    "request_id": request_id,
                    "is_reconnect": is_reconnect
                }
                
                # Emit to the specific client
                emit("progress_message", join_message, room=request_id, namespace='/scp')
                
                # Emit to all clients in the room
                emit("client_joined", {
                    "message": f"New client joined the SCP room",
                    "timestamp": self._get_timestamp()
                }, room=request_id)

                # Log room members after join
                room_members = self.socket_manager.get_room_members(request_id)
                print('now about to run task for scp evaluation...')
                
                # For SCP tasks
                scp_processor = BackgroundTaskProcessor(task_type="celery", process_type="evaluate_scp")
                scp_processor.process_task(request_id=request_id)

                # # call the redis task
                # q = random.choice(queues["scp_"])   
                # q.enqueue(redis_scp_task, request_id, retry=Retry(max=3))
                
                # # Call the Celery task
                # celery_scp_task.delay(request_id)

                print(f"Current room {request_id} members: {room_members}")

        except Exception as e:
            print(f"Error in on_join: {str(e)}")
            emit("error", {
                "message": f"Error joining room: {str(e)}",
                "timestamp": self._get_timestamp()
            }, room=sid)

    def _get_timestamp(self):
        """Helper method to get formatted timestamp."""
        return datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')


