import os
import shutil
import docx2txt
import docx
import glob
from pdfminer.high_level import extract_text
from services.docx_loader import DocsLoader
import random
import string
from models import File,Merged_file,Project,File_merged_file_association
import logging
import services.aggregate_project_documents as file_aggregator

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s',
                    filename='reaggregate.log',
                    filemode='a')


class ReAggregateMergedFiles:
    def __init__(self, data_directory):
        self.data_directory = data_directory

    def process_deleted_file(self, project_id):
        try:
           
            # Delete merged file by project ID
            try:
                self.revert_merged_file_status_by_project_id(project_id)
                logging.info(f"Response from delete_merged_file_by_project_id")
            except Exception as e:
                logging.error(f"Error in delete_merged_file_by_project_id: {e}")

            # Revert file status by project ID
            try:
                self.revert_file_status_by_project_id(project_id)
                logging.info(f"Response from revert_file_status_by_project_id")
            except Exception as e:
                logging.error(f"Error in revert_file_status_by_project_id: {e}")

            # Delete merged .docx files in project
            try:
                self.delete_merged_docx_files_in_project(project_id)
                logging.info(f"Response from delete_merged_docx_files_in_project")
            except Exception as e:
                logging.error(f"Error in delete_merged_docx_files_in_project: {e}")

            # Reaggregate all files in project
            try:
                self.reaggregate_all_files_in_project(project_id)
                logging.info(f"Response from reaggregate_all_files_in_project")
            except Exception as e:
                logging.error(f"Error in reaggregate_all_files_in_project: {e}")

        except Exception as e:
            logging.error(f"Error processing deleted file: {e}")
    
    def get_project_directory(self, project_id):
        return os.path.join(self.data_directory, project_id)
    
    def revert_merged_file_status_by_project_id(self, project_id):
        all_merged_files = Merged_file.get_by(project_id=project_id)
        for merge_file in all_merged_files:
            Merged_file.update(id=merge_file["id"], status="deleted")
    
    def revert_file_status_by_project_id(self, project_id):
        all_files = File.get_by(project_id=project_id)
        for file in all_files:
            File.update(id=file["id"], merge_status="not_merged")
    
    def delete_merged_docx_files_in_project(self, project_id):
        directory = self.get_project_directory(project_id)
        search_pattern = os.path.join(directory, "*_merged.docx")

        # Find all files matching the search pattern
        merged_files = glob.glob(search_pattern)

        # Delete each merged file found
        for merged_file in merged_files:
            try:
                os.remove(merged_file)
                logging.info(f"Deleted merged file: {merged_file}")
            except Exception as e:
                logging.error(f"Error deleting merged file {merged_file}: {e}")

    def reaggregate_all_files_in_project(self, project_id):
        pending_documents = File.get_by(project_id=project_id, merge_status="not_merged")
        if len(pending_documents) > 0:
            aggregator = file_aggregator.ProjectDataAggregator(self.data_directory, project_id, pending_documents)
            aggregator.merge_most_recent_file_content()
        else:
            logging.info(f"No recent modifications in project directory {project_id}")


def check_deleted_file(data_directory):
    try:
        projects_with_deleted_file = Project.get_by(has_deleted_file=True)
        if projects_with_deleted_file:
            for project in projects_with_deleted_file:
                try:
                    logging.info(f"Project with deleted file: {project}")
                    reaggregator = ReAggregateMergedFiles(data_directory)
                    reaggregator.process_deleted_file(project["id"])
                except Exception as e:
                    logging.error(f"Error processing deleted file for project {project['id']}: {e}")
        else:
            logging.info("No projects found with deleted files.")
    except Exception as e:
        logging.error(f"Error checking deleted files: {e}")



if __name__ == "__main__":
    DATA_DIR = os.getenv("DATA_DIR")
    APP_ROOT = os.path.dirname(os.path.dirname(__file__))
    data_directory = os.path.join(APP_ROOT,DATA_DIR)
    check_deleted_file(data_directory)