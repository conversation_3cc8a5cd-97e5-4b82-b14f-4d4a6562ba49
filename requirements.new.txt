accelerate==0.33.0
aiohttp==3.9.5
aiosignal==1.3.1
alembic==1.13.2
amqp==5.3.1
annotated-types==0.7.0
anthropic==0.32.0
anyio==4.4.0
arrow==1.3.0
async-timeout==4.0.3
attrs==23.2.0
beautifulsoup4==4.12.3
bidict==0.23.1
billiard==4.2.1
bitsandbytes==0.43.2
blinker==1.9.0
boto3==1.34.150
botocore==1.34.150
celery==5.4.0
cerebras_cloud_sdk==1.15.0
certifi==2024.7.4
cffi==1.16.0
chardet==5.2.0
charset-normalizer==3.3.2
click==8.1.7
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
cobble==0.1.4
cohere==5.6.2
coloredlogs==15.0.1
cryptography==43.0.0
dataclasses-json==0.6.7
datasets==2.20.0
Deprecated==1.2.15
dill==0.3.8
dirtyjson==1.0.8
distro==1.9.0
dnspython==2.7.0
docstring_parser==0.16
docx2txt==0.8
et_xmlfile==2.0.0
eventlet==0.38.0
exceptiongroup==1.2.2
faiss-cpu==1.9.0.post1
fastavro==1.9.5
filelock==3.15.4
filetype==1.2.0
Flask==2.0.1
Flask-Cors==5.0.0
Flask-Migrate==4.0.7
Flask-SocketIO==5.5.1
Flask-SQLAlchemy==2.5.1
fpdf==1.7.2
frozenlist==1.4.1
fsspec==2024.5.0
greenlet==3.0.3
groq==0.13.0
gunicorn==22.0.0
h11==0.14.0
httpcore==1.0.5
httpx==0.27.0
httpx-sse==0.4.0
huggingface-hub==0.24.3
humanfriendly==10.0
idna==3.7
instructor==1.7.2
itsdangerous==2.2.0
Jinja2==3.1.4
jiter==0.8.2
jmespath==1.0.1
joblib==1.4.2
jsonpatch==1.33
jsonpointer==3.0.0
kombu==5.4.2
langchain==0.3.14
langchain-cohere==0.3.4
langchain-community==0.3.14
langchain-core==0.3.29
langchain-experimental==0.3.4
langchain-text-splitters==0.3.5
langsmith==0.2.10
llama-cloud==0.1.7
llama-index==0.12.9
llama-index-agent-openai==0.4.1
llama-index-cli==0.4.0
llama-index-core==0.12.10.post1
llama-index-embeddings-openai==0.3.1
llama-index-indices-managed-llama-cloud==0.6.3
llama-index-llms-openai==0.3.12
llama-index-multi-modal-llms-openai==0.4.2
llama-index-program-openai==0.3.1
llama-index-question-gen-openai==0.3.0
llama-index-readers-file==0.4.2
llama-index-readers-llama-parse==0.4.0
llama-parse==0.5.19
lxml==5.2.2
Mako==1.3.5
mammoth==1.8.0
markdown-it-py==3.0.0
MarkupSafe==2.1.5
marshmallow==3.21.3
mdurl==0.1.2
mpmath==1.3.0
multidict==6.0.5
multiprocess==0.70.16
mypy-extensions==1.0.0
nest-asyncio==1.6.0
networkx==3.3
nltk==3.9.1
numpy==1.26.4
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.1.105
nvidia-cuda-nvrtc-cu12==12.1.105
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==*********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==**********
nvidia-cusparse-cu12==**********
nvidia-nccl-cu12==2.20.5
nvidia-nvjitlink-cu12==12.5.82
nvidia-nvtx-cu12==12.1.105
openai==1.58.1
openpyxl==3.1.5
optimum==1.21.2
orjson==3.10.6
packaging==23.2
pandas==2.2.2
parameterized==0.9.0
pdfminer.six==20240706
peft==0.12.0
pillow==10.4.0
pinecone-client==3.1.0
prompt_toolkit==3.0.50
protobuf==5.27.2
psutil==6.0.0
pyarrow==17.0.0
pyarrow-hotfix==0.6
pycparser==2.22
pydantic==2.8.2
pydantic-settings==2.7.1
pydantic_core==2.20.1
Pygments==2.18.0
PyMuPDF==1.24.9
PyMuPDFb==1.24.9
PyMySQL==1.1.1
pypdf==5.1.0
python-dateutil==2.9.0.post0
python-docx==1.1.2
python-dotenv==1.0.1
python-engineio==4.11.2
python-socketio==5.12.1
pytz==2024.1
PyYAML==6.0.1
redis==5.2.1
Redis-Sentinel-Url==1.0.1
regex==2024.7.24
reportlab==4.2.2
requests==2.32.3
requests-toolbelt==1.0.0
rich==13.9.4
rq==2.1.0
rq-dashboard==*******
s3transfer==0.10.2
safetensors==0.4.3
scikit-learn==1.5.1
scipy==1.14.0
sentence-transformers==3.0.1
sentencepiece==0.2.0
sentry-sdk==2.20.0
shellingham==1.5.4
simple-websocket==1.1.0
six==1.16.0
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==1.4.20
striprtf==0.0.26
sympy==1.13.1
tabulate==0.9.0
tenacity==9.0.0
threadpoolctl==3.5.0
tiktoken==0.8.0
tokenizers==0.19.1
torch==2.4.0
tqdm==4.66.4
transformers==4.42.4
triton==3.0.0
typer==0.15.1
types-python-dateutil==2.9.0.20241206
types-requests==2.32.0.20240712
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2024.1
urllib3==2.2.2
uuid==1.30
uWSGI==2.0.26
vine==5.1.0
wcwidth==0.2.13
Werkzeug==2.0.1
wrapt==1.17.0
wsproto==1.2.0
xxhash==3.4.1
yarl==1.9.4
