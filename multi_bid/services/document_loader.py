
from langchain_community.document_loaders import CSVLoader, PDFMinerLoader, TextLoader, UnstructuredExcelLoader, Docx2txtLoader,PyPDFLoader
import os
import mammoth
from services.docx_loader import DocsLoader

DOCUMENT_MAP = {
    ".txt": TextLoader,
    ".py": TextLoader,
    ".pdf": PyPDFLoader,
    ".csv": CSVLoader,
    ".xls": UnstructuredExcelLoader,
    ".xlxs": UnstructuredExcelLoader,
    ".docx": DocsLoader,
    ".doc": DocsLoader,
}


def load_single_document(file_path: str, allowed_tags=''):
    # Loads a single document from a file path
    file_extension = os.path.splitext(file_path)[1]
    loader_class = DOCUMENT_MAP.get(file_extension)
    
    if loader_class and file_extension != ".pdf":
        loader = loader_class(file_path, allowed_tags=allowed_tags)
        return loader.load()[0].__dict__['page_content']
    elif file_extension == ".pdf":
        loader = loader_class(file_path)
        return loader.load_and_split()
    else:
        raise ValueError("Document type is undefined")
    
    
    


