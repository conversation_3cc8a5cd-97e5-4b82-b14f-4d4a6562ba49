#init.py
# import eventlet
# eventlet.monkey_patch(thread=False)

import gevent
from gevent import monkey
monkey.patch_all()

import os
import json
from flask import Flask
from flask_cors import CORS
from extensions import db, socketio, migrate, socket_manager, celery
from services.socket_manager import SocketManager
from socket_instance import socket_instance
import sentry_sdk
from celery_app import make_celery
from sentry_sdk.integrations.flask import FlaskIntegration
from sentry_sdk.integrations.redis import RedisIntegration

from dramatiq_broker import broker as dramatiq_broker
from dramatiq_file_broker import broker as dramatiq_file_broker

def create_app():
    env_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'env.json')
    with open(env_file_path, 'r') as f:
        env_data = json.load(f)

    os.environ['FLASK_ENV'] = 'production'
    os.environ["KMP_DUPLICATE_LIB_OK"]="TRUE"

    # Determine if running locally or in a live environment
    is_local = os.getenv('FLASK_ENV', 'development') == 'development'

    allowed_origins = [
        '*',
        'null',
        'http://localhost:*',  # Allow all localhost ports
        'http://localhost:3000',
        'http://127.0.0.1:5500',
        'http://127.0.0.1:*',  # Allow all localhost IP ports
        'https://backend.aienergy-oilandgas.com',
        'https://app.aienergy-oilandgas.com',
        "http://localhost:5500",
        'https://ainrg.draglobal.com',
        'https://ainrgds.draglobal.com'
    ]
    
    
    app = Flask(__name__)
    CORS(app, resources={r"/*": {"origins": allowed_origins}})

    # Redis configuration
    REDIS_URL = env_data.get("REDIS_URL", "redis://localhost:6379/0")
    RABBITMQ_URL = env_data.get("RABBITMQ_URL", 'guest:guest@localhost:5672/')
    
    
    
    socket_instance.set_redis_url(REDIS_URL)
    # url git hub 
    if is_local:
        print('i am local user..')
        socketio.init_app(app, cors_allowed_origins=allowed_origins, async_mode='gevent', message_queue=REDIS_URL, logger=True, engineio_logger=True)
        app.config['SQLALCHEMY_DATABASE_URI'] = f'''mysql+pymysql://{env_data.get("MYSQL_USER_LOCAL")}:{env_data.get("MYSQL_PASSWORD_LOCAL")}@{env_data.get("MYSQL_HOST_LOCAL")}:{env_data.get("MYSQL_PORT_LOCAL")}/{env_data.get("MYSQL_DATABASE_LOCAL")}'''
    else:
        # Initialize Sentry
        try:
            dsn = env_data["SENTRY_DSN"]
            sentry_sdk.init(
                dsn=dsn,
                traces_sample_rate=0.1,
                environment="prod",
                profiles_sample_rate=0.1
            )
            print("Sentry has been initialized.")
        except Exception:
             pass



        socketio.init_app(app, cors_allowed_origins=allowed_origins, async_mode='gevent', message_queue=REDIS_URL, logger=True, engineio_logger=True)
        app.config['SQLALCHEMY_DATABASE_URI'] = f'''mysql+pymysql://{env_data.get("MYSQL_USER")}:{env_data.get("MYSQL_PASSWORD")}@{env_data.get("MYSQL_HOST")}:{env_data.get("MYSQL_PORT")}/{env_data.get("MYSQL_DATABASE")}?charset=utf8mb4&use_unicode=1'''



    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SQLALCHEMY_POOL_SIZE'] = 10
    app.config['SQLALCHEMY_MAX_OVERFLOW'] = 20
    app.config['SQLALCHEMY_POOL_TIMEOUT'] = 30
    app.config['CELERY_BROKER_URL'] = 'amqp://' + RABBITMQ_URL #REDIS_URL
    app.config['CELERY_RESULT_BACKEND'] = 'rpc://' + RABBITMQ_URL
    app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
        'pool_recycle': 280,
        'pool_pre_ping': True
    }

    db.init_app(app)
    migrate.init_app(app, db)
    make_celery(app, os.environ['FLASK_ENV'], env_data.get("SENTRY_DSN"))
    dramatiq_broker.init_app(app)
    dramatiq_file_broker.init_app(app)

    # Initialize socket_manager with Redis URL
    socket_manager = SocketManager(socketio)
    socket_instance.set_instance(socket_manager)

    print(f"Socket manager initialized: {socket_manager is not None}")    

    @app.teardown_appcontext
    def shutdown_session(exception=None):
        print('closing database connection after request')
        db.session.remove()

    return app, socketio, socket_manager, celery, dramatiq_broker, dramatiq_file_broker

# app = create_app()
app, socketio, socket_manager, celery, dramatiq_broker, dramatiq_file_broker = create_app()
print(f"check Socket manager initialized: {socket_manager is not None}")

# Export both app and socket_manager
__all__ = ['app', 'socketio', 'socket_manager', 'celery', 'dramatiq_broker', 'dramatiq_file_broker']