    
import uuid
import os
import threading
import asyncio
from werkzeug.utils import secure_filename
from services.docx_to_pdf_v2 import DocxToPdf
from services.document_seeder import DocumentSeeder
from models import File
from concurrent.futures import ThreadPoolExecutor
from flask import current_app

class Fileuploader:

    def __init__(self, dirtry, project_id, file_ids, file_names) -> None:
        self.file_dirtry = dirtry
        self.folder_path = os.path.join(dirtry , project_id)
        os.makedirs(self.folder_path, mode=0o775, exist_ok=True)
        self.file_ids = file_ids
        self.file_names = file_names
        self.all_success = True
        self.all_failed = True
        self.lock = threading.Lock()
        self.doc_to_pdf_processor = DocxToPdf()
        self.document_seeder_processor = DocumentSeeder()
        self.thread_pool = ThreadPoolExecutor(max_workers=5)

    def uploader(self):
        print('now starting background processing for file uploader...')
        # Start background processing
        self.background_processing()

    def background_processing(self):
        """Handle all background processing tasks"""
        with current_app.app_context():
            try:
                print('Converting idle docx to pdf before upload.....')
                # self.doc_to_pdf_processor.convert_idle_docx_files()

                print("Started background processing of files.")
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                async def process_all_files():
                    tasks = []
                    for idx, (file_id, file_name) in enumerate(zip(self.file_ids, self.file_names)):
                        tasks.append(self.process_single_file(file_id, file_name, idx, len(self.file_ids)))
                    await asyncio.gather(*tasks)

                loop.run_until_complete(process_all_files())
                loop.close()
                print("Background file processing complete.")
                
            except Exception as e:
                print(f"Error in background processing: {str(e)}")
            finally:
                self.thread_pool.shutdown(wait=False)

    async def process_single_file(self, file_id, file_name, index, total):
        """Process a single file and log progress"""
        with current_app.app_context():
            try:
                await self.document_seeder_processor.handle_single_document(file_id)
                print(f"File {index + 1} of {total} processed: {file_name}")
            except Exception as e:
                print(f"Error processing file {file_name}: {str(e)}")

    def __del__(self):
        """Cleanup method to ensure thread pool is shut down"""
        if hasattr(self, 'thread_pool'):
            self.thread_pool.shutdown(wait=False)