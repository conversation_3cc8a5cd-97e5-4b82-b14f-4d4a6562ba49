def extract_criteria_info(self, evaluate_summary_chunk):
    """
    Extract criteria information from evaluate_summary_chunk and categorize them.
    
    Args:
        evaluate_summary_chunk: List of HTML chunks containing criteria evaluations
    
    Returns:
        List of dictionaries with criteria information and categorization
    """
    criteria_info = []
    
    for chunk in evaluate_summary_chunk:
        if not chunk:
            continue
            
        # Parse HTML
        soup = BeautifulSoup(chunk, 'html.parser')
        
        # Extract main criteria name
        main_criteria_h3 = soup.find('h3')
        if not main_criteria_h3:
            continue
            
        main_criteria_text = main_criteria_h3.text.strip()
        criteria_name = None
        
        # Extract criteria name (format: "Criteria X: Name")
        criteria_match = re.search(r'Criteria \d+: (.+)', main_criteria_text)
        if criteria_match:
            criteria_name = criteria_match.group(1).strip()
        
        # Skip if no criteria name found
        if not criteria_name:
            continue
            
        # Check for error message (No Equipment Information)
        error_msg = soup.find('p', style=lambda s: s and 'color: #cc0000' in s)
        if error_msg:
            criteria_info.append({
                'criteria_name': criteria_name,
                'category': self._categorize_criteria(criteria_name),
                'has_error': True,
                'error_message': error_msg.text.strip(),
                'main_analysis': None,
                'sub_criteria': []
            })
            continue
        
        # Extract main criteria analysis
        main_analysis = None
        reason_element = soup.find('h5', {'class': lambda c: c and 'reason' in c})
        if reason_element:
            main_analysis = reason_element.text.strip()
        
        # Extract sub-criteria
        sub_criteria = []
        sub_criteria_h4s = soup.find_all('h4')
        
        for sub_h4 in sub_criteria_h4s:
            sub_name = sub_h4.text.strip()
            
            # Find the next ul element after this h4
            sub_ul = sub_h4.find_next('ul')
            if not sub_ul:
                continue
                
            # Extract sub-criteria analysis
            sub_analysis = None
            sub_reason = sub_ul.find('h6', {'class': lambda c: c and 'reason' in c})
            if sub_reason:
                sub_analysis = sub_reason.text.strip()
            
            sub_criteria.append({
                'sub_name': sub_name,
                'sub_analysis': sub_analysis,
                'category': self._categorize_criteria(sub_name)
            })
        
        criteria_info.append({
            'criteria_name': criteria_name,
            'category': self._categorize_criteria(criteria_name),
            'has_error': False,
            'main_analysis': main_analysis,
            'sub_criteria': sub_criteria
        })
    
    return criteria_info

def _categorize_criteria(self, criteria_name):
    """
    Categorize criteria based on name.
    
    Args:
        criteria_name: Name of the criteria
    
    Returns:
        Category string: "price", "delivery", or "other"
    """
    criteria_lower = criteria_name.lower()
    
    # Check if criteria contains price-related terms
    if any(term in criteria_lower for term in ['price', 'cost', 'financial', 'pricing', 'budget']):
        return "price"
    
    # Check if criteria contains delivery-related terms
    elif any(term in criteria_lower for term in ['delivery', 'schedule', 'timeline', 'time', 'duration']):
        return "delivery"
    
    # Default category
    return "other"

def process_criteria_by_category(self, criteria_info):
    """
    Process criteria based on their category.
    
    Args:
        criteria_info: List of criteria information dictionaries
    
    Returns:
        Processed analysis results
    """
    results = {
        'price_analysis': [],
        'delivery_analysis': [],
        'other_analysis': []
    }
    
    for criteria in criteria_info:
        category = criteria['category']
        
        # Skip criteria with errors
        if criteria['has_error']:
            continue
        
        if category == "price":
            # Special handling for price criteria
            price_result = self._process_price_criteria(criteria)
            results['price_analysis'].append(price_result)
            
        elif category == "delivery":
            # Special handling for delivery criteria
            delivery_result = self._process_delivery_criteria(criteria)
            results['delivery_analysis'].append(delivery_result)
            
        else:
            # Standard handling for other criteria
            other_result = self._process_standard_criteria(criteria)
            results['other_analysis'].append(other_result)
    
    return results

def _process_price_criteria(self, criteria):
    """Special processing for price criteria"""
    # Extract price-specific information using LLM
    prompt = f"""
    Extract key price information from the following analysis:
    
    Main Criteria: {criteria['criteria_name']}
    Analysis: {criteria['main_analysis']}
    
    Sub-criteria analyses:
    {self._format_sub_criteria(criteria['sub_criteria'])}
    
    Extract and summarize:
    1. Total price/cost figures mentioned
    2. Price competitiveness assessment
    3. Any price-related strengths or weaknesses
    4. Value for money assessment
    """
    
    # Call LLM to extract price information
    price_analysis = self._call_llm_for_extraction(prompt)
    
    return {
        'criteria_name': criteria['criteria_name'],
        'price_analysis': price_analysis
    }

def _process_delivery_criteria(self, criteria):
    """Special processing for delivery criteria"""
    # Extract delivery-specific information using LLM
    prompt = f"""
    Extract key delivery information from the following analysis:
    
    Main Criteria: {criteria['criteria_name']}
    Analysis: {criteria['main_analysis']}
    
    Sub-criteria analyses:
    {self._format_sub_criteria(criteria['sub_criteria'])}
    
    Extract and summarize:
    1. Delivery timeframes mentioned
    2. Schedule feasibility assessment
    3. Any delivery-related strengths or weaknesses
    4. Critical path considerations
    """
    
    # Call LLM to extract delivery information
    delivery_analysis = self._call_llm_for_extraction(prompt)
    
    return {
        'criteria_name': criteria['criteria_name'],
        'delivery_analysis': delivery_analysis
    }

def _process_standard_criteria(self, criteria):
    """Standard processing for other criteria"""
    # Extract general information using LLM
    prompt = f"""
    Extract key information from the following analysis:
    
    Main Criteria: {criteria['criteria_name']}
    Analysis: {criteria['main_analysis']}
    
    Sub-criteria analyses:
    {self._format_sub_criteria(criteria['sub_criteria'])}
    
    Extract and summarize:
    1. Main strengths identified
    2. Main weaknesses identified
    3. Overall assessment
    """
    
    # Call LLM for general extraction
    standard_analysis = self._call_llm_for_extraction(prompt)
    
    return {
        'criteria_name': criteria['criteria_name'],
        'standard_analysis': standard_analysis
    }

def _format_sub_criteria(self, sub_criteria):
    """Format sub-criteria for LLM prompt"""
    formatted = ""
    for sub in sub_criteria:
        formatted += f"- {sub['sub_name']}: {sub['sub_analysis']}\n\n"
    return formatted

def _call_llm_for_extraction(self, prompt):
    """Call LLM to extract information from analysis text"""
    # Implement your LLM call here
    # This is a placeholder - replace with actual LLM implementation
    try:
        response = self.llm_client.generate_text(prompt)
        return response
    except Exception as e:
        print(f"Error calling LLM: {e}")
        return "Error extracting information"