import os, json, random
import google.generativeai as genai
from pydantic import BaseModel
import instructor
from models import LLMUsage

class GeminiService:
    def __init__(self):
        """
        Initialize the GeminiService with API key and default settings.
        """
        self.parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.env_data = self._load_env()
        
        # Configure the Gemini API
        genai.configure(api_key=self.env_data.get("GEMINI_API_KEY", ""))
        
        # Map deployments to models
        self.deployments_map = {
            "Gemini 1.5 Pro": "gemini-1.5-pro",
            "Gemini 1.5 Flash": "gemini-1.5-flash",
            "Gemini 1.0 Pro": "gemini-1.0-pro"
        }
        
        self.model_ids = ["gemini-1.5-pro", "gemini-1.5-flash"]
        self.temperature = 0.01

    def _load_env(self):
        """Load environment variables from env.json file."""
        env_path = os.path.join(self.parent_dir, 'env.json')
        with open(env_path, 'r') as f:
            return json.load(f)
    
    def generate_completion(self, messages, temperature=0.01, max_tokens=1024, model=None, top_p=1.0, stream=False, stop=None):
        """
        Generate a completion using Gemini models.
        
        :param messages: List of message dictionaries with 'role' and 'content'
        :param temperature: Sampling temperature
        :param max_tokens: Maximum tokens to generate
        :param model: Specific model to use
        :param top_p: Top-p sampling parameter
        :param stream: Whether to stream the response
        :param stop: Stop sequences
        :return: Generated text or None if all models fail
        """
        models_to_try = [model] if model else self.model_ids
        
        for model_id in models_to_try:
            try:
                # Convert messages format from OpenAI to Gemini
                gemini_messages = []
                for msg in messages:
                    role = "user" if msg["role"] == "user" else "model"
                    gemini_messages.append({"role": role, "parts": [msg["content"]]})
                
                # Initialize the model
                model = genai.GenerativeModel(model_id)
                
                # Generate response
                if stream:
                    response = ""
                    for chunk in model.generate_content(
                        gemini_messages,
                        generation_config={
                            "temperature": temperature,
                            "max_output_tokens": max_tokens,
                            "top_p": top_p
                        },
                        stream=True
                    ):
                        response += chunk.text
                    return response
                else:
                    response = model.generate_content(
                        gemini_messages,
                        generation_config={
                            "temperature": temperature,
                            "max_output_tokens": max_tokens,
                            "top_p": top_p
                        }
                    )
                    
                    # Record usage (Note: Gemini might not provide token counts in the same way)
                    self._record_usage(model_id, response)
                    
                    return response.text
                    
            except Exception as e:
                print(f"Error with model {model_id}: {e}")
        
        return None
    
    def extract_data(self, schema: BaseModel, messages, model=None):
        """
        Extract structured data using Gemini with the specified schema and prompts.
        
        :param schema: Pydantic model specifying the response schema
        :param messages: List of message dictionaries
        :param model: Specific model to use
        :return: An instance of the schema populated with extracted data
        """
        try:
            # Currently, instructor doesn't directly support Gemini
            # We'll need to implement a custom approach or wait for instructor support
            # For now, we'll generate text and then parse it into the schema
            
            # Generate text response
            text_response = self.generate_completion(messages, model=model)
            
            if text_response:
                # Try to parse the response as JSON
                try:
                    import json
                    json_data = json.loads(text_response)
                    # Create an instance of the schema with the parsed data
                    return schema.model_validate(json_data)
                except Exception as e:
                    print(f"Error parsing Gemini response as JSON: {e}")
            
            return None
            
        except Exception as e:
            print(f"Error extracting data with Gemini: {e}")
            return None
    
    def _record_usage(self, model_id, response):
        """Helper method to record Gemini API usage"""
        try:
            # Note: Gemini might not provide token counts in the same way as OpenAI/Claude
            # You might need to estimate tokens or track differently
            input_tokens = 0
            output_tokens = 0
            
            # If response has usage information, use it
            if hasattr(response, 'usage'):
                input_tokens = getattr(response.usage, 'prompt_tokens', 0)
                output_tokens = getattr(response.usage, 'completion_tokens', 0)
            
            LLMUsage.create(
                provider="gemini",
                model=model_id,
                input_tokens=input_tokens,
                output_tokens=output_tokens
            )
        except Exception as e:
            print(f"Error recording Gemini usage: {e}")