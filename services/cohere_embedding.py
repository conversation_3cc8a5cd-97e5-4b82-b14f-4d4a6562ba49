import cohere
import numpy as np
import os, json
import time
import requests
from typing import Dict, List, Optional
from datetime import datetime


class CohereService:
    def __init__(self):
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # Load environment variables from env.json file in the parent directory
        env_file_path = os.path.join(parent_dir, 'env.json')

        
        # Load environment variables from env.json file
        with open(env_file_path, 'r') as f:
            env_data = json.load(f)

        self.cohere_client = cohere.Client(api_key=env_data.get('COHERE_API_KEY', ''))
        self.cohere_client2 = cohere.ClientV2(api_key=env_data.get('COHERE_API_KEY', ''))
        
        self.models = ["embed-v4.0", "embed-multilingual-v3.0"]
        
        self.status_checker = CohereStatusChecker()

    def get_model(self, model_name):
        status_results = self.status_checker.check_specific_models(self.models)
        
        # If provided model is operational, return it
        if model_name in status_results and status_results.get(model_name, {}).get("operational"):
            return model_name
            
        # Find first operational model in the list
        for model in self.models:
            if status_results.get(model, {}).get("operational"):
                return model
                
        # If no operational models found, return the first model
        return self.models[0]

    def embed_texts(self, texts, input_type, model="embed-multilingual-v3.0"):
        model = self.get_model(model)
        response = self.cohere_client2.embed(
            texts=texts,
            input_type=input_type,
            model=model,
            dimension=1024,
            embedding_types=["float"],
        )
        
        embeddings = response.embeddings.float
        return np.asarray(embeddings)
    
    
    def embed_texts_as_list(self, texts, input_type, model="embed-multilingual-v3.0"):
        model = self.get_model(model)
        for attempt in range(3):
            try:
                response = self.cohere_client2.embed(
                    texts=texts,
                    input_type=input_type,
                    model=model,
                    output_dimension=1024,
                    embedding_types=["float"],
                )
                embeddings = response.embeddings.float
                print('cohere embeddings usage: ', response.meta)
                return embeddings
            except Exception as e:
                print(f"Attempt {attempt + 1} failed: {e}")
                time.sleep(2)
        else:
            raise RuntimeError("Cohere API failed after 3 retries.")

    def search_similar(self, query, chunked_text, num_of_result = 4):
        
        # Embed the documents (chunked text) with input type 'search_document'
        doc_embeddings = self.embed_texts(chunked_text, input_type="search_document")

        # Embed the query with input type 'search_query'
        query_embedding = self.embed_texts([query], input_type="search_query")[0]

        # Compute the dot product between query embedding and document embeddings
        scores = np.dot(query_embedding, doc_embeddings.T)

        # Find the highest scores
        max_idx = np.argsort(-scores)

        num_indices = min(len(chunked_text), num_of_result)
        # best_texts = [chunked_text[max_idx[i]] for i in range(num_indices)]
        # Retrieve the top results and their scores
        best_results = [
            {"text": chunked_text[max_idx[i]], "score": scores[max_idx[i]]}
            for i in range(num_indices)
        ]

        merged_texts_with_scores = []
        for i in range(0, len(best_results), 2):
            merged_text = " ".join([r["text"] for r in best_results[i:i+2]])
            avg_score = sum([r["score"] for r in best_results[i:i+2]]) / len(best_results[i:i+2])
            merged_texts_with_scores.append({"text": merged_text, "score": avg_score})
            
        return merged_texts_with_scores
    
    def rerank_texts(self, query, documents, num_of_result = 4):
        response = self.cohere_client2.rerank(
            model="rerank-v3.5",
            query=query,
            documents=documents,
            top_n=num_of_result,
            return_documents=True
        )
        print('rerank usage: ', response.meta)
        reranked_results = []
        for result in response.results:
            document_dict = dict(result.document)
            reranked_results.append(document_dict)
            
        return reranked_results




class CohereStatusChecker:
    def __init__(self):
        self.status_url = "https://status.cohere.com"
        self.components_endpoint = f"{self.status_url}/api/v2/components.json"
        self.status_endpoint = f"{self.status_url}/api/v2/status.json"
        
    def get_components_data(self) -> Optional[Dict]:
        """Fetch the components data from Cohere's status API."""
        try:
            response = requests.get(self.components_endpoint, timeout=10)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error fetching components data: {e}")
            return None
    
    def get_status_data(self) -> Optional[Dict]:
        """Fetch the overall status data from Cohere's status API."""
        try:
            response = requests.get(self.status_endpoint, timeout=10)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error fetching status data: {e}")
            return None
    
    def find_embed_models_status(self) -> Dict[str, str]:
        """Find the status of embed-v4.0 and embed-multilingual-v3.0 models."""
        components_data = self.get_components_data()
        status_results = {
            "embed-v4.0": "unknown",
            "embed-multilingual-v3.0": "unknown",
            "overall_status": "unknown"
        }
        
        if not components_data:
            return status_results
        
        # Get overall status
        status_data = self.get_status_data()
        if status_data:
            status_results["overall_status"] = status_data.get("status", {}).get("indicator", "unknown")
        
        # Search through components for embed models
        components = components_data.get("components", [])
        
        for component in components:
            name = component.get("name", "")
            status = component.get("status", "unknown")
            
            # Check for embed-v4.0 (exact match)
            if name == "embed-v4.0":
                status_results["embed-v4.0"] = status
                
            # Check for embed-multilingual-v3.0 (exact match)
            elif name == "embed-multilingual-v3.0":
                status_results["embed-multilingual-v3.0"] = status
        
        return status_results
    
    def is_model_operational(self, model_name: str) -> bool:
        """Check if a specific model is operational."""
        status_results = self.find_embed_models_status()
        model_status = status_results.get(model_name, "unknown")
        return model_status.lower() == "operational"
    
    def check_all_models(self) -> Dict[str, bool]:
        """Check operational status of both embed models."""
        return {
            "embed-v4.0": self.is_model_operational("embed-v4.0"),
            "embed-multilingual-v3.0": self.is_model_operational("embed-multilingual-v3.0")
        }
    
    def get_detailed_status(self) -> Dict:
        """Get detailed status information including timestamps and component details."""
        status_results = self.find_embed_models_status()
        components_data = self.get_components_data()
        
        detailed_info = {
            "timestamp": datetime.now().isoformat(),
            "models": status_results,
            "operational": {},
            "component_details": {}
        }
        
        # Add operational status
        for model in ["embed-v4.0", "embed-multilingual-v3.0"]:
            detailed_info["operational"][model] = self.is_model_operational(model)
        
        # Add component details if available
        if components_data:
            components = components_data.get("components", [])
            for component in components:
                name = component.get("name", "")
                if name in ["embed-v4.0", "embed-multilingual-v3.0"]:
                    detailed_info["component_details"][name] = {
                        "id": component.get("id"),
                        "status": component.get("status"),
                        "description": component.get("description", ""),
                        "created_at": component.get("created_at"),
                        "updated_at": component.get("updated_at"),
                        "position": component.get("position")
                    }
        
        return detailed_info
    
    def get_all_embed_models(self) -> List[Dict]:
        """Get status of all embedding models available."""
        components_data = self.get_components_data()
        embed_models = []
        
        if not components_data:
            return embed_models
        
        components = components_data.get("components", [])
        
        for component in components:
            name = component.get("name", "")
            if name.startswith("embed-"):
                embed_models.append({
                    "name": name,
                    "status": component.get("status", "unknown"),
                    "description": component.get("description", ""),
                    "operational": component.get("status", "").lower() == "operational",
                    "updated_at": component.get("updated_at"),
                    "id": component.get("id")
                })
        
        # Sort by position or name
        embed_models.sort(key=lambda x: x["name"])
        return embed_models
    
    def monitor_status(self, interval_seconds: int = 300, max_checks: int = None):
        """Continuously monitor the status of embed models."""
        check_count = 0
        print(f"Starting monitoring (checking every {interval_seconds} seconds)...")
        print("-" * 60)
        
        try:
            while True:
                if max_checks and check_count >= max_checks:
                    break
                    
                status_info = self.get_detailed_status()
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                
                print(f"[{timestamp}] Status Check #{check_count + 1}")
                print(f"Overall Status: {status_info['models']['overall_status']}")
                
                for model, is_operational in status_info['operational'].items():
                    status_text = "✅ OPERATIONAL" if is_operational else "❌ NOT OPERATIONAL"
                    raw_status = status_info['models'][model]
                    
                    # Add last updated info if available
                    updated_info = ""
                    if model in status_info['component_details']:
                        updated_at = status_info['component_details'][model]['updated_at']
                        if updated_at:
                            updated_info = f" (last updated: {updated_at})"
                    
                    print(f"{model}: {status_text} (status: {raw_status}){updated_info}")
                
                print("-" * 60)
                
                check_count += 1
                if max_checks is None or check_count < max_checks:
                    time.sleep(interval_seconds)
                    
        except KeyboardInterrupt:
            print("\nMonitoring stopped by user.")
    
    def check_specific_models(self, model_names: List[str]) -> Dict[str, Dict]:
        """Check status of specific models by name."""
        components_data = self.get_components_data()
        results = {}
        
        if not components_data:
            return {name: {"status": "unknown", "error": "Could not fetch data"} for name in model_names}
        
        components = components_data.get("components", [])
        
        for model_name in model_names:
            found = False
            for component in components:
                if component.get("name", "") == model_name:
                    results[model_name] = {
                        "status": component.get("status", "unknown"),
                        "operational": component.get("status", "").lower() == "operational",
                        "description": component.get("description", ""),
                        "updated_at": component.get("updated_at"),
                        "id": component.get("id")
                    }
                    found = True
                    break
            
            if not found:
                results[model_name] = {
                    "status": "not_found",
                    "operational": False,
                    "error": f"Model '{model_name}' not found in components"
                }
        
        return results
    

    
# if __name__ == "__main__":
#     cohere_service = CohereService()
#     model = cohere_service.get_model("embed-multilingual-v3.0")
#     print('model: ', model)
    # res = cohere_service.embed_texts_as_list(["Hello, world!", "xyz"], input_type="search_query")
    # print(len(res[0]))