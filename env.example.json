{"CLIENT_NAME": "", "PINECONE_API_KEY": "a0c04247-817d-47b4-9cd9-4a289a7594dc", "WORKSPACE_DIR": "ds_oil_and_gas", "CHRONOBID_DIR": "chronobid", "DATA_DIR": "data", "SPECS_DIR": "specs", "ENGR_DOCS": "scp_engr_documents.json", "HUGGINGFACE_TOKEN": "", "AZURE_ENDPOINT": "https://aienergy.openai.azure.com", "AZURE_API_KEY": "********************************", "OPENAI_API_KEY": "", "COHERE_TRIAL_API_KEY": "", "COHERE_API_KEY": "", "CLAUDE_API_KEY": "", "CLAUDE_API_KEY_2": "", "MYSQL_USER": "ds_oil_and_gas_local", "MYSQL_PASSWORD": "", "MYSQL_HOST": "", "MYSQL_PORT": "3306", "MYSQL_DATABASE": "ds_oil_and_gas_dev", "MYSQL_PORT_LOCAL": "3306", "MYSQL_DATABASE_LOCAL": "ds_oil_and_gas_dev", "MYSQL_USER_LOCAL": "root", "MYSQL_PASSWORD_LOCAL": "0123596793", "MYSQL_HOST_LOCAL": "localhost", "BACKEND_BASE_URL": "https://backend.aienergy-oilandgas.com", "BACKEND_PROJECT_ID": "YWllbmVyZ3k6aHNxdHJ0cDdoc3dpamp6dWU3c2N3emV4ZDhodTRh"}