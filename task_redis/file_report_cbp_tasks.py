# file_upload_tasks.py
from services.handle_cbp_report import GenerateReportCBP
from init import app, socketio, socket_manager

# Redis connection
def get_cbp_report_task(request_id):
    try:
        with app.app_context():
            print('now initiating the cbp report class...')
            report_generator = GenerateReportCBP(request_id, 0)
            return report_generator._generate_report()
    except Exception as e:
        print(f"Error during file upload: {e}")
        return str(e)
