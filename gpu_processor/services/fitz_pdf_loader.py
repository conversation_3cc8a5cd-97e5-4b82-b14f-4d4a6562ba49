import fitz
from concurrent.futures import ThreadPoolExecutor

def process_page(page):
    """
    Extract text from a single page.
    """
    return page.get_text()

def load_pdf_parallel(file_path):
    """
    Load a PDF file and extract text from all pages in parallel.
    Returns an array where each entry corresponds to the text content of a page.
    """
    with fitz.open(file_path) as pdf:
        with ThreadPoolExecutor() as executor:
            results = list(executor.map(process_page, pdf))  # Convert map object to a list
        return results

# Example Usage
if __name__ == "__main__":
    file_path = "large_file.pdf"  # Replace with your PDF file path
    page_texts = load_pdf_parallel(file_path)
    print(f"Extracted text from {len(page_texts)} pages.")
