import gevent
from gevent import monkey
monkey.patch_all()
import json
import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor

from cbp.services.embeddings.vectore_database_service import VectorDatabaseService
from cbp.pipelines.bid_evaluation.technical_bid.utils import *
from cbp.services.llm.claude import AnthropicService
from cbp.services.llm.claude import AnthropicService
from models import CbpRequirementTechnicalReport
from services.faiss_embedding import FaissEmbedding

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Vector Search Service Layer
class VectorSearchLayer:
    def __init__(self):
        self.vector_service = FaissEmbedding()
    
    async def search(self, query: str, request_id: str, num_results: int = 50) -> Dict[str, Any]:
        """Execute vector search for a query."""
        try:
            results = await self.vector_service._execute_gpu_search(
                search_queries=[query], 
                request_id=request_id, 
                num_results=num_results
            )
            return {"success": True, "results": results}
        except Exception as e:
            logger.error(f"Vector search failed: {str(e)}")
            return {"success": False, "results": ""}
        
    def filter_top_k_sources_by_median(self,sources, k=5, median_threshold=0.9):
        import statistics
        """
        Filters sources based on 90% of the median score and returns top-k by score.

        Args:
            sources (list): List of dicts with a 'score' key.
            k (int): Max number of sources to return.

        Returns:
            list: Top-k filtered and sorted sources.
        """
        valid_scores = [s.get('score', 0) for s in sources if s.get('score') is not None]

        if not valid_scores:
            return []

        median_score = statistics.median(valid_scores)
        threshold = median_threshold * median_score

        filtered = [s for s in sources if s.get('score', 0) >= threshold]

        return sorted(filtered, key=lambda s: s.get('score', 0), reverse=True)[:k]
    
    def search_sync(self, query: str, request_id: str, num_results: int = 20) -> Dict[str, Any]:
        """Execute vector search for a query."""
        try:
            sources = self.vector_service.search_sync(
                query=[query], 
                request_id=request_id, 
                k=num_results
            )

            final_results = [{
                        "source_map": {},
                        "source_list": [],
                        "questions": [query]
                    }]
            sources_list = []
            source_map = {}
            for i, source in enumerate(sources[0]):
                source_map[i] = source[0]
                sources_list.append({
                    "id": i,
                    "content": source[0],
                    "score": source[1],
                    "section_id": i,
                    "source": source[2],
                    "page_number": source[3]
                })
            final_results[0]['source_map'] = source_map
            final_results[0]['source_list'] = sources_list
            sources = final_results
            filtered = self.filter_top_k_sources_by_median(sources[0]['source_list'], k=30, median_threshold=0.65)

            print(f"LENGHT OF FILTERED CHUNKS: {len(filtered)}")
            return {"success": True, "results": filtered}
        except Exception as e:
            logger.error(f"Vector search failed: {str(e)}")
            return {"success": False, "results": ""}
        
from services.claude_ai_service import ClaudeService
# AI Processing Service Layer
class AIProcessingLayer:
    def __init__(self):
        self.llm_service = ClaudeService(claude_keys=['CLAUDE_API_CBP', 'CLAUDE_API_CBP'],default_key="CLAUDE_API_CBP")
    
    async def process_queries(self, document: str, formatted_queries: str) -> Dict[str, Any]:
        """Process queries using the AI service."""
        prompt = self._construct_prompt(document, formatted_queries)
        
        messages = [{"role": "user", "content": prompt}]
        
        success = True
        try:
            response = self.llm_service.generate_message_agent_sonnet_new_sync(
                    messages=messages,
                    temperature=0.00001,
                    max_tokens=4096

                )
            response = response.content[0].text
        
        except:
          success = False
        
        if not success:
            logger.error("Failed to generate response from Claude")
            return {"success": False}
        response = json.loads(response)
        print(f"THIS IS LLM DATA :{response}")
        return {"success": True, "results": response}
    

    def process_queries_sync(self, document: str, formatted_queries: str) -> Dict[str, Any]:
        """Process queries using the AI service."""
        prompt = self._construct_prompt(document, formatted_queries)
        
        messages = [{"role": "user", "content": prompt}]
        success = True
        try:
            response = self.llm_service.generate_message_agent_sonnet_new_sync(
                    messages=messages,
                    temperature=0.00001,
                    max_tokens=4096

                )
            response = response.content[0].text
            print(f"Quesry processin RESULT: {response}")
        except:
          success = False
        
        if not success:
            logger.error("Failed to generate response from Claude")
            return {"success": False}
        response = json.loads(response)
        return {"success": True, "results": response}
    
    def _construct_prompt(self, document: str, formatted_queries: str) -> str:
        """Construct the prompt for the AI service."""
        return f"""Here is a document to analyze:

DOCUMENT:
{document}

Please answer the following queries about this document:

QUERIES:
{formatted_queries}

INSTRUCTIONS:
Your task is to extract precise information from the provided document based on the queries. Please follow these guidelines carefully:

1. For each query:
   - Extract EXACT matches from the document text
   - Do not paraphrase or interpret - use only verbatim text
   - If information is not found, respond with 'N/A'
   - Preserve the original query text exactly as provided

2. Response Format:
   Provide your response in JSON format with this structure:
   {{
     "queries": [
       {{
         "query": "<original query with tags>",
         "answer": "<exact match from document or N/A>"
       }},
       ...
     ]
   }}
NOTE: DO NOT PUT ```json before or after the json output. and no explanation before or after the json output.

3. Query structure explanation:
   Each query contains XML-style tags that define the search parameters:
   - <eq> : Equipment name (e.g., "BALL MILL")
   - <cat> : Category within equipment (e.g., "GENERAL")
   - <item> : Specific attribute to find
   - <co> : Additional context like units

4. Search approach:
   - First locate the equipment section
   - Then find the category subsection
   - Finally extract the specific item value
   - Consider units and context from <co> tag
   - Only return information that exactly matches these parameters

CRITICAL: The document is the sole source of truth. Do not infer, calculate or derive information not explicitly stated. This is extremely important for maintaining data accuracy and integrity.

Remember to format your entire response as a valid JSON object. NOTHING BEFORE OR AFTER, VALID JSON please

CRITICAL: RETURN A VALID JSON RESPONSE
"""

# Query Processing Service Layer
class TechnicalBidProcessor:
    def __init__(self, socket_manager=None, requirement_id=None):
        self.socket_manager = socket_manager
        self.requirement_id = requirement_id
        self.vector_search_service = VectorSearchLayer()
        self.ai_processing_service = AIProcessingLayer()
        self.event_name = "CBP_REPORT"
        self.test = True
    
    def add_event(self, requirement_id, event_name, data):
        """Emit socket event to client for progress tracking"""
        print(f"Adding event {event_name} to room {requirement_id}")
        if self.socket_manager:
            self.socket_manager.emit_to_client(requirement_id, event_name, data)

    def format_queries(self, queries_with_tags: List[str]) -> str:
        """Format queries with numbering."""
        return "\n".join([f"{i+1}. {query}" for i, query in enumerate(queries_with_tags)])
    
    async def process_batch(self, queries_with_tags: List[str], queries_without_tags: List[str], request_id: str) -> Dict[str, Any]:
        """Process a batch of queries."""
        # Join the queries without tags
        queries_str = ", ".join(queries_without_tags)
        logger.info(f"Processing batch with {len(queries_with_tags)}")
        
        # Execute search
        search_response = await self.vector_search_service.search(queries_str, request_id)
        if not search_response["success"]:
            return {"success": False, "batch_results": []}
        
        # Format queries for AI processing
        formatted_queries = self.format_queries(queries_with_tags)
        
        # Process with AI
        ai_response = await self.ai_processing_service.process_queries(
            search_response["results"], 
            formatted_queries
        )
        
        if not ai_response["success"]:
            return {"success": False, "batch_results": []}
        
        return {"success": True, "batch_results": ai_response["results"]}
    

    def process_batch_sync(self, queries_with_tags: List[str], queries_without_tags: List[str], request_id: str) -> Dict[str, Any]:
        """Process a batch of queries."""
        # Join the queries without tags
        queries_str = ", ".join(queries_without_tags)
        logger.info(f"Processing batch with {len(queries_with_tags)}")
        
        logger.info(f"QUERY STRING :{queries_str}")
        # Execute search
        search_response = self.vector_search_service.search_sync(queries_str, request_id)

        print(f"Search response: {search_response}")
        if not search_response["success"]:
            return {"success": False, "batch_results": []}
        
        # Format queries for AI processing
        formatted_queries = self.format_queries(queries_with_tags)
        
        # Process with AI
        ai_response = self.ai_processing_service.process_queries_sync(
            search_response["results"], 
            formatted_queries
        )
        if not ai_response["success"]:
            return {"success": False, "batch_results": []}
        
        return {"success": True, "batch_results": ai_response["results"]}
    
    async def query_processor(self, all_queries_with_tags: List[str], all_queries_without_tags: List[str], 
                                request_id: str, batch_size: int = 50) -> Dict[str, Any]:
        """Process all queries in batches with parallel execution."""
        # Calculate the number of batches
        total_queries = len(all_queries_with_tags)
        num_batches = (total_queries + batch_size - 1) // batch_size  # Ceiling division
        
        logger.info(f"Processing {total_queries} queries in {num_batches} batches")
        
        # Send progress update via socket
        if self.socket_manager and self.requirement_id:
            self.add_event(self.requirement_id, 'progress_message', {
                'event_type': self.event_name,
                'request_id': self.requirement_id,
                'data': {
                    'status': 'processing_queries', 
                    'message': f"Processing {total_queries} queries in {num_batches} batches"
                }
            })
        
        # Create batches
        batch_tasks = []
        for i in range(num_batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, total_queries)
            
            batch_with_tags = all_queries_with_tags[start_idx:end_idx]
            batch_without_tags = all_queries_without_tags[start_idx:end_idx]
            
            batch_tasks.append(self.process_batch(batch_with_tags, batch_without_tags, request_id))
        
        # Execute all batches in parallel
        batch_results = await asyncio.gather(*batch_tasks)
        
        # Aggregate results
        all_results = []
        for batch_result in batch_results:
            if batch_result["success"]:
                all_results.extend(batch_result["batch_results"]["queries"])
        
        # Return aggregated results
        return {
            "success": True,
            "aggregated_results": all_results
        }
    
    def query_processor_sync(self, all_queries_with_tags: List[str], all_queries_without_tags: List[str], 
                                request_id: str, batch_size: int = 50) -> Dict[str, Any]:
        """Process all queries in batches with parallel execution."""
        # Calculate the number of batches
        total_queries = len(all_queries_with_tags)
        num_batches = (total_queries + batch_size - 1) // batch_size  # Ceiling division
        
        logger.info(f"Processing {total_queries} queries in {num_batches} batches")
        
        # Send progress update via socket
        if self.socket_manager and self.requirement_id:
            self.add_event(self.requirement_id, 'progress_message', {
                'event_type': self.event_name,
                'request_id': self.requirement_id,
                'data': {
                    'status': 'processing_queries', 
                    'message': f"Processing {total_queries} queries in {num_batches} batches"
                }
            })
        
        # Create batches
        batch_tasks = []
        for i in range(num_batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, total_queries)
            
            batch_with_tags = all_queries_with_tags[start_idx:end_idx]
            batch_without_tags = all_queries_without_tags[start_idx:end_idx]
            
            batch_tasks.append(gevent.spawn(self.process_batch_sync, batch_with_tags, batch_without_tags, request_id))
        
        # Execute all batches in parallel
        batch_results = gevent.joinall(batch_tasks)
        batch_results = [task.value for task in batch_tasks]
        
        # Aggregate results
        all_results = []
        for batch_result in batch_results:
            if batch_result["success"]:
                all_results.extend(batch_result["batch_results"]["queries"])
        
        # Return aggregated results
        return {
            "success": True,
            "aggregated_results": all_results
        }

    # Main Application Layer
   
    async def process_multiple_bids(self, equipment_list: list, bid_ids: list):
        """
        Process multiple equipment across multiple bid IDs
        
        Args:
            requirement_id: ID of the requirement being processed
            equipment_list: List of equipment dictionaries
            bid_ids: List of bid IDs to process for each equipment
            
        Returns:
            Dictionary with bid IDs as keys and their respective equipment results as values
        """
        try:
            requirement_id = self.requirement_id
            all_bid_results = {}
            
            # Update requirement status to pending
            if not self.test:
              CbpRequirementTechnicalReport.update(requirement_id, status='pending')
            
            # Notify client that processing has started
            self.add_event(requirement_id, 'progress_message', {
                'event_type': self.event_name,
                'request_id': requirement_id,
                'data': {'status': 'processing_started', 'message': f"Starting technical evaluation of {len(bid_ids)} bids"}
            })
            
            for i, bid_id in enumerate(bid_ids):
                logger.info(f"Processing bid ID: {bid_id} ({i+1}/{len(bid_ids)})")
                
                # Update socket with current bid progress
                self.add_event(requirement_id, 'progress_message', {
                    'event_type': self.event_name,
                    'request_id': requirement_id,
                    'data': {'status': 'processing_bid', 'message': f"Analyzing bid {i+1} of {len(bid_ids)}"}
                })
                
                # Process all equipment for this bid ID
                bid_results = await self.process_multi_equipment(equipment_list, bid_id)
                
                # Store results for this bid ID
                all_bid_results[bid_id] = bid_results
                
                logger.info(f"Completed processing for bid ID: {bid_id}")
            
            # All processing completed, update requirement to completed
            if not self.test:
                CbpRequirementTechnicalReport.update(requirement_id, status='done', result=all_bid_results)
            
            # Send final completion event
            
            '''output_file_path = f"bid_results_{requirement_id}.json"
            with open(output_file_path, 'w') as json_file:
                json.dump(all_bid_results, json_file, indent=4)'''

            self.add_event(requirement_id, 'progress_message', {
                'event_type': self.event_name,
                'request_id': requirement_id,
                'data': {
                    'status': 'completed', 
                    'message': f"Technical evaluation completed successfully"
                }
            })
            
            return all_bid_results
            
        except Exception as e:
            logger.error(f"Error processing multiple bids: {str(e)}")
            # Update requirement as failed
            if not self.test:
               CbpRequirementTechnicalReport.update(requirement_id, status='idle', error=str(e))
            
            # Notify client of failure
            self.add_event(requirement_id, 'progress_message', {
                'event_type': self.event_name,
                'request_id': requirement_id,
                'data': {'status': 'failed', 'message': f"Processing failed: {str(e)}"}
            })
            
            raise e  # Re-raise the exception
        
    def process_multiple_bids_sync(self, equipment_list: list, bid_ids: list, bidder_names: list = None):
        """
        Process multiple equipment across multiple bid IDs
        
        Args:
            requirement_id: ID of the requirement being processed
            equipment_list: List of equipment dictionaries
            bid_ids: List of bid IDs to process for each equipment
            bidder_names: List of dictionaries containing bidder_name and bid_id
            
        Returns:
            Dictionary with bid IDs as keys and their respective equipment results as values
        """
        try:
            requirement_id = self.requirement_id
            all_bid_results = {}
            
            # Create bid_id to bidder_name mapping
            bidder_name_map = {}
            if bidder_names:
                bidder_name_map = {bid['bid_id']: bid['bidder_name'] for bid in bidder_names}
            
            # Update requirement status to pending
            if not self.test:
               CbpRequirementTechnicalReport.update(requirement_id, status='pending')
            

            # Notify client that processing has started
            self.add_event(requirement_id, 'progress_message', {
                'event_type': self.event_name,
                'request_id': requirement_id,
                'data': {'status': 'processing_started', 'message': f"Starting technical evaluation of {len(bid_ids)} bids"}
            })
            
            for i, bid_id in enumerate(bid_ids):
                logger.info(f"Processing bid ID: {bid_id} ({i+1}/{len(bid_ids)})")
                
                # Update socket with current bid progress
                self.add_event(requirement_id, 'progress_message', {
                    'event_type': self.event_name,
                    'request_id': requirement_id,
                    'data': {'status': 'processing_bid', 'message': f"Analyzing bid {i+1} of {len(bid_ids)}"}
                })
                
                # Process all equipment for this bid ID
                bid_results = self.process_multi_equipment_sync(equipment_list, bid_id)
                
                # Add bidder name to results if available
                if bid_id in bidder_name_map:
                    for result in bid_results:
                        result['bidder_name'] = bidder_name_map[bid_id]
                
                # Store results for this bid ID
                all_bid_results[bid_id] = bid_results
                
                logger.info(f"Completed processing for bid ID: {bid_id}")
            
            # All processing completed, update requirement to completed
            if not self.test:
              CbpRequirementTechnicalReport.update(requirement_id, status='done', result=all_bid_results)
            
            # Send final completion event
            
            '''output_file_path = f"bid_results_{requirement_id}.json"
            with open(output_file_path, 'w') as json_file:
                json.dump(all_bid_results, json_file, indent=4)'''

            self.add_event(requirement_id, 'progress_message', {
                'event_type': self.event_name,
                'request_id': requirement_id,
                'data': {
                    'status': 'completed', 
                    'message': f"Technical evaluation completed successfully"
                }
            })
            
            return all_bid_results
            
        except Exception as e:
            logger.error(f"Error processing multiple bids: {str(e)}")
            # Update requirement as failed
            if not self.test:
               CbpRequirementTechnicalReport.update(requirement_id, status='idle', error=str(e))
            
            # Notify client of failure
            self.add_event(requirement_id, 'progress_message', {
                'event_type': self.event_name,
                'request_id': requirement_id,
                'data': {'status': 'failed', 'message': f"Processing failed: {str(e)}"}
            })
            
            raise e  # Re-raise the exception

    async def process_multi_equipment(self, equipment_list: list, bid_id: str):
        """
        Process multiple equipment for a single bid ID
        
        Args:
            equipment_list: List of equipment dictionaries
            bid_id: Bid ID to process
            
        Returns:
            List of results for each equipment
        """
        final_result_all = []
        
        # Notify beginning of source extraction
        self.add_event(self.requirement_id, 'progress_message', {
            'event_type': self.event_name,
            'request_id': self.requirement_id,
            'data': {'status': 'extracting_sources', 'message': f"Extracting sources for bid {bid_id}"}
        })
        
        total_equipment = len(equipment_list)
        for idx, equipment in enumerate(equipment_list):
            try:
                equipment_name = equipment.get("equipment_name", "Unknown")
                logger.info(f"Processing equipment {idx+1}/{total_equipment}: {equipment_name}")
                
                # Update socket with current equipment progress
                self.add_event(self.requirement_id, 'progress_message', {
                    'event_type': self.event_name,
                    'request_id': self.requirement_id,
                    'data': {
                        'status': 'processing_equipment', 
                        'message': f"Evaluating {equipment_name} specifications"
                    }
                })
                
                # Initialize status as failed by default
                equipment_result = {
                    "equipment": equipment_name,
                    "status": "failed",
                    "data": None,
                    "bid_id": bid_id  # Include bid ID in results for traceability
                }

                # Construct queries
                queries_with_tags, queries_without_tags = construct_queries(equipment)
                
                # Process all queries - first pass
                first_result = await self.query_processor(
                    queries_with_tags, 
                    queries_without_tags, 
                    request_id=bid_id,  # Using bid_id as the request_id parameter
                    batch_size=50
                )
                
                if not first_result["success"]:
                    logger.error(f"Failed to process queries in the first pass for equipment: {equipment_name} with bid ID: {bid_id}")
                    final_result_all.append(equipment_result)
                    continue
                    
                # Convert to list of dictionaries
                first_pass_results = first_result["aggregated_results"]
                first_pass_results_non_na = [entry for entry in first_pass_results if entry.get("answer") != "N/A"]
                
                # Reconstruct the results with answers
                result_reconstructed_initial = reconstruct_samples(first_pass_results, add_answer=True)
                result_reconstructed_initial_non_na = reconstruct_samples(first_pass_results_non_na, add_answer=True)
                
                # Filter for N/A answers
                na_queries = [entry for entry in first_pass_results if entry.get("answer") == "N/A"]
                
                # If we have N/A queries, do a second pass
                if na_queries:
                    logger.info(f"Found {len(na_queries)} queries with N/A answers for bid ID {bid_id}. Running second pass.")
                    
                    # Update socket for second pass
                    self.add_event(self.requirement_id, 'progress_message', {
                        'event_type': self.event_name,
                        'request_id': self.requirement_id,
                        'data': {
                            'status': 'second_pass', 
                            'message': f"Performing detailed analysis of remaining specifications"
                        }
                    })
                    
                    # Reconstruct the N/A queries without answers
                    na_queries_reconstructed = reconstruct_samples(na_queries, add_answer=False)
                    
                    # Construct queries for second pass
                    na_queries_with_tags, na_queries_without_tags = construct_queries(na_queries_reconstructed)
                    
                    # Process N/A queries in second pass
                    second_result = await self.query_processor(
                        na_queries_with_tags, 
                        na_queries_without_tags, 
                        request_id=bid_id,  # Using bid_id as the request_id parameter
                        batch_size=50
                    )
                    
                    if not second_result["success"]:
                        logger.error(f"Failed to process queries in the second pass for equipment: {equipment_name} with bid ID: {bid_id}")
                        # Still include first pass results as they're valid
                        equipment_result["data"] = result_reconstructed_initial
                        equipment_result["status"] = "partial_success"  # Partial success since first pass worked
                        final_result_all.append(equipment_result)
                        continue
                    
                    # Convert second pass results to list of dictionaries
                    second_pass_results = second_result["aggregated_results"]
                    
                    # Reconstruct second pass results with answers
                    result_reconstructed_second = reconstruct_samples(second_pass_results, add_answer=True)
                    
                    # Merge results from both passes
                    final_result = merge_equipment_lists(result_reconstructed_initial_non_na, result_reconstructed_second)
                    
                    logger.info(f"Successfully merged results from both passes for equipment: {equipment_name} with bid ID: {bid_id}")
                    
                    # Set success status and data
                    equipment_result["status"] = "success"
                    equipment_result["data"] = final_result
                else:
                    logger.info(f"No N/A answers found for equipment: {equipment_name} with bid ID: {bid_id}. Using first pass results.")
                    
                    # Set success status and data
                    equipment_result["status"] = "success"
                    equipment_result["data"] = result_reconstructed_initial
                
                final_result_all.append(equipment_result)
            
            except Exception as e:
                equipment_name = equipment.get("equipment_name", "Unknown")
                logger.error(f"Error processing equipment {equipment_name} with bid ID: {bid_id}: {str(e)}")
                
                # Notify of equipment processing error
                self.add_event(self.requirement_id, 'progress_message', {
                    'event_type': self.event_name,
                    'request_id': self.requirement_id,
                    'data': {
                        'status': 'equipment_error', 
                        'message': f"Error processing equipment {equipment_name}: {str(e)}"
                    }
                })
                
                final_result_all.append({
                    "equipment": equipment.get("equipment_name", "Unknown"),
                    "status": "failed",
                    "data": None,
                    "error": str(e),
                    "bid_id": bid_id
                })

        # Notify completion of processing for this bid
        self.add_event(self.requirement_id, 'progress_message', {
            'event_type': self.event_name,
            'request_id': self.requirement_id,
            'data': {
                'status': 'bid_processed', 
                'message': f"Completed processing bid {bid_id}",
                'bid_results': final_result_all
            }
        })

        return final_result_all
    
    def process_multi_equipment_sync(self, equipment_list: list, bid_id: str):
        """
        Process multiple equipment for a single bid ID
        
        Args:
            equipment_list: List of equipment dictionaries
            bid_id: Bid ID to process
            
        Returns:
            List of results for each equipment
        """
        final_result_all = []
        
        # Notify beginning of source extraction
        self.add_event(self.requirement_id, 'progress_message', {
            'event_type': self.event_name,
            'request_id': self.requirement_id,
            'data': {'status': 'extracting_sources', 'message': f"Extracting sources for bid {bid_id}"}
        })
        
        total_equipment = len(equipment_list)
        for idx, equipment in enumerate(equipment_list):
            try:
                equipment_name = equipment.get("equipment_name", "Unknown")
                logger.info(f"Processing equipment {idx+1}/{total_equipment}: {equipment_name}")
                
                # Update socket with current equipment progress
                self.add_event(self.requirement_id, 'progress_message', {
                    'event_type': self.event_name,
                    'request_id': self.requirement_id,
                    'data': {
                        'status': 'processing_equipment', 
                        'message': f"Evaluating {equipment_name} specifications"
                    }
                })
                
                # Initialize status as failed by default
                equipment_result = {
                    "equipment": equipment_name,
                    "status": "failed",
                    "data": None,
                    "bid_id": bid_id  # Include bid ID in results for traceability
                }

                # Construct queries
                queries_with_tags, queries_without_tags = construct_queries(equipment)
                
                # Process all queries - first pass
                first_result = self.query_processor_sync(
                    queries_with_tags, 
                    queries_without_tags, 
                    request_id=bid_id,  # Using bid_id as the request_id parameter
                    batch_size=30
                )
                
                if not first_result["success"]:
                    logger.error(f"Failed to process queries in the first pass for equipment: {equipment_name} with bid ID: {bid_id}")
                    final_result_all.append(equipment_result)
                    continue
                    
                # Convert to list of dictionaries
                first_pass_results = first_result["aggregated_results"]
                first_pass_results_non_na = [entry for entry in first_pass_results if entry.get("answer") != "N/A"]
                
                # Reconstruct the results with answers
                result_reconstructed_initial = reconstruct_samples(first_pass_results, add_answer=True)
                result_reconstructed_initial_non_na = reconstruct_samples(first_pass_results_non_na, add_answer=True)
                
                # Filter out empty reconstructed results
                if result_reconstructed_initial["equipment_name"] is None:
                    result_reconstructed_initial = None
                if result_reconstructed_initial_non_na["equipment_name"] is None:
                    result_reconstructed_initial_non_na = None
                
                # Filter for N/A answers
                na_queries = [entry for entry in first_pass_results if entry.get("answer") == "N/A"]
                
                # If we have N/A queries, do a second pass
                if na_queries:
                    logger.info(f"Found {len(na_queries)} queries with N/A answers for bid ID {bid_id}. Running second pass.")
                    
                    # Update socket for second pass
                    self.add_event(self.requirement_id, 'progress_message', {
                        'event_type': self.event_name,
                        'request_id': self.requirement_id,
                        'data': {
                            'status': 'second_pass', 
                            'message': f"Performing detailed analysis of remaining specifications"
                        }
                    })
                    
                    # Reconstruct the N/A queries without answers
                    na_queries_reconstructed = reconstruct_samples(na_queries, add_answer=False)
                    
                    # Construct queries for second pass
                    na_queries_with_tags, na_queries_without_tags = construct_queries(na_queries_reconstructed)
                    
                    # Process N/A queries in second pass
                    second_result = self.query_processor_sync(
                        na_queries_with_tags, 
                        na_queries_without_tags, 
                        request_id=bid_id,  # Using bid_id as the request_id parameter
                        batch_size=50
                    )
                    
                    if not second_result["success"]:
                        logger.error(f"Failed to process queries in the second pass for equipment: {equipment_name} with bid ID: {bid_id}")
                        # Still include first pass results as they're valid
                        equipment_result["data"] = [result_reconstructed_initial] if result_reconstructed_initial else []
                        equipment_result["status"] = "partial_success"  # Partial success since first pass worked
                        final_result_all.append(equipment_result)
                        continue
                    
                    # Convert second pass results to list of dictionaries
                    second_pass_results = second_result["aggregated_results"]
                    
                    # Reconstruct second pass results with answers
                    result_reconstructed_second = reconstruct_samples(second_pass_results, add_answer=True)
                    
                    # Filter out empty reconstructed results
                    if result_reconstructed_second["equipment_name"] is None:
                        result_reconstructed_second = None
                    
                    # Merge results from both passes
                    self.add_event(self.requirement_id, 'progress_message', {
                        'event_type': self.event_name,
                        'request_id': self.requirement_id,
                        'data': {
                            'status': 'second_pass', 
                            'message': f"Merging all equipments information for final technical report"
                        }
                    })
                    
                    # Only merge if we have valid results
                    if result_reconstructed_initial_non_na and result_reconstructed_second:
                        final_result = merge_equipment_lists(result_reconstructed_initial_non_na, result_reconstructed_second)
                    elif result_reconstructed_initial_non_na:
                        final_result = result_reconstructed_initial_non_na
                    elif result_reconstructed_second:
                        final_result = result_reconstructed_second
                    else:
                        final_result = None
                    
                    logger.info(f"Successfully merged results from both passes for equipment: {equipment_name} with bid ID: {bid_id}")
                    
                    # Set success status and data
                    equipment_result["status"] = "success"
                    equipment_result["data"] = [final_result] if final_result else []
                else:
                    logger.info(f"No N/A answers found for equipment: {equipment_name} with bid ID: {bid_id}. Using first pass results.")
                    
                    # Set success status and data
                    equipment_result["status"] = "success"
                    equipment_result["data"] = [result_reconstructed_initial] if result_reconstructed_initial else []
                
                final_result_all.append(equipment_result)
            
            except Exception as e:
                equipment_name = equipment.get("equipment_name", "Unknown")
                logger.error(f"Error processing equipment {equipment_name} with bid ID: {bid_id}: {str(e)}")
                
                # Notify of equipment processing error
                self.add_event(self.requirement_id, 'error_event', {
                        'event_type': self.event_name,
                        'request_id': self.requirement_id,
                        'data': {'status': 'error', 'message': f"Oops! Something went wrong while processing technical evalution report", 'error': str(e)}
                    })
                
                final_result_all.append({
                    "equipment": equipment.get("equipment_name", "Unknown"),
                    "status": "failed",
                    "data": None,
                    "error": str(e),
                    "bid_id": bid_id
                })

        # Notify completion of processing for this bid
        self.add_event(self.requirement_id, 'progress_message', {
            'event_type': self.event_name,
            'request_id': self.requirement_id,
            'data': {
                'status': 'bid_processed', 
                'message': f"Completed processing bid {bid_id} for technical report",
                'bid_results': final_result_all
            }
        })

        return final_result_all




if __name__ == "__main__":

    sample_equipment_list = [{
    "equipment_name": "BALL MILL",
    "id":"fhhbf",
    "categories": [
        {
        "name": "GENERAL",
        "items": [
            {"name": "Mill Type", "unit": "", "discipline": "MECHANICAL"},
            {"name": "Mill ID", "unit": "", "discipline": "MECHANICAL"},
            {"name": "Mill Length (Effective Grinding Length)", "unit": "", "discipline": "MECHANICAL"},
            {"name": "Mill Speed", "unit": "rpm", "discipline": "MECHANICAL"},
            {"name": "N.C.", "unit": "", "discipline": "MECHANICAL"},
            {"name": "Power Consumption", "unit": "kw (hp)", "discipline": "MECHANICAL"},
            {"name": "Pinion power draw at:", "unit": "", "discipline": "MECHANICAL"},
            {"name": "New Liners", "unit": "kw (hp)", "discipline": "MECHANICAL"},
            {"name": "Worn Liners", "unit": "kw (hp)", "discipline": "MECHANICAL"}
        ]
        },
        {
        "name": "MILL SHELL",
        "items": [
            {"name": "Drilling", "unit": "", "discipline": "MECHANICAL"},
            {"name": "Drilling Method", "unit": "", "discipline": "MECHANICAL"},
            {"name": "Shell Material", "unit": "", "discipline": "MECHANICAL"},
            {"name": "Flange Material", "unit": "", "discipline": "MECHANICAL"},
            {"name": "Shell Thickness", "unit": "mm", "discipline": "MECHANICAL"},
            {"name": "Flange Thickness", "unit": "mm", "discipline": "MECHANICAL"},
            {"name": "Number of Manholes", "unit": "", "discipline": "MECHANICAL"}
        ]
        },
        {
        "name": "MILL HEADS",
        "items": [
            {"name": "MILL HEADS", "unit": "", "discipline": "MECHANICAL"},
            {"name": "Material", "unit": "", "discipline": "MECHANICAL"},
            {"name": "Thickness at Trunnion Bearing", "unit": "mm", "discipline": "MECHANICAL"},
            {"name": "Head Mass (KG)", "unit": "kg", "discipline": "MECHANICAL"},
            {"name": "Trunnion Mass (KG)", "unit": "kg", "discipline": "MECHANICAL"}
        ]
        },
        {
        "name": "GIRTH GEAR",
        "items": [
            {"name": "Country of Origin", "unit": "", "discipline": "ELECTRICAL"},
            {"name": "Supplier", "unit": "", "discipline": "ELECTRICAL"},
            {"name": "Diameter", "unit": "mm", "discipline": "ELECTRICAL"},
            {"name": "Face Width", "unit": "mm", "discipline": "ELECTRICAL"},
            {"name": "Number of Segments", "unit": "", "discipline": "ELECTRICAL"},
            {"name": "Gear Type", "unit": "", "discipline": "ELECTRICAL"},
            {"name": "No. of Teeth", "unit": "", "discipline": "ELECTRICAL"},
            {"name": "Material", "unit": "", "discipline": "ELECTRICAL"},
            {"name": "AGMA", "unit": "", "discipline": "ELECTRICAL"},
            {"name": "Surface Finish", "unit": "µm", "discipline": "ELECTRICAL"},
            {"name": "Service Factor: Durability", "unit": "", "discipline": "ELECTRICAL"},
            {"name": "Service Factor: Strength", "unit": "", "discipline": "ELECTRICAL"},
            {"name": "Hardness", "unit": "HB", "discipline": "ELECTRICAL"},
            {"name": "Reversible", "unit": "yes/no", "discipline": "ELECTRICAL"},
            {"name": "Mass (KG)", "unit": "kg", "discipline": "ELECTRICAL"},
            {"name": "Module", "unit": "mm", "discipline": "ELECTRICAL"},
            {"name": "Guard supplier", "unit": "", "discipline": "ELECTRICAL"}
        ]
        }
    ]
    }]
    '''request_id = "62b432fe-72a1-4a13-9baa-799253d554df"
    from cbp.services.embeddings.vectore_database_service import VectorDatabaseService

    async def main():
        vs = VectorDatabaseService()
        res = await vs._execute_gpu_search(search_queries=["Hello"], num_results=3, request_id=request_id)
        print(res)
    
    asyncio.run(main())'''
    
    bids_ids = ["6aa67866-913e-42a8-97cc-b0b7d662241e"]

    bidder_names = [
    {
      "bidder_name": "BALL MILL 2",
      "bid_id": "6aa67866-913e-42a8-97cc-b0b7d662241e"
    }
  ]
    
    requirement_id = "test"
    processor = TechnicalBidProcessor(
requirement_id=requirement_id
    )
    results = processor.process_multiple_bids_sync(bid_ids=bids_ids,equipment_list=sample_equipment_list, bidder_names=bidder_names)


    with open("tech.json", "w") as f:
        json.dump(results, f, indent=2)
    print(f"Final results: {results}")
    

    