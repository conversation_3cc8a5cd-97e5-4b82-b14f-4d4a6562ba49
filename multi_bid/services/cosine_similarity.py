from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from generate_question_field import QuestionGenerator


question_generator = QuestionGenerator()
def get_cosine_similarity(text):
    generated_text, generated_text_pipeline = question_generator.generate_questions_and_fields(text)
    questions , fields = question_generator.preprocess_text(generated_text_pipeline)
    vectorizer = TfidfVectorizer(stop_words='english')

    full_question = ""
    for question in questions:
        final_question = question + "?" + "\n"
        full_question += final_question

    full_feilds = ""
    for field in fields:
        full_feilds += field + "\n"
    # Vectorize the questions, fields, and sentences
    tfidf_matrix = vectorizer.fit_transform([full_question, full_feilds] + text)

    # Compute cosine similarity between each sentence and questions, and each sentence and fields
    similarity_matrix = cosine_similarity(tfidf_matrix, tfidf_matrix[:2])

    # Define a threshold or number of top sentences you want to extract
    threshold = 0.1

    relevant_sentences_for_questions = [sentences[i-2] for i, score in enumerate(similarity_matrix[2:, 0]) if score > threshold]
    relevant_sentences_for_fields = [sentences[i-2] for i, score in enumerate(similarity_matrix[2:, 1]) if score > threshold]
    print("Relevant sentences for questions:")
    for sentence in relevant_sentences_for_questions:
        print(sentence)

    print("\nRelevant sentences for fields:")
    for sentence in relevant_sentences_for_fields:
        print(sentence)

    relevant_sentences = ""
    for sentense in relevant_sentences_for_questions:
        relevant_sentences += sentense
    relevant_sentences_fields = ""
    for sen_fil in relevant_sentences_for_fields:
        relevant_sentences_fields += sen_fil

    return relevant_sentences, relevant_sentences_fields


