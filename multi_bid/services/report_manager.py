import os
import json

class ReportManager:
    def __init__(self, directory, project_id, requirement_id):
        self.directory = directory
        self.project_id = project_id
        self.requirement_id = requirement_id
        
        os.makedirs(os.path.join(self.directory, self.project_id), exist_ok=True)
        self.report_path = os.path.join(self.directory, self.project_id, self.requirement_id + ".json")

    def create_initial_report(self, log):
        with open(self.report_path, 'w', encoding='utf8') as json_file:
            json.dump(log, json_file, ensure_ascii=False)

    def load_report(self):
        with open(self.report_path, 'r', encoding='utf8') as json_file:
            return json.load(json_file)
    
    def update_log(self, updated_log):
        print('updating log for request...')
        self.create_initial_report(updated_log)


