import asyncio
# This file is added to make the 'tests' directory a package

from .chronobid.test_chronobid_document_processor import ChronobidProcessorManager
# from .specs.test_specs_document_processor import SpecsComplyProcessorManager
# from .qmp.test_question_answerer import QuestionAnswerTester 
import os

async def run_cbp():
    print('I am started...')
    # Usage example:
    # Get the current working directory
    current_directory = os.getcwd()
    
    # Define the relative paths
    json_data_path = os.path.join(current_directory, 'tests', "test_data.json")
    files_directory = os.path.join(current_directory, 'tests', "data_files")
    
    processor_manager = ChronobidProcessorManager(json_data_path, files_directory)
    
    # Await the run method
    await processor_manager.run()  # Call the async run method


if __name__ == "__main__":
    print('i am started...')
    asyncio.run(run_cbp())