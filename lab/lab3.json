[{"source_map": {"de9c30e7-0ef6-4faf-8d55-f5eb8b4d940a": {"id": "ddbec3d6-f8a4-4d8c-9991-6c06a0692a9c", "matched_content": "sk assessment shall be performed with a 5-steps approaches : • Define the risk analysis methodology (e.g. Architecture based) • Identify major items (organization, system, subsystems, networks) • Identification, evaluation of the threat scenarios", "section_id": "de9c30e7-0ef6-4faf-8d55-f5eb8b4d940a", "score": 0.237662, "source": "ESO_FSO_Scope_of_Work.pdf", "title": "TITLE_45_ESO", "page_number": "45", "content": " \nEXPORT SCHEME OPTIMISATION (ESO) \nPROJECT –  TWO CONVERTED FSOs  \n \n \n \nSCOPE OF WORK, Call for Tender \nPage 45 of 118 \n \n \nPerformance of a cyber security study for all links between the FSOs and/or with the New Reservoir \nPlatforms (A and B locations). \nFor Industrial Information Systems (SII – PCS, SIS, Package Unit Control Panel, PDS with relevant \nnetworks), Cyber security assessment(in accordance with IEC 62443) shall be performed at early stage \nof detail engineering to have cyber security protection methods integrated into the design. \nThe cyber security risk assessment shall be performed with a 5-steps approaches : \n• \nDefine the risk analysis methodology (e.g. Architecture based) \n• \nIdentify major items (organization, system, subsystems, networks) \n• \nIdentification, evaluation of the threat scenarios with their impact and likelihood \n• \nReduce the risks by designing adequate countermeasures \n• \nSummarize the results in a Risk Register \nCyber security risk assessment Findings, Recommendations and Risk resister shall be reported and \nimplemented in the project cyber security documentation. \nCyber security documentation shall be supplied by Contractor as minimum the following : \n• \nDetailed SII inventory list \n• \nCyber security risk analysis report and associated actions \n• \nAntivirus study, configuration detail, update and running procedures \n• \nNetwork configuration details \n• \nPatching procedures \n• \nBack-up and restoration procedures for operating system, network configuration, archive \nand historical data, application program, network and software components. \n• \nIP address plan \n• \nPhysical/logical access protections \n• \nDisaster recovery plan \nAs a part of Cyber security risk assessment, COMPANY will initiate audits in order to verify cyber \nsecurity organization, documentation, operating system, network, configurations and firewall \nconfiguration. \nCyber security test shall be performed prior to delivery of the package and ICSS equipment including \ntests of network configuration and various procedures(e.g. Backup and Restore) \n \nICSS Brownfield Integration Engineering on New Reservoir Platforms (Locations A and B) \nCONTRACTOR shall design the FSOs ICSS systems and develop the SCADA system of the New Reservoir \nplatforms (location A/B) to be compatible (or interfaced) together. The FSOs’ ICSS systems shall allow \nthe FSOs operators to monitor key points of the process operations and ESD status on the New \nReservoir platform and vice versa. In both cases, this shall be on a ‘read only’ basis and it shall not be \npossible for either the FSOs or the Platforms to intervene in each other’s operations. \nCONTRACTOR shall develop all ESD Logic diagrams for the integration of the FSOs and the interface \nwith the existing New Reservoir platforms (A/B location). \nCONTRACTOR shall perform all integration study on New Reservoir platforms (A/B location) in order \nto monitor all inter trip signals between the FSOs and the existing field as per the ESD logic diagram \nand ICSS supervision requirement. \nCONTRACTOR shall perform a detailed dedicated ICSS Integration Architecture, and an Hazop/SIL \nassessment with Third Party in order to demonstrate that design shall not introduce any failure mode \nor Cyber safety issue on the existing New Reservoir ICSS. \nCONTRACTOR shall carry out integration engineering on New Reservoir platforms (A/B locations) to \nensure monitoring and to perform the safety actions between the FSOs and these platforms. \n"}, "d1308dad-020a-4802-9742-5faa3fa6fee0": {"id": "9b5e9975-aac0-4a5a-9f66-60ddf2c6f5dc", "matched_content": "o To determine the interface elevation transfer functions for unit amplitude of the FSO(P) motions; o Combine the transfer functions and the wash tank RAOs to calculate the water/DPZ (Dense Packed Zone) interface RAOs, the DPZ/Oil interface RAOs and", "section_id": "d1308dad-020a-4802-9742-5faa3fa6fee0", "score": 0.226597935, "source": "ESO_FSO_Scope_of_Work.pdf", "title": "TITLE_54_ESO", "page_number": "54", "content": " \nEXPORT SCHEME OPTIMISATION (ESO) \nPROJECT –  TWO CONVERTED FSOs  \n \n \n \nSCOPE OF WORK, Call for Tender \nPage 54 of 118 \n \n \nf) Perform CFD modelling of the distributor feed arms to confirm the quality of the distribution in \neach chamber; \ng) Design of distributors shall take into consideration the presence of gas. \nSand Production and Handling \nIn the course of the design, CONTRACTOR shall pay attention to sand accumulation inside the process \nand storage tanks and ensure the design takes into account this factor. CONTRACTOR shall ensure that \nthe efficiency of the whole process shall not be affected by sand accumulation. For instance, \nCONTRACTOR shall carefully check the sizing of the holes and distances between them for the \ndistributors. CONTRACTOR shall document the measures incorporated in the design to minimise the \nrisk of blockage of the distributors with sand. \nCONTRACTOR shall provide appropriate sand monitoring and filtering equipment as per the DESIGN \nDOSSIER. \nCONTRACTOR shall establish a sand washing programme (crude oil washing or similar) to prevent sand \nbuild-up in the tanks reaching unacceptable levels, the frequency of which shall be APPROVED by \nCOMPANY. CONTRACTOR shall be fully responsible for the removal of the sand accumulated in the \ntanks. \n \nCFD Modeling for Sloshing Analyses \nCONTRACTOR shall perform CFD analyses for the wash tanks to confirm that the oil and water \nseparation process is not effected by the sloshing of liquids due to the vessel motions. \nCONTRACTOR shall submit design brief detailing the methodology of the analyses for COMPANY \nreview before starting the analyses. \nThe analyses shall cover the following, as a minimum \na) The FSO(P) and wash tank motion analysis to calculate the motion and acceleration RAO at the \ncentre of gravity of the wash tanks according to the headings, the Hull geometry and the loading \ncases; \nb) Screening analyses aiming to compute the dynamic elevation of each phase due to the FSO(P) \nmotions. For each wave spectrum, headings and loading cases, the methodology is \no To determine the interface elevation transfer functions for unit amplitude of the FSO(P) \nmotions; \no Combine the transfer functions and the wash tank RAOs to calculate the water/DPZ (Dense \nPacked Zone) interface RAOs, the DPZ/Oil interface RAOs and the tank elevation RAOs (oil / \ngas interface); \no Following the screening process, appropriate number of load cases shall be identified which \nare more critical than others on the basis of highest interface amplitudes. For each selected \ncase, the next step is to generate wash tank excitation as time series (3 hours) for each of \nthe six degrees of freedom. These time series data sets will be used for the subsequent CFD \nsimulation, \nc) The methodology to define the separation model (equation of BS&W profile) inside the DPZ \nshall be included in the design brief and subject to review. 2D CFD simulations shall be used to \nnarrow down the critical cases identified in the previous steps. This step shall result in verifying \nthe criticality of the selected case compared to the operability criteria. Three or four most critical \ncases shall be identified by CONTRACTOR in terms of exceedance of the probability criteria \nwhich are to be further assessed by 3D CFD simulations; \n"}, "305a2f13-8ec0-4d81-ba8b-47809ddbf7d2": {"id": "2691c971-a23a-40b4-bc20-3849852803e2", "matched_content": "eas a) Engine room, b) Pump room, c) FSOs Hull deck (including AFT / FWD area / manifold), d) Wash tanks, desalting tanks, cargo tanks, e) Future location of the MOORING SYSTEMS, f) Outer profile of the FSOs. The final 3D modelling of the FSOs shall", "section_id": "305a2f13-8ec0-4d81-ba8b-47809ddbf7d2", "score": 0.*********, "source": "ESO_FSO_Scope_of_Work.pdf", "title": "TITLE_36_ESO", "page_number": "36", "content": " \nEXPORT SCHEME OPTIMISATION (ESO) \nPROJECT –  TWO CONVERTED FSOs  \n \n \n \nSCOPE OF WORK, Call for Tender \nPage 36 of 118 \n \n \nx) CONTRACTOR shall deliver to COMPANY all structural analyses FE models, input files and output \nfiles of the FSO in a format useable for planned inspection, maintenance and re-assessment during \noperation, and capable of being up-dated with results of inspections. The latest intact and damage \nstability models, input file and output files shall also be delivered to the COMPANY. \ny) Preservation \nCONTRACTOR shall develop equipment storage, preservation and protection plan and procedures \nfor CONTRACTOR ITEMS and COMPANY ITEMS. CONTRACTOR’s plans, procedures, specifications \nshall take into account all VENDOR requirements regarding storage, preservation and protection \nof the equipment including supply of grease, oils, space heaters, turning of rotating shafts, \ntemporary HVAC for preservation etc. These documents and procedures shall be developed for all \nthe phases of the WORK; \nz) Any and all engineering works and/or services required to complete the WORK. \n2.3.2 \nEndorsement of the DESIGN DOSSIER \nAs provided in EXHIBIT E, CONTRACTOR shall upon the EFFECTIVE DATE, be deemed to have fully \nchecked and verified all aspects of the documents contained within the DESIGN DOSSIER and to have \nfully endorsed the documents stated \"to be endorsed\" as being free of any errors, omissions, \ndeficiencies, inaccuracies, contradictions, ambiguities, and/or discrepancies. \n<PERSON><PERSON><PERSON>CTOR by his signature of the Certificate of <PERSON>orsement of the DE<PERSON>GN DOSSIER acknowledges \nhis endorsement of the DESIGN DOS<PERSON>ER. \nCONTRACTOR may use the electronic data so supplied (native files) at his own risk and COMPANY shall \nnot be responsible for any error, omission, contradiction or any other discrepancy which may become \napparent, within and / or between the data supplied and any other COMPANY supplied documents. \nCONTRACTOR shall be responsible for his own clarifications of the CONTRACT DOCUMENTS and of the \ndocumentation supplied within the CONTRACT. Any requirement for check, modification will be under \nCONTRACTOR’s responsibility. \n2.3.3 \nRequirement Related to 3D Model \nCONTRACTOR shall perform pre-conversion 3D scanning of the FSOs to develop 3D Model. \nCONTRACTOR shall perform laser scan survey and convert to LFM point cloud data to generate a \nprimitive 3D model, which shall include but not be limited to the following areas \na) Engine room, \nb) Pump room, \nc) FSOs Hull deck (including AFT / FWD area / manifold), \nd) Wash tanks, desalting tanks, cargo tanks, \ne) Future location of the MOORING SYSTEMS, \nf) Outer profile of the FSOs. \nThe final 3D modelling of the FSOs shall cover, as a minimum,  the following areas \na) Hull, Hull appurtenances and Topsides, \nb) Pump room and engine room, \nc) RISER BALCONY AREA, \nd) MOORING SYSTEM, \ne) Pull-In system for RISERS and MOORING LINES, \n \nThe 3D model shall incorporate as a minimum the following elements \na) All piping, equipment, packages, \nb) All safety equipment, \n"}}, "source_list": [{"id": "ddbec3d6-f8a4-4d8c-9991-6c06a0692a9c", "matched_content": "sk assessment shall be performed with a 5-steps approaches : • Define the risk analysis methodology (e.g. Architecture based) • Identify major items (organization, system, subsystems, networks) • Identification, evaluation of the threat scenarios", "section_id": "de9c30e7-0ef6-4faf-8d55-f5eb8b4d940a", "score": 0.237662, "source": "ESO_FSO_Scope_of_Work.pdf", "title": "TITLE_45_ESO", "page_number": "45", "content": " \nEXPORT SCHEME OPTIMISATION (ESO) \nPROJECT –  TWO CONVERTED FSOs  \n \n \n \nSCOPE OF WORK, Call for Tender \nPage 45 of 118 \n \n \nPerformance of a cyber security study for all links between the FSOs and/or with the New Reservoir \nPlatforms (A and B locations). \nFor Industrial Information Systems (SII – PCS, SIS, Package Unit Control Panel, PDS with relevant \nnetworks), Cyber security assessment(in accordance with IEC 62443) shall be performed at early stage \nof detail engineering to have cyber security protection methods integrated into the design. \nThe cyber security risk assessment shall be performed with a 5-steps approaches : \n• \nDefine the risk analysis methodology (e.g. Architecture based) \n• \nIdentify major items (organization, system, subsystems, networks) \n• \nIdentification, evaluation of the threat scenarios with their impact and likelihood \n• \nReduce the risks by designing adequate countermeasures \n• \nSummarize the results in a Risk Register \nCyber security risk assessment Findings, Recommendations and Risk resister shall be reported and \nimplemented in the project cyber security documentation. \nCyber security documentation shall be supplied by Contractor as minimum the following : \n• \nDetailed SII inventory list \n• \nCyber security risk analysis report and associated actions \n• \nAntivirus study, configuration detail, update and running procedures \n• \nNetwork configuration details \n• \nPatching procedures \n• \nBack-up and restoration procedures for operating system, network configuration, archive \nand historical data, application program, network and software components. \n• \nIP address plan \n• \nPhysical/logical access protections \n• \nDisaster recovery plan \nAs a part of Cyber security risk assessment, COMPANY will initiate audits in order to verify cyber \nsecurity organization, documentation, operating system, network, configurations and firewall \nconfiguration. \nCyber security test shall be performed prior to delivery of the package and ICSS equipment including \ntests of network configuration and various procedures(e.g. Backup and Restore) \n \nICSS Brownfield Integration Engineering on New Reservoir Platforms (Locations A and B) \nCONTRACTOR shall design the FSOs ICSS systems and develop the SCADA system of the New Reservoir \nplatforms (location A/B) to be compatible (or interfaced) together. The FSOs’ ICSS systems shall allow \nthe FSOs operators to monitor key points of the process operations and ESD status on the New \nReservoir platform and vice versa. In both cases, this shall be on a ‘read only’ basis and it shall not be \npossible for either the FSOs or the Platforms to intervene in each other’s operations. \nCONTRACTOR shall develop all ESD Logic diagrams for the integration of the FSOs and the interface \nwith the existing New Reservoir platforms (A/B location). \nCONTRACTOR shall perform all integration study on New Reservoir platforms (A/B location) in order \nto monitor all inter trip signals between the FSOs and the existing field as per the ESD logic diagram \nand ICSS supervision requirement. \nCONTRACTOR shall perform a detailed dedicated ICSS Integration Architecture, and an Hazop/SIL \nassessment with Third Party in order to demonstrate that design shall not introduce any failure mode \nor Cyber safety issue on the existing New Reservoir ICSS. \nCONTRACTOR shall carry out integration engineering on New Reservoir platforms (A/B locations) to \nensure monitoring and to perform the safety actions between the FSOs and these platforms. \n"}, {"id": "9b5e9975-aac0-4a5a-9f66-60ddf2c6f5dc", "matched_content": "o To determine the interface elevation transfer functions for unit amplitude of the FSO(P) motions; o Combine the transfer functions and the wash tank RAOs to calculate the water/DPZ (Dense Packed Zone) interface RAOs, the DPZ/Oil interface RAOs and", "section_id": "d1308dad-020a-4802-9742-5faa3fa6fee0", "score": 0.226597935, "source": "ESO_FSO_Scope_of_Work.pdf", "title": "TITLE_54_ESO", "page_number": "54", "content": " \nEXPORT SCHEME OPTIMISATION (ESO) \nPROJECT –  TWO CONVERTED FSOs  \n \n \n \nSCOPE OF WORK, Call for Tender \nPage 54 of 118 \n \n \nf) Perform CFD modelling of the distributor feed arms to confirm the quality of the distribution in \neach chamber; \ng) Design of distributors shall take into consideration the presence of gas. \nSand Production and Handling \nIn the course of the design, CONTRACTOR shall pay attention to sand accumulation inside the process \nand storage tanks and ensure the design takes into account this factor. CONTRACTOR shall ensure that \nthe efficiency of the whole process shall not be affected by sand accumulation. For instance, \nCONTRACTOR shall carefully check the sizing of the holes and distances between them for the \ndistributors. CONTRACTOR shall document the measures incorporated in the design to minimise the \nrisk of blockage of the distributors with sand. \nCONTRACTOR shall provide appropriate sand monitoring and filtering equipment as per the DESIGN \nDOSSIER. \nCONTRACTOR shall establish a sand washing programme (crude oil washing or similar) to prevent sand \nbuild-up in the tanks reaching unacceptable levels, the frequency of which shall be APPROVED by \nCOMPANY. CONTRACTOR shall be fully responsible for the removal of the sand accumulated in the \ntanks. \n \nCFD Modeling for Sloshing Analyses \nCONTRACTOR shall perform CFD analyses for the wash tanks to confirm that the oil and water \nseparation process is not effected by the sloshing of liquids due to the vessel motions. \nCONTRACTOR shall submit design brief detailing the methodology of the analyses for COMPANY \nreview before starting the analyses. \nThe analyses shall cover the following, as a minimum \na) The FSO(P) and wash tank motion analysis to calculate the motion and acceleration RAO at the \ncentre of gravity of the wash tanks according to the headings, the Hull geometry and the loading \ncases; \nb) Screening analyses aiming to compute the dynamic elevation of each phase due to the FSO(P) \nmotions. For each wave spectrum, headings and loading cases, the methodology is \no To determine the interface elevation transfer functions for unit amplitude of the FSO(P) \nmotions; \no Combine the transfer functions and the wash tank RAOs to calculate the water/DPZ (Dense \nPacked Zone) interface RAOs, the DPZ/Oil interface RAOs and the tank elevation RAOs (oil / \ngas interface); \no Following the screening process, appropriate number of load cases shall be identified which \nare more critical than others on the basis of highest interface amplitudes. For each selected \ncase, the next step is to generate wash tank excitation as time series (3 hours) for each of \nthe six degrees of freedom. These time series data sets will be used for the subsequent CFD \nsimulation, \nc) The methodology to define the separation model (equation of BS&W profile) inside the DPZ \nshall be included in the design brief and subject to review. 2D CFD simulations shall be used to \nnarrow down the critical cases identified in the previous steps. This step shall result in verifying \nthe criticality of the selected case compared to the operability criteria. Three or four most critical \ncases shall be identified by CONTRACTOR in terms of exceedance of the probability criteria \nwhich are to be further assessed by 3D CFD simulations; \n"}, {"id": "2691c971-a23a-40b4-bc20-3849852803e2", "matched_content": "eas a) Engine room, b) Pump room, c) FSOs Hull deck (including AFT / FWD area / manifold), d) Wash tanks, desalting tanks, cargo tanks, e) Future location of the MOORING SYSTEMS, f) Outer profile of the FSOs. The final 3D modelling of the FSOs shall", "section_id": "305a2f13-8ec0-4d81-ba8b-47809ddbf7d2", "score": 0.*********, "source": "ESO_FSO_Scope_of_Work.pdf", "title": "TITLE_36_ESO", "page_number": "36", "content": " \nEXPORT SCHEME OPTIMISATION (ESO) \nPROJECT –  TWO CONVERTED FSOs  \n \n \n \nSCOPE OF WORK, Call for Tender \nPage 36 of 118 \n \n \nx) CONTRACTOR shall deliver to COMPANY all structural analyses FE models, input files and output \nfiles of the FSO in a format useable for planned inspection, maintenance and re-assessment during \noperation, and capable of being up-dated with results of inspections. The latest intact and damage \nstability models, input file and output files shall also be delivered to the COMPANY. \ny) Preservation \nCONTRACTOR shall develop equipment storage, preservation and protection plan and procedures \nfor CONTRACTOR ITEMS and COMPANY ITEMS. CONTRACTOR’s plans, procedures, specifications \nshall take into account all VENDOR requirements regarding storage, preservation and protection \nof the equipment including supply of grease, oils, space heaters, turning of rotating shafts, \ntemporary HVAC for preservation etc. These documents and procedures shall be developed for all \nthe phases of the WORK; \nz) Any and all engineering works and/or services required to complete the WORK. \n2.3.2 \nEndorsement of the DESIGN DOSSIER \nAs provided in EXHIBIT E, CONTRACTOR shall upon the EFFECTIVE DATE, be deemed to have fully \nchecked and verified all aspects of the documents contained within the DESIGN DOSSIER and to have \nfully endorsed the documents stated \"to be endorsed\" as being free of any errors, omissions, \ndeficiencies, inaccuracies, contradictions, ambiguities, and/or discrepancies. \n<PERSON><PERSON><PERSON>CTOR by his signature of the Certificate of <PERSON>orsement of the DE<PERSON>GN DOSSIER acknowledges \nhis endorsement of the DESIGN DOS<PERSON>ER. \nCONTRACTOR may use the electronic data so supplied (native files) at his own risk and COMPANY shall \nnot be responsible for any error, omission, contradiction or any other discrepancy which may become \napparent, within and / or between the data supplied and any other COMPANY supplied documents. \nCONTRACTOR shall be responsible for his own clarifications of the CONTRACT DOCUMENTS and of the \ndocumentation supplied within the CONTRACT. Any requirement for check, modification will be under \nCONTRACTOR’s responsibility. \n2.3.3 \nRequirement Related to 3D Model \nCONTRACTOR shall perform pre-conversion 3D scanning of the FSOs to develop 3D Model. \nCONTRACTOR shall perform laser scan survey and convert to LFM point cloud data to generate a \nprimitive 3D model, which shall include but not be limited to the following areas \na) Engine room, \nb) Pump room, \nc) FSOs Hull deck (including AFT / FWD area / manifold), \nd) Wash tanks, desalting tanks, cargo tanks, \ne) Future location of the MOORING SYSTEMS, \nf) Outer profile of the FSOs. \nThe final 3D modelling of the FSOs shall cover, as a minimum,  the following areas \na) Hull, Hull appurtenances and Topsides, \nb) Pump room and engine room, \nc) RISER BALCONY AREA, \nd) MOORING SYSTEM, \ne) Pull-In system for RISERS and MOORING LINES, \n \nThe 3D model shall incorporate as a minimum the following elements \na) All piping, equipment, packages, \nb) All safety equipment, \n"}], "questions": ["Deep learning with keras?"]}, {"source_map": {"4a9f958c-9671-4d93-affc-29ba0c214245": {"id": "6555b0f2-0679-40bf-9034-719eb70d3354", "matched_content": "even though <PERSON><PERSON> has an Adagrad optimizer, you should not use it to train deep neural networks (it may be efficient for simpler tasks such as linear regression, though). Still, understanding AdaGrad is helpful to comprehend the other adaptive", "section_id": "4a9f958c-9671-4d93-affc-29ba0c214245", "score": 0.719987452, "source": "hands-on-machine-learning-with-scikit-learn-keras-and-tensorflow-9781492032649-9781098125974.pdf", "title": "TITLE_471_hands", "page_number": "471", "content": "Figure 11-8. AdaGrad versus gradient descent: the former can correct its direction earlier to point to the\noptimum\nAda<PERSON>rad frequently performs well for simple quadratic problems, but it often\nstops too early when training neural networks: the learning rate gets scaled down\nso much that the algorithm ends up stopping entirely before reaching the global\noptimum. So even though <PERSON><PERSON> has an Adagrad optimizer, you should not use\nit to train deep neural networks (it may be efficient for simpler tasks such as\nlinear regression, though). Still, understanding AdaGrad is helpful to\ncomprehend the other adaptive learning rate optimizers.\nRMSProp\nAs we’ve seen, AdaGrad runs the risk of slowing down a bit too fast and never\nconverging to the global optimum. The RMSProp algorithm⁠\n fixes this by\naccumulating only the gradients from the most recent iterations, as opposed to\nall the gradients since the beginning of training. It does so by using exponential\ndecay in the first step (see Equation 11-8).\nEquation 11-8. RMSProp algorithm\nThe decay rate ρ is typically set to 0.9.⁠\n Yes, it is once again a new\nhyperparameter, but this default value often works well, so you may not need to\n18\n19\n"}, "e1d61c33-1d89-40ae-b298-8b4c1db802bb": {"id": "0a897c94-d0da-45cb-8603-a5abc5bba7be", "matched_content": "as Google’s BigQuery service (see https://tensorflow.org/io). Keras also comes with powerful yet easy-to-use preprocessing layers that can be embedded in your models: this way, when you deploy a model to production, it will be able to ingest raw", "section_id": "e1d61c33-1d89-40ae-b298-8b4c1db802bb", "score": 0.693040192, "source": "hands-on-machine-learning-with-scikit-learn-keras-and-tensorflow-9781492032649-9781098125974.pdf", "title": "TITLE_543_hands", "page_number": "543", "content": "Chapter 13. Loading and\nPreprocessing Data with\nTensorFlow\nIn Chapter 2, you saw that loading and preprocessing data is an important part of\nany machine learning project. You used Pandas to load and explore the\n(modified) California housing dataset—which was stored in a CSV file—and\nyou applied Scikit-Learn’s transformers for preprocessing. These tools are quite\nconvenient, and you will probably be using them often, especially when\nexploring and experimenting with data.\nHowever, when training TensorFlow models on large datasets, you may prefer to\nuse TensorFlow’s own data loading and preprocessing API, called tf.data. It is\ncapable of loading and preprocessing data extremely efficiently, reading from\nmultiple files in parallel using multithreading and queuing, shuffling and\nbatching samples, and more. Plus, it can do all of this on the fly—it loads and\npreprocesses the next batch of data across multiple CPU cores, while your GPUs\nor TPUs are busy training the current batch of data.\nThe tf.data API lets you handle datasets that don’t fit in memory, and it allows\nyou to make full use of your hardware resources, thereby speeding up training.\nOff the shelf, the tf.data API can read from text files (such as CSV files), binary\nfiles with fixed-size records, and binary files that use TensorFlow’s TFRecord\nformat, which supports records of varying sizes.\nTFRecord is a flexible and efficient binary format usually containing protocol\nbuffers (an open source binary format). The tf.data API also has support for\nreading from SQL databases. Moreover, many open source extensions are\navailable to read from all sorts of data sources, such as Google’s BigQuery\nservice (see https://tensorflow.org/io).\nKeras also comes with powerful yet easy-to-use preprocessing layers that can be\nembedded in your models: this way, when you deploy a model to production, it\nwill be able to ingest raw data directly, without you having to add any additional\n"}, "8de90371-4fd0-40d8-b725-4e46e8c4f4e2": {"id": "f362409b-1ce4-459a-a511-cbc7842c8a6a", "matched_content": "arning applications. It was open sourced in November 2015, and version 2.0 was released in September 2019. Keras is a high-level deep learning API that makes it very simple to train and run neural networks. Keras comes bundled with TensorFlow, and it", "section_id": "8de90371-4fd0-40d8-b725-4e46e8c4f4e2", "score": 0.687538087, "source": "hands-on-machine-learning-with-scikit-learn-keras-and-tensorflow-9781492032649-9781098125974.pdf", "title": "TITLE_5_hands", "page_number": "5", "content": "Segment customers and find the best marketing strategy for each group.\nRecommend products for each client based on what similar clients bought.\nDetect which transactions are likely to be fraudulent.\nForecast next year’s revenue.\nWhatever the reason, you have decided to learn machine learning and implement\nit in your projects. Great idea!\nObjective and Approach\nThis book assumes that you know close to nothing about machine learning. Its\ngoal is to give you the concepts, tools, and intuition you need to implement\nprograms capable of learning from data.\nWe will cover a large number of techniques, from the simplest and most\ncommonly used (such as linear regression) to some of the deep learning\ntechniques that regularly win competitions. For this, we will be using\nproduction-ready Python frameworks:\nScikit-Learn is very easy to use, yet it implements many machine learning\nalgorithms efficiently, so it makes for a great entry point to learning\nmachine learning. It was created by <PERSON> in 2007, and is now\nled by a team of researchers at the French Institute for Research in\nComputer Science and Automation (Inria).\nTensorFlow is a more complex library for distributed numerical\ncomputation. It makes it possible to train and run very large neural\nnetworks efficiently by distributing the computations across potentially\nhundreds of multi-GPU (graphics processing unit) servers. TensorFlow (TF)\nwas created at Google and supports many of its large-scale machine\nlearning applications. It was open sourced in November 2015, and version\n2.0 was released in September 2019.\nKeras is a high-level deep learning API that makes it very simple to train\nand run neural networks. Keras comes bundled with TensorFlow, and it\nrelies on TensorFlow for all the intensive computations.\n"}, "d5be183c-b1f1-4ec6-95a3-b560101848dc": {"id": "b45173f8-ffe9-4df2-8020-8f7411001e5f", "matched_content": "Conclusions Keras is a powerful and battery-included framework for Deep Learning in Python Keras is simple to use.. ...but it is not for simple things!", "section_id": "d5be183c-b1f1-4ec6-95a3-b560101848dc", "score": 0.685862064, "source": "deep-learning-with-keras-and-tensorflow.pdf", "title": "TITLE_556_deep", "page_number": "556", "content": "Conclusions\nKeras is a powerful and battery-included framework for\nDeep Learning in Python\nKeras is simple to use..\n...but it is not for simple things!\n"}, "57db5652-808a-441a-9a53-afd6f66bce50": {"id": "b7cf43cc-df37-47cb-b2c0-3675d4d551ce", "matched_content": "Keras: Deep Learning library for Theano and TensorFlow Keras is a minimalist, highly modular neural networks library, written in Python and capable of running on top of either TensorFlow or Theano. It was developed with a focus on enabling fast", "section_id": "57db5652-808a-441a-9a53-afd6f66bce50", "score": 0.671793222, "source": "deep-learning-with-keras-and-tensorflow.pdf", "title": "TITLE_144_deep", "page_number": "144", "content": "Keras: Deep Learning library for\nTheano and TensorFlow\nKeras is a minimalist, highly modular neural networks library,\nwritten in Python and capable of running on top of either\nTensorFlow or Theano.\nIt was developed with a focus on enabling fast\nexperimentation. Being able to go from idea to result with\nthe least possible delay is key to doing good research. ref:\nhttps://keras.io/\nKaggle Challenge Data\nThe Otto Group is one of the world’s biggest e-commerce\ncompanies, A consistent analysis of the performance of\nproducts is crucial. However, due to diverse global\ninfrastructure, many identical products get classified\ndifferently. For this competition, we have provided a dataset\nwith 93 features for more than 200,000 products. The\nobjective is to build a predictive model which is able to\ndistinguish between our main product categories. Each row\ncorresponds to a single product. There are a total of 93\nnumerical features, which represent counts of different\nevents. All features have been obfuscated and will not be\ndefined any further.\nhttps://www.kaggle.com/c/otto-group-product-classification-\nchallenge/data\n"}}, "source_list": [{"id": "6555b0f2-0679-40bf-9034-719eb70d3354", "matched_content": "even though <PERSON><PERSON> has an Adagrad optimizer, you should not use it to train deep neural networks (it may be efficient for simpler tasks such as linear regression, though). Still, understanding AdaGrad is helpful to comprehend the other adaptive", "section_id": "4a9f958c-9671-4d93-affc-29ba0c214245", "score": 0.719987452, "source": "hands-on-machine-learning-with-scikit-learn-keras-and-tensorflow-9781492032649-9781098125974.pdf", "title": "TITLE_471_hands", "page_number": "471", "content": "Figure 11-8. AdaGrad versus gradient descent: the former can correct its direction earlier to point to the\noptimum\nAda<PERSON>rad frequently performs well for simple quadratic problems, but it often\nstops too early when training neural networks: the learning rate gets scaled down\nso much that the algorithm ends up stopping entirely before reaching the global\noptimum. So even though <PERSON><PERSON> has an Adagrad optimizer, you should not use\nit to train deep neural networks (it may be efficient for simpler tasks such as\nlinear regression, though). Still, understanding AdaGrad is helpful to\ncomprehend the other adaptive learning rate optimizers.\nRMSProp\nAs we’ve seen, AdaGrad runs the risk of slowing down a bit too fast and never\nconverging to the global optimum. The RMSProp algorithm⁠\n fixes this by\naccumulating only the gradients from the most recent iterations, as opposed to\nall the gradients since the beginning of training. It does so by using exponential\ndecay in the first step (see Equation 11-8).\nEquation 11-8. RMSProp algorithm\nThe decay rate ρ is typically set to 0.9.⁠\n Yes, it is once again a new\nhyperparameter, but this default value often works well, so you may not need to\n18\n19\n"}, {"id": "0a897c94-d0da-45cb-8603-a5abc5bba7be", "matched_content": "as Google’s BigQuery service (see https://tensorflow.org/io). Keras also comes with powerful yet easy-to-use preprocessing layers that can be embedded in your models: this way, when you deploy a model to production, it will be able to ingest raw", "section_id": "e1d61c33-1d89-40ae-b298-8b4c1db802bb", "score": 0.693040192, "source": "hands-on-machine-learning-with-scikit-learn-keras-and-tensorflow-9781492032649-9781098125974.pdf", "title": "TITLE_543_hands", "page_number": "543", "content": "Chapter 13. Loading and\nPreprocessing Data with\nTensorFlow\nIn Chapter 2, you saw that loading and preprocessing data is an important part of\nany machine learning project. You used Pandas to load and explore the\n(modified) California housing dataset—which was stored in a CSV file—and\nyou applied Scikit-Learn’s transformers for preprocessing. These tools are quite\nconvenient, and you will probably be using them often, especially when\nexploring and experimenting with data.\nHowever, when training TensorFlow models on large datasets, you may prefer to\nuse TensorFlow’s own data loading and preprocessing API, called tf.data. It is\ncapable of loading and preprocessing data extremely efficiently, reading from\nmultiple files in parallel using multithreading and queuing, shuffling and\nbatching samples, and more. Plus, it can do all of this on the fly—it loads and\npreprocesses the next batch of data across multiple CPU cores, while your GPUs\nor TPUs are busy training the current batch of data.\nThe tf.data API lets you handle datasets that don’t fit in memory, and it allows\nyou to make full use of your hardware resources, thereby speeding up training.\nOff the shelf, the tf.data API can read from text files (such as CSV files), binary\nfiles with fixed-size records, and binary files that use TensorFlow’s TFRecord\nformat, which supports records of varying sizes.\nTFRecord is a flexible and efficient binary format usually containing protocol\nbuffers (an open source binary format). The tf.data API also has support for\nreading from SQL databases. Moreover, many open source extensions are\navailable to read from all sorts of data sources, such as Google’s BigQuery\nservice (see https://tensorflow.org/io).\nKeras also comes with powerful yet easy-to-use preprocessing layers that can be\nembedded in your models: this way, when you deploy a model to production, it\nwill be able to ingest raw data directly, without you having to add any additional\n"}, {"id": "f362409b-1ce4-459a-a511-cbc7842c8a6a", "matched_content": "arning applications. It was open sourced in November 2015, and version 2.0 was released in September 2019. Keras is a high-level deep learning API that makes it very simple to train and run neural networks. Keras comes bundled with TensorFlow, and it", "section_id": "8de90371-4fd0-40d8-b725-4e46e8c4f4e2", "score": 0.687538087, "source": "hands-on-machine-learning-with-scikit-learn-keras-and-tensorflow-9781492032649-9781098125974.pdf", "title": "TITLE_5_hands", "page_number": "5", "content": "Segment customers and find the best marketing strategy for each group.\nRecommend products for each client based on what similar clients bought.\nDetect which transactions are likely to be fraudulent.\nForecast next year’s revenue.\nWhatever the reason, you have decided to learn machine learning and implement\nit in your projects. Great idea!\nObjective and Approach\nThis book assumes that you know close to nothing about machine learning. Its\ngoal is to give you the concepts, tools, and intuition you need to implement\nprograms capable of learning from data.\nWe will cover a large number of techniques, from the simplest and most\ncommonly used (such as linear regression) to some of the deep learning\ntechniques that regularly win competitions. For this, we will be using\nproduction-ready Python frameworks:\nScikit-Learn is very easy to use, yet it implements many machine learning\nalgorithms efficiently, so it makes for a great entry point to learning\nmachine learning. It was created by <PERSON> in 2007, and is now\nled by a team of researchers at the French Institute for Research in\nComputer Science and Automation (Inria).\nTensorFlow is a more complex library for distributed numerical\ncomputation. It makes it possible to train and run very large neural\nnetworks efficiently by distributing the computations across potentially\nhundreds of multi-GPU (graphics processing unit) servers. TensorFlow (TF)\nwas created at Google and supports many of its large-scale machine\nlearning applications. It was open sourced in November 2015, and version\n2.0 was released in September 2019.\nKeras is a high-level deep learning API that makes it very simple to train\nand run neural networks. Keras comes bundled with TensorFlow, and it\nrelies on TensorFlow for all the intensive computations.\n"}, {"id": "b45173f8-ffe9-4df2-8020-8f7411001e5f", "matched_content": "Conclusions Keras is a powerful and battery-included framework for Deep Learning in Python Keras is simple to use.. ...but it is not for simple things!", "section_id": "d5be183c-b1f1-4ec6-95a3-b560101848dc", "score": 0.685862064, "source": "deep-learning-with-keras-and-tensorflow.pdf", "title": "TITLE_556_deep", "page_number": "556", "content": "Conclusions\nKeras is a powerful and battery-included framework for\nDeep Learning in Python\nKeras is simple to use..\n...but it is not for simple things!\n"}, {"id": "b7cf43cc-df37-47cb-b2c0-3675d4d551ce", "matched_content": "Keras: Deep Learning library for Theano and TensorFlow Keras is a minimalist, highly modular neural networks library, written in Python and capable of running on top of either TensorFlow or Theano. It was developed with a focus on enabling fast", "section_id": "57db5652-808a-441a-9a53-afd6f66bce50", "score": 0.671793222, "source": "deep-learning-with-keras-and-tensorflow.pdf", "title": "TITLE_144_deep", "page_number": "144", "content": "Keras: Deep Learning library for\nTheano and TensorFlow\nKeras is a minimalist, highly modular neural networks library,\nwritten in Python and capable of running on top of either\nTensorFlow or Theano.\nIt was developed with a focus on enabling fast\nexperimentation. Being able to go from idea to result with\nthe least possible delay is key to doing good research. ref:\nhttps://keras.io/\nKaggle Challenge Data\nThe Otto Group is one of the world’s biggest e-commerce\ncompanies, A consistent analysis of the performance of\nproducts is crucial. However, due to diverse global\ninfrastructure, many identical products get classified\ndifferently. For this competition, we have provided a dataset\nwith 93 features for more than 200,000 products. The\nobjective is to build a predictive model which is able to\ndistinguish between our main product categories. Each row\ncorresponds to a single product. There are a total of 93\nnumerical features, which represent counts of different\nevents. All features have been obfuscated and will not be\ndefined any further.\nhttps://www.kaggle.com/c/otto-group-product-classification-\nchallenge/data\n"}], "questions": ["Deep learning with keras?"]}, {"source_map": {"65a775ab-f75d-43f4-b40b-0382f285a257": {"id": "b0bee741-e352-40fa-ba9b-058c2bfca6f5", "matched_content": "PI RP 14J: Design and Hazards Analysis for Offshore Production Facilities API RP 16Q: Design, Selection, Operation and Maintenance of Marine Drilling Riser System API RP 65-1: Cementing Shallow-water Flow Zones in Deepwater Wells API RP 96: Deepwater", "section_id": "65a775ab-f75d-43f4-b40b-0382f285a257", "score": 0.204360649, "source": "API-International-Usage-and-Deployment-Report-2022.pdf", "title": "TITLE_53_API", "page_number": "53", "content": "API International Standards Report\nAmericas   49\nGuidelines Issued by ASEA (cont.)\nGuidance\nAPI Standard Referenced\nGuidelines on Industrial Safety, Operational Safety and \nProtection of the Environment for Reconnaissance, \nSurface Exploration, Exploration, and Extraction of \nHydrocarbons\nAPI RP 53: Blowout Prevention Equipment Systems for \nDrilling Wells\nAPI 65-2: Isolating Potential Flow Zones During Well \nConstruction\nAPI RP 7G: Drill Stem Design and Operating Limits\nAPI RP 10D-2: Centralizer Placement and Stop-collar \nTesting\nAPI RP 13B-1: Field Testing Water-based Drilling Fluids\nAPI RP 13B-2: Field Testing Oil-based Drilling Fluids\nAPI RP 13D: Rheology and Hydraulics of Oil-well Drilling \nFluids\nAPI RP 14B: Design, Installation, Repair and Operation of \nSubsurface Safety Valve Systems\nAPI RP 14C: Analysis, Design, Installation, and Testing of \nSafety Systems for Offshore Production Facilities\nAPI RP 14G: Fire Prevention and Control on Fixed Open-\ntype Offshore Production Platforms\nAPI RP 14H: Installation, Maintenance and Repair of \nSurface Safety Valves and Underwater Safety Valves \nOffshore\nAPI RP 14J: Design and Hazards Analysis for Offshore \nProduction Facilities\nAPI RP 16Q: Design, Selection, Operation and Maintenance \nof Marine Drilling Riser System\nAPI RP 65-1: Cementing Shallow-water Flow Zones in \nDeepwater Wells\nAPI RP 96: Deepwater Well Design and Construction\nAPI RP 1111: Design, Construction, Operation, and \nMaintenance of Offshore Hydrocarbon Pipelines \nAPI RP 2A-WSD: Planning, Designing, and Constructing \nFixed Offshore Platforms-Working Stress Design\nAPI RP 2SIM: Structural Integrity Management of Fixed \nOffshore Structures\n"}, "49cb9819-2d1c-4ea4-8021-471316b0ec4d": {"id": "4cf1d0ae-3df6-4df8-b3ac-3a569e5c40f2", "matched_content": "for Remotely Operated Tools and Inerfaces on Subsea Production Systems API RP 17N, Recommended Practice on Subsea Production System Reliability, Technical Risk, and Integrity Management API RP 17P, Recommended Practice for Subsea Structures and", "section_id": "49cb9819-2d1c-4ea4-8021-471316b0ec4d", "score": 0.204187244, "source": "API-International-Usage-and-Deployment-Report-2022.pdf", "title": "TITLE_90_API", "page_number": "90", "content": "API International Standards Report\nIndo-Pacific   86\nExploration and \nProduction (cont.)\nAPI RP 17B, Recommended Practice for Flexible Pipe\nAPI RP 17A, Design and Operation of Subsea Production Systems-General Requirements and \nRecommendations\nAPI SPEC 17D, and Operation of Subsea Production Systems-Subsea Wellhead and Tree \nEquipment\nAPI 17F\nAPI RP 17H, Recommended Practice for Remotely Operated Tools and Inerfaces on Subsea \nProduction Systems\nAPI RP 17N, Recommended Practice on Subsea Production System Reliability, Technical Risk, \nand Integrity Management\nAPI RP 17P, Recommended Practice for Subsea Structures and Manifolds\nAPI RP 17V, Recommended Practice for Analysis, Design, Installation, and Testing of Safety \nSystems for Subsea Applications\nAPI RP 17W, Recommended Practice for Subsea Capping Stacks\nAPI RP 17R, Recommended Practice for Flowline Connectors and Jumpers\nAPI RP 17U, Recommended Practice for Wet and Dry Thermal Insulation of Subsea Flowlines \nand Equipment\nAPI RP 17Q, Recommended Practice on Subsea Equipment Qualification\nAPI RP 17S, Recommended Practice for the Design, Testing, and Operation of Subsea \nMultiphase Flow Meters\nAPI STD 53, Well Control Equipment Systems for Drilling Wells\nAPI RP 55, Recommended Practice for Oil and Gas Producing and Gas Processing Plant \nOperations Involving Hydrogen Sulfide\nAPI RP 85, Use of Subsea Wet-gas Flowmeters in Allocation Measurement Systems\nAPI RP 86, Recommended Practice for Measurement of Multiphase Flow\nAPI RP 170, Recommended Practice for Subsea High Integrity Pressure Protection System \n(HIPPS)\nAPI Spec 6A, Specification for Wellhead and Christmas Tree Equipment\nAPI SPEC 6AV1, Validation of Wellhead Surface Safety Valves and Underwater Safety Valves \nfor Offshore Service\nAPI SPEC 16A, Drill-through Equipment\nAPI SPEC 16D, Control Systems for Drilling Well\nAPI RP 16Q, Design, Selection, Operation, and Maintenance of Marine Drilling Riser Systems\nAPI RP 17Q, Subsea Equipment Qualification-Standardized Process for Documentation\n"}, "7b878eed-a5d3-4bb0-ac7d-907eb548cccd": {"id": "ccaebd8a-4787-4a0f-b1ca-40e8a2ca7b9a", "matched_content": "2022 API STANDARDS: INTERNATIONAL USAGE AND DEPLOYMENT", "section_id": "7b878eed-a5d3-4bb0-ac7d-907eb548cccd", "score": 0.203252375, "source": "API-International-Usage-and-Deployment-Report-2022.pdf", "title": "TITLE_1_API", "page_number": "1", "content": "2022\nAPI STANDARDS: \nINTERNATIONAL USAGE \nAND DEPLOYMENT\n"}, "8e961f85-13a4-47e4-b145-e5b7fb4b3f31": {"id": "2076f493-df43-4df3-8372-bf688052b316", "matched_content": "ction Systems and Tension Leg Platforms API RP 65-2, Cementing Shallow Water Flow Zones in Deepwater Wells. Isolating Potential Flow Zones API RP 65-1, Cementing Shallow Water Flow Zones in Deepwater Wells API RP 49, Recommended Practice for Drilling", "section_id": "8e961f85-13a4-47e4-b145-e5b7fb4b3f31", "score": 0.20278047, "source": "API-International-Usage-and-Deployment-Report-2022.pdf", "title": "TITLE_50_API", "page_number": "50", "content": "API International Standards Report\nAmericas   46\nGuidelines Issued by CNH (cont.)\nNOM\nAPI Standard Referenced\nGuidelines for Well Drilling (cont.)\nAPI RP 90-2, Annular Casing Pressure Management for \nOnshore Wells\nAPI 14C, Analysis Design, Installation, and Testing of Basic \nSurface Safety Systems for Offshore Production\nAPI RP 17N, Subsea Reliability and technical Risk \nmanagement\nAPI RP 2 MOP, Marine Operations\nAPI RP 2A-WSD Planning, Designing, and Constructing \nFixed Offshore Platforms— Working Stress Design\nAPI RP 2RD, Design of Risers for Floating Production \nSystems and Tension Leg Platforms\nAPI RP 65-2, Cementing Shallow Water Flow Zones in \nDeepwater Wells. Isolating Potential Flow Zones\nAPI RP 65-1, Cementing Shallow Water Flow Zones in \nDeepwater Wells\nAPI RP 49, Recommended Practice for Drilling and Well \nServicing Operations Involving Hydrogen Sulfide\nAPI RP 53, Recommended Practices for Blowout \nPrevention Equipment Systems for Drilling Wells\nAPI RP 54, Recommended Practice for Occupational \nSafety for Oil and Gas Well Drilling and Servicing \nOperations\nAPI RP 90, Annular Casing Pressure Management for \nOffshore Wells\nAPI RP 90-1, Annular Casing Pressure Management for \nOffshore Wells\nAPI RP 96, Deepwater well design and construction\nAPI Spec 16F, Specification for Marine Drilling Riser \nEquipment\nAPI SPEC 17W, Subsea Capping Stacks\nAPI RP 17B, Flexible pipe systems for subsea and marine \napplications\n"}}, "source_list": [{"id": "b0bee741-e352-40fa-ba9b-058c2bfca6f5", "matched_content": "PI RP 14J: Design and Hazards Analysis for Offshore Production Facilities API RP 16Q: Design, Selection, Operation and Maintenance of Marine Drilling Riser System API RP 65-1: Cementing Shallow-water Flow Zones in Deepwater Wells API RP 96: Deepwater", "section_id": "65a775ab-f75d-43f4-b40b-0382f285a257", "score": 0.204360649, "source": "API-International-Usage-and-Deployment-Report-2022.pdf", "title": "TITLE_53_API", "page_number": "53", "content": "API International Standards Report\nAmericas   49\nGuidelines Issued by ASEA (cont.)\nGuidance\nAPI Standard Referenced\nGuidelines on Industrial Safety, Operational Safety and \nProtection of the Environment for Reconnaissance, \nSurface Exploration, Exploration, and Extraction of \nHydrocarbons\nAPI RP 53: Blowout Prevention Equipment Systems for \nDrilling Wells\nAPI 65-2: Isolating Potential Flow Zones During Well \nConstruction\nAPI RP 7G: Drill Stem Design and Operating Limits\nAPI RP 10D-2: Centralizer Placement and Stop-collar \nTesting\nAPI RP 13B-1: Field Testing Water-based Drilling Fluids\nAPI RP 13B-2: Field Testing Oil-based Drilling Fluids\nAPI RP 13D: Rheology and Hydraulics of Oil-well Drilling \nFluids\nAPI RP 14B: Design, Installation, Repair and Operation of \nSubsurface Safety Valve Systems\nAPI RP 14C: Analysis, Design, Installation, and Testing of \nSafety Systems for Offshore Production Facilities\nAPI RP 14G: Fire Prevention and Control on Fixed Open-\ntype Offshore Production Platforms\nAPI RP 14H: Installation, Maintenance and Repair of \nSurface Safety Valves and Underwater Safety Valves \nOffshore\nAPI RP 14J: Design and Hazards Analysis for Offshore \nProduction Facilities\nAPI RP 16Q: Design, Selection, Operation and Maintenance \nof Marine Drilling Riser System\nAPI RP 65-1: Cementing Shallow-water Flow Zones in \nDeepwater Wells\nAPI RP 96: Deepwater Well Design and Construction\nAPI RP 1111: Design, Construction, Operation, and \nMaintenance of Offshore Hydrocarbon Pipelines \nAPI RP 2A-WSD: Planning, Designing, and Constructing \nFixed Offshore Platforms-Working Stress Design\nAPI RP 2SIM: Structural Integrity Management of Fixed \nOffshore Structures\n"}, {"id": "4cf1d0ae-3df6-4df8-b3ac-3a569e5c40f2", "matched_content": "for Remotely Operated Tools and Inerfaces on Subsea Production Systems API RP 17N, Recommended Practice on Subsea Production System Reliability, Technical Risk, and Integrity Management API RP 17P, Recommended Practice for Subsea Structures and", "section_id": "49cb9819-2d1c-4ea4-8021-471316b0ec4d", "score": 0.204187244, "source": "API-International-Usage-and-Deployment-Report-2022.pdf", "title": "TITLE_90_API", "page_number": "90", "content": "API International Standards Report\nIndo-Pacific   86\nExploration and \nProduction (cont.)\nAPI RP 17B, Recommended Practice for Flexible Pipe\nAPI RP 17A, Design and Operation of Subsea Production Systems-General Requirements and \nRecommendations\nAPI SPEC 17D, and Operation of Subsea Production Systems-Subsea Wellhead and Tree \nEquipment\nAPI 17F\nAPI RP 17H, Recommended Practice for Remotely Operated Tools and Inerfaces on Subsea \nProduction Systems\nAPI RP 17N, Recommended Practice on Subsea Production System Reliability, Technical Risk, \nand Integrity Management\nAPI RP 17P, Recommended Practice for Subsea Structures and Manifolds\nAPI RP 17V, Recommended Practice for Analysis, Design, Installation, and Testing of Safety \nSystems for Subsea Applications\nAPI RP 17W, Recommended Practice for Subsea Capping Stacks\nAPI RP 17R, Recommended Practice for Flowline Connectors and Jumpers\nAPI RP 17U, Recommended Practice for Wet and Dry Thermal Insulation of Subsea Flowlines \nand Equipment\nAPI RP 17Q, Recommended Practice on Subsea Equipment Qualification\nAPI RP 17S, Recommended Practice for the Design, Testing, and Operation of Subsea \nMultiphase Flow Meters\nAPI STD 53, Well Control Equipment Systems for Drilling Wells\nAPI RP 55, Recommended Practice for Oil and Gas Producing and Gas Processing Plant \nOperations Involving Hydrogen Sulfide\nAPI RP 85, Use of Subsea Wet-gas Flowmeters in Allocation Measurement Systems\nAPI RP 86, Recommended Practice for Measurement of Multiphase Flow\nAPI RP 170, Recommended Practice for Subsea High Integrity Pressure Protection System \n(HIPPS)\nAPI Spec 6A, Specification for Wellhead and Christmas Tree Equipment\nAPI SPEC 6AV1, Validation of Wellhead Surface Safety Valves and Underwater Safety Valves \nfor Offshore Service\nAPI SPEC 16A, Drill-through Equipment\nAPI SPEC 16D, Control Systems for Drilling Well\nAPI RP 16Q, Design, Selection, Operation, and Maintenance of Marine Drilling Riser Systems\nAPI RP 17Q, Subsea Equipment Qualification-Standardized Process for Documentation\n"}, {"id": "ccaebd8a-4787-4a0f-b1ca-40e8a2ca7b9a", "matched_content": "2022 API STANDARDS: INTERNATIONAL USAGE AND DEPLOYMENT", "section_id": "7b878eed-a5d3-4bb0-ac7d-907eb548cccd", "score": 0.203252375, "source": "API-International-Usage-and-Deployment-Report-2022.pdf", "title": "TITLE_1_API", "page_number": "1", "content": "2022\nAPI STANDARDS: \nINTERNATIONAL USAGE \nAND DEPLOYMENT\n"}, {"id": "2076f493-df43-4df3-8372-bf688052b316", "matched_content": "ction Systems and Tension Leg Platforms API RP 65-2, Cementing Shallow Water Flow Zones in Deepwater Wells. Isolating Potential Flow Zones API RP 65-1, Cementing Shallow Water Flow Zones in Deepwater Wells API RP 49, Recommended Practice for Drilling", "section_id": "8e961f85-13a4-47e4-b145-e5b7fb4b3f31", "score": 0.20278047, "source": "API-International-Usage-and-Deployment-Report-2022.pdf", "title": "TITLE_50_API", "page_number": "50", "content": "API International Standards Report\nAmericas   46\nGuidelines Issued by CNH (cont.)\nNOM\nAPI Standard Referenced\nGuidelines for Well Drilling (cont.)\nAPI RP 90-2, Annular Casing Pressure Management for \nOnshore Wells\nAPI 14C, Analysis Design, Installation, and Testing of Basic \nSurface Safety Systems for Offshore Production\nAPI RP 17N, Subsea Reliability and technical Risk \nmanagement\nAPI RP 2 MOP, Marine Operations\nAPI RP 2A-WSD Planning, Designing, and Constructing \nFixed Offshore Platforms— Working Stress Design\nAPI RP 2RD, Design of Risers for Floating Production \nSystems and Tension Leg Platforms\nAPI RP 65-2, Cementing Shallow Water Flow Zones in \nDeepwater Wells. Isolating Potential Flow Zones\nAPI RP 65-1, Cementing Shallow Water Flow Zones in \nDeepwater Wells\nAPI RP 49, Recommended Practice for Drilling and Well \nServicing Operations Involving Hydrogen Sulfide\nAPI RP 53, Recommended Practices for Blowout \nPrevention Equipment Systems for Drilling Wells\nAPI RP 54, Recommended Practice for Occupational \nSafety for Oil and Gas Well Drilling and Servicing \nOperations\nAPI RP 90, Annular Casing Pressure Management for \nOffshore Wells\nAPI RP 90-1, Annular Casing Pressure Management for \nOffshore Wells\nAPI RP 96, Deepwater well design and construction\nAPI Spec 16F, Specification for Marine Drilling Riser \nEquipment\nAPI SPEC 17W, Subsea Capping Stacks\nAPI RP 17B, Flexible pipe systems for subsea and marine \napplications\n"}], "questions": ["Deep learning with keras?"]}]