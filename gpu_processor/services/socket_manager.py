#services/socket_manager

from flask_socketio import emit, join_room, leave_room
from threading import Lock
import redis
import json
from datetime import datetime
from flask_socketio import SocketIO


class SocketManager:
    def __init__(self, socketio):
        self.socketio = socketio
        self.lock = Lock()
        self.redis_client = redis.StrictRedis(host='localhost', port=6379, decode_responses=True)

    def add_client(self, sid, request_id):
        print('display: ', sid, request_id)
        
        self.redis_client.hset(f"clients:{sid}", "request_id", request_id)
        self.redis_client.sadd(f"room_members:{request_id}", sid)
        join_room(request_id, sid)
        print(f"Socketmanager message showing Client {sid} joined room {request_id}")
        
        # Check for stored messages
        stored_messages = self.redis_client.lrange(f"room_messages:{request_id}", 0, -1)
        print(f'Found {len(stored_messages)} stored messages for room {request_id}')
        
        if stored_messages:
            for message in stored_messages:
                print(f'Processing stored message: {message}')
                try:
                    data = json.loads(message)
                    print(f'Emitting stored message: {data["event_name"]}')
                    self.socketio.emit(
                        data["event_name"], 
                        data["data"], 
                        room=request_id, 
                        namespace=data["namespace"]
                    )
                except Exception as e:
                    print(f"Error processing stored message: {str(e)}")
            
            # Clear processed messages
            self.redis_client.delete(f"room_messages:{request_id}")

    def remove_client(self, sid):
        # with self.lock:
        request_id = self.redis_client.hget(f"clients:{sid}", "request_id")
        if request_id:
            self.redis_client.hdel(f"clients:{sid}", "request_id")
            self.redis_client.srem(f"room_members:{request_id}", sid)
            leave_room(request_id, sid)
            print(f"Client {sid} left room {request_id}")

    def get_room_members(self, request_id):
        """Get all members of a room from Redis"""
        return self.redis_client.smembers(f"room_members:{request_id}")

    def emit_to_client(self, request_id, event_name, data, namespace='/cbp'):
        # with self.lock:
        if "uploaded_text" in data:
            del data["uploaded_text"]

        # Check room members in Redis
        room_members = self.get_room_members(request_id)
        print(f'Room {request_id} members from Redis: {room_members}')
        
        message_data = {
            "event_name": event_name,
            "data": data,
            "namespace": namespace,
            "timestamp": json.dumps(datetime.utcnow(), default=str)
        }

        try:
            # Always store the message first
            self.redis_client.rpush(
                f"room_messages:{request_id}",
                json.dumps(message_data)
            )
            
            print('this is room members: ', room_members)
            print(self.get_room_members(request_id))
            if room_members:
                print(f"Emitting {event_name} to room {request_id} with {len(room_members)} members")
                self.socketio.emit(
                    event_name,
                    data,
                    room=request_id,
                    namespace=namespace
                )
            else:
                print(f"Room {request_id} is empty. Message stored for later delivery.")

        except Exception as e:
            print(f"Error in emit_to_client: {str(e)}")

    def handle_reconnect(self, sid, request_id):
        """Handles a client reconnection."""
        # with self.lock:
        # Clean up any existing associations
        old_request_id = self.redis_client.hget(f"clients:{sid}", "request_id")
        if old_request_id:
            self.redis_client.srem(f"room_members:{old_request_id}", sid)
        
        # Add new association
        self.add_client(sid, request_id)
        print(f"Client {sid} reconnected to room {request_id}")

    def cleanup_room(self, request_id):
        """Clean up room data when it's no longer needed"""
        # with self.lock:
        self.redis_client.delete(f"room_members:{request_id}")
        self.redis_client.delete(f"room_messages:{request_id}")
