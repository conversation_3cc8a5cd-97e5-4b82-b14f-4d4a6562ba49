Requirement already satisfied: transformers in ./venv/lib/python3.10/site-packages (4.36.0.dev0)
Requirement already satisfied: optimum in ./venv/lib/python3.10/site-packages (1.14.0)
Requirement already satisfied: tqdm>=4.27 in ./venv/lib/python3.10/site-packages (from transformers) (4.66.1)
Requirement already satisfied: tokenizers<0.15,>=0.14 in ./venv/lib/python3.10/site-packages (from transformers) (0.14.1)
Requirement already satisfied: safetensors>=0.3.1 in ./venv/lib/python3.10/site-packages (from transformers) (0.4.0)
Requirement already satisfied: packaging>=20.0 in ./venv/lib/python3.10/site-packages (from transformers) (23.2)
Requirement already satisfied: filelock in ./venv/lib/python3.10/site-packages (from transformers) (3.13.1)
Requirement already satisfied: huggingface-hub<1.0,>=0.16.4 in ./venv/lib/python3.10/site-packages (from transformers) (0.17.3)
Requirement already satisfied: regex!=2019.12.17 in ./venv/lib/python3.10/site-packages (from transformers) (2023.10.3)
Requirement already satisfied: requests in ./venv/lib/python3.10/site-packages (from transformers) (2.31.0)
Requirement already satisfied: numpy>=1.17 in ./venv/lib/python3.10/site-packages (from transformers) (1.26.1)
Requirement already satisfied: pyyaml>=5.1 in ./venv/lib/python3.10/site-packages (from transformers) (6.0.1)
Requirement already satisfied: torch>=1.9 in ./venv/lib/python3.10/site-packages (from optimum) (2.1.0)
Requirement already satisfied: sympy in ./venv/lib/python3.10/site-packages (from optimum) (1.12)
Requirement already satisfied: datasets in ./venv/lib/python3.10/site-packages (from optimum) (2.14.6)
Requirement already satisfied: coloredlogs in ./venv/lib/python3.10/site-packages (from optimum) (15.0.1)
Requirement already satisfied: fsspec in ./venv/lib/python3.10/site-packages (from huggingface-hub<1.0,>=0.16.4->transformers) (2023.10.0)
Requirement already satisfied: typing-extensions>=3.7.4.3 in ./venv/lib/python3.10/site-packages (from huggingface-hub<1.0,>=0.16.4->transformers) (4.8.0)
Requirement already satisfied: triton==2.1.0 in ./venv/lib/python3.10/site-packages (from torch>=1.9->optimum) (2.1.0)
Requirement already satisfied: nvidia-cusolver-cu12==11.4.5.107 in ./venv/lib/python3.10/site-packages (from torch>=1.9->optimum) (11.4.5.107)
Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.1.105 in ./venv/lib/python3.10/site-packages (from torch>=1.9->optimum) (12.1.105)
Requirement already satisfied: jinja2 in ./venv/lib/python3.10/site-packages (from torch>=1.9->optimum) (3.1.2)
Requirement already satisfied: nvidia-cuda-runtime-cu12==12.1.105 in ./venv/lib/python3.10/site-packages (from torch>=1.9->optimum) (12.1.105)
Requirement already satisfied: nvidia-cuda-cupti-cu12==12.1.105 in ./venv/lib/python3.10/site-packages (from torch>=1.9->optimum) (12.1.105)
Requirement already satisfied: nvidia-nccl-cu12==2.18.1 in ./venv/lib/python3.10/site-packages (from torch>=1.9->optimum) (2.18.1)
Requirement already satisfied: nvidia-nvtx-cu12==12.1.105 in ./venv/lib/python3.10/site-packages (from torch>=1.9->optimum) (12.1.105)
Requirement already satisfied: nvidia-cublas-cu12==12.1.3.1 in ./venv/lib/python3.10/site-packages (from torch>=1.9->optimum) (12.1.3.1)
Requirement already satisfied: nvidia-cusparse-cu12==12.1.0.106 in ./venv/lib/python3.10/site-packages (from torch>=1.9->optimum) (12.1.0.106)
Requirement already satisfied: nvidia-cudnn-cu12==8.9.2.26 in ./venv/lib/python3.10/site-packages (from torch>=1.9->optimum) (8.9.2.26)
Requirement already satisfied: nvidia-curand-cu12==10.3.2.106 in ./venv/lib/python3.10/site-packages (from torch>=1.9->optimum) (10.3.2.106)
Requirement already satisfied: nvidia-cufft-cu12==11.0.2.54 in ./venv/lib/python3.10/site-packages (from torch>=1.9->optimum) (11.0.2.54)
Requirement already satisfied: networkx in ./venv/lib/python3.10/site-packages (from torch>=1.9->optimum) (3.2.1)
Requirement already satisfied: nvidia-nvjitlink-cu12 in ./venv/lib/python3.10/site-packages (from nvidia-cusolver-cu12==11.4.5.107->torch>=1.9->optimum) (12.3.52)
Requirement already satisfied: sentencepiece!=0.1.92,>=0.1.91 in ./venv/lib/python3.10/site-packages (from transformers) (0.1.99)
Requirement already satisfied: protobuf in ./venv/lib/python3.10/site-packages (from transformers) (4.25.0)
Requirement already satisfied: humanfriendly>=9.1 in ./venv/lib/python3.10/site-packages (from coloredlogs->optimum) (10.0)
Requirement already satisfied: pandas in ./venv/lib/python3.10/site-packages (from datasets->optimum) (2.1.2)
Requirement already satisfied: xxhash in ./venv/lib/python3.10/site-packages (from datasets->optimum) (3.4.1)
Requirement already satisfied: aiohttp in ./venv/lib/python3.10/site-packages (from datasets->optimum) (3.8.6)
Requirement already satisfied: multiprocess in ./venv/lib/python3.10/site-packages (from datasets->optimum) (0.70.15)
Requirement already satisfied: pyarrow>=8.0.0 in ./venv/lib/python3.10/site-packages (from datasets->optimum) (14.0.1)
Requirement already satisfied: dill<0.3.8,>=0.3.0 in ./venv/lib/python3.10/site-packages (from datasets->optimum) (0.3.7)
Requirement already satisfied: urllib3<3,>=1.21.1 in ./venv/lib/python3.10/site-packages (from requests->transformers) (1.26.18)
Requirement already satisfied: certifi>=2017.4.17 in ./venv/lib/python3.10/site-packages (from requests->transformers) (2023.7.22)
Requirement already satisfied: idna<4,>=2.5 in ./venv/lib/python3.10/site-packages (from requests->transformers) (3.4)
Requirement already satisfied: charset-normalizer<4,>=2 in ./venv/lib/python3.10/site-packages (from requests->transformers) (3.3.2)
Requirement already satisfied: mpmath>=0.19 in ./venv/lib/python3.10/site-packages (from sympy->optimum) (1.3.0)
Requirement already satisfied: aiosignal>=1.1.2 in ./venv/lib/python3.10/site-packages (from aiohttp->datasets->optimum) (1.3.1)
Requirement already satisfied: attrs>=17.3.0 in ./venv/lib/python3.10/site-packages (from aiohttp->datasets->optimum) (23.1.0)
Requirement already satisfied: multidict<7.0,>=4.5 in ./venv/lib/python3.10/site-packages (from aiohttp->datasets->optimum) (6.0.4)
Requirement already satisfied: frozenlist>=1.1.1 in ./venv/lib/python3.10/site-packages (from aiohttp->datasets->optimum) (1.4.0)
Requirement already satisfied: async-timeout<5.0,>=4.0.0a3 in ./venv/lib/python3.10/site-packages (from aiohttp->datasets->optimum) (4.0.3)
Requirement already satisfied: yarl<2.0,>=1.0 in ./venv/lib/python3.10/site-packages (from aiohttp->datasets->optimum) (1.9.2)
Requirement already satisfied: MarkupSafe>=2.0 in ./venv/lib/python3.10/site-packages (from jinja2->torch>=1.9->optimum) (2.1.3)
Requirement already satisfied: pytz>=2020.1 in ./venv/lib/python3.10/site-packages (from pandas->datasets->optimum) (2023.3.post1)
Requirement already satisfied: tzdata>=2022.1 in ./venv/lib/python3.10/site-packages (from pandas->datasets->optimum) (2023.3)
Requirement already satisfied: python-dateutil>=2.8.2 in ./venv/lib/python3.10/site-packages (from pandas->datasets->optimum) (2.8.2)
Requirement already satisfied: six>=1.5 in ./venv/lib/python3.10/site-packages (from python-dateutil>=2.8.2->pandas->datasets->optimum) (1.16.0)
