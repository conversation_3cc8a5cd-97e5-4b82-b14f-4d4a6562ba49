import time
import os
import json
from services.open_ai_service import <PERSON><PERSON><PERSON><PERSON>
from services.claude_ai_service import ClaudeService
import asyncio
import re

class SpecsComplyReasoning:
    def __init__(self):
        self.open_ai_manager = AssistantManager()
        self.claude_client = ClaudeService()
                     
    async def get_claude_to_summarize_evaluate_v2(self, log, steps=3):
        print("Generating AI response for text formatting claude scp...")
        
        messages = [
            {
                "role": "user",
                "content": f'''
                    <UPLOADED DOCUMENT>
                        {log['evaluate_summary_processing']}
                    </UPLOADED DOCUMENT>
                    <PROJECT_TITLE>
                        {log['project_title']}
                    </PROJECT_TITLE>
                    <DOCUMENT_TITLE>
                        {log['document_title']}
                    </DOCUMENT_TITLE>
                    <DOCUMENT_NUMBER>
                        {log['document_number']}
                    </DOCUMENT_NUMBER>
                    <ROLE>
                        1.) You are an Industrial enginner helpful assistant to do summarization of the above evaluation
                    </ROLE>
                    <SUMMARIZATION RULE>
                         - Provide it with proper formatting in HTML snippet wrapped in a div tag, exclude everything else other than the html snippet, it must start with a div tag.
                         - Title should be size 18
                         - Section headers should be size 15
                         - body text should be 11- 12 size
                         - No text should be bold
                         - Do not mention the comformity score i.e either of non-compliance, Minimal Compliance, full compliance etc.
                         - The report should strictly follow the below format.
                        <SCORE INTERPRETATION>
                            - Use the following score criteria for evaluation:
                            code 1: Approved (95%- 100% score)
                            code 2: Accepted with comments (between 80-95%)
                            code 3: Reviewed with comments (between 60-79%)
                            code 4: Rejected (score < 59%)
                        </SCORE INTERPRETATION>
                        <IMPORTANT>
                            - Do not include any additional text, explanations, or preambles in your response.
                            - Only return the reformatted text. No headings, context, or introductory phrases like "Here is the reformatted text."
                        </IMPORTANT>
                        <OUTPUT_FORMAT>
                            Only return the formatted text, nothing else.
                        </OUTPUT_FORMAT>
                        <EXAMPLE>
                            <div>
                                <h2 style="font-size: 18px; font-weight: bold;">Project Title: Pump Replacement Project</h2>
                                <h3 style="font-size: 18px; font-weight: bold;">Document Title: Specifications for API 610 Centifugal Pump</h3>
                                <h4 style="font-size: 18px; font-weight: bold;">Document Number and Revision: 34-06-12345-Specifications API Pump - rev0</h4>
                                <h4 style="font-size: 18px; font-weight: bold;"><DOC_SUMMARY_CODE>code 3</DOC_SUMMARY_CODE></h4>  
                                <p><DOC_SUMMARY>The Specifications for API 610 Centifugal Pump Document document no. 34-06-12345-Specifications API Pump -rev0 was reviewed against the project document specifications document reference 34-06409-1092 and industry standards, including API Standard 610: Centrifugal Pumps for Petroleum, Petrochemical, and Natural Gas Industries and ASME B73.1: Specification for Horizontal End Suction Centrifugal Pumps for Chemical Process codes and other applicable regulations. While the document demonstrates a commendable level of detail, several critical non-compliance findings were identified during the analysis, necessitating corrective actions to ensure compliance and performance. Refer to the details below for further references. The document revision cannot be accepted and should be returned as Code 3 "Rejected". Contractor is kindly requested to address the identified issues and prepare a revised version of the document for resubmission at the earliest convenience. The revised document should incorporate all necessary corrections and updates to ensure compliance with the original project document specifications and any additional feedback provided.</DOC_SUMMARY></p>
                            </div>
                        </EXAMPLE>
                        <EXAMPLE>
                            <div>
                                <h2 style="font-size: 18px;">Project Title: Quality Improvement Plan</h2>
                                <h3 style="font-size: 18px;">Document Title: Process Optimization Strategy</h3>
                                <h4 style="font-size: 18px;">Document Number and Revision: 21-04-56789-Optimization Strategy - rev1</h4>
                                <h4 style="font-size: 18px;"><DOC_SUMMARY_CODE>code 1</DOC_SUMMARY_CODE></h4>  
                                <p><DOC_SUMMARY>The Process Optimization Strategy document no. 21-04-56789-Optimization Strategy -rev1 was thoroughly evaluated against the project specifications and industry best practices. The document provides a comprehensive and detailed approach to process optimization, meeting all necessary criteria and standards. It is well-organized and clearly articulated, with no major issues identified. The document has been reviewed and found to be fully compliant with the project requirements and industry standards. The document is approved for implementation without any further revisions.</DOC_SUMMARY></p>
                            </div>
                        </EXAMPLE>
                        <EXAMPLE>
                            <div>
                                <h2 style="font-size: 18px;">Project Title: Equipment Calibration</h2>
                                <h3 style="font-size: 18px;">Document Title: Calibration Procedures for Measuring Instruments</h3>
                                <h4 style="font-size: 18px;">Document Number and Revision: 18-02-34567-Calibration Procedures - rev2</h4>
                                <h4 style="font-size: 18px;"><DOC_SUMMARY_CODE>code 4</DOC_SUMMARY_CODE></h4>  
                                <p><DOC_SUMMARY>The Calibration Procedures document no. 18-02-34567-Calibration Procedures -rev2 was reviewed against the project specifications and relevant standards. The document fails to meet the necessary criteria and standards due to several significant deficiencies, including incomplete procedures and lack of critical calibration details. These issues impact the reliability and accuracy of the calibration processes outlined in the document. As a result, the document is not acceptable and is classified as Code 4 "Rejected". The contractor is requested to address the identified issues thoroughly and submit a revised version that fully complies with the required standards and specifications.</DOC_SUMMARY></p>
                            </div>
                        </EXAMPLE>
                    </SUMMARIZATION RULE> 
                    '''
            }
        ]
        while steps > 0:
            steps -= 1
            try:
                completion = await self.claude_client.generate_message(
                    messages=messages,
                    temperature=0.001,
                    model="Claude 3 Sonnet"
                )
                if completion and hasattr(completion.content[0], 'text'):
                    response = completion.content[0].text
                    # token_count_in = completion.usage.input_tokens
                    # token_count_out = completion.usage.output_tokens
                    break
                else:
                    raise ValueError("Invalid response received")
            except Exception as e:
                print(e)
                await asyncio.sleep(10)
        
        # log['input_token'] += token_count_in
        # log['output_token'] += token_count_out
        log['evaluation_report_section']['introduction'] = response
        log['evaluate_summary_code'] = self.extract_code(response)
        log['evaluate_summary_intro'] = self.extract_doc_summary(response)
      

    async def get_claude_to_give_non_compliance_comment(self, text, criteria, count, log, steps=2):
        print('now giving non_compliance comment....')
        # print('text: ', text)
        print('criteria is: ', criteria)
        print('count: ', count - 1)
        messages = [
            {
                "role": "user",
                "content": f'''
                    <REPORT>
                        <Count>{count}</Count>
                        <Criteria>{criteria}</Criteria>
                            {text}
                        <PROJECT_TITLE>
                            {log['project_title']}
                        </PROJECT_TITLE>
                        <DOCUMENT_TITLE>
                            {log['document_title']}
                        </DOCUMENT_TITLE>
                        <DOCUMENT_NUMBER>
                            {log['document_number']}
                        </DOCUMENT_NUMBER>
                    </REPORT>
                    <ROLE>
                        1.) You are an Industrial enginner helpful assistant to do detailed document review of the above report based on the reason & the reference.
                    </ROLE>
                    <RULE>
                         - Provide it with proper formatting in HTML snippet wrapped in a div tag, exclude everything else other than the html snippet, it must start with a div tag.
                         - Title should be size 18
                         - Section headers should be size 15 pt
                         - body text should be 11- 12 size pt
                         - No text should be bold
                         - The detailed review should be just one detailed paragraph.
                         - The reference should also be just one paragraph same as the actual reference given.
                         - Do not use the term UPLOADED_DOCUMENT instead use the document title itself.
                         - Do not use the term SOURCE_DOCUMENT instead use the Project title.
                         - The report should strictly follow the below format whereby we have the detailed review wrapped in DETAILED_REVIEW tag &  reference wrapped between REFERENCE tag:
                            <DETAILED_REVIEW>
                            </DETAILED_REVIEW>
                            <REFERENCE>
                            </REFERENCE>
                         - The title is gotten from the count & criteria example the below is gotten from <Count>1</Count><Criteria>section 4.2 page 16 design life</Criteria>
                    </RULE>
                    <EXAMPLE>
                        <DETAILED_REVIEW>
                            1. section 4.2 page 16 design life: The submitted centrifugal pump design specification proposes a service life of 25 years, which falls short of the specified minimum design service life requirement of thirty (30) years outlined in the project specification. Additionally, the minimum continuous operation period without the need for shutdown is stated as three (3) years, rather than the required minimum of five (5) years. These deviations from the specified requirements raise concerns regarding the longevity and reliability of the equipment under operational conditions. Further documentation or justification is necessary to ensure compliance with the specified minimum design service life and continuous operation period requirements.
                        </DETAILED_REVIEW>
                        <REFERENCE>
                            Section 4.2 Page 16 - Design Life
                            Within the scope of our engineering and manufacturing excellence, the centrifugal pump has been engineered with a keen focus on durability and operational longevity, achieving a service life of 25 years. This specification underscores our commitment to providing high-quality, durable solutions that support the long-term objectives of our clients. Recognizing the challenges and demands of the operational environments these pumps are destined for, the design incorporates advanced materials and engineering techniques aimed at reducing wear and tear, thereby extending the operational life of the equipment. Additionally, the design facilitates a minimum continuous operation period of three (3) years, a feature meticulously developed to ensure that our clients benefit from reduced maintenance requirements and uninterrupted service. This operational characteristic is vital for ensuring that workflow efficiencies are maximized, and operational costs are minimized, reflecting our dedication to enhancing the value and reliability of our products within their intended application environments.
                        </REFERENCE>
                    </EXAMPLE>
                    <EXAMPLE>
                        <DETAILED_REVIEW>
                            2 Attachment 2 page 34 Environemental Data: The proposed material specifications do not align with the environmental data provided for Canada, especially concerning minimum temperature requirements. For instance, the specified environmental parameters for onshore engineering projects in Canada include a minimum recorded temperature of -35°C, while the proposed material specifications overlook this critical threshold, proposing a minimum temperature of 0°C. This discrepancy raises significant concerns regarding the materials' ability to withstand extreme conditions and maintain performance and integrity. It is imperative to revise the material specifications to align with the provided environmental data, ensuring suitability and durability for the intended application in Canada.  
                        </DETAILED_REVIEW>
                        <REFERENCE>
                            Attachment 2 page 34 - Material Specifications for Canadian Climate Conditions
                            Environmental Compatibility and Performance
                            Our centrifugal pump design has been meticulously engineered to excel in various operational environments, with a specific focus on the challenging climate conditions found in Canada. Understanding the critical nature of environmental adaptability, our specifications are tailored to ensure unparalleled durability and performance, even in extreme weather conditions.
                        </REFERENCE>
                    </EXAMPLE>
                    <EXAMPLE>
                        <DETAILED_REVIEW>
                            3. Transport and lifting drawings: the submitted documentation lacks the required section covering Transport and Lifting Drawings, as stipulated in the code. Specifically, the final transportation and lifting drawings, load test certificates for lifting equipment, spreader bars, slings, davits, and shackles, along with weights and "centre of gravity" diagrams are absent. Additionally, the submission fails to include one copy each of the installation and operation manual and the preservation instruction book for each equipment/package, as mandated. This non-conformity poses a risk to the efficient and safe handling, installation, and operation of the equipment. Immediate action is necessary to rectify this deficiency and ensure compliance with the specified requirements.  
                        </DETAILED_REVIEW>
                        <REFERENCE>
                            Minimum Temperature Compliance:

                            Specified Environmental Parameters: In anticipation of the demanding Canadian winters, our engineering approach has integrated material specifications designed to withstand significant temperature variations. Specifically, we have identified a minimum operational temperature threshold of 0°C, based on our comprehensive analysis of material resilience and performance metrics.
                            Rationale and Engineering Insights: This specification is underpinned by our commitment to ensuring operational integrity and reliability under a wide range of environmental conditions. Our selection of materials and engineering techniques are the results of rigorous testing and evaluation, aiming to balance cost-efficiency with the highest standards of environmental compatibility.
                            Section 6.2.9: Performance Test Specifications
                            Adherence to Industry Standards and Project Requirements

                            Our centrifugal pumps are designed to meet and exceed the operational demands of our clients, with performance testing being a cornerstone of our quality assurance process. We adhere strictly to industry standards, ensuring that our equipment delivers reliable and efficient service throughout its lifespan.
                        </REFERENCE>
                    </EXAMPLE> 
                '''
            }    
        ]
        while steps > 0:
            steps -= 1
            try:
                completion = await self.claude_client.generate_message(
                    messages=messages,
                    temperature=0.001,
                    model="Claude 3 Sonnet"
                )
                print(f"this is completion of criteria {count - 1}:", completion)
                if completion and hasattr(completion.content[0], 'text'):
                    response = completion.content[0].text
                    print('now here after response...')
                    log['evaluation_report_section']['evaluation_report'][count-1] = response
                    log['evaluation_report_section']['engr_comment'][count-1] = self.extract_detailed_review(response)
                    print('returning.....')
                    return 
                else:
                    raise ValueError("Invalid response received")
            except Exception as e:
                print(e)
                await asyncio.sleep(2)

    def get_reasoning_summary(self, key_points, criteria, count, log={}):
        print("reasoning summary scp......")
        concatenated_criteria = f"criteria {count}: Title: {criteria['name']} \n Description: {criteria['description']}\n"
        messages=[
            {"role": "system", "content": f'''You are a helpful assistant to do summarization.'''},
            {"role": "user", "content": f'''
            STEP1: Before giving summary 
             - Give a stragith forward answer. example: below
                ANS: The answer is YES, The section 3 of the Uploaded document contains the roles of the key personnel at site, including the HSE roles. 
             - show the answer maybe the document is conforming, non-compliance or neutral based on your evaluation of all the points.
            EXAMPLE BELOW:
            <div>
                <h3> Does the Bidder have an emergency response plan ?</h3>
                <h5> ANS: The answer is YES, The section 3 of the Uploaded document contains the roles of the key personnel at site, including the HSE roles. </h5>
                <h3>Document Conformity: Compliance(✓) </h3>
                <h3>Document Score: 40%</h3>
                <h2>Summary of Key Findings:</h2>
                
                <h3>Compliances (✓):</h3>
                <p>compliance_summary</p>
                
                <h3>Non-Compliances (✗):</h3>
                <p>non_compliance_summary</p>
                
                <h3>Neutral Factors (*):</h3>
                <p>neutral_summary</p>
            </div>
            STEP2: Summarize the key findings based on the compliances (✓), non-compliances (X) and neutral(*) factors. 
            Summarization should be in three paragraphs for each of compliances, non compliances and neutral factors. The contents of the paragraphs should be a top view of the key findings rather than mentioning all the points.
            Provide it with proper formatting in HTML snippet wrapped in a div tag, exclude everything else other than the html snippet, it must start with a div tag.
    
            STEP3: Your results should be for the each of the criterias you have.
                Example if you have Criteria1 & Criteria 2 then Evaluation Summary has to be done for each of the criteria.
                
            STEP4: The document Score will be based on all points score, so if the document score should be 30, then the 30 has to be dervied based on the evaluation gotten from all the points given.
            
            {key_points}
            
            CRITERIA:
            {concatenated_criteria}
    
            **Important:**
                - Also lookout for any suggestions, examples or any form of statement that supportts the criteria.
                - We need every single statement no matter how small that could help the criteria.
                - Also point to which part of the document it is.  
                  
            '''
            },
        ]

        log["reasoning_summary_prompt"] = messages[1]["content"]

        resp = self.open_ai_manager.get_response(messages, 0.01, "gpt-3.5-turbo-16k")
        log["reasoning_summary_prompt_token"] = resp.usage.prompt_tokens
        log["reasoning_summary_response_token"] =  resp.usage.completion_tokens

        resp = resp.choices[0].message.content

        log["reasoning_summary_response"] = resp

        return resp

    def extract_detailed_review(self, text):
        match = re.search(r'<DETAILED_REVIEW>(.*?)</DETAILED_REVIEW>', text, re.DOTALL)
        if match:
            return match.group(1).strip()
        return None

    def extract_doc_summary(self, content: str):
        match = re.search(r"<DOC_SUMMARY>(.*?)</DOC_SUMMARY>", content, re.DOTALL)
        return match.group(1).strip() if match else ""
    
    def extract_code(self, content: str):
        match = re.search(r"<DOC_SUMMARY_CODE>(.*?)</DOC_SUMMARY_CODE>", content, re.DOTALL)
        return match.group(1).strip() if match else ""