import asyncio

from extensions import celery
from socket_instance import socket_instance
from services.handle_new_cbp_documents import ChronobidDocumentProcessor

@celery.task(bind=True, max_retries=3, name="task_celery.file_evaluator_cbp_tasks.evaluate_files_cbp_task")
def evaluate_files_cbp_task(self, req_id):
    try:
        # Get the socket manager instance and emit a test event
        
        socket_manager = socket_instance.get_instance()
        socket_manager.emit_to_client(
            req_id,
            'test_event',
            {'message': 'Test from CBP Celery'},
            namespace='/cbp'
        )
        # Instantiate your document processor
        cbp_loader = ChronobidDocumentProcessor(socket_manager)
        cbp_loader.process_cbp(req_id)
        
        print('Starting to evaluate CBP files...')
        return "CBP File evaluation started."
    except Exception as e:
        print(f"Error during CBP file evaluation: {e}")
        return str(e)
