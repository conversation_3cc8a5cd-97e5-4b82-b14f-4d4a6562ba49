from init import db,app
import uuid
from sqlalchemy import or_

from sqlalchemy import Column, String, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ForeignKey
from sqlalchemy.orm import relationship

# # Create tables if they do not exist
# with app.app_context():
#     db.create_all()


class Project(db.Model):
    id = Column(String(36), primary_key=True)  # Specify length for VARCHAR
    name = Column(String(255), nullable=False)  # Specify length for VARCHAR
    entity_type = Column(String(100), nullable=True)  # Specify length for VARCHAR
    assistant_id = Column(String(36), nullable=True)  # Specify length for VARCHAR
    discipline_code = Column(String(50), nullable=True)  # Specify length for VARCHAR
    has_deleted_file = Column(Boolean, nullable=False, default=False)

    files = relationship('File', back_populates='project')
    merged_files = relationship('Merged_file', back_populates='project')

    @property
    def serialize(self):
        return {
            "id": self.id,
            "name": self.name,
            "entity_type": self.entity_type,
            "files": [file.serialize for file in self.files],
            "discipline_code": self.discipline_code,
            "merged_files": [merged_file.serialize for merged_file in self.merged_files],
            "assistant_id": self.assistant_id,
            "has_deleted_file": self.has_deleted_file
        }
    
    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize
    
    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [project.serialize for project in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []
    
    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def get_one_by(cls, **kwargs):
        try:
            return db.session.query(cls).filter_by(**kwargs).first()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def create(cls, **kwargs):
        try:
            project = cls(**kwargs)
            db.session.add(project)
            db.session.commit()
            return project.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def update(cls, id, **kwargs):
        try:
            project = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(project, key, value)
            db.session.commit()
            return project.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None


class File_merged_file_association(db.Model):
    __tablename__ = 'file_merged_file_association'
    
    id = Column(String(36), primary_key=True)  # Specify length for VARCHAR
    file_id = Column(String(36), ForeignKey('file.id'))  # Specify length for VARCHAR
    merged_file_id = Column(String(36), ForeignKey('merged_file.id'))  # Specify length for VARCHAR
    merge_start_index = Column(Integer, nullable=True)
    merge_end_index = Column(Integer, nullable=True)

    file = relationship('File', back_populates='associations')
    merged_file = relationship('Merged_file', back_populates='associations')

    # Define relationships with File and Merged_file tables if necessary
    # file = db.relationship('File', back_populates='file_associations')
    # merged_file = db.relationship('Merged_file', back_populates='merged_file_associations')

    @property
    def serialize(self):
        return {
            "id": self.id,
            "file_id": self.file_id,
            "merged_file_id": self.merged_file_id,
            "merge_start_index": self.merge_start_index,
            "merge_end_index": self.merge_end_index
        }

    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [x.serialize for x in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []

    @classmethod
    def create(cls, file_id, merged_file_id):
        try:
            id = str(uuid.uuid4())
            association = cls(id=id, file_id=file_id, merged_file_id=merged_file_id)
            db.session.add(association)
            db.session.commit()
            return association
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def update(cls, file_id, merged_file_id, **kwargs):
        try:
            association = cls.query.filter_by(file_id=file_id, merged_file_id=merged_file_id).first()
            if association:
                for key, value in kwargs.items():
                    setattr(association, key, value)
                db.session.commit()
                return association
            else:
                return None
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def delete_by(cls, **kwargs):
        try:
            association = cls.query.filter_by(**kwargs).first()
            if association:
                db.session.delete(association)
                db.session.commit()
                return True
            else:
                return False
        except Exception as e:
            print(e)
            db.session.rollback()
            return False

    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [file.serialize for file in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []


class Merged_file(db.Model):
    id = db.Column(db.String, primary_key=True)
    project_id = db.Column(db.String, db.ForeignKey('project.id'), nullable=False)
    assistant_file_id = db.Column(db.String)
    status = db.Column(db.String)
    
    project = db.relationship('Project', back_populates='merged_files')
    files = db.relationship('File', secondary='file_merged_file_association', back_populates='merged_files')

    @property
    def serialize(self):
        return {
            "id": self.id,
            "project_id": self.project_id,
            "assistant_file_id": self.assistant_file_id,
            "status": self.status,
            "files": [file.serialize_basic for file in self.files]
        }
    @property
    def serialize_basic(self):
        return {
            "id": self.id,
            "project_id": self.project_id,
            "assistant_file_id": self.assistant_file_id,
            "status": self.status,
        }

    @classmethod
    def get_single(cls, id):
        try:
            return db.session.query(cls).filter_by(id=id).first().serialize
        except Exception as e:
            print(e)
            return None

    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [merged_file.serialize for merged_file in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []
    
    @classmethod
    def get_by_prefix(cls, prefix):
        try:
            records = db.session.query(cls).filter(cls.id.like(f'{prefix}%')).all()
            return [record.serialize for record in records]
        except Exception as e:
            print(e)
            return []

    @classmethod
    def delete_all(cls):
        try:
            db.session.query(cls).delete()
            db.session.commit()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def get_one_by(cls, **kwargs):
        try:
            return db.session.query(cls).filter_by(**kwargs).first()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def create(cls, **kwargs):
        try:
            merged_file = cls(**kwargs)
            db.session.add(merged_file)
            db.session.commit()
            return merged_file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def update(cls, id, **kwargs):
        try:
            merged_file = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(merged_file, key, value)
            db.session.commit()
            return merged_file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None


class File(db.Model):
    id = db.Column(db.String, primary_key=True)
    name = db.Column(db.String, nullable=False)
    project_id = db.Column(db.String, db.ForeignKey('project.id'), nullable=False)
    file_type = db.Column(db.String, nullable=False)
    file_dirtry = db.Column(db.String, nullable=False, default='qmp')
    category = db.Column(db.String, nullable=True)
    status = db.Column(db.String, nullable=True, default='idle')
    merge_status = db.Column(db.String, default='not_merged')
    tried = db.Column(db.Integer, default= 0)
    
    

    project = db.relationship('Project', back_populates='files')
    # file_associations = db.relationship('File_merged_file_association', back_populates='file')
    merged_files = db.relationship('Merged_file', secondary='file_merged_file_association', back_populates ='files')
    
    @property
    def serialize(self):
        return {
           "id": self.id,
           "name": self.name,
           "project_id": self.project_id,
           "file_type": self.file_type,
           "file_dirtry": self.file_dirtry,
           "category": self.category,
           "status": self.status,
           "merged_files": [merged_file.serialize_basic for merged_file in self.merged_files],
           "tried": self.tried,
           "merge_status": self.merge_status
        }
    @property
    def serialize_basic(self):
        return {
           "id": self.id,
           "name": self.name,
           "project_id": self.project_id,
           "file_type": self.file_type,
           "file_dirtry": self.file_dirtry,
           "category": self.category,
           "status": self.status,
           "tried": self.tried,
           "merge_status": self.merge_status
        }
    
    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize
    
    @classmethod
    def get_by(cls, **kwargs):
        try:
            # print(kwargs)
            return [file.serialize for file in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []
    
    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None

    @classmethod
    def create(cls, **kwargs):
        try:
            file = cls(**kwargs)
            db.session.add(file)
            db.session.commit()
            return file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def update(cls, id, **kwargs):
        try:
            file = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(file, key, value)
            db.session.commit()
            return file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None


class Chunks(db.Model):
    id = Column(String(36), primary_key=True)  # Specify length for VARCHAR
    title = Column(String(255), nullable=False)  # Specify length for VARCHAR
    content = Column(Text, nullable=False)  # Text does not need length
    page = Column(String(50), nullable=True)  # Specify length for VARCHAR
    source = Column(String(100), nullable=True)  # Specify length for VARCHAR
    summary = Column(Text, nullable=True)  # Text does not need length

    project_id = Column(String(36), nullable=False)  # Specify length for VARCHAR
    file_id = Column(String(36), nullable=False)  # Specify length for VARCHAR

    tags = relationship('Tags', back_populates='chunks')

    @property
    def serialize(self):
        return {
            "id": self.id,
            "title": self.title,
            "content": self.content,
            "page": self.page,
            "source": self.source,
            "summary": self.summary,
            "tags": [tag.serialize for tag in self.tags],
            "project_id": self.project_id,
            "file_id": self.file_id,
        }

    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize

    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [chunk.serialize for chunk in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []


    @classmethod
    def create(cls, **kwargs):
        try:
            chunk = cls(**kwargs)
            db.session.add(chunk)
            db.session.commit()
            return chunk.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def update(cls, id, **kwargs):
        try:
            chunk = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(chunk, key, value)
            db.session.commit()
            return chunk.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None

class Tags(db.Model):
    id = Column(String(36), primary_key=True)  # Specify length for VARCHAR
    name = Column(String(255), nullable=False)  # Specify length for VARCHAR
    chunk_id = Column(String(36), ForeignKey('chunks.id'), nullable=False)  # Specify length for VARCHAR

    chunk = relationship('Chunks', back_populates='tags')

    def __init__(self, name, chunk_id):
        self.name = name
        self.chunk_id = chunk_id

    @property
    def serialize(self):
        return {
            "id": self.id,
            "name": self.name,
            "chunk_id": self.chunk_id
        }

    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize

    @classmethod
    def get_by(cls, **kwargs):
        try:
            return [tag.serialize for tag in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []

    @classmethod
    def create(cls, **kwargs):
        try:
            tag = cls(**kwargs)
            db.session.add(tag)
            db.session.commit()
            return tag.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

    @classmethod
    def update(cls, id, **kwargs):
        try:
            tag = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(tag, key, value)
            db.session.commit()
            return tag.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None

class Requirement(db.Model):
    id = Column(String(36), primary_key=True)  # Specify length for VARCHAR
    name = Column(String(255), nullable=False)  # Specify length for VARCHAR
    project_id = Column(String(36), ForeignKey('project.id'), nullable=False)  # Specify length for VARCHAR
    file_type = Column(String(50), nullable=False)  # Specify length for VARCHAR
    category = Column(String(50), nullable=True)  # Specify length for VARCHAR
    status = Column(String(50), nullable=True, default='idle')  # Specify length for VARCHAR
    tried = Column(Integer, default=0)
    score = Column(Integer, nullable=True)
    criteria = Column(String(255), nullable=True)  # Specify length for VARCHAR
    
    
    @property
    def serialize(self):
       return {
           "id": self.id,
           "name": self.name,
           "project_id": self.project_id,
           "file_type": self.file_type,
           "category": self.category,
           "status": self.status,
           "tried": self.tried,
           "score": self.score,
           "criteria": self.criteria
       }

    @classmethod
    def get_single(cls, id):
        return db.session.query(cls).filter_by(id=id).first().serialize
    
    @classmethod
    def get_by(cls, **kwargs):
        try:
            # print(kwargs)
            return [file.serialize for file in db.session.query(cls).filter_by(**kwargs).all()]
        except Exception as e:
            print(e)
            return []
    
    @classmethod
    def get_scp_by(cls, **kwargs):
        try:
            query = db.session.query(cls).filter_by(**kwargs)
            # Add an additional filter to exclude records with an empty category field
            query = query.filter(or_(cls.criteria.is_(None), cls.criteria == ''))
            return [file.serialize for file in query.all()]
        except Exception as e:
            print(e)
            return []
    
    @classmethod
    def get_by_not_null_criteria(cls, **kwargs):
        try:
            query = db.session.query(cls)
    
            # Apply filter criteria where 'criteria' is not null or empty
            query = query.filter(or_(cls.criteria != None, cls.criteria != ''))
    
            # Apply additional filter criteria provided in kwargs
            if kwargs:
                query = query.filter_by(**kwargs)
    
            # Retrieve records and return serialized objects
            return [file.serialize for file in query.all()]
    
        except Exception as e:
            print(e)
            return []

    @classmethod
    def get_by_null_criteria(cls, **kwargs):
        try:
            # Query all records where 'criteria' is null or empty
            return [file.serialize for file in db.session.query(cls).filter(or_(cls.criteria == None, cls.criteria == '')).all()]
        
        except Exception as e:
            print(e)
            return []

    @classmethod
    def create(cls, **kwargs):
        try:
            file = cls(**kwargs)
            db.session.add(file)
            db.session.commit()
            return file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def update(cls, id, **kwargs):
        try:
            file = db.session.query(cls).filter_by(id=id).first()
            for key, value in kwargs.items():
                setattr(file, key, value)
            db.session.commit()
            return file.serialize
        except Exception as e:
            print(e)
            db.session.rollback()
            return None
    
    @classmethod
    def delete_by(cls, **kwargs):
        try:
            db.session.delete(db.session.query(cls).filter_by(**kwargs).first())
            db.session.commit()
        except Exception as e:
            print(e)
            return None
