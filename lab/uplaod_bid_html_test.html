// File Upload Socket Test - Improved Dev Tools Version
// Paste this into your browser's dev tools console to create the test interface

(function() {
    // First, let's clear any existing socket connections
    if (window.socketTestCleanup) {
        window.socketTestCleanup();
    }
    
    // Load Socket.IO if not already loaded
    if (typeof io === 'undefined') {
        console.log('Loading Socket.IO library...');
        const script = document.createElement('script');
        script.src = 'https://cdn.socket.io/4.5.4/socket.io.min.js';
        script.onload = function() {
            console.log('Socket.IO loaded successfully');
            initializeApp();
        };
        script.onerror = function() {
            console.error('Failed to load Socket.IO library');
            document.body.innerHTML = '<div style="color: red; padding: 20px;">Failed to load Socket.IO library. Please check your internet connection.</div>';
        };
        document.head.appendChild(script);
    } else {
        console.log('Socket.IO already available');
        initializeApp();
    }

    function initializeApp() {
        // Clear the page and add our content
        document.body.innerHTML = '';
        document.title = 'File Upload Socket Test';
        
        // Add styles
        const styleElement = document.createElement('style');
        styleElement.textContent = `
            body {
                font-family: Arial, sans-serif;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .container {
                display: flex;
                flex-direction: column;
                gap: 20px;
            }
            .card {
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 20px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                background-color: white;
            }
            .controls {
                display: flex;
                gap: 10px;
                align-items: center;
                flex-wrap: wrap;
            }
            input, button {
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 14px;
            }
            input {
                border: 1px solid #ccc;
                flex-grow: 1;
                min-width: 200px;
            }
            button {
                background-color: #4CAF50;
                color: white;
                border: none;
                cursor: pointer;
                transition: background-color 0.3s;
            }
            button:hover:not(:disabled) {
                background-color: #45a049;
            }
            button:disabled {
                background-color: #cccccc;
                cursor: not-allowed;
            }
            #messages {
                max-height: 400px;
                overflow-y: auto;
                border: 1px solid #eee;
                border-radius: 4px;
                padding: 10px;
            }
            .message {
                margin-bottom: 10px;
                padding: 10px;
                border-radius: 4px;
                word-wrap: break-word;
            }
            .info {
                background-color: #e7f3fe;
                border-left: 4px solid #2196F3;
            }
            .success {
                background-color: #ddffdd;
                border-left: 4px solid #4CAF50;
            }
            .error {
                background-color: #ffdddd;
                border-left: 4px solid #f44336;
            }
            .warning {
                background-color: #fff3cd;
                border-left: 4px solid #ffc107;
            }
            .progress-container {
                width: 100%;
                background-color: #f1f1f1;
                border-radius: 4px;
                margin-top: 10px;
                height: 30px;
                position: relative;
            }
            .progress-bar {
                height: 100%;
                background-color: #4CAF50;
                border-radius: 4px;
                text-align: center;
                color: white;
                line-height: 30px;
                transition: width 0.3s ease;
                font-weight: bold;
            }
            .timestamp {
                color: #666;
                font-size: 0.8em;
                margin-top: 5px;
            }
            .connection-details {
                font-family: monospace;
                background-color: #f8f9fa;
                padding: 10px;
                border-radius: 4px;
                margin-top: 10px;
            }
            .debug-info {
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 4px;
                padding: 10px;
                margin-top: 10px;
                font-family: monospace;
                font-size: 12px;
            }
        `;
        document.head.appendChild(styleElement);
        
        // Create HTML structure
        const container = document.createElement('div');
        container.className = 'container';
        container.innerHTML = `
            <div class="card">
                <h1>File Upload Socket Test</h1>
                <p>Enter a request ID to connect to the file upload socket and monitor progress messages.</p>
                
                <div class="controls">
                    <input type="text" id="serverUrl" placeholder="Server URL (e.g., wss://your-server.com)" value="">
                    <input type="text" id="namespace" placeholder="Namespace (e.g., /file_upload)" value="/file_upload">
                    <input type="text" id="requestId" placeholder="Enter Request ID">
                    <button id="connectBtn">Connect</button>
                    <button id="disconnectBtn" disabled>Disconnect</button>
                    <button id="testBtn" disabled>Send Test Message</button>
                </div>
                
                <div class="connection-details">
                    <strong>Connection Info:</strong>
                    <div id="connectionInfo">Not connected</div>
                </div>
            </div>
            
            <div class="card">
                <h2>Connection Status</h2>
                <div id="status" class="message info">Not connected</div>
                
                <h2>Overall Progress</h2>
                <div class="progress-container">
                    <div id="progressBar" class="progress-bar" style="width:0%">0%</div>
                </div>
            </div>
            
            <div class="card">
                <h2>Messages <span id="messageCount">(0)</span></h2>
                <div style="margin-bottom: 10px;">
                    <button id="clearBtn">Clear Messages</button>
                    <button id="exportBtn">Export Messages</button>
                </div>
                <div id="messages"></div>
            </div>
            
            <div class="card">
                <h2>Debug Information</h2>
                <div id="debugInfo" class="debug-info">
                    Socket.IO Version: ${io.version || 'Unknown'}<br>
                    Browser: ${navigator.userAgent}<br>
                    Protocol: ${window.location.protocol}
                </div>
            </div>
        `;
        
        document.body.appendChild(container);
        
        // DOM elements
        const serverUrlInput = document.getElementById('serverUrl');
        const namespaceInput = document.getElementById('namespace');
        const requestIdInput = document.getElementById('requestId');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const testBtn = document.getElementById('testBtn');
        const clearBtn = document.getElementById('clearBtn');
        const exportBtn = document.getElementById('exportBtn');
        const statusDiv = document.getElementById('status');
        const messagesDiv = document.getElementById('messages');
        const progressBar = document.getElementById('progressBar');
        const connectionInfo = document.getElementById('connectionInfo');
        const messageCount = document.getElementById('messageCount');
        const debugInfo = document.getElementById('debugInfo');
        
        // Socket variables
        let socket = null;
        let isConnected = false;
        let messageCounter = 0;
        let allMessages = [];
        
        // Set default server URL based on current page if it looks like a development server
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            serverUrlInput.value = `http://${window.location.hostname}:5130`;
        }
        
        // Connect to socket
        connectBtn.addEventListener('click', () => {
            const serverUrl = serverUrlInput.value.trim();
            const namespace = namespaceInput.value.trim() || '/';
            const requestId = requestIdInput.value.trim();
            
            if (!serverUrl) {
                addMessage('Please enter a server URL', 'error');
                return;
            }
            
            if (!requestId) {
                addMessage('Please enter a request ID', 'error');
                return;
            }
            
            // Disconnect existing socket if any
            if (socket) {
                addMessage('Disconnecting existing connection...', 'warning');
                socket.disconnect();
                socket = null;
            }
            
            // Update UI
            statusDiv.textContent = 'Connecting...';
            statusDiv.className = 'message info';
            connectBtn.disabled = true;
            connectionInfo.textContent = `Attempting to connect to ${serverUrl}${namespace}`;
            
            // Parse URL to determine if it's HTTP or HTTPS/WSS
            let socketUrl = serverUrl;
            if (!serverUrl.startsWith('http://') && !serverUrl.startsWith('https://') && !serverUrl.startsWith('ws://') && !serverUrl.startsWith('wss://')) {
                // Default to https if no protocol specified
                socketUrl = 'https://' + serverUrl;
            }
            
            addMessage(`Attempting to connect to: ${socketUrl}${namespace}`, 'info');
            
            // Connect to socket with better error handling
            try {
                socket = io(`${socketUrl}${namespace}`, {
                    transports: ['websocket', 'polling'],
                    reconnection: true,
                    reconnectionAttempts: 5,
                    reconnectionDelay: 1000,
                    reconnectionDelayMax: 5000,
                    maxReconnectionAttempts: 5,
                    timeout: 10000,
                    forceNew: true,
                    upgrade: true,
                    rememberUpgrade: true
                });
                
                // Socket event handlers
                socket.on('connect', () => {
                    isConnected = true;
                    statusDiv.textContent = 'Connected to server';
                    statusDiv.className = 'message success';
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                    testBtn.disabled = false;
                    
                    connectionInfo.innerHTML = `
                        Connected to: ${socketUrl}${namespace}<br>
                        Socket ID: ${socket.id}<br>
                        Transport: ${socket.io.engine.transport.name}
                    `;
                    
                    addMessage('✅ Connected to server successfully', 'success');
                    
                    // Join room with request ID
                    socket.emit('join', { request_id: requestId });
                    addMessage(`📡 Joining room with request ID: ${requestId}`, 'info');
                });
                
                socket.on('disconnect', (reason) => {
                    isConnected = false;
                    statusDiv.textContent = `Disconnected: ${reason}`;
                    statusDiv.className = 'message error';
                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                    testBtn.disabled = true;
                    
                    connectionInfo.textContent = 'Not connected';
                    
                    addMessage(`❌ Disconnected from server. Reason: ${reason}`, 'error');
                });
                
                socket.on('connect_error', (error) => {
                    statusDiv.textContent = `Connection error: ${error.message}`;
                    statusDiv.className = 'message error';
                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                    testBtn.disabled = true;
                    
                    connectionInfo.textContent = 'Connection failed';
                    
                    addMessage(`🚫 Connection error: ${error.message}`, 'error');
                    addMessage(`Error details: ${error.description || 'No additional details'}`, 'error');
                    
                    // Additional debugging info
                    updateDebugInfo(`Last error: ${error.message} at ${new Date().toISOString()}`);
                });
                
                socket.on('reconnect', (attemptNumber) => {
                    addMessage(`🔄 Reconnected after ${attemptNumber} attempts`, 'success');
                });
                
                socket.on('reconnect_attempt', (attemptNumber) => {
                    addMessage(`🔄 Reconnection attempt ${attemptNumber}...`, 'warning');
                });
                
                socket.on('reconnect_error', (error) => {
                    addMessage(`🔄 Reconnection failed: ${error.message}`, 'error');
                });
                
                socket.on('reconnect_failed', () => {
                    addMessage('🔄 Reconnection failed after maximum attempts', 'error');
                });
                
                // Listen for progress messages
                socket.on('progress_message', (data) => {
                    console.log('Received progress message:', data);
                    
                    let messageType = 'info';
                    if (data.data && data.data.status) {
                        if (data.data.status === 'success') messageType = 'success';
                        if (data.data.status === 'error') messageType = 'error';
                    }
                    
                    // Extract message
                    let message = '';
                    if (data.data && data.data.message) {
                        message = data.data.message;
                    } else if (data.message) {
                        message = data.message;
                    } else {
                        message = JSON.stringify(data, null, 2);
                    }
                    
                    addMessage(`📊 Progress: ${message}`, messageType);
                    
                    // Update progress bar if progress information is available
                    if (data.data && data.data.progress !== undefined) {
                        updateProgressBar(data.data.progress);
                    }
                });
                
                // Listen for completed event
                socket.on('completed_event', (data) => {
                    console.log('Received completed event:', data);
                    
                    let messageType = 'success';
                    if (data.data && data.data.status) {
                        if (data.data.status === 'error') messageType = 'error';
                        if (data.data.status === 'partial') messageType = 'warning';
                    }
                    
                    // Extract message
                    let message = '';
                    if (data.data && data.data.message) {
                        message = data.data.message;
                    } else if (data.message) {
                        message = data.message;
                    } else {
                        message = JSON.stringify(data, null, 2);
                    }
                    
                    addMessage(`✅ COMPLETED: ${message}`, messageType);
                    
                    // Show failed files if any
                    if (data.data && data.data.failed_files_messages && data.data.failed_files_messages.length > 0) {
                        addMessage('❌ Failed files:', 'error');
                        data.data.failed_files_messages.forEach(failedMsg => {
                            addMessage(`  • ${failedMsg}`, 'error');
                        });
                    }
                    
                    // Update progress bar to 100% on completion
                    updateProgressBar(100);
                });
                
                // Listen for error event
                socket.on('error_event', (data) => {
                    console.log('Received error event:', data);
                    
                    let message = '';
                    if (data.data && data.data.message) {
                        message = data.data.message;
                    } else if (data.message) {
                        message = data.message;
                    } else {
                        message = JSON.stringify(data, null, 2);
                    }
                    
                    addMessage(`❌ ERROR: ${message}`, 'error');
                });
                
                // Listen for test event
                socket.on('test_event', (data) => {
                    console.log('Received test event:', data);
                    
                    let message = '';
                    if (data.message) {
                        message = data.message;
                    } else {
                        message = JSON.stringify(data, null, 2);
                    }
                    
                    addMessage(`🧪 Test event: ${message}`, 'info');
                });
                
                // Listen for any other events
                socket.onAny((eventName, ...args) => {
                    if (!['connect', 'disconnect', 'connect_error', 'progress_message', 'completed_event', 'error_event', 'test_event', 'reconnect', 'reconnect_attempt', 'reconnect_error', 'reconnect_failed'].includes(eventName)) {
                        console.log(`Received unknown event: ${eventName}`, args);
                        addMessage(`🔍 Unknown event: ${eventName} - ${JSON.stringify(args)}`, 'warning');
                    }
                });
                
            } catch (error) {
                statusDiv.textContent = `Error: ${error.message}`;
                statusDiv.className = 'message error';
                connectBtn.disabled = false;
                connectionInfo.textContent = 'Connection failed';
                
                addMessage(`🚫 Error: ${error.message}`, 'error');
                updateDebugInfo(`Initialization error: ${error.message} at ${new Date().toISOString()}`);
            }
        });
        
        // Disconnect from socket
        disconnectBtn.addEventListener('click', () => {
            if (socket) {
                socket.disconnect();
                socket = null;
                
                statusDiv.textContent = 'Disconnected';
                statusDiv.className = 'message info';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                testBtn.disabled = true;
                connectionInfo.textContent = 'Manually disconnected';
                
                addMessage('🔌 Manually disconnected from server', 'info');
            }
        });
        
        // Send test message
        testBtn.addEventListener('click', () => {
            if (socket && isConnected) {
                const testData = {
                    test: true,
                    timestamp: new Date().toISOString(),
                    requestId: requestIdInput.value.trim()
                };
                socket.emit('test_message', testData);
                addMessage(`🧪 Sent test message: ${JSON.stringify(testData)}`, 'info');
            }
        });
        
        // Clear messages
        clearBtn.addEventListener('click', () => {
            messagesDiv.innerHTML = '';
            messageCounter = 0;
            allMessages = [];
            updateMessageCount();
            updateProgressBar(0);
            addMessage('🧹 Messages cleared', 'info');
        });
        
        // Export messages
        exportBtn.addEventListener('click', () => {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `socket-messages-${timestamp}.json`;
            const dataStr = JSON.stringify({
                exportTime: new Date().toISOString(),
                serverUrl: serverUrlInput.value,
                namespace: namespaceInput.value,
                requestId: requestIdInput.value,
                messages: allMessages
            }, null, 2);
            
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            link.click();
            URL.revokeObjectURL(url);
            
            addMessage(`📁 Messages exported to ${filename}`, 'success');
        });
        
        // Helper function to add a message to the messages div
        function addMessage(message, type = 'info') {
            messageCounter++;
            const timestamp = new Date();
            
            const messageObj = {
                id: messageCounter,
                message,
                type,
                timestamp: timestamp.toISOString()
            };
            allMessages.push(messageObj);
            
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const messageContent = document.createElement('div');
            messageContent.textContent = message;
            messageDiv.appendChild(messageContent);
            
            const timestampDiv = document.createElement('div');
            timestampDiv.className = 'timestamp';
            timestampDiv.textContent = timestamp.toLocaleTimeString();
            messageDiv.appendChild(timestampDiv);
            
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
            
            updateMessageCount();
        }
        
        // Helper function to update message count
        function updateMessageCount() {
            messageCount.textContent = `(${messageCounter})`;
        }
        
        // Helper function to update the progress bar
        function updateProgressBar(progress) {
            const percentage = Math.min(Math.max(progress, 0), 100);
            progressBar.style.width = `${percentage}%`;
            progressBar.textContent = `${Math.round(percentage)}%`;
        }
        
        // Helper function to update debug info
        function updateDebugInfo(additionalInfo) {
            const currentInfo = debugInfo.innerHTML;
            debugInfo.innerHTML = currentInfo + '<br>' + additionalInfo;
        }
        
        // Cleanup function for global scope
        window.socketTestCleanup = function() {
            if (socket) {
                socket.disconnect();
                socket = null;
            }
            console.log('Socket test cleaned up');
        };
        
        // Add initial message
        addMessage('🚀 Socket test interface initialized', 'success');
        
        console.log('File Upload Socket Test initialized successfully!');
        console.log('Tips:');
        console.log('- Make sure your server URL includes the protocol (http:// or https://)');
        console.log('- Check the browser console for additional debugging information');
        console.log('- Use the Export Messages button to save all received messages');
    }
})();