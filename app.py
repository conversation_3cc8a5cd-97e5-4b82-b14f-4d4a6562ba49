# import eventlet
# eventlet.monkey_patch(thread=False)

import gevent.monkey
gevent.monkey.patch_all()

from init import app, socketio, socket_manager
from extensions import db, queues
#from socket_instance import socket_instance

from flask import Flask, Response, request, jsonify, send_from_directory, send_file
from werkzeug.utils import secure_filename
from flask_socketio import SocketIO
from models import Project, File, Requirement, Prompt, BidFile, Bids, EquipmentPackage, EquipmentCategory, EquipmentCategorySpecs,CbpRequirementTechnicalReport, Transmittal_run, LLMUsage
from services.question_answer import QuestionAnswerer
from services.handle_new_qmp_query import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>and<PERSON>
from services.assistant_synchronization import AssistantSyncManager
from services.document_seeder import DocumentSeeder
from services.docx_to_pdf_v2 import DocxToPdf
from services.data_handler import DataManager
from services.file_merger import FileMerger
from services.docx_to_pdf_v2 import DocxToPdf
from services.file_processor_cbp import CBPFileProcessor
from services.file_processor_scp import SCPFileProcessor
from services.handle_cbp_report import GenerateReportCBP
from services.background_task_processor import BackgroundTaskProcessor
from services.engr_doc_types_detection import EngrDocTypesDetection
from services.parser_openai_parellel import OpenAIProcessor
# from services.handle_new_cbp_documents import ChronobidDocumentProcessor
# from services.handle_new_specs_documents import SpecsDocumentProcessor
# from task.file_evaluator_cbp_tasks import evaluate_files_cbp_task
# from task.file_evaluator_scp_tasks import evaluate_files_scp_task
# from task.file_upload_tasks import upload_files_task
# from task_redis.file_evaluator_cbp_tasks import evaluate_files_cbp_task
# from task_redis.file_evaluator_scp_tasks import evaluate_files_scp_task
# from task_redis.file_upload_tasks import upload_files_task
from task_redis.file_report_cbp_tasks import get_cbp_report_task
from namespaces.test_namespaces import TestNamespace
from namespaces.scp_namespaces import SCPNamespace
from namespaces.cbp_namespaces import CBPNamespace
from namespaces.qmp_namespaces import QMPNamespace
from namespaces.cbp_report import CBPReportNamespace
from namespaces.file_upload import FileUploadNamespace
from namespaces.file_processing_namespaces import ProcessingFilesNamespace
from namespaces.epc_namespaces import EPCNamespace
from namespaces.epo_namespaces import EPONamespace
from Reports.report import Reports
from DocumentNumber.documentnumber import DocumentNumbering
from services.faiss_embedding import FaissEmbedding
import sys, time
import random
import threading
import asyncio
import traceback
import json
import uuid
import os
from functools import wraps
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
from datetime import timedelta
from rq import Retry
from flask import request, jsonify
from models import Users
from datetime import datetime, timezone
import redis
from task_celery.test import test_task
from cryptography.fernet import Fernet
from scripts.transmittal_new import transmittal_daily_work
from scripts.transmittal_new import get_session_key
from scripts.transmittal_download_new import ineight_download_handle
from services.ineight import InEightClient

from documentClassification.document_classifier import DocumentClassifier, NumberingRuleGenerator

from dramatiq_broker import (
run_qmp_query,
run_cbp_task,
run_cbp_multibid_task,
run_cbp_report_task,
run_epc_contractor_task,
run_engineering_project_owner_task,
#ineight_download_task
)

from dramatiq_file_broker import run_file_upload_task
from services.zip_extractor import ZipExtractorForUpload
from services.bid_zip_extractor import BidZipExtractor
from sqlalchemy import text



allowed_origins = [
    '*',
    'null',
    'http://localhost:*',  # Allow all localhost ports
    'http://127.0.0.1:*',  # Allow all localhost IP ports
    'https://backend.aienergy-oilandgas.com',
    'https://app.aienergy-oilandgas.com',
    'http://127.0.0.1:5500'
]

# socketio.on_namespace(TestNamespace('/scp', socket_manager))

# socketio.on_namespace(TestNamespace('/test', socket_manager))
socketio.on_namespace(SCPNamespace('/scp', socket_manager))
socketio.on_namespace(CBPNamespace('/cbp', socket_manager))
socketio.on_namespace(QMPNamespace('/qmp', socket_manager))
socketio.on_namespace(EPCNamespace('/epc', socket_manager))
socketio.on_namespace(CBPReportNamespace('/cbp_report',socket_manager))
socketio.on_namespace(FileUploadNamespace('/file_upload',socket_manager))
socketio.on_namespace(ProcessingFilesNamespace('/processing_files', socket_manager))
socketio.on_namespace(EPONamespace('/epo', socket_manager))

REDIS_CLIENT = redis.StrictRedis(host='localhost', port=6379, db=0, decode_responses=True)

# Load environment variables from env.json file in the parent directory
env_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'env.json')
with open(env_file_path, 'r') as f:
    env_data = json.load(f)

UPLOAD_FOLDER = env_data.get("DATA_DIR")
CHRONOBID_FOLDER = env_data.get("CHRONOBID_DIR")
SPECS_FOLDER = env_data.get("SPECS_DIR")
FERNET_KEY = env_data.get("FERNET_KEY")

ALLOWED_EXTENSIONS = {'doc', 'docx', 'pdf', 'txt', "csv", "xls", "xlsx"}

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['CHRONOBID_FOLDER'] = CHRONOBID_FOLDER
app.config['SPECS_FOLDER'] = SPECS_FOLDER
app.config['RABBITMQ_URL'] = env_data.get("RABBITMQ_URL")
app.config['CELERY_BROKER_URL'] = 'amqp://' + app.config['RABBITMQ_URL']
app.config['CELERY_RESULT_BACKEND'] = 'rpc://' + app.config['RABBITMQ_URL']

cipher = Fernet(FERNET_KEY.encode())
question_answerer = QuestionAnswerer()


data_manager = DataManager()


assistant_sync_manager = AssistantSyncManager()

document_seeder_processor = DocumentSeeder()


doc_to_pdf_processor = DocxToPdf()


reports = Reports()


document_numbering = DocumentNumbering()

engr_document_number_agent = EngrDocTypesDetection()


open_ai_processor = OpenAIProcessor()


main_event_loop = asyncio.new_event_loop()


loop_thread = threading.Thread(target=main_event_loop.run_forever, daemon=True)
loop_thread.start()


clients = {}

client_joined = {}

# socket_manager = socket_instance.get_instance()
print(f"check3 Socket manager initialized: {socket_manager is not None}")


def async_route(f):
    @wraps(f)
    def wrapper(*args, **kwargs):
        return asyncio.run(f(*args, **kwargs))
    return wrapper

@app.route('/shutdown', methods=['POST'])
def shutdown():
    main_event_loop.call_soon_threadsafe(main_event_loop.stop)
    loop_thread.join()
    return "Event loop stopped."

@app.route('/health')
def home():
    return 'TEST OK!'

@app.route('/test_celery', methods=['GET'])
def test_celery():
    try:
        test_task.delay()
        return 'Celery task started'
    except Exception as e:
        return f'Error starting celery task: {str(e)}'

def success_response(message, data={}, status_code=200):
    return jsonify({"success": True, "message": message, "data": data}), status_code

def error_response(message, data={}, status_code=400):
    return jsonify({"success": False, "message": message, "data": data}), status_code

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/upload_1', methods=['POST'])
def upload_file_1():
    project_id = request.form.get('project_id')
    project_type = request.form.get('type')

    if not project_id:
        return error_response("Project ID is required")

    project = Project.get_by(id=project_id)
    print(project)
    if not project:
        return error_response("Invalid project_id")

    if 'file' not in request.files:
        return error_response("No file part")

    files = request.files.getlist('file')

    if not files:
        return error_response('No selected files', 400)

    if not all(allowed_file(file.filename) for file in files):
        return error_response('Some files are not allowed', 400)

    try:
        # dirtry = SPECS_FOLDER if project['entity_type'] != '' else UPLOAD_FOLDER
        dirtry = UPLOAD_FOLDER
        # dirtry = UPLOAD_FOLDER if project_type != 'scp' else SPECS_FOLDER
        file_dirtry = dirtry
        folder_path = os.path.join(dirtry , project_id)
        os.makedirs(folder_path, mode=0o775, exist_ok=True)

        file_id = str(uuid.uuid4())
        # Generate a random 6-digit number
        random_code = random.randint(100000, 999999)

        if len(files) > 1:
            # Merge files
            file_merger = FileMerger(folder_path)
            file_name = f"{project['name']}_{random_code}_merged.pdf"
            print('merged_file_name:', file_name)
            file_path = file_merger.merge_files(files, file_name)
        else:
            file_name = secure_filename(files[0].filename)
            temp_file_path = os.path.join(folder_path, file_name)

            if file_name.rsplit('.', 1)[1].lower() == 'pdf':
                file_path = temp_file_path
                files[0].save(file_path)
            else:
                files[0].save(temp_file_path)
                file_converter = DocxToPdf()
                file_path = file_converter.convert_docx_to_pdf_file_to_file(temp_file_path, folder_path)
                file_name = file_path.split('/')[-1]

        file_data = {
            'id': file_id,
            'name': file_name,
            'project_id': project_id,
            'file_dirtry': file_dirtry,
            'file_type': file_name.rsplit('.', 1)[1].lower()
        }

        file_save = File(**file_data)
        db.session.add(file_save)
        db.session.commit()

        return success_response(f'File successfully uploaded and saved to {file_path}')
    except Exception as e:
        traceback.print_exc()
        db.session.rollback()
        return error_response(f"Error saving file: {str(e)}", 500)

@app.route('/upload_tender', methods=['POST'])
def upload_tender_file():
    package_id = request.form.get('package_id')
    if not package_id:
        return error_response('package_id is required')

    package = Project.get_single(package_id)

    if not package:
        return error_response('Invalid package_id')

    if 'file' not in request.files:
        return error_response('No file part')

    files = request.files.getlist('file')

    if not files:
        return error_response('No selected files', 400)

    if not all(allowed_file(file.filename) for file in files):
        return error_response('Some files are not allowed', 400)

    try:
        folder_path = os.path.join(CHRONOBID_FOLDER, package_id)
        os.makedirs(folder_path, mode=0o775, exist_ok=True)
        file_id = str(uuid.uuid4())
        # Generate a random 6-digit number
        result = []
        file_ids = []
        all_success = True
        all_failed = True
        lock = threading.Lock()

        # Async file upload
        async def upload_file_task(file_itm):
            nonlocal all_success, all_failed
            file_id = str(uuid.uuid4())
            file_name = secure_filename(file_itm.filename)
            file_path = os.path.join(folder_path, file_name)
            try:
                print(f"Processing file: {file_name}")
                file_itm.save(file_path)
                file_extension = file_name.rsplit('.', 1)[-1].lower()

                pdf_conversion_status = 'done' if file_extension == 'pdf' else 'idle'

                file_data = {
                    'id': file_id,
                    'name': file_name,
                    'project_id': package_id,
                    'file_dirtry': CHRONOBID_FOLDER,
                    'file_type': file_extension,
                    'pdf_conversion_status': pdf_conversion_status
                }
                File.create(**file_data)
                with lock:
                    all_failed = False
                result.append({"success": True, "file": file_name})
                file_ids.append(file_id)
                print(f"Successfully uploaded: {file_name}")
            except Exception as e:
                with lock:
                    all_success = False
                result.append({"success": False, "file": file_name, "error": str(e)})
                print(f"Failed to upload file {file_name}: {str(e)}")

        
        # Run all uploads concurrently
        async def upload_files_concurrently():
            print(f"Starting concurrent file uploads for {len(files)} files.")
            tasks = [upload_file_task(file_itm) for file_itm in files]
            await asyncio.gather(*tasks)
            print("Concurrent file uploads finished.")

        # Synchronous wrapper for async task
        def run_upload_tasks():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(upload_files_concurrently())
            finally:
                loop.close()

        # Run the upload tasks in a separate thread
        print("Starting upload thread...")
        upload_thread = threading.Thread(target=run_upload_tasks, daemon=True)
        upload_thread.start()
        upload_thread.join()

        if all_success:
            message = "All files successfully uploaded"
        elif all_failed:
            message = "All files failed to upload"
        else:
            message = "One or more files failed to upload"
        
        print(f"Upload process complete: {message}")
        
        # Run process_files in the background
        def process_files():
            print('convert idle docx to pdf before upload.....')
            doc_to_pdf_processor.convert_idle_docx_files()

            print("Started background processing of files.")
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(
                asyncio.gather(
                    *[document_seeder_processor.handle_single_document(file_id) for file_id in file_ids]
                )
            )
            print("Background file processing complete.")

        # Start background task
        print("Starting background processing thread.")
        threading.Thread(target=process_files , daemon=True).start()

        return success_response(message, result)
    except Exception as e:
        traceback.print_exc()
        db.session.rollback()
        return error_response(f"Error saving file: {str(e)}", 500)


@app.route('/upload_tender_requirement', methods=['POST'])
def upload_tender_requirement():
    
    if 'file' not in request.files:
        return error_response('No file part')

    files = request.files.getlist('file')

    print(f'received {len(files)} files uploaded...')
    package_id = request.form.get('package_id')
    req_id = request.form.get('request_id', False)
    # is_zip = request.form.get('is_zip')

    if not package_id:
        return 'package is required', 400

    if not files:
        return error_response('No selected files', 400)

    if not all(allowed_file(file.filename) for file in files):
        return error_response('Some files are not allowed', 400)

    if req_id:
        existing_req = Requirement.get_single(req_id)
    else:
        existing_req = None
        req_id = str(uuid.uuid4())
    
    # Step 3: Fetch package details
    try:
        package = Project.get_single(package_id)
    except Exception as e:
        return 'Invalid package_id', 400

     
    folder_path = os.path.join(CHRONOBID_FOLDER, package_id, req_id)
    os.makedirs(folder_path, mode=0o775, exist_ok=True)

    try:
        
        result = []
        file_names_arr = []
        all_success = True
        all_failed = True
        lock = threading.Lock()

        if not existing_req:
            create_kwargs = {
                'id': req_id,
                'name': json.dumps(file_names_arr),
                'project_id': package_id,
                'type': 'cbp',
                'file_type': files[0].filename.rsplit('.', 1)[1].lower(),
                'criteria': ""
            }
            print('creating requirement...')
            Requirement.create(**create_kwargs)

        # Async file upload
        def upload_file_task(file_itm):
            nonlocal all_success, all_failed
            file_name = secure_filename(file_itm.filename)
            file_path = os.path.join(folder_path, file_name)
            file_names_arr.append(file_name)
            try:
                print(f"uploading file: {file_name}")
                file_itm.save(file_path)
                result.append({"success": True, "file": file_name})
                print(f"Successfully uploaded: {file_name}")
            except Exception as e:
                with lock:
                    all_success = False
                result.append({"success": False, "file": file_name, "error": str(e)})
                print(f"Failed to upload file {file_name}: {str(e)}")

        
        # Run all uploads concurrently
        def _upload_files_concurrently():
            print(f"Starting concurrent file uploads for {len(files)} files.")
            # tasks = [upload_file_task(file_itm) for file_itm in files]
            # await asyncio.gather(*tasks)
            pool = eventlet.greenpool.GreenPool()  # Create a GreenPool to manage concurrent tasks
            for file_itm in files:
                pool.spawn(upload_file_task, file_itm)  # Spawn each file upload as a greenlet
            pool.waitall()
            print("Concurrent file uploads finished now processing...")
            # print('Completed embedding data into vec db...')
        
        async def async_upload_files_concurrently():
            await asyncio.to_thread(_upload_files_concurrently)
        
        async def _process_files():
            try:
                print('starting to process files...')
                file_processor = CBPFileProcessor(socket_manager, req_id, instant_file_names=file_names_arr)
                await file_processor.process_files()
            except Exception as e:
                print(f"Error during file processing: {e}")

        # Function to ensure an asyncio event loop exists and run a coroutine
        def ensure_event_loop_and_run(coro):
            try:
                future = asyncio.run_coroutine_threadsafe(coro, main_event_loop)
                return future.result() 
            except RuntimeError as e:
                print(f"Error running coroutine: {e}")
                raise

        # Run the upload tasks with Eventlet's greenlet
        def run_upload_tasks():
            try:
                ensure_event_loop_and_run(async_upload_files_concurrently())
                # Run the asynchronous function safely in the event loop
                ensure_event_loop_and_run(_process_files())
            except Exception as e:
                print(f"Error during file upload processing: {e}")

        print("# Runner initiated....")
        run_upload_tasks()
        
        
        if existing_req:
            existing_req_names = json.loads(existing_req["name"])
            new_names = file_names_arr + existing_req_names
            Requirement.update(req_id, name=json.dumps(new_names))
        else:
            Requirement.update(req_id, name=json.dumps(file_names_arr))
            

        if all_success:
            message = "All files successfully uploaded"
        elif all_failed:
            message = "All files failed to upload"
        else:
            message = "One or more files failed to upload"
        
        print(f"Upload process complete: {message}")
                
        return {'request_id': req_id, 'message' : message, 'result' : result}
    except Exception as e:
        print(e)



@app.route('/upload_project_requirement', methods=['POST'])
def upload_project_requirement():
    
    if 'file' not in request.files:
        return error_response('No file part')

    files = request.files.getlist('file')

    print(f'received {len(files)} files uploaded...')
    project_id = request.form.get('project_id')
    req_id = request.form.get('request_id', False)
    # is_zip = request.form.get('is_zip')

    if not project_id:
        return 'project is required', 400

    if not files:
        return error_response('No selected files', 400)

    if not all(allowed_file(file.filename) for file in files):
        return error_response('Some files are not allowed', 400)

    if req_id:
        existing_req = Requirement.get_single(req_id)
    else:
        existing_req = None
        req_id = str(uuid.uuid4())
    
    # Step 3: Fetch package details
    try:
        project = Project.get_single(project_id)
    except Exception as e:
        return 'Invalid package_id', 400

     
    folder_path = os.path.join(SPECS_FOLDER, project_id, req_id)
    os.makedirs(folder_path, mode=0o775, exist_ok=True)

    try:
        
        result = []
        file_names_arr = []
        all_success = True
        all_failed = True
        lock = threading.Lock()

        if not existing_req:
            create_kwargs = {
                'id': req_id,
                'name': json.dumps(file_names_arr),
                'project_id': project_id,
                'type': 'cbp',
                'file_type': files[0].filename.rsplit('.', 1)[1].lower(),
                'criteria': ""
            }
            print('creating requirement...')
            Requirement.create(**create_kwargs)

        # Async file upload
        def upload_file_task(file_itm):
            nonlocal all_success, all_failed
            file_name = secure_filename(file_itm.filename)
            file_path = os.path.join(folder_path, file_name)
            file_names_arr.append(file_name)
            try:
                print(f"uploading file: {file_name}")
                file_itm.save(file_path)
                result.append({"success": True, "file": file_name})
                print(f"Successfully uploaded: {file_name}")
            except Exception as e:
                with lock:
                    all_success = False
                result.append({"success": False, "file": file_name, "error": str(e)})
                print(f"Failed to upload file {file_name}: {str(e)}")

        
        # Run all uploads concurrently
        def _upload_files_concurrently():
            print(f"Starting concurrent file uploads for {len(files)} files.")
            # tasks = [upload_file_task(file_itm) for file_itm in files]
            # await asyncio.gather(*tasks)
            pool = eventlet.greenpool.GreenPool()  # Create a GreenPool to manage concurrent tasks
            for file_itm in files:
                pool.spawn(upload_file_task, file_itm)  # Spawn each file upload as a greenlet
            pool.waitall()
            print("Concurrent file uploads finished now processing...")
            # print('Completed embedding data into vec db...')
        
        async def async_upload_files_concurrently():
            await asyncio.to_thread(_upload_files_concurrently)
        
        async def _process_files():
            try:
                print('starting to process files...')
                file_processor = SCPFileProcessor(socket_manager, req_id, instant_file_names=file_names_arr)
                await file_processor.process_files()
            except Exception as e:
                print(f"Error during file processing: {e}")

        # Function to ensure an asyncio event loop exists and run a coroutine
        def ensure_event_loop_and_run(coro):
            try:
                future = asyncio.run_coroutine_threadsafe(coro, main_event_loop)
                return future.result() 
            except RuntimeError as e:
                print(f"Error running coroutine: {e}")
                raise

        # Run the upload tasks with Eventlet's greenlet
        def run_upload_tasks():
            try:
                ensure_event_loop_and_run(async_upload_files_concurrently())
                # Run the asynchronous function safely in the event loop
                ensure_event_loop_and_run(_process_files())
            except Exception as e:
                print(f"Error during file upload processing: {e}")

        print("# Runner initiated....")
        run_upload_tasks()
        
        if existing_req:
            existing_req_names = json.loads(existing_req["name"])
            new_names = file_names_arr + existing_req_names
            Requirement.update(req_id, name=json.dumps(new_names))
        else:
            Requirement.update(req_id, name=json.dumps(file_names_arr))
            

        if all_success:
            message = "All files successfully uploaded"
        elif all_failed:
            message = "All files failed to upload"
        else:
            message = "One or more files failed to upload"
        
        print(f"Upload process complete: {message}")
                
        return {'request_id': req_id, 'message' : message, 'result' : result}
    except Exception as e:
        print(e)

@app.route('/search_tender_requirement', methods=['POST'])
@async_route
async def search_tender_requirement():
    try:
        # Get data from JSON instead of form data since we're sending JSON
        data = request.get_json()
        
        request_id = data.get('request_id')
        search_queries = data.get('search_queries')
        k = data.get('k', 5)  # Get k from request or default to 5

        if not search_queries:
            return error_response("Query parameter is required", 400)

        # Initialize FAISS instance
        faiss_instance = FaissEmbedding(request_id=request_id, batch_size=1000)
        if not faiss_instance:
            return error_response("FAISS instance not initialized", 500)

        # Directly await the search method
        # results = await faiss_instance.search(query=query, k=k)
        search_results = await asyncio.gather(*[
            faiss_instance.search(query, k=3) for query in search_queries
        ])

        print('this is search results: ', search_results)
        # Format the results
        formatted_results = []
        for query_result in search_results:
            query_matches = []
            for doc, score in query_result:
                query_matches.append({
                    'content': doc.page_content,
                    'metadata': doc.metadata,
                    'score': float(score)  # Convert numpy float to Python float
                })
            formatted_results.append(query_matches)

        # Return success response with formatted results
        return success_response({
            'request_id': request_id,
            'query_count': len(search_queries),
            'results': formatted_results
        })

    except Exception as e:
        print(f"Error in search_tender_requirement: {str(e)}")
        return error_response(f"Search failed: {str(e)}", 500)

@app.route('/search_project_requirement', methods=['POST'])
@async_route
async def search_project_requirement():
    try:
        # Get data from JSON instead of form data since we're sending JSON
        data = request.get_json()
        
        request_id = data.get('request_id')
        search_queries = data.get('search_queries')
        k = data.get('k', 5)  # Get k from request or default to 5

        if not search_queries:
            return error_response("Query parameter is required", 400)

        # Initialize FAISS instance
        faiss_instance = FaissEmbedding(request_id=request_id, batch_size=1000)
        if not faiss_instance:
            return error_response("FAISS instance not initialized", 500)

        # Directly await the search method
        # results = await faiss_instance.search(query=query, k=k)
        search_results = await asyncio.gather(*[
            faiss_instance.search(query, k=3) for query in search_queries
        ])

        print('this is search results for project: ', search_results)
        # Format the results
        formatted_results = []
        for query_result in search_results:
            query_matches = []
            for doc, score in query_result:
                query_matches.append({
                    'content': doc.page_content,
                    'metadata': doc.metadata,
                    'score': float(score)  # Convert numpy float to Python float
                })
            formatted_results.append(query_matches)

        # Return success response with formatted results
        return success_response({
            'request_id': request_id,
            'query_count': len(search_queries),
            'results': formatted_results
        })

    except Exception as e:
        print(f"Error in search_project_requirement: {str(e)}")
        return error_response(f"Search failed: {str(e)}", 500)


@app.route('/upload', methods=['POST'])
def upload_file():
    try:
        project_id = request.form.get('project_id')
        project_type = request.form.get('type')

        if not project_id:
            return error_response("Project ID is required")

        project = Project.get_by(id=project_id)
        if not project:
            return error_response("Invalid project_id")

        if 'file' not in request.files:
            return error_response("No file part")

        files = request.files.getlist('file')

        if not files:
            return error_response('No selected files', 400)

        if not all(allowed_file(file.filename) for file in files):
            return error_response('Some files are not allowed', 400)

        request_id = str(uuid.uuid4())

        # Create new requirement
        create_kwargs = {
            'id': request_id,
            'name': 'File upload',
            'project_id': project_id,
            'status': 'idle',
            'file_type': 'pdf'
        }

        print('creating requirement...')
        Requirement.create(**create_kwargs)
        dirtry = UPLOAD_FOLDER
        file_dirtry = dirtry
        folder_path = os.path.join(dirtry, project_id)
        os.makedirs(folder_path, mode=0o775, exist_ok=True)
        try:
            os.chown(folder_path, os.getuid(), os.getgid())
        except:
            pass

        result = {"result": []}
        result["request_id"] = request_id
        file_ids = []
        file_names = []
        all_success = True
        all_failed = True
        lock = threading.Lock()

        # Only classify if project has numbering rule
        classification_map = {}
        if project[0]["numbering_rule"]:
            # Initialize document classifier
            classifier = DocumentClassifier(project_id=project_id)
            
            # Get filenames for classification
            filenames_to_classify = [secure_filename(file.filename) for file in files]
            
            # Classify the documents
            classifications = classifier.classify_documents(filenames_to_classify)
            
            # Create a mapping of filename to classification
            classification_map = {
                filename: {
                    'file_type': class_info['file_type'],
                    'discipline': class_info['discipline']
                }
                for filename, class_info in classifications.items()
            }

        # Convert to synchronous processing
        def process_file(file_itm):
            nonlocal all_success, all_failed

            with app.app_context():
                file_id = str(uuid.uuid4())
                file_name = secure_filename(file_itm.filename)
                file_path = os.path.join(folder_path, file_name)
                file_itm.save(file_path)
                try:
                    print(f"Processing file: {file_name}")
                    file_extension = file_name.rsplit('.', 1)[-1].lower()

                    pdf_conversion_status = 'done' if file_extension == 'pdf' else 'idle'

                    # Get classification for this file if available
                    classification = classification_map.get(file_name, {
                        'file_type': None,
                        'discipline': None
                    })

                    file_data = {
                        'id': file_id,
                        'name': file_name,
                        'project_id': project_id,
                        'file_dirtry': dirtry,
                        'file_type': file_extension,
                        'pdf_conversion_status': pdf_conversion_status,
                        'document_type': classification['file_type'],
                        'document_discipline': classification['discipline']
                    }

                    if project_type:
                        file_data['category'] = project_type

                        if project_type == "Numbering":
                            print('this is the file path: ', file_path)
                            classifier = NumberingRuleGenerator(document_numbering_file_path=file_path, project_id=project_id)
                            classifier.parse()

                    File.create(**file_data)
                    with lock:
                        all_failed = False
                    result["result"].append({"success": True, "file": file_name})
                    file_ids.append(file_id)
                    file_names.append(file_name)
                    print(f"Successfully uploaded: {file_name}")
                except Exception as e:
                    with lock:
                        all_success = False
                    result["result"].append({"success": False, "file": file_name, "error": str(e)})
                    print(f"Failed to upload file {file_name}: {str(e)}")

        # Process files using a thread pool
        with ThreadPoolExecutor() as executor:
            list(executor.map(process_file, files))

        if all_success:
            message = "All files successfully uploaded"
        elif all_failed:
            message = "All files failed to upload"
        else:
            message = "One or more files failed to upload"
        
        print(f"Upload process complete: {message}")

        print('trying to send files to method...')
        # with app.app_context():
        #     print('now running the task for file upload...')
        #     q = random.choice(queues["file_upload_"])
        #     q.enqueue(upload_files_task, UPLOAD_FOLDER, project_id, file_ids, file_names, retry=Retry(max=2))

        # For file upload tasks with additional arguments
        print(f"project id : {project_id}")
        print(f"file ids : {file_ids}")
        print(f"file names {file_names}")
        print(f"Upload folder : {UPLOAD_FOLDER}")
        
        # processor = BackgroundTaskProcessor(task_type="celery", process_type="file_upload")
        # processor.process_task(
        #     dirtry=UPLOAD_FOLDER,
        #     project_id=project_id,
        #     file_ids=file_ids,
        #     file_names=file_names
        # )
        run_file_upload_task.send(
            UPLOAD_FOLDER,
            project_id,
            file_ids,
            file_names,
            request_id
        )
        return success_response(message, result)
    except Exception as e:
        traceback.print_exc()
        db.session.rollback()
        return error_response(f"Error saving file: {str(e)}", 500)



@app.route('/upload_zip', methods=['POST'])
def upload_zip():
    try:
        project_id = request.form.get('project_id')
        project_type = request.form.get('type')

        if not project_id:
            return error_response("Project ID is required")

        project = Project.get_by(id=project_id)
        if not project:
            return error_response("Invalid project_id")

        if 'file' not in request.files:
            return error_response("No file part")

        zip_file = request.files['file']
        
        if not zip_file.filename:
            return error_response('No selected file', 400)

        # Check if it's a zip file
        if not zip_file.filename.lower().endswith('.zip'):
            return error_response('File must be a zip file', 400)

        request_id = str(uuid.uuid4())
        
        # Create new requirement
        create_kwargs = {
            'id': request_id,
            'name': 'Zip file upload',
            'project_id': project_id,
            'status': 'idle',
            'file_type': 'zip'
        }

        print('creating requirement...')
        Requirement.create(**create_kwargs)
        
        # Create project directory
        folder_path = os.path.join(UPLOAD_FOLDER, project_id)
        os.makedirs(folder_path, mode=0o775, exist_ok=True)
        try:
            os.chown(folder_path, os.getuid(), os.getgid())
        except:
            pass

        # Initialize result
        result = {"result": []}
        result["request_id"] = request_id
        file_ids = []
        file_names = []
        all_success = True
        all_failed = True
        lock = threading.Lock()

        # Extract zip file using ZipExtractorForUpload
        extractor = ZipExtractorForUpload(UPLOAD_FOLDER, project_id, project_type)
        extracted_files, unsupported = extractor.extract_zip_to_project(zip_file, folder_path)

        print(f"Extraction complete. Extracted files: {len(extracted_files)}, Unsupported files: {len(unsupported)}")
        print(f"Unsupported files list: {unsupported}")

        if not extracted_files:
            return error_response("No supported files found in zip or extraction failed", 400)

        classification_map = {}
        if project[0]["numbering_rule"]:
            # Initialize document classifier
            classifier = DocumentClassifier(project_id=project_id)
            
            # Get filenames for classification
            filenames_to_classify = [file_info['name'] for file_info in extracted_files]
            
            # Classify the documents
            classifications = classifier.classify_documents(filenames_to_classify)
            
            # Create a mapping of filename to classification
            classification_map = {
                filename: {
                    'file_type': class_info['file_type'],
                    'discipline': class_info['discipline']
                }
                for filename, class_info in classifications.items()
            }

        # Process files using ThreadPoolExecutor for parallel processing
        def process_extracted_file(file_info):
            with app.app_context():
                nonlocal all_success, all_failed
                
                try:
                    file_id = str(uuid.uuid4())
                    file_name = file_info['name']
                    file_extension = file_info['extension']
                    
                    pdf_conversion_status = 'done' if file_extension == 'pdf' else 'idle'

                    # Get classification for this file
                    classification = classification_map.get(file_name, {
                        'file_type': None,
                        'discipline': None
                    })

                    file_data = {
                        'id': file_id,
                        'name': file_name,
                        'project_id': project_id,
                        'file_dirtry': UPLOAD_FOLDER,
                        'file_type': file_extension,
                        'pdf_conversion_status': pdf_conversion_status,
                        'document_type': classification['file_type'],
                        'document_discipline': classification['discipline']
                    }

                    if project_type:
                        file_data['category'] = project_type

                    File.create(**file_data)
                    with lock:
                        all_failed = False
                    result["result"].append({"success": True, "file": file_name})
                    file_ids.append(file_id)
                    file_names.append(file_name)
                    print(f"Successfully processed: {file_name}")
                    return True
                except Exception as e:
                    with lock:
                        all_success = False
                    result["result"].append({"success": False, "file": file_info['name'], "error": str(e)})
                    print(f"Failed to process file {file_info['name']}: {str(e)}")
                    return False

      
        # Use ThreadPoolExecutor to process files in parallel
        with ThreadPoolExecutor() as executor:
            process_results = list(executor.map(process_extracted_file, extracted_files))

        # Add unsupported files to result
        result["unsupported_files"] = unsupported

        if all_success:
            message = f"Zip file successfully extracted. {len(extracted_files)} files are processing in progress"
        else:
            message = "Zip file extracted but some files failed to process"
        
        if unsupported:
            message += f". {len(unsupported)} unsupported files skipped"

        print(f"Zip extraction complete: {message}")

        # Send to queue if we have files to process
        if file_ids:
            print(f"project id : {project_id}")
            print(f"file ids : {file_ids}")
            print(f"file names : {file_names}")
            print(f"Upload folder : {UPLOAD_FOLDER}")
            
            run_file_upload_task.send(
                UPLOAD_FOLDER,
                project_id,
                file_ids,
                file_names,
                request_id
            )

        return success_response(message, result)
        
    except Exception as e:
        traceback.print_exc()
        db.session.rollback()
        return error_response(f"Error processing zip file: {str(e)}", 500)




@app.route('/upload_retry', methods=['POST'])
def upload_file_retry():
    project_id = request.form.get('project_id')

    if not project_id:
        return error_response("Project ID is required")

    project = Project.get_by(id=project_id)
    # print(project)
    if not project:
        return error_response("Invalid project_id")

    idle_files = File.get_by(project_id=project_id, status="idle")
    pending_files = File.get_by(project_id=project_id, status="pending")
    files = idle_files + pending_files
    if not files:
        return error_response("No files found for this project")
    
    file_ids = [file['id'] for file in files]
    file_names = [file['name'] for file in files]


    try:

        request_id = str(uuid.uuid4())

            # Parse bids info
    

            # Create new requirement
            #Note: WE ARE USING PACKAGE ID FOR CBP 2
        create_kwargs = {
                'id': request_id,
                'name': 'File upload retry',
                'project_id': project_id,
                'status': 'idle',
                'file_type':'pdf'
            }

        print('creating requirement...')
        Requirement.create(**create_kwargs)

        print('trying to send files to method...')
        # with app.app_context():
        #     print('now running the task for file upload...')
        #     q = random.choice(queues["file_upload_"])
        #     q.enqueue(upload_files_task, UPLOAD_FOLDER, project_id, file_ids, file_names, retry=Retry(max=2))

        # For file upload tasks with additional arguments
        print(f"project id : {project_id}")
        
        # processor = BackgroundTaskProcessor(task_type="celery", process_type="file_upload")
        # processor.process_task(
        #     dirtry=UPLOAD_FOLDER,
        #     project_id=project_id,
        #     file_ids=file_ids,
        #     file_names=file_names
        # )
        
        # Add is_retry=True parameter to indicate this is a retry operation
        run_file_upload_task.send(
            UPLOAD_FOLDER,
            project_id,
            file_ids,
            file_names,
            request_id,
            is_retry=True
        )

        return success_response("File processing retry started", {"message": "File processing retry started","request_id":request_id})
    except Exception as e:
        traceback.print_exc()
        db.session.rollback()
        return error_response(f"Error saving file: {str(e)}", 500)


# @app.route('/upload_zip', methods=['POST'])


@app.route('/test_file_upload_v2', methods=['POST'])
def test_file_upload_v2():
    try:
        from cbp.sample_docs.test import Fileuploader
        # Get parameters from request
        project_id = "fabe9fa4-ce68-4a73-82bc-259babd21398"
        file_ids = [
            '4dd3133f-3c86-41ba-acef-b4ed80d7f62c',
            '754c6e77-6fd7-4a88-8591-b0f13afabd2f',
            '1340d0bb-0844-448b-8bfb-01fa7be9bb57',
            'cb0f8b0b-0acf-4327-bd02-fd1118f90e0d',
            'dfda089d-b01c-4ef6-898a-5c20c049b452',
            'cb569239-34ad-4053-9d01-a47efa4b41ec',
            'ca68df97-dca3-4fed-a8d3-ea881be64541',
            '72d8c268-ab4c-4c8b-afed-43a6e5385f98'
        ]
        file_names = [
            'Conveyor_Schedule_2019.04.23.xlsx',
            'P3803-000-GNRL-DC-001_-_General_Design_Criteria_signed.pdf',
            'P3803-000-GNRL-DC-001_-_General_Design_Criteria_signed.pdf',
            'Conveyor_Schedule_2019.04.23.xlsx',
            'Citic-S21-GP-047_To_M3_AG_Mill__Ball_Mill_Proposal_for_Las_Truchas_Project_Rev._4.pdf',
            'Engineering_Manufacturing_Specs.xlsx',
            'Citic-S21-GP-047_To_M3_AG_Mill__Ball_Mill_Proposal_for_Las_Truchas_Project_Rev._4.pdf',
            'Engineering_Manufacturing_Specs.xlsx'
        ]
        upload_folder = "data"

        # Create an instance of Fileuploader and start the upload process
        uploader_instance = Fileuploader(upload_folder, project_id, file_ids, file_names)
        uploader_instance.uploader()
        message = "Testing has started"
        result = "started"
        return  success_response(message, result)
    except Exception as e:
        print(f"Error in test_file_upload: {str(e)}")
        return {"error": True, "message": f"Error occurred during test: {str(e)}"}, 500
# @app.route('/compare_upload', methods=['POST'])
# def compare_upload():
#     project_id = request.form.get('project_id')

#     if not project_id:
#         return error_response('project is required', 400)

#     project = Project.get_single(project_id)
#     if not project:
#         return error_response('Invalid project', 400)

#     if 'file' not in request.files:
#         return error_response('No file part', 400)

#     files = request.files.getlist('file')

#     if not files:
#         return error_response('No selected files', 400)

#     if not all(allowed_file(file.filename) for file in files):
#         return error_response('Some files are not allowed', 400)

#     else:
#         return error_response('Allowed file types are .doc and .docx', 400)
@app.route('/compare_upload_chronobid', methods=['POST'])
def compare_upload_chronobid():
    start_time = time.time()
    print("Operation started at:", start_time)

    exist_bid = request.form.get('exist_bid')
    print("Exist Bid:", exist_bid)
    exist_bid = exist_bid.lower() == "true" if exist_bid else False

    package_id = request.form.get('package_id')
    print("Package ID:", package_id)

    other_project_ids = request.form.get('other_project_ids')
    print("Other Project IDs:", other_project_ids)

    criteria_str = request.form.get('criteria')
    print("Criteria:", criteria_str)

    if not criteria_str:
        return 'criteria needed', 400
    
    if not package_id:
        return 'package is required', 400

    # Step 3: Fetch package details
    try:
        package = Project.get_single(package_id)
    except Exception as e:
        return 'Invalid package', 400
    
    print(exist_bid and exist_bid == True)
    print('compare above logic exist bid...')

    if exist_bid:
        print('i am in exist bid...')
        print("bid_id")
        bid_id = request.form.get('bid_id')
        print(f"bid_id:{bid_id}")
        try:
            selectedBid = Bids.get_single(bid_id)
        except Exception as e:
            return 'Invalid Bid Selected', 400
        
        request_id = str(uuid.uuid4())
        filenames = []
        bid_files = BidFile.get_by(bid_id=bid_id)
        print(f"Bid files : {bid_files} ")
        for bid_file in bid_files:
            filenames.append(bid_file['name'])


        req_metadata = {'exist_bid': exist_bid, 'bid_id': request.form.get('bid_id', False)}
            

        create_kwargs = {
            'id': request_id,
            'name': json.dumps(filenames),
            'project_id': package_id,
            'type': type,
            'file_type': 'pdf',
            'criteria': "",
            "req_metadata":json.dumps(req_metadata)

        }


        print('creating requirement...')
        Requirement.create(**create_kwargs)
        
    else:
        print('i am in not exist bid...')
        request_id = request.form.get('request_id')
        file_names = request.form.get('file_names')

        if not request_id:
            return 'request is required', 400
        
        try:
            requirement = Requirement.get_single(request_id)
        except Exception as e:
            return 'Invalid requirement', 400
        
        if file_names:
            data = json.loads(file_names)
            filenames = [item['file'] for item in data]

    print('this are filenames: ', filenames)
    
    try:
       
        


        REDIS_CLIENT.set(f"{request_id}_metadata", json.dumps({'exist_bid': exist_bid, 'bid_id': request.form.get('bid_id', False)}))

        exist_bid_value = 1 if exist_bid == True else 0
        Requirement.update(request_id, criteria=criteria_str, exist_bid=exist_bid_value)
        print(f"Operation completed successfully. Total time: {time.time() - start_time:.4f} seconds")

        run_cbp_task.send(
            req_id=request_id
        )

        # Return the request_id immediately
        return {"success": True, 'message': 'request submitted successfully...', 'request_id': request_id}

    except Exception as e:
        print(f"Error occurred: {str(e)}")
        return {"success": False, "message": "An error occurred while processing the request."}



#         return error_response('Allowed file types are .doc and .docx', 400)

@app.route('/compare_upload_chrono_multi_bid', methods=['POST'])
def compare_upload_multi_chronobid():
    start_time = time.time()
    print("Operation started at:", start_time)

    # Access JSON payload instead of form data
    data = request.get_json()
    bids_info = data.get('bids_info')
    criteria_str = json.loads(data.get('criteria'))
    package_id = data.get('package_id')

    if not bids_info:
        return error_response('bids_info is required', 400)
    
    if not criteria_str:
        return error_response('criteria is required', 400)

    if not package_id:
        return error_response('project_id is required', 400)

    try:
        # Generate new request ID
        request_id = str(uuid.uuid4())

        # Parse bids info
        try:
            bids_info = json.loads(bids_info)
        except json.JSONDecodeError:
            return error_response('Invalid bids_info format', 400)
        

        # Create new requirement
        #Note: WE ARE USING PACKAGE ID FOR CBP 2
        create_kwargs = {
            'id': request_id,
            'name': 'Multi-bid Comparison',
            'project_id': package_id,
            'file_type': 'pdf',
            'criteria': json.dumps(criteria_str),
            'is_multibid': 1,
            'multi_bids_info': json.dumps(bids_info),
            'status': 'idle'
        }
        
        Requirement.create(**create_kwargs)

        run_cbp_multibid_task.send(
            req_id=request_id
        )
            #return error_response(f"An error occurred while processing the task: {str(e)}")
        return success_response('Request submitted successfully', {'request_id': request_id})

    except Exception as e:
        print(f"Error occurred: {str(e)}")
        return error_response(f"An error occurred while processing the request: {str(e)}")


@app.route('/compare_report_chronobid', methods=['POST'])
def compare_report_chronobid():

    request_id = request.form.get('request_id')

    if not request_id:
        return error_response('request_id is required', 400)

    try:
        requirement = Requirement.get_by(id=request_id)

        if not requirement or len(requirement) == 0:
            return error_response("Invalid Request!")

        requirement = requirement[0]

        if requirement["status"] not in ["done", "response_started"]:
            return success_response("The request is still being processed!", {"status": requirement["status"]})

        report_path = os.path.join(app.config['CHRONOBID_FOLDER'], requirement["project_id"], request_id + ".json")

        with open(report_path, 'r', encoding ='utf8') as json_file:
            ret = json.load(json_file)

        ret["status"] = requirement["status"]
        return success_response(ret)

    except Exception as e:
        print(e)
        return error_response(f"Error Occurred: {str(e)}")

@app.route('/compare_report', methods=['POST'])
def comapre_report():

    request_id = request.form.get('request_id')

    if not request_id:
        return error_response('request_id is required', 400)

    try:
        requirement = Requirement.get_by(id= request_id)[0]

        if not requirement:
            return error_response("Invalid Request!", {"status": requirement["status"]})

        if requirement["status"] != "done":
            return success_response("The request is still being processed!",{"status": requirement["status"]})

        report_path = os.path.join(app.config['SPECS_FOLDER'], requirement["project_id"], request_id + ".json")

        with open(report_path, 'r', encoding ='utf8') as json_file:
            ret = json.load(json_file)

        ret["status"] = requirement["status"]
        return success_response(ret)


    except Exception as e:
        return error_response('Error Occured!', {"error": True})


@app.route('/compare_queue', methods=['GET'])
def comapre_queue():
    try:
        return {"queue": Requirement.get_by()}, 200
    except Exception as e:
        print(e)
        return {"error": True, "message": "Error Occured!"}



@app.route('/compare_report_log', methods=['POST'])
def comapre_report_log():

    request_id = request.json['request_id']

    if not request_id:
        return 'request_id is required', 400

    try:
        requirement = Requirement.get_by(id= request_id)[0]

        if not requirement:
            return {"message": "Invalid Request!", "status": requirement["status"]}

        if requirement["status"] != "done":
            return {"message": "The request is still being processed!", "status": requirement["status"]}

        report_log_path = os.path.join(app.config['SPECS_FOLDER'], requirement["project_id"], request_id + "_log.json")


        with open(report_log_path, 'r', encoding ='utf8') as json_file:
            ret = json.load(json_file)

        ret["status"] = requirement["status"]
        return ret

    except Exception as e:
        print(e)
        return {"error": True, "message": "Error Occured!"}

@app.route('/get_cbp_report_by_request_id', methods=['POST'])
def get_report_by_request_id():
    request_id = request.json.get('request_id')

    if not request_id:
        return 'request_id is required', 400

    try:
        print('request_id:', request_id)
        report_generator = GenerateReportCBP(request_id, 0)
        # Use asyncio.run_coroutine_threadsafe to schedule the coroutine on the main event loop
        asyncio.run_coroutine_threadsafe(report_generator._generate_report(), main_event_loop)
        return success_response("The request is currently processing", {})
  
    except Exception as e:
        print(e)
        error_response(e, 400)

@app.route('/question_answer', methods=['POST'])
def question_answer():
    try:
        # project_id = request.form.get('project_id')
        # question = request.form.get('question')
        project_id = request.json['project_id']
        question = request.json['question']

        if not project_id:
            return 'project_id is required', 400

        try:
            print(project_id, question)
            result = question_answerer.answer_question(project_id, question)
            return result
            # return {"answer": result["answer"], "sources": result["sources"]}
        except Exception as e:
            traceback.print_exc()
            return {"error": True, "message": "Error Occured!"}
    except Exception as e:
        traceback.print_exc()



@app.route('/question_answer_v1_1', methods=['GET'])
def question_answer_v1_response():

    request_id = request.args.get('request_id')

    if not request_id:
        return error_response('request_id is required', 400)

    try:
        requirement = Requirement.get_by(id=request_id)

        if not requirement or len(requirement) == 0:
            return error_response("Invalid Request!")

        requirement = requirement[0]

        if requirement["status"] not in ["done", "response_started"]:
            return success_response("The request is still being processed!", {"status": requirement["status"]})

        report_path = os.path.join(app.config['UPLOAD_FOLDER'], requirement["project_id"], request_id + ".json")
        if not os.path.exists(report_path):
            report_path = os.path.join(app.config['UPLOAD_FOLDER'], requirement["project_id"], 'requirement_' + request_id, 'report.json')

        with open(report_path, 'r', encoding ='utf8') as json_file:
            ret = json.load(json_file)

        ret["status"] = requirement["status"]
        return success_response(ret)

    except Exception as e:
        print(e)
        return error_response(f"Error Occurred: {str(e)}")


@app.route('/question_answer_v1_1', methods=['POST'])
def question_answer_v1_1():

    project_id = request.json.get("project_id", None)
    package_id = request.json.get("package_id", None)
    engineer_id = request.json.get("engineer_id", None)
    vendor_id = request.json.get("vendor_id", None)

    question = request.json['question']
    other_project_ids = request.json['other_project_ids']
    
    exist_bid = request.json['exist_bid'] if request.json['exist_bid'] else False
    bid_id = request.json['bid_id'] if request.json['bid_id'] else None
    break_cache = request.json.get('break_cache', True)

    #ADD QUOTED TEXT
    quoted_text = request.json.get('quoted_text', '')
    follow_up_query = request.json.get('follow_up_query', '')

    #ADD LANGUAGE
    language = request.json.get('language', 'English')

    # if not project_id:
    #     return 'project_id is required', 400

    try:
        request_id = str(uuid.uuid4())
        create_kwargs = {
            'id': request_id,
            'name': '',
            'project_id': project_id,
            'package_id': package_id,
            'engineer_id': engineer_id,
            'vendor_id': vendor_id,
            'file_type': '',
            'type' : 'qmp',
            'criteria': question, 
        }

        
        Requirement.create(**create_kwargs)
        print('request_id:', request_id)
        
        run_qmp_query.send(
            request_id, 
            other_project_ids, 
            exist_bid, 
            bid_id, 
            break_cache,
            quoted_text,
            follow_up_query,
            language
        )
        
        
        
        # processor = BackgroundTaskProcessor(task_type="celery", process_type="evaluate_qmp")
        # processor.process_task(
        #     request_id=request_id,
        #     other_project_ids=other_project_ids,
        #     exist_bid=exist_bid,
        #     bid_id=bid_id,
        #     break_cache=break_cache,
        #     quoted_text=quoted_text,
        #     follow_up_query=follow_up_query
        # )

        # processor = QMPQueryHandler(socket_manager, is_test=False)
        # asyncio.run_coroutine_threadsafe(processor.answer_question_v1_1(request_id, other_project_ids), main_event_loop)

        return {"request_id": request_id}

    except Exception as e:
        print(e)
        traceback.print_exc()
        return {"error": True, "message": "Error Occured!"}

@app.route('/test/question_answer_v1_1', methods=['POST'])
def test_question_answer_v1_1():

    try:
        request_id = '11cc5979-8e23-4372-a671-ef39125dce0e'
        print('request_id:', request_id)
        return {"request_id": request_id}

    except Exception as e:
        print(e)
        traceback.print_exc()
        return {"error": True, "message": "Error Occured!"}


@app.route('/create_project', methods=['POST'])
def create_project():
    try:
        project_name = request.form.get('name')
        project_uuid = request.form.get('uuid') if request.form.get('uuid') else str(uuid.uuid4())
        
        user_id = request.form.get('user_id')
        ineight_project_id = request.form.get('ineight_project_id')
        ineight_user_id = request.form.get('ineight_user_id')
        ineight_company_id = request.form.get('ineight_company_id')
        ineight_password = request.form.get('ineight_password')
        is_ineight = int(request.form.get('is_ineight', 0))

        if is_ineight:
            if not all([ineight_project_id, ineight_user_id, ineight_company_id, ineight_password]):
                return error_response('Missing iNeight credentials', 400)
            
            env_config = {
                'base_url': 'https://sa1.doc.ineight.com',
                'userID': ineight_user_id,
                'companyID': ineight_company_id,
                'Password': ineight_password,
                'ProjectNo': ineight_project_id,
                'TfaToken': '.',  # Default value
                'connectingProduct': '.',  # Default value
                'Application': 'InEight Document'
            }

            session_key = get_session_key(env_config)
            if "ERROR" in session_key:
                return error_response(f"Session key error: {session_key}", 400)

        def encrypt(val):
            return cipher.encrypt(val.encode()).decode() if val else None

        encrypted_data = {
            'ineight_project_id': encrypt(ineight_project_id),
            'ineight_user_id': encrypt(ineight_user_id),
            'ineight_company_id': encrypt(ineight_company_id),
            'ineight_password': encrypt(ineight_password)
        }

        project_data = {
            'id': project_uuid,
            'name': project_name,
            'entity_type': 'project',
            'user_id': user_id,
            'is_ineight': is_ineight,
            **encrypted_data
        }

        project = Project(**project_data)
        db.session.add(project)
        db.session.commit()

        project_folder = os.path.join(UPLOAD_FOLDER, project_data['id'])
        os.makedirs(project_folder, mode=0o777, exist_ok=True)
        os.chmod(project_folder, 0o777)

        directory_status = "Directory created successfully" if os.path.exists(project_folder) else "Failed to create directory"

        return success_response("Project created successfully", {'id': project_data['id'], 'directory_status': directory_status}, 201)

    except Exception as e:
        db.session.rollback()
        return error_response(f"Error creating project: {str(e)}", 500)

@app.route('/create_tender_package', methods=['POST'])
def create_tender_package():
    package_name = request.form.get('name')
    parent_id = request.form.get('parent_id')
    package_id = request.form.get('uuid') if request.form.get('uuid') else str(uuid.uuid4())

    # Adding numbering rule for vendor
    if parent_id:
        numbering_rule = Project.get_single(parent_id)['numbering_rule']
    else:
        numbering_rule = None

    if not package_name:
        return error_response('Package name is required', 400)
    if not parent_id:
        return error_response('Project id is required', 400)

    existing_package = Project.get_one_by(name=package_name, entity_type="tender")

    # if existing_package:
    #     return error_response('A package with this name already exists', 400)

    try:
        Project.create(id=package_id, name=package_name, entity_type='tender', parent_id=parent_id, numbering_rule=numbering_rule)
        project_folder = os.path.join(CHRONOBID_FOLDER, package_id)
        os.makedirs(project_folder, mode=0o777, exist_ok=True)
        # Ensure the directory is writable
        os.chmod(project_folder, 0o777)

        # Check if the directory was created
        if os.path.exists(project_folder) and os.path.isdir(project_folder):
            directory_status = "Directory created successfully"
        else:
            directory_status = "Failed to create directory"

    except Exception as e:
        db.session.rollback()
        return error_response(f'Error creating tender package: {str(e)}', 500)

    return success_response("Tender Package created successfully", {'id': package_id, 'directory_status': directory_status}, 201)

@app.route('/project_update/<string:id>', methods=['PUT'])
def update_project(id):
    try:
        # Get the project to update
        project = Project.get_single(id)
        if not project:
            return error_response('Project not found', 404)
        
        # Get updated fields from the request
        name = request.form.get('name')
        parent_id = request.form.get('parent_id')
        
        # Prepare update data
        update_data = {}
        if name:
            update_data['name'] = name
        if parent_id:
            update_data['parent_id'] = parent_id
            
        # Check if we have any data to update
        if not update_data:
            return error_response('No update data provided', 400)
            
        # Update the project
        updated_project = Project.update(id, **update_data)
        if not updated_project:
            return error_response('Failed to update project', 500)
            
        return success_response('Project updated successfully', {'project': updated_project})
    except Exception as e:
        return error_response(f"Error updating project: {str(e)}", 500)

@app.route('/add_tender_package_to_project', methods=['POST'])
def add_tender_package_to_project():
    data = request.get_json()
    project_id = data.get('project_id')
    package_ids = data.get('package_ids')
    
    if not project_id or not package_ids:
        return error_response('Both project_id and package_ids are required', 400)

    try:
        added_packages = []
        skipped_packages = []
        
        for package_id in package_ids:
            package = Project.get_one_by(id=package_id, entity_type='tender')
            if not package:
                skipped_packages.append({
                    'id': package_id,
                    'reason': 'Package not found'
                })
                continue
                
            # Check if package already exists under this project
            if package.parent_id == project_id:
                skipped_packages.append({
                    'id': package_id,
                    'reason': 'Package already exists in project'
                })
                continue
                
            package.parent_id = project_id
            added_packages.append(package_id)
            
        db.session.commit()
        
        response_data = {
            'added_packages': added_packages,
            'skipped_packages': skipped_packages
        }
        
        if not added_packages and skipped_packages:
            return error_response("No packages were added", response_data, 400)
            
        return success_response("Tender packages processed successfully", response_data, 200)

    except Exception as e:
        db.session.rollback()
        return error_response(f'Error adding tender packages to project: {str(e)}', 500)



@app.route('/fetch_tender_packages_by_project/<string:project_id>', methods=['GET'])
def fetch_tender_packages_by_project(project_id):
    try:
        packages = Project.get_packages_by_project(project_id)
        if not packages:
            return error_response("No tender packages found for this project", 404)
        return success_response("Fetched tender packages successfully", {'packages': packages}, 200)
    except Exception as e:
        return error_response(f"Error fetching tender packages: {str(e)}", 500)


@app.route('/add_bid_to_package', methods=['POST'])
def add_bid_to_package():
    data = request.get_json()
    package_id = data.get('package_id')
    bid_ids = data.get('bid_ids')

    if not package_id or not bid_ids:
        return error_response('Both package_id and bid_ids are required', 400)
    try:
        package = Project.get_one_by(id=package_id, entity_type='tender')
        if not package:
            return error_response('Package not found or not of type tender', 404)
               
        added_bids = []
        skipped_bids = []
        
        for bid_id in bid_ids:
            bid = Bids.get_one_by(id=bid_id)
            if not bid:
                skipped_bids.append({
                    'id': bid_id,
                    'reason': 'Bid not found'
                })
                continue
                
            # Check if bid already exists under this package
            if bid.package_id == package_id:
                skipped_bids.append({
                    'id': bid_id,
                    'reason': 'Bid already exists in package'
                })
                continue
                
            result = Project.add_bid_to_package(package_id=package_id, bid_id=bid_id)
            if result:
                added_bids.append(bid_id)
            else:
                skipped_bids.append({
                    'id': bid_id,
                    'reason': 'Failed to add bid to package'
                })
            
        db.session.commit()
        
        response_data = {
            'added_bids': added_bids,
            'skipped_bids': skipped_bids
        }
        
        if not added_bids and skipped_bids:
            return error_response("No bids were added", response_data, 400)
            
        return success_response("Bids processed successfully", response_data, 200)

    except Exception as e:
        db.session.rollback()
        return error_response(f'Error adding bids to package: {str(e)}', 500)


@app.route('/get_bids_by_package/<string:package_id>', methods=['GET'])
def get_bids_by_package(package_id):
    try:
        bids = Project.get_bids_by_package(package_id)
        if not bids:
            return error_response("No bids found for this package", 404)
        return success_response("Fetched bids successfully", {'bids': bids}, 200)
    except Exception as e:
        return error_response(f"Error fetching bids: {str(e)}", 500)



@app.route('/fetch_tender_packages', methods=['GET'])
def fetch_tender_packages():

    args = request.args.to_dict()
    # print(args)
    packages = Project.get_by(entity_type='tender')

    if not packages:
        return 'No tender packages found', 404

    return {'packages': packages}, 200

@app.route('/fetch_tender_packages_by_id', methods=['GET'])
def fetch_tender_packages_single():
    tender_id = request.args.get('id')

    if not tender_id:
        return jsonify({'error': "'id' query parameter is required"}), 400

    package = Project.get_by(id=tender_id,entity_type='tender')

    if not package:
        return jsonify({'message': 'No tender packages found'}), 404

    return jsonify({'packages': package}), 200


@app.route('/fetch_projects', methods=['GET'])
def fetch_projects():

    args = request.args.to_dict()
    projects = Project.get_by(entity_type='project')
    # print('this is projects: ', projects)

    if not projects:
        return 'No projects found', 404

    return {'projects': projects}, 200

@app.route('/fetch_projects_all', methods=['GET'])
def fetch_projects_all():

    args = request.args.to_dict()
    projects = Project.get_by()

    if not projects:
        return 'No projects found', 404

    return {'projects': projects}, 200

@app.route('/search', methods=['GET'])
def search_data():
    try:
        project_id = request.args.get('project_id')
        query = request.args.get('query')
        limit = request.args.get('limit')
        if not project_id:
            return 'project_id is required', 400

        if not query:
            return 'query is required', 400

        if not limit:
            limit = 5
        else:
            limit = int(limit)

        project = Project.get_single(project_id)
        if not project:
            return 'Invalid project_id', 400

        # results = vector_handler.get_data(project_id, [query], limit=limit)
        results = []
        return {'results': results}, 200
    except Exception as e:
        print(e)
        return {"error": True, "message": "Error Occured!"}

@app.route('/search_v2', methods=['GET'])
def search_data_v2():
    try:
        project_id = request.args.get('project_id')
        query = request.args.get('query')
        limit = request.args.get('limit')
        if not project_id:
            return 'project is required', 400

        if not query:
            return 'query is required', 400

        if not limit:
            limit = 5   
        else:
            limit = int(limit)

        project = Project.get_single(project_id)
        if not project:
            return 'Invalid project', 400

        results = asyncio.run(data_manager.extract_data(project_id, [query]))

        return {'results': results}, 200
    except Exception as e:
        print(e)
        return {"error": True, "message": "Error Occured!"}

@app.route('/get_files_by_package', methods=['GET'])
def get_files_chronobid():
    try:
        project_id = request.args.get('project_id', None)

        if project_id is None:
            return 'project is required', 400

        pid = Project.get_single(project_id)
        if not pid:
            return 'Invalid project', 400

        # Extracting only the required fields from each file
        files_data = []
        for file_info in pid['files']:
            files_data.append({
                'package_id': pid['id'],
                'file_id': file_info['id'],
                'file_name': file_info['name'],
                'status': file_info['status']
            })

        return {"results": files_data}

    except Exception as e:
        print(e)
        return {"error": True, "message": e.__dict__}



@app.route('/get_all_data', methods=['GET'])
def get_all_vector_data():
    try:
        # project_id = request.args.get('project_id')
        project_id = request.args.get('project_id', None)

        # return {'results': [package_id,project_id]}

        if project_id is None:
            return 'project is required', 400

        pid = Project.get_single(project_id)
        if not pid:
            return 'Invalid project', 400

        return {"results":pid}

    except Exception as e:
        print(e)
        return {"error": True, "message": e.__dict__}


@app.route('/delete_file/<file_id>', methods=['DELETE'])
def delete_file(file_id):
    try:
        # Check if the file exists
        file = File.get_single(file_id)
        if not file:
            return error_response('File not found', 404)

        # Delete the file
        File.delete_by(id=file_id)

        # Optionally, remove the actual file from the filesystem
        file_path = os.path.join(CHRONOBID_FOLDER, file['project_id'], file['name'])
        if os.path.exists(file_path):
            os.remove(file_path)

        return success_response(f"File with id {file_id} successfully deleted")

    except Exception as e:
        return error_response(f"Error deleting file: {str(e)}", 500)


@app.route('/delete_project/<project_id>', methods=['DELETE'])
def delete_project(project_id):
    try:
        # Check if the project exists
        project = Project.get_by(id=project_id)
        if not project or len(project) == 0:
            return error_response('Project not found', 404)

        # Delete all related requirements
        requirements = Requirement.get_by(project_id=project_id)
        if requirements:
            try:
                for req in requirements:
                    Requirement.delete_by(id=req['id'])
            except Exception as e:
                print('Unable to delete project requirements...', str(e))
                return error_response('Error deleting project requirements', 500)

        # Optionally, delete all related files before deleting the project
        related_files = File.get_by(project_id=project_id)
        if related_files:
            try:
                dirtry = CHRONOBID_FOLDER if project[0]['entity_type'] == 'tender' else UPLOAD_FOLDER
                for file in related_files:
                    file_path = os.path.join(dirtry, project_id, file['name'])
                    if os.path.exists(file_path):
                        os.remove(file_path)
                    File.delete_by(id=file['id'])
            except Exception as e:
                print('unable to delete project files...')

        # Delete the project
        Project.delete_by(id=project_id)

        return success_response(f"Project with id {project_id} successfully deleted")

    except Exception as e:
        return error_response(f"Error deleting project: {str(e)}", 500)

# API to return all prompts
@app.route('/prompts', methods=['GET'])
def get_all_prompts():
    try:
        prompts = Prompt.get_by()  # Fetch all prompts
        return success_response("Fetched all prompts", {"prompts": prompts})
    except Exception as e:
        print(e)
        return error_response(f"Error fetching prompts: {str(e)}", 500)


# API to return a prompt by name
@app.route('/prompt/<string:name>', methods=['GET'])
def get_prompt_by_name(name):
    try:
        prompt = Prompt.get_by(name=name)  # Fetch prompt by name
        if not prompt:
            return error_response("Prompt not found", 404)
        return success_response(f"Fetched prompt: {name}", {"prompt": prompt})
    except Exception as e:
        print(e)
        return error_response(f"Error fetching prompt: {str(e)}", 500)


# New API to return all files
@app.route('/files', methods=['GET'])
def get_all_files():
    try:
        # Collect filters from query params
        project_id = request.args.get('project_id')
        name = request.args.get('name')
        status = request.args.get('status')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        order_by = request.args.get('order_by', 'created_at')
        order_direction = request.args.get('order_direction', 'desc').lower()

        # Validate order direction
        if order_direction not in ['asc', 'desc']:
            order_direction = 'desc'

        # Build WHERE clause
        where_clauses = []
        params = {}

        if project_id:
            where_clauses.append("project_id = :project_id")
            params['project_id'] = project_id
        if name:
            where_clauses.append("LOWER(name) LIKE LOWER(:name)")
            params['name'] = f"%{name.lower()}%"
        if status:
            where_clauses.append("status = :status")
            params['status'] = status

        where_clause = " AND ".join(where_clauses)
        if where_clause:
            where_clause = "WHERE " + where_clause

        # Calculate offset
        offset = (page - 1) * per_page

        # Count query
        count_query = text(f"SELECT COUNT(*) FROM file {where_clause}")
        total_files = db.session.execute(count_query, params).scalar()

        # Data query
        data_query = text(f"""
            SELECT * FROM file
            {where_clause}
            ORDER BY {order_by} {order_direction}
            LIMIT :per_page OFFSET :offset
        """)
        params.update({'per_page': per_page, 'offset': offset})
        result = db.session.execute(data_query, params)
        files = [dict(row) for row in result.fetchall()]

        return success_response("Fetched filtered and paginated files", {
            "files": files,
            "total": total_files,
            "page": page,
            "per_page": per_page
        })
    except Exception as e:
        print(e)
        return error_response(f"Error fetching files: {str(e)}", 500)
@app.route('/bid_files', methods=['GET'])
def get_all_bid_files():
    try:
        # Collect filters from query params
        bid_id = request.args.get('bid_id')
        name = request.args.get('name')
        status = request.args.get('status')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        order_by = request.args.get('order_by', 'created_at')
        order_direction = request.args.get('order_direction', 'desc').lower()

        # Validate order direction
        if order_direction not in ['asc', 'desc']:
            order_direction = 'desc'

        # Build WHERE clause
        where_clauses = []
        params = {}

        if bid_id:
            where_clauses.append("bid_id = :bid_id")
            params['bid_id'] = bid_id
        if name:
            where_clauses.append("LOWER(name) LIKE LOWER(:name)")
            params['name'] = f"%{name.lower()}%"
        if status:
            where_clauses.append("status = :status")
            params['status'] = status

        where_clause = " AND ".join(where_clauses)
        if where_clause:
            where_clause = "WHERE " + where_clause

        # Calculate offset
        offset = (page - 1) * per_page

        # Count query
        count_query = text(f"SELECT COUNT(*) FROM bid_files {where_clause}")
        total_files = db.session.execute(count_query, params).scalar()

        # Data query
        data_query = text(f"""
            SELECT * FROM bid_files
            {where_clause}
            ORDER BY {order_by} {order_direction}
            LIMIT :per_page OFFSET :offset
        """)
        params.update({'per_page': per_page, 'offset': offset})
        result = db.session.execute(data_query, params)
        files = [dict(row) for row in result.fetchall()]

        return success_response("Fetched filtered and paginated files", {
            "files": files,
            "total": total_files,
            "page": page,
            "per_page": per_page
        })
    except Exception as e:
        print(e)
        return error_response(f"Error fetching files: {str(e)}", 500)


# New API to return a file by ID
@app.route('/file/<string:file_id>', methods=['GET'])
def get_file_by_id(file_id):
    try:
        file = File.get_by(id=file_id)  # Fetch file by ID
        if not file:
            return error_response("File not found", 404)

        # Construct the full path to the file
        file_name = file[0]['name']  # File name
        base_dir = os.path.abspath(os.getcwd())
        file_directory = os.path.join(base_dir, file[0]['file_dirtry'], file[0]['project_id'], file_name)  # Directory containing the file
        return send_file(file_directory, as_attachment=False)  # as_attachment=False for browser viewing
    except Exception as e:
        print(e)
        return error_response(f"Error fetching file: {str(e)}", 500)

# Route to create a report
@app.route('/reports', methods=['POST'])
def create_report():
    try:
        client = request.form.get('client')
        intro_template = request.form.get('intro_template')
        body_template = request.form.get('body_template')
        conclusion_template = request.form.get('conclusion_template')

        new_report = reports.create(client, intro_template, body_template, conclusion_template)
        return jsonify(new_report), 201
    except Exception as e:
        return jsonify({"error": str(e)}), 400

# Route to update a report
@app.route('/reports/<id>', methods=['PUT'])
def update_report(id):
    try:
        intro_template = request.form.get('intro_template')
        body_template = request.form.get('body_template')
        conclusion_template = request.form.get('conclusion_template')

        updated_report = reports.update(id, intro_template, body_template, conclusion_template)
        if updated_report:
            return jsonify(updated_report), 200
        return jsonify({"error": "Report not found"}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 400

# Route to delete a report
@app.route('/reports/<id>', methods=['DELETE'])
def delete_report(id):
    try:
        result = reports.delete(id)
        if result:
            return jsonify({"message": "Report deleted successfully"}), 200
        return jsonify({"error": "Report not found"}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 400

# Route to list all reports
@app.route('/reports', methods=['GET'])
def list_reports():
    try:
        filters = request.args.to_dict()  # Get filters from query params
        reports_list = reports.list(**filters)
        return jsonify(reports_list), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 400

# Route to view a single report by ID
@app.route('/reports/<id>', methods=['GET'])
def view_report(id):
    try:
        report = reports.view(id)
        if report:
            return jsonify(report), 200
        return jsonify({"error": "Report not found"}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 400

# Route to create a document numbering template
@app.route('/document_numbering', methods=['POST'])
def create_document_numbering():
    try:
        client = request.form.get('client')
        template = request.form.get('template')

        new_template = document_numbering.create(client, template)
        return jsonify(new_template), 201
    except Exception as e:
        return jsonify({"error": str(e)}), 400

# Route to update a document numbering template
@app.route('/document_numbering/<id>', methods=['PUT'])
def update_document_numbering(id):
    try:
        template = request.form.get('template')

        updated_template = document_numbering.update(id, template)
        if updated_template:
            return jsonify(updated_template), 200
        return jsonify({"error": "Template not found"}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 400

# Route to delete a document numbering template
@app.route('/document_numbering/<id>', methods=['DELETE'])
def delete_document_numbering(id):
    try:
        result = document_numbering.delete(id)
        if result:
            return jsonify({"message": "Template deleted successfully"}), 200
        return jsonify({"error": "Template not found"}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 400

# Route to list all document numbering templates
@app.route('/document_numbering', methods=['GET'])
def list_document_numbering():
    try:
        filters = request.args.to_dict()  # Get filters from query params
        templates_list = document_numbering.list(**filters)
        return jsonify(templates_list), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 400

# Route to view a single document numbering template by ID
@app.route('/document_numbering/<id>', methods=['GET'])
def view_document_numbering(id):
    try:
        template = document_numbering.view(id)
        if template:
            return jsonify(template), 200
        return jsonify({"error": "Template not found"}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 400


# @app.route('/test_scp', methods=['GET'])
# def tester():
#     with app.app_context():
#         q.enqueue(evaluate_files_scp_task, 'd11cf7c3-504f-44bc-a6f8-57877aef4005', retry=Retry(max=2))

#     return jsonify({"message": "Processing started"}), 202


@app.route('/create_user', methods=['POST'])
def create_user():
    try:
        data = request.get_json()
        name = data.get('name')
        status = data.get('status')
        role = data.get('role')
        id = data.get('id')

        if not name or not status or not role:
            return error_response('Name, status, and role are required')

        user_data = {
            'name': name,
            'status': status,
            'id': id,
            'role': role
        }
        user = Users.create(**user_data)

        if user:
            return success_response('User created successfully', user)
        else:
            return error_response('Failed to create user')

    except Exception as e:
        return error_response(f"Error creating user: {str(e)}", 500)

@app.route('/update_user/<user_id>', methods=['PUT'])
def update_user(user_id):
    try:
        name = request.form.get('name')
        status = request.form.get('status')
        role = request.form.get('role')

        if not name and not status and not role:
            return error_response('At least one field is required to update')

        user = Users.update(user_id, name=name, status=status, role=role)

        if user:
            return success_response('User updated successfully', user)
        else:
            return error_response('Failed to update user')

    except Exception as e:
        return error_response(f"Error updating user: {str(e)}", 500)


@app.route('/delete_user/<user_id>', methods=['DELETE'])
def delete_user(user_id):
    try:
        user = Users.delete_by(id=user_id)

        if user:
            return success_response('User deleted successfully', user)
        else:
            return error_response('Failed to delete user')

    except Exception as e:
        return error_response(f"Error deleting user: {str(e)}", 500)

def run_async_task(task, *args, **kwargs):
    """
    Runs an async function in the existing event loop running in a separate thread.
    """
    future = asyncio.run_coroutine_threadsafe(task(*args, **kwargs), main_event_loop)
    return future.result()


@app.route('/process_document_numbering', methods=['POST'])
def extract_document_numbering():
    try:
        files = request.files.getlist('file')

        if not files:
            return error_response('No selected files', 400)

        # Ensure the target folder exists
        folder_path = os.path.join(UPLOAD_FOLDER, 'lab')
        os.makedirs(folder_path, mode=0o775, exist_ok=True)

        # Process the first file (assuming only one file is uploaded)
        file = files[0]
        filename = secure_filename(file.filename)
        file_path = os.path.join(folder_path, filename)
        file.save(file_path)  # Save file

        # Determine file type
        file_type = os.path.splitext(file_path)[-1].lower().lstrip('.')

        # Process file based on type
        if file_type == 'pdf':
            text_single_string, chunks = run_async_task(open_ai_processor.extract_text_from_pdf, file_path)
            result = chunks
        elif file_type in ["docx", "doc"]:
            chunks = run_async_task(open_ai_processor.extract_text_from_docx, file_path)
            result = chunks[1]
        elif file_type in ["csv", "xls", "xlsx", "xlsm"]:
            chunks = run_async_task(open_ai_processor.extract_text_from_excel_or_csv_batch, file_path)
            result = chunks[1]
        else:
            raise ValueError(f"Unsupported file type: {file_type}")

        # Load disciplines (consider caching this)
        specs_dir_path = os.path.join(
            engr_document_number_agent.parent_dir,
            engr_document_number_agent.env_data.get("SPECS_DIR", ""),
            'Aienergy.discipline.json'
        )

        with open(specs_dir_path, 'r', encoding='utf-8') as file:
            disciplines = json.load(file)

        # Extract document info
        document_info = run_async_task(
            engr_document_number_agent.Agent_document_info_extraction,
            result[:3], disciplines
        )

        print('Document info processed successfully...')
        return success_response('Document Processed successfully', {
            "filename": filename,  # Return filename
            "document_info": document_info
        })


    except Exception as e:
        return error_response(f"Error Processing Document: {str(e)}", 500)


@app.route('/test_document_extraction', methods=['POST'])
def test_document_extraction():
    try:
        files = request.files.getlist('file')

        if not files:
            return error_response('No selected files', 400)

        # Ensure the target folder exists
        folder_path = os.path.join(UPLOAD_FOLDER, 'lab')
        os.makedirs(folder_path, mode=0o775, exist_ok=True)

        # Process the first file (assuming only one file is uploaded)
        file = files[0]
        filename = secure_filename(file.filename)
        file_path = os.path.join(folder_path, filename)
        file.save(file_path)  # Save file

        # Determine file type
        file_type = os.path.splitext(file_path)[-1].lower().lstrip('.')

        # Process file based on type
        if file_type == 'pdf':
            text_single_string, chunks = run_async_task(open_ai_processor.extract_text_from_pdf, file_path)
            extracted_content = text_single_string
        elif file_type in ["docx", "doc"]:
            chunks = run_async_task(open_ai_processor.extract_text_from_docx, file_path)
            extracted_content = chunks[1]  # Extract full document content
        elif file_type in ["csv", "xls", "xlsx"]:
            chunks = run_async_task(open_ai_processor.extract_text_from_excel_or_csv_batch, file_path)
            extracted_content = chunks[1]  # Extract full document content
        else:
            raise ValueError(f"Unsupported file type: {file_type}")

        print('Document extraction test successful...')
        return success_response('Document extracted successfully', {
            "filename": filename,  
            "extracted_content": extracted_content
        })

    except Exception as e:
        return error_response(f"Error extracting document content: {str(e)}", 500)

# Equipment Package APIs
@app.route('/equipment_packages', methods=['POST'])
def create_equipment_package():
    try:
        data = request.get_json()
        name = data.get('name')
        user_id = data.get('user_id', None)
        
        if not name:
            return error_response('Name is  required', 400)
        

            
        # Check if equipment package with same name exists for this user
        existing_package = EquipmentPackage.get_by(user_id=user_id, name=name)
        if existing_package:
            return error_response('Equipment package with this name already exists', 400)
            
        # Generate UUID for the package
        package_id = str(uuid.uuid4())
            
        # Create the equipment package with ID and user_id
        equipment_package = EquipmentPackage.create(
            user_id=user_id,
            id=package_id,
            name=name
        )
        
        if equipment_package:
            return success_response('Equipment package created successfully', equipment_package, 201)
        else:
            return error_response('Failed to create equipment package')
            
    except Exception as e:
        return error_response(f"Error creating equipment package: {str(e)}", 500)


@app.route('/equipment_packages', methods=['GET'])
def get_equipment_packages():
    try:
        user_id = request.args.get('user_id', None)

        # Only get packages for the specified user
        packages = EquipmentPackage.get_by(user_id=user_id)
        return success_response('Equipment packages retrieved successfully', {'packages': packages})
    except Exception as e:
        return error_response(f"Error retrieving equipment packages: {str(e)}", 500)


@app.route('/equipment_packages/<package_id>', methods=['GET'])
def get_equipment_package(package_id):
    try:
        user_id = request.args.get('user_id', None)
        
        # Only get the package if it belongs to the user
        package = EquipmentPackage.get_single(id=package_id, user_id=user_id)
        if not package:
            return error_response('Equipment package not found', 404)
        return success_response('Equipment package retrieved successfully', package)
    except Exception as e:
        return error_response(f"Error retrieving equipment package: {str(e)}", 500)


@app.route('/equipment_packages/<package_id>', methods=['PUT'])
def update_equipment_package(package_id):
    try:
        data = request.get_json()
        name = data.get('name')
        user_id = data.get('user_id', None)
        
        if not name:
            return error_response('Name is not  required', 400)
         
        # Check if another package with same name exists for this user
        existing_package = EquipmentPackage.get_by(user_id=user_id, name=name)
        if existing_package and existing_package[0]['id'] != package_id:
            return error_response('Equipment package with this name already exists', 400)
            
        # Only update if the package belongs to the user
        package = EquipmentPackage.update(id=package_id, user_id=user_id, name=name)
        if package:
            return success_response('Equipment package updated successfully', package)
        else:
            return error_response('Equipment package not found or does not belong to the user', 404)
            
    except Exception as e:
        return error_response(f"Error updating equipment package: {str(e)}", 500)


@app.route('/equipment_packages/<package_id>', methods=['DELETE'])
def delete_equipment_package(package_id):
    try:
        user_id = request.args.get('user_id', None)
        
            
        # Check if package exists and belongs to the user
        package = EquipmentPackage.get_single(id=package_id, user_id=user_id)
        if not package:
            return error_response('Equipment package not found or does not belong to the user', 404)
            
        # Get all categories under this package
        categories = EquipmentCategory.get_by(user_id=user_id, equipment_package_id=package_id)
        
        # For each category, delete its specs first
        for category in categories:
            # Delete all specs under this category
            EquipmentCategorySpecs.delete_by(user_id=user_id, equipment_category_id=category['id'])
            
        # Delete all categories under this package
        EquipmentCategory.delete_by(user_id=user_id, equipment_package_id=package_id)
            
        # Finally delete the package itself
        EquipmentPackage.delete_by(user_id=user_id, id=package_id)
        return success_response('Equipment package and all its contents deleted successfully')
        
    except Exception as e:
        return error_response(f"Error deleting equipment package: {str(e)}", 500)


# Equipment Category APIs
@app.route('/equipment_categories', methods=['POST'])
def create_equipment_category():
    try:
        data = request.get_json()
        name = data.get('name')
        equipment_package_id = data.get('equipment_package_id')
        user_id = data.get('user_id', None)
        
        if not name or not equipment_package_id:
            return error_response('Name, equipment_package_id, are required', 400)
            
        # Validate user exist
            
        # Check if parent package exists and belongs to the user
        parent_package = EquipmentPackage.get_single(id=equipment_package_id, user_id=user_id)
        if not parent_package:
            return error_response('Parent equipment package not found or does not belong to the user', 404)
        
        # Check if category name already exists under the specified equipment package
        existing_category = EquipmentCategory.get_by(user_id=user_id, name=name, equipment_package_id=equipment_package_id)
        if existing_category:
            return error_response('Equipment category with this name already exists under the specified package', 400)

        id = str(uuid.uuid4())
        category = EquipmentCategory.create(
            user_id=user_id,
            id=id,
            name=name,
            equipment_package_id=equipment_package_id
        )
        
        if category:
            return success_response('Equipment category created successfully', category, 201)
        else:
            return error_response('Failed to create equipment category')
            
    except Exception as e:
        return error_response(f"Error creating equipment category: {str(e)}", 500)


@app.route('/equipment_categories', methods=['GET'])
def get_equipment_categories():
    try:
        user_id = request.args.get('user_id', None)
        # Get categories for this user
        categories = EquipmentCategory.get_by(user_id=user_id)
        return success_response('Equipment categories retrieved successfully', {'categories': categories})
    except Exception as e:
        return error_response(f"Error retrieving equipment categories: {str(e)}", 500)


@app.route('/equipment_categories/<category_id>', methods=['GET'])
def get_equipment_category(category_id):
    try:
        user_id = request.args.get('user_id', None)
        
        
        # Get category if it belongs to the user
        category = EquipmentCategory.get_single(id=category_id, user_id=user_id)
        if not category:
            return error_response('Equipment category not found or does not belong to the user', 404)
        return success_response('Equipment category retrieved successfully', category)
    except Exception as e:
        return error_response(f"Error retrieving equipment category: {str(e)}", 500)


@app.route('/equipment_categories/<category_id>', methods=['PUT'])
def update_equipment_category(category_id):
    try:
        data = request.get_json()
        name = data.get('name')
        equipment_package_id = data.get('equipment_package_id')
        user_id = data.get('user_id', None)
        
        if not name:
            return error_response('Name and user_id are required', 400)
            
            
        # If package_id is being changed, verify new package exists and belongs to the user
        if equipment_package_id:
            parent_package = EquipmentPackage.get_single(id=equipment_package_id, user_id=user_id)
            if not parent_package:
                return error_response('Parent equipment package not found or does not belong to the user', 404)
                
        # Update the category
        category = EquipmentCategory.update(
            id=category_id,
            user_id=user_id,
            name=name,
            equipment_package_id=equipment_package_id if equipment_package_id else None
        )
        
        if category:
            return success_response('Equipment category updated successfully', category)
        else:
            return error_response('Equipment category not found or does not belong to the user', 404)
            
    except Exception as e:
        return error_response(f"Error updating equipment category: {str(e)}", 500)


@app.route('/equipment_categories/<category_id>', methods=['DELETE'])
def delete_equipment_category(category_id):
    try:
        user_id = request.args.get('user_id', None)
        
        # Check if category exists and belongs to the user
        category = EquipmentCategory.get_single(id=category_id, user_id=user_id)
        if not category:
            return error_response('Equipment category not found or does not belong to the user', 404)
            
        # First delete all specs under this category
        EquipmentCategorySpecs.delete_by(user_id=user_id, equipment_category_id=category_id)
            
        # Then delete the category itself
        EquipmentCategory.delete_by(user_id=user_id, id=category_id)
        return success_response('Equipment category and all its specifications deleted successfully')
        
    except Exception as e:
        return error_response(f"Error deleting equipment category: {str(e)}", 500)




# Equipment Category Specs APIs
@app.route('/equipment_category_specs', methods=['POST'])
def create_equipment_category_spec():
    try:
        data = request.get_json()
        name = data.get('name')
        equipment_category_id = data.get('equipment_category_id')
        unit = data.get('unit')
        discipline = data.get('discipline')
        user_id = data.get('user_id', None)
        
        if not name or not equipment_category_id:
            return error_response('Name, equipment_category_id, are required', 400)
            
            
        # Check if parent category exists and belongs to the user
        parent_category = EquipmentCategory.get_single(id=equipment_category_id, user_id=user_id)
        if not parent_category:
            return error_response('Parent equipment category not found or does not belong to the user', 404)
            
        # Check if spec with same name exists under this category
        existing_spec = EquipmentCategorySpecs.get_by(
            user_id=user_id,
            name=name,
            equipment_category_id=equipment_category_id
        )
        
        if existing_spec:
            return error_response('Specification with this name already exists in this category', 400)
            
        id = str(uuid.uuid4())
        spec = EquipmentCategorySpecs.create(
            user_id=user_id,
            id=id,
            name=name,
            equipment_category_id=equipment_category_id,
            unit=unit,
            discipline=discipline
        )
        
        if spec:
            return success_response('Equipment category spec created successfully', spec, 201)
        else:
            return error_response('Failed to create equipment category spec')
            
    except Exception as e:
        return error_response(f"Error creating equipment category spec: {str(e)}", 500)


@app.route('/equipment_category_specs', methods=['GET'])
def get_equipment_category_specs():
    try:
        user_id = request.args.get('user_id', None)
        
        # Get specs for this user
        specs = EquipmentCategorySpecs.get_by(user_id=user_id)
        return success_response('Equipment category specs retrieved successfully', {'specs': specs})
    except Exception as e:
        return error_response(f"Error retrieving equipment category specs: {str(e)}", 500)


@app.route('/equipment_category_specs/<spec_id>', methods=['GET'])
def get_equipment_category_spec(spec_id):
    try:
        user_id = request.args.get('user_id', None)
        # Validate user exists
        user = Users.get_single(user_id)
        if not user:
            return error_response('User not found', 404)
        
        # Get spec if it belongs to the user
        spec = EquipmentCategorySpecs.get_single(id=spec_id, user_id=user_id)
        if not spec:
            return error_response('Equipment category spec not found or does not belong to the user', 404)
        return success_response('Equipment category spec retrieved successfully', spec)
    except Exception as e:
        return error_response(f"Error retrieving equipment category spec: {str(e)}", 500)


@app.route('/equipment_category_specs/<spec_id>', methods=['PUT'])
def update_equipment_category_spec(spec_id):
    try:
        data = request.get_json()
        name = data.get('name')
        equipment_category_id = data.get('equipment_category_id')
        unit = data.get('unit')
        discipline = data.get('discipline')
        user_id = data.get('user_id', None)
        
        if not name:
            return error_response('Name  is required', 400)
            
        # If category_id is being changed, verify new category exists and belongs to the user
        if equipment_category_id:
            parent_category = EquipmentCategory.get_single(id=equipment_category_id, user_id=user_id)
            if not parent_category:
                return error_response('Parent equipment category not found or does not belong to the user', 404)
                
            # Check if spec with same name exists under new category
            existing_spec = EquipmentCategorySpecs.get_by(
                user_id=user_id,
                name=name,
                equipment_category_id=equipment_category_id
            )
            
            if existing_spec and existing_spec[0]['id'] != spec_id:
                return error_response('Specification with this name already exists in this category', 400)
                
        # Update the spec
        spec = EquipmentCategorySpecs.update(
            id=spec_id,
            user_id=user_id,
            name=name,
            equipment_category_id=equipment_category_id if equipment_category_id else None,
            unit=unit,
            discipline=discipline
        )
        
        if spec:
            return success_response('Equipment category spec updated successfully', spec)
        else:
            return error_response('Equipment category spec not found or does not belong to the user', 404)
            
    except Exception as e:
        return error_response(f"Error updating equipment category spec: {str(e)}", 500)


@app.route('/equipment_category_specs/<spec_id>', methods=['DELETE'])
def delete_equipment_category_spec(spec_id):
    try:
        user_id = request.args.get('user_id', None)
        
        
        # Check if spec exists and belongs to the user
        spec = EquipmentCategorySpecs.get_single(id=spec_id, user_id=user_id)
        if not spec:
            return error_response('Equipment category spec not found or does not belong to the user', 404)
            
        # Delete the spec
        EquipmentCategorySpecs.delete_by(user_id=user_id, id=spec_id)
        return success_response('Equipment category spec deleted successfully')
        
    except Exception as e:
        return error_response(f"Error deleting equipment category spec: {str(e)}", 500)


@app.route('/equipment_packages/<package_id>/hierarchy', methods=['GET'])
def get_equipment_package_hierarchy(package_id):
    try:
        user_id = request.args.get('user_id', None)
       
        
        # Get the equipment package if it belongs to the user
        package = EquipmentPackage.get_single(id=package_id, user_id=user_id)
        if not package:
            return error_response('Equipment package not found or does not belong to the user', 404)
            
        # Get all categories under this package
        categories = EquipmentCategory.get_by(user_id=user_id, equipment_package_id=package_id)
        
        # For each category, get its specs
        for category in categories:
            specs = EquipmentCategorySpecs.get_by(user_id=user_id, equipment_category_id=category['id'])
            category['specifications'] = specs
            
        # Add categories to package data
        package['categories'] = categories
        
        return success_response('Equipment package hierarchy retrieved successfully', package)
        
    except Exception as e:
        return error_response(f"Error retrieving equipment package hierarchy: {str(e)}", 500)


@app.route('/equipment_hierarchy', methods=['GET'])
def get_all_equipment_hierarchy():
    try:
        user_id = request.args.get('user_id', None)
    
        
        # Get all equipment packages for this user
        packages = EquipmentPackage.get_by(user_id=user_id)
        
        # For each package, get its categories and specs
        for package in packages:
            # Get all categories under this package
            categories = EquipmentCategory.get_by(user_id=user_id, equipment_package_id=package['id'])
            
            # For each category, get its specs
            for category in categories:
                specs = EquipmentCategorySpecs.get_by(user_id=user_id, equipment_category_id=category['id'])
                category['specifications'] = specs
                
            # Add categories to package data
            package['categories'] = categories
            
        return success_response('Complete equipment hierarchy retrieved successfully', {'packages': packages})
        
    except Exception as e:
        return error_response(f"Error retrieving equipment hierarchy: {str(e)}", 500)


@app.route('/equipment_packages/full_data', methods=['POST'])
def get_equip_by_user():
    try:
        data = request.get_json()
        # Get the list of equipment package IDs from the request JSON
        package_ids = data.get('equipment_package_ids')
        user_id = data.get('user_id', None)
        
        if not package_ids or not isinstance(package_ids, list):
            return error_response('A list of equipment package IDs is required', 400)
        
        # Get the equipment packages with their specs for this user
        result_data = EquipmentPackage.get_equipment_with_specs(
            equipment_package_ids=package_ids,
            user_id=user_id
        )
        
        if not result_data:
            return error_response('No equipment packages found for the provided IDs or user', 404)
        
        return success_response('Equipment package hierarchy retrieved successfully', result_data)
        
    except Exception as e:
        return error_response(f"Error retrieving equipment package hierarchy: {str(e)}", 500)
    


@app.route('/equipment_packages/all', methods=['GET'])
def get_all_equipments():
    try:
    
        # Get the equipment packages with their specs for this user
        result_data = EquipmentPackage.get_all_equipment_packages()
        
        if not result_data:
            return error_response('No equipment packages found for the provided IDs or user', 404)
        
        return success_response('Equipment package hierarchy retrieved successfully', result_data)
        
    except Exception as e:
        return error_response(f"Error retrieving equipment package hierarchy: {str(e)}", 500)


@app.route('/request_cbp_report', methods=['POST'])
def trigger_technical_report():
    start_time = time.time()

    try:
        data = request.get_json()

        report_bid_ids = data.get('report_bid_ids')  # Expecting list or JSON-encoded string
        equipment_ids = data.get('equipment_ids')
        user_id = data.get('user_id')     # Expecting list or JSON-encoded string
        user = Users.get_single(user_id)
        user_id = user["id"]
        if not user:
            return error_response('User not found', 404)

        print("Report Bid IDs:", report_bid_ids)
        print("Equipment IDs:", equipment_ids)
        cbp_request_id = data.get('cbp_request_id')

        request_id = str(uuid.uuid4())
        
        create_kwargs = {
            'id': request_id,
            'report_bid_ids': json.dumps(report_bid_ids),
            'report_equipment': json.dumps(equipment_ids),
            
            'user_id':user_id,
            'status': 'pending'
        }

        print('Creating technical report entry...')
        result = CbpRequirementTechnicalReport.create(**create_kwargs)

        request_id = {

            "request_id":result["id"]
        }

        if result:
            run_cbp_report_task.send(
                req_id=result["id"],
                cbp_request_id = cbp_request_id
            )
            return success_response('cbp report request created succesfully', request_id)
        else:
            return jsonify({
                "success": False,
                "message": "Failed to create the report request"
            }), 500

    except Exception as e:
        print(f"Error occurred: {str(e)}")
        return error_response(f"Error creating teachnical report request: {str(e)}", 500)


# API to get the last_time of a transmittal run by ID
@app.route('/transmittal_run/<string:transmittal_id>/last_time', methods=['GET'])
def get_transmittal_run_last_time(transmittal_id):
    try:
        transmittal_run = Transmittal_run.get_single(transmittal_id)  # Fetch transmittal run by ID
        if not transmittal_run:
            return error_response("Transmittal run not found", 404)
        return success_response("Fetched transmittal run last_time", transmittal_run)
    except Exception as e:
        print(e)
        return error_response(f"Error fetching transmittal run last_time: {str(e)}", 500)

# API to update the last_time of a transmittal run by ID
@app.route('/transmittal_run/<string:transmittal_id>/last_time', methods=['PUT'])
def update_transmittal_run_last_time(transmittal_id):
    try:
        print("transmittal_id >>", transmittal_id)
        current_time =  datetime.now(timezone.utc)
        current_time_utc = datetime.now(timezone.utc)
        desired_timezone = timezone(timedelta(hours=2))  # UTC+2
        converted_time = current_time_utc.astimezone(desired_timezone)# Get the current time

        updated_transmittal_run = Transmittal_run.update(transmittal_id, last_time=converted_time)  # Update last_time with current time
        if not updated_transmittal_run:
            return error_response("Failed to update transmittal run", 404)

        return success_response("Updated transmittal run last_time", {"last_time": updated_transmittal_run['last_time']})
    except Exception as e:
        print(e)
        return error_response(f"Error updating transmittal run last_time: {str(e)}", 500)

@app.route('/transmittal_daily_work', methods=['GET'])
def run_transmittal_work():
    try:
        print("run_transmittal_work")
        # all_projects = Project.get_by(id='5b6b77d1-5e71-499e-9a44-3594374ffa85')
        # all_projects = Project.get_by(is_ineight=1)
        # print("all_projects >> ", all_projects)
        ineight_client = InEightClient(fernet_key=FERNET_KEY)
        data = ineight_client.fetch_project_transmittal_data()
        return success_response("Updated transmittal run last_time", {"all_projects": data})
    except Exception as e:
        print(e)
        return error_response(f"Error updating transmittal run last_time: {str(e)}", 500)
@app.route('/transmittal_download_process', methods=['GET'])
def transmittal_download_process():
    try:
        print("run_transmittal_work")
        data = ineight_download_handle(FERNET_KEY, Project, File, run_file_upload_task, app)
        return success_response("Updated transmittal run last_time", {"all_projects": data})
    except Exception as e:
        print(e)
        return error_response(f"Error updating transmittal run last_time: {str(e)}", 500)
    
@app.route('/create_vendor', methods=['POST'])
def create_vendor():
    vendor_name = request.form.get('name')
    user_id = request.form.get('user_id')
    parent_id = request.form.get('parent_id')
    vendor_id = request.form.get('uuid') if request.form.get('uuid') else str(uuid.uuid4())

    # Adding numbering rule for vendor
    project_id = Project.get_single(parent_id)['parent_id']

    if project_id:
        numbering_rule = Project.get_single(project_id)['numbering_rule']
    else:
        numbering_rule = None
    
    
    if not vendor_name:
        return error_response('Vendor name is required', 400)
    try:
        Project.create(id=vendor_id, name=vendor_name, entity_type='vendor', user_id=user_id, parent_id=parent_id, numbering_rule=numbering_rule)
        project_folder = os.path.join(CHRONOBID_FOLDER, vendor_id)
        os.makedirs(project_folder, mode=0o777, exist_ok=True)
        # Ensure the directory is writable
        os.chmod(project_folder, 0o777)

        # Check if the directory was created
        if os.path.exists(project_folder) and os.path.isdir(project_folder):
            directory_status = "Directory created successfully"
        else:
            directory_status = "Failed to create directory"

    except Exception as e:
        db.session.rollback()
        return error_response(f'Error creating tender package: {str(e)}', 500)

    return success_response("Vendor created successfully", {'id': vendor_id, 'directory_status': directory_status}, 201)

@app.route('/fetch_vendor', methods=['GET'])
def fetch_vendor():
    args = request.args.to_dict()
    user_id = request.args.get('user_id')
    # print(args)
    vendor = Project.get_by(entity_type='vendor', user_id=user_id)

    return {'vendor': vendor}, 200

# New API to get a single vendor by ID
@app.route('/vendor/<string:vendor_id>', methods=['GET'])
def get_vendor(vendor_id):
    try:
        vendor = Project.get_single(vendor_id)
        if not vendor:
            return error_response('Vendor not found', 404)
        
        # Verify it's a vendor type
        if vendor.get('entity_type') != 'vendor':
            return error_response('Specified ID is not a vendor', 400)
            
        return success_response('Vendor retrieved successfully', {'vendor': vendor})
    except Exception as e:
        return error_response(f"Error retrieving vendor: {str(e)}", 500)
@app.route('/vendor/parent/<string:parent_id>', methods=['GET'])
def get_vendor_parent(parent_id):
    try:
        user_id = request.args.get('user_id')
        print(user_id, parent_id)
        vendor = Project.get_by(entity_type='vendor', user_id=user_id, parent_id=parent_id)
        print("vendor >> ", vendor)
        if not vendor:
            return error_response('Vendor not found', 404)
            
        return success_response('Vendor retrieved successfully', {'vendor': vendor})
    except Exception as e:
        return error_response(f"Error retrieving vendor: {str(e)}", 500)

# New API to update a vendor
@app.route('/vendor/<string:vendor_id>', methods=['PUT'])
def update_vendor(vendor_id):
    try:
        # Get the vendor to update
        vendor = Project.get_single(vendor_id)
        if not vendor:
            return error_response('Vendor not found', 404)
            
        # Verify it's a vendor type
        if vendor.get('entity_type') != 'vendor':
            return error_response('Specified ID is not a vendor', 400)
        
        # Get the updated name from the request
        name = request.form.get('name')
        if not name:
            return error_response('Name is required', 400)
            
        # Update the vendor
        updated_vendor = Project.update(vendor_id, name=name)
        if not updated_vendor:
            return error_response('Failed to update vendor', 500)
            
        return success_response('Vendor updated successfully', {'vendor': updated_vendor})
    except Exception as e:
        return error_response(f"Error updating vendor: {str(e)}", 500)
    
# New API routes for Engineer
@app.route('/create_engineer', methods=['POST'])
def create_engineer():
    package_name = request.form.get('name')
    user_id = request.form.get('user_id')
    parent_id = request.form.get('parent_id')    
    package_id = request.form.get('uuid') if request.form.get('uuid') else str(uuid.uuid4())

    # Adding numbering rule for vendor
    project_id = Project.get_single(parent_id)['parent_id']

    if project_id:
        numbering_rule = Project.get_single(project_id)['numbering_rule']
    else:
        numbering_rule = None

    if not package_name:
        return error_response('Package name is required', 400)
    try:
        Project.create(id=package_id, name=package_name, entity_type='engineer', user_id=user_id, parent_id=parent_id, numbering_rule=numbering_rule)
        project_folder = os.path.join(CHRONOBID_FOLDER, package_id)
        os.makedirs(project_folder, mode=0o777, exist_ok=True)
        # Ensure the directory is writable
        os.chmod(project_folder, 0o777)

        # Check if the directory was created
        if os.path.exists(project_folder) and os.path.isdir(project_folder):
            directory_status = "Directory created successfully"
        else:
            directory_status = "Failed to create directory"

    except Exception as e:
        db.session.rollback()
        return error_response(f'Error creating engineer: {str(e)}', 500)

    return success_response("Engineer created successfully", {'id': package_id, 'directory_status': directory_status}, 201)

@app.route('/fetch_engineer', methods=['GET'])
def fetch_engineer():
    args = request.args.to_dict()
    user_id = request.args.get('user_id')
    # print(args)
    engineer = Project.get_by(entity_type='engineer', user_id=user_id)

    return {'engineer': engineer}, 200

# New API to get a single engineer by ID
@app.route('/engineer/<string:engineer_id>', methods=['GET'])
def get_engineer(engineer_id):
    try:
        engineer = Project.get_single(engineer_id)
        if not engineer:
            return error_response('engineer not found', 404)
        
        # Verify it's a vendor type
        if engineer.get('entity_type') != 'engineer':
            return error_response('Specified ID is not an engineer', 400)
            
        return success_response('engineer retrieved successfully', {'engineer': engineer})
    except Exception as e:
        return error_response(f"Error retrieving engineer: {str(e)}", 500)

@app.route('/engineer/parent/<string:parent_id>', methods=['GET'])
def get_engineer_parent(parent_id):
    try:
        user_id = request.args.get('user_id')
        engineer = Project.get_by(entity_type='engineer', user_id=user_id, parent_id=parent_id)
        if not engineer:
            return error_response('engineer not found', 404)
            
        return success_response('engineer retrieved successfully', {'engineer': engineer})
    except Exception as e:
        return error_response(f"Error retrieving engineer: {str(e)}", 500)

# New API to update an engineer
@app.route('/engineer/<string:engineer_id>', methods=['PUT'])
def update_engineer(engineer_id):
    try:
        # Get the vendor to update
        engineer = Project.get_single(engineer_id)
        if not engineer:
            return error_response('engineer not found', 404)
            
        # Verify it's a vendor type
        if engineer.get('entity_type') != 'engineer':
            return error_response('Specified ID is not an Engineer', 400)
        
        # Get the updated name from the request
        name = request.form.get('name')
        if not name:
            return error_response('Name is required', 400)
            
        # Update the vendor
        updated_engineer = Project.update(engineer_id, name=name)
        if not updated_engineer:
            return error_response('Failed to update engineer', 500)
            
        return success_response('engineer updated successfully', {'engineer': updated_engineer})
    except Exception as e:
        return error_response(f"Error updating vendor: {str(e)}", 500)
    
@app.route('/get_standard_project_files', methods=['GET'])
def get_standard_project_files():
    project_id = request.args.get('project_id')
    if not project_id:
        return error_response("Project ID is required", status_code=400)
    
    try:
        # Get all files associated with the project_id
        files = File.get_by(project_id=project_id, category='standard')
        if not files:
            return success_response("No files found for the specified project", {"files": []})
            
        return success_response("Fetched project files successfully", {"files": files})
    except Exception as e:
        return error_response(f"Error fetching project files: {str(e)}", status_code=500)
    
@app.route('/get_number_project_files', methods=['GET'])
def get_number_project_files():
    project_id = request.args.get('project_id')
    if not project_id:
        return error_response("Project ID is required", status_code=400)
    
    try:
        # Get all files associated with the project_id
        files = File.get_by(project_id=project_id, category='number')
        if not files:
            return success_response("No files found for the specified project", {"files": []})
            
        return success_response("Fetched project files successfully", {"files": files})
    except Exception as e:
        return error_response(f"Error fetching project files: {str(e)}", status_code=500)
    

@app.route('/fetch_llm_usage', methods=['GET'])
def fetch_llm_usage():
    # Parse pagination parameters from query string
    # Format: page=1,10 (page number, limit per page)
    page_param = request.args.get('page', '1,10')
    try:
        page_parts = page_param.split(',')
        page = int(page_parts[0])
        per_page = int(page_parts[1]) if len(page_parts) > 1 else 10
    except (ValueError, IndexError):
        page = 1
        per_page = 10
    
    # Parse ordering parameters
    # Format: order=field,direction (e.g., order=id,desc)
    order_param = request.args.get('order', 'created_at,desc')
    try:
        order_parts = order_param.split(',')
        order_field = order_parts[0]
        order_direction = order_parts[1] if len(order_parts) > 1 else 'desc'
    except (ValueError, IndexError):
        order_field = 'created_at'
        order_direction = 'desc'
    
    # Validate pagination parameters
    if page < 1:
        page = 1
    if per_page < 1 or per_page > 100:  # Set reasonable limits
        per_page = 10
    
    try:
        # Get total count for pagination metadata
        total_records = LLMUsage.count_all()
        
        # Calculate total pages
        num_pages = (total_records + per_page - 1) // per_page  # Ceiling division
        
        # Get paginated and ordered records
        llm_usage = LLMUsage.get_paginated_ordered(
            page=page, 
            per_page=per_page,
            order_by=order_field,
            order_direction=order_direction
        )
        
        if not llm_usage and total_records > 0 and page > 1:
            return error_response("Page number exceeds available pages", 404)
        
        # Format the response according to the requested structure
        response = {
            "list": llm_usage,
            "page": page,
            "limit": per_page,
            "total": total_records,
            "num_pages": num_pages
        }
        
        return success_response("Fetched LLM usage successfully", response)
    except Exception as e:
        return error_response(f"Error fetching LLM usage: {str(e)}", 500)


@app.route('/test_auto_criteria', methods=['POST'])
def test_auto_criteria():
    try:
        # Extract data from request
        data = request.get_json()
        questions = data.get("questions")
        subquestions = data.get("subquestions")
        project_id = data.get("project_id")

        if not all([questions, subquestions, project_id]):
            return error_response("Missing required fields", 400)

        # Initialize data manager
        data_manager = DataManager()

        # Run async function
        result = data_manager.extract_data_v2_sync(project_id,questions, subquestions,15)
        from services.prompt_loader import PromptLoader
        from services.claude_ai_service import ClaudeService

        

        output_format =      [
                          
                                "Ball Mill",
                                 "AG Mill",
                                "Fedder",
                                "Pressure Meter"
                          
                        ]


        document_content = json.dumps(result[0]['source_list'])
        prompt = PromptLoader().get_prompt('auto_criteria_detection', {
        "document_content": document_content,
        "output_format": output_format
                
                })
        

        print(f"Prompt: {prompt}")
                
                # Call LLM to generate comparison
        messages = [
            {
                "role": "user",
                "content": prompt
            }
]       
        
        temperauture  = 0.0001
        claude_client = ClaudeService()

        #response = fast_apis.generate_completion([{"role": "user", "content": prompt}], model=model)
        response = claude_client.generate_message_sync(messages, "", temperauture, "Claude 3 Sonnet", 4096)
        data = json.loads(response)
        return jsonify({"status": "success", "data": data}), 200

    except Exception as e:
        db.session.rollback()
        return error_response(f'Error creating tender package: {str(e)}', 500)
    
@app.route('/get_all_bid_by_user_id/<user_id>', methods=['GET'])
def get_bid_by_user_id(user_id):
    try:
        if not user_id:
            return error_response("User ID is required.")
        
        # Use a raw SQL query to join bids with projects (packages) and parent projects
        query = """
        SELECT 
            b.id, b.name, b.status, b.user_id, b.package_id, 
            b.created_at, b.updated_at,
            p.name AS package_name,
            parent.id AS project_id,
            parent.name AS project_name
        FROM bids b
        LEFT JOIN project p ON b.package_id = p.id
        LEFT JOIN project parent ON p.parent_id = parent.id
        WHERE b.user_id = :user_id
        ORDER BY b.created_at DESC
        """
        
        # Execute the query with SQLAlchemy
        result = db.session.execute(query, {"user_id": user_id})
        
        # Convert the result to a list of dictionaries
        all_bids = []
        for row in result:
            # Convert row to dict
            bid_dict = {
                "id": row.id,
                "name": row.name,
                "status": row.status,
                "user_id": row.user_id,
                "package_id": row.package_id,
                "created_at": row.created_at.isoformat() if row.created_at else None,
                "updated_at": row.updated_at.isoformat() if row.updated_at else None,
                "package_name": row.package_name,
                "project_id": row.project_id,
                "project_name": row.project_name
            }
            all_bids.append(bid_dict)
        
        return success_response(
            message="Bids retrieved successfully",
            data=all_bids
        )
    except Exception as e:
        return error_response(f"Error retrieving bids: {str(e)}")
@app.route('/update_bid/<string:bid_id>', methods=['PUT'])
def update_bid(bid_id):
    try:
        # Check if the bid exists
        bid = Bids.get_single(bid_id)
        if not bid:
            return error_response('Bid not found', 404)
        
        # Get data from request (support both form data and JSON)
        if request.is_json:
            data = request.get_json()
            name = data.get('name')
            package_id = data.get('package_id')
        else:
            name = request.form.get('name')
            package_id = request.form.get('package_id')
        
        # Prepare update data
        update_data = {}
        if name:
            update_data['name'] = name
        if package_id:
            # Verify the package exists
            package = Project.get_single(package_id)
            if not package:
                return error_response('Package not found', 404)
            # Verify it's a tender package
            if package.get('entity_type') != 'tender':
                return error_response('Specified ID is not a tender package', 400)
            update_data['package_id'] = package_id
            
        # Check if we have any data to update
        if not update_data:
            return error_response('No update data provided', 400)
            
        # Update the bid using Bids.update
        updated_bid = Bids.update(bid_id, **update_data)
        if not updated_bid:
            return error_response('Failed to update bid', 500)
        
        # If update was successful, get the enhanced bid data with package and project info
        query = """
        SELECT 
            b.id, b.name, b.status, b.user_id, b.package_id, 
            b.created_at, b.updated_at,
            p.name AS package_name,
            parent.id AS project_id,
            parent.name AS project_name
        FROM bids b
        LEFT JOIN project p ON b.package_id = p.id
        LEFT JOIN project parent ON p.parent_id = parent.id
        WHERE b.id = :bid_id
        """
        
        result = db.session.execute(query, {"bid_id": bid_id}).first()
        
        if result:
            enhanced_bid = {
                "id": result.id,
                "name": result.name,
                "status": result.status,
                "user_id": result.user_id,
                "package_id": result.package_id,
                "created_at": result.created_at.isoformat() if result.created_at else None,
                "updated_at": result.updated_at.isoformat() if result.updated_at else None,
                "package_name": result.package_name,
                "project_id": result.project_id,
                "project_name": result.project_name
            }
        else:
            enhanced_bid = updated_bid
            
        return success_response('Bid updated successfully', {'bid': enhanced_bid})
    except Exception as e:
        return error_response(f"Error updating bid: {str(e)}", 500)
@app.route('/upload_zip_bid', methods=['POST'])
def upload_zip_bid():
    try:
        if 'file' not in request.files:
            return error_response('No file part')
        
        zip_file = request.files['file']
        directory_name = request.form.get('directory_name')
        user_id = request.form.get('user_id')
        
        if not zip_file or not directory_name:
            return error_response('Both zip file and directory name are required')
        
        if not zip_file.filename.endswith('.zip'):
            return error_response('File must be a ZIP archive')
        request_id = str(uuid.uuid4())
        
        zip_processor = BidZipExtractor(CHRONOBID_FOLDER, user_id)
        result, bid_id, unsupported_files = zip_processor.process_zip(zip_file, directory_name, request_id)
        
        if not result or not bid_id:
            return error_response("Failed to process ZIP file")

        create_kwargs = {
                'id': request_id,
                'name': 'Bid Processing',
                'status': 'idle',
                'file_type':'all_types'
            }

        print('creating requirement...')
        Requirement.create(**create_kwargs)
        
        return success_response(
            message="ZIP file extracted successfully and file processing started",
            data={
                "directory": result, 
                "bid_id": bid_id,
                'request_id': request_id,
                'unsupported_files': unsupported_files
            }
        )

    except Exception as e:
        return error_response(f"Error processing ZIP file: {str(e)}", 500)

@app.route('/epc_contractor', methods=['POST'])
def epc_contractor():

    package_id = request.json.get("package_id", None)
    vendor_id = request.json.get("vendor_id", None)
    vendor_file_id = request.json.get("vendor_file_id", None)

    if not package_id:
        return 'package_id is required', 400

    try:
        request_id = str(uuid.uuid4())
        print('request_id:', request_id)
        
        run_epc_contractor_task.send(
            request_id, 
            package_id,
            vendor_id,
            vendor_file_id
        )
        return {"request_id": request_id}

    except Exception as e:
        print(e)
        traceback.print_exc()
        return {"error": True, "message": "Error Occured!"}
    

@app.route('/engineering_project_owner', methods=['POST'])
def engineering_project_owner():

    project_id = request.json.get("project_id", None)
    engineering_id = request.json.get("engineer_id", None)
    engineer_file_id = request.json.get("engineer_file_id", None)
    

    if not project_id or not engineering_id:
        return 'project_id and engineering_id are required', 400

    try:
        request_id = str(uuid.uuid4())
        print('request_id:', request_id)
        
        run_engineering_project_owner_task.send(
            request_id, 
            project_id,
            engineering_id,
            engineer_file_id
        )
        return {"request_id": request_id}

    except Exception as e:
        print(e)
        traceback.print_exc()
        return {"error": True, "message": "Error Occured!"}
    
@app.route('/get_all_bids', methods=['GET'])
def get_all_bids():
    try:
        query = """
        SELECT 
            b.id, b.name, b.status, b.user_id, b.package_id, 
            b.created_at, b.updated_at,
            p.name AS package_name,
            parent.id AS project_id,
            parent.name AS project_name
        FROM bids b
        LEFT JOIN project p ON b.package_id = p.id
        LEFT JOIN project parent ON p.parent_id = parent.id
        """
        
        # Execute the query with SQLAlchemy
        result = db.session.execute(query)
        all_bids = []
        for row in result:
            # Convert row to dict
            bid_dict = {
                "id": row.id,
                "name": row.name,
                "status": row.status,
                "user_id": row.user_id,
                "package_id": row.package_id,
                "created_at": row.created_at.isoformat() if row.created_at else None,
                "updated_at": row.updated_at.isoformat() if row.updated_at else None,
                "package_name": row.package_name,
                "project_id": row.project_id,
                "project_name": row.project_name
            }
            all_bids.append(bid_dict)
        
        return success_response(
            message="Bids retrieved successfully",
            data=all_bids
        )
    except Exception as e:
        return error_response(f"Error retrieving bids: {str(e)}")

if __name__ == '__main__':
    try:
        print("Starting server on port 5130...")
        print("Initializing SocketIO server...")
        socketio.run(app, 
                    port=5130, 
                    host='0.0.0.0', 
                    debug=True)  # Allow unsafe Werkzeug usage in development
    except Exception as e:
        print(f"Error starting server: {e}")
        import traceback
        traceback.print_exc()
        raise
