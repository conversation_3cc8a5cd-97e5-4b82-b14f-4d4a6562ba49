

import time
from services.open_ai_service import get_response
import os
import json

def parseClass(text):
    print(text)
    x = -1
    while x<=1:
        if str(x) in text:
            return x
        x+=1
    
    return 0

def parseData(text, source_map={}):
    data = text.split('$$')
    ret = []
    for x in data:
        tmp = x.split('//')
        print(tmp)
        if len(tmp) < 2:
            continue
        
        ret.append({
            "content": tmp[0],
            "evalution": parseClass(tmp[1]),
            "source_id": str(tmp[2]).strip() if len(tmp) == 3 else "",
            "source_title": source_map[str(tmp[2]).strip()]["title"] if len(tmp) == 3 and source_map.get(str(tmp[2]).strip()) else "",
            "source_name": source_map[str(tmp[2]).strip()]["source"] if len(tmp) == 3 and source_map.get(str(tmp[2]).strip()) else "",
        }) 
    
    return ret

def count_max_tokens(text):
    return max(len(text.split(' ')) * 2, 500)

def reasoning(sources, specifications, source_map={}, log = {}):
    text = "Uploaded Document:\n" + specifications
    text += 'Sources Documents:\n'
    idx = 1
    for x in sources:
        text += "Source ID: " + x['id'] + '\n' +  x['title'] + ": "  + x["content"] + "\n"
        idx+=1
    
   
    log["reasoning_prompt"] = text

    messages=[
        {"role": "system", "content": f'''As an assistant, your primary task is to conduct a thorough analysis of the uploaded document. Here's the process you need to follow:

        Identification of Key Requirements: Begin by identifying all key requirements explicitly mentioned in the uploaded document. Your focus should be solely on these requirements.

        Comparison with Source Documents: Once you have identified the key requirements from the uploaded document, compare each of these requirements with the corresponding information in the source documents provided in the message.

        Exclusion of Extra Information: Ensure that you do not mention or consider any items that are present in the source documents but are not included in the uploaded document. Your analysis should be strictly confined to the contents of the the uploaded document.

        Noting Absent Elements: If there are items listed in the uploaded document that are not mentioned or covered in the source documents, you should note these as 'neutral'. Specifically, if the source document does not mention anything about an element of the uploaded dcoument, that element should be considered 'neutral'.

        Evaluation and Categorization: Evaluate each element from the uploaded document. Based on their alignment with the information in the source documents, categorize each element as follows:
        'Non-Conforming', score -1 if it does not align with the source documents.
        'Compliant', score 1, if it aligns with the source documents.
        'Neutral', score 0, if the element is absent in the source documents or not mentioned.

        Presentation of Findings: Present your findings in a clear, list format. For each point of comparison, include the source ID to reference the specific part of the source documents that corresponds to the requirements.
        When presenting each finding, strictly follow the prescribed format. Each finding should be stated, followed by the score and the source ID, like this: 'Key Point: [Finding] //score//[source_id]'. This is the only place where the source ID should appear.
        Your adherence to this structured approach is crucial for an accurate and relevant analysis.
                        '''},
                        {"role": "user", "content": f'''Uploaded Document:
                        We will run the heat engine in the morning at full speed. We'll turn it off before 12 PM. We can discuss the day of the week as off day for this process.
                        Source Documents:
                        Source ID: abcd-1-235
                        3.1.1 Heat Engine: Heat engine should be started at the morning and turned on at full speed. It should not be cooled before afternoon. This should be followed everyday except Friday.
                        '''},
                        {"role": "assistant", "content": f'''$$ Heat Engine Speed & Starting Time:  The secification suggest that heat engine will be running at full speed in the morning which aligns with the source document.//1//abcd-1-235
                        $$Heat Engine Closing Time: Heat engine will be turned off at 12 PM but in the requirements, it's stated that it should not be turned off before the afternoon. So, it does not align with the requirement.//-1//abcd-1-235
                        '''},
                        {
        "role": "user",
        "content": f'''
        Uploaded Document:
        The new smartphone model "XPhone2024" should have a 6.5 inches OLED display, a battery life of at least 48 hours, and a triple lens camera with a minimum of 48 MP main sensor. It must be water-resistant with an IP68 rating, run on XOS v5.0 or higher, and support wireless charging.
        Source Documents:
        Source ID: R&D-1024
        6.5 inches OLED display is upgraded to 6.7 inches. The battery life achieved is 36 hours. The phone includes a 48 MP main sensor camera and has an IP67 water resistance rating. XOS v5.1 will be the operating system. Wireless charging is not included in the design.
        Source ID: MKT-2048
        Marketing brief highlights a 6.5 inches OLED display, with emphasis on the camera's advanced features. No specific details are given about battery life, water resistance, operating system, or wireless charging features.
        '''
        },
        {
        "role": "assistant",
        "content": f'''
        $$ Screen Size: The specification calls for a 6.5 inches OLED display. R&D reports an upgrade to 6.7 inches, which does not align. However, the marketing brief complies by highlighting a 6.5 inches display.//1//MKT-2048
        $$ Battery Life: The specified battery life of 48 hours is not met as per R&D's report of 36 hours. The marketing brief does not mention battery life.//-1//R&D-1024
        $$ Camera: Both the R&D report and marketing brief align with the specification of a triple lens camera with a 48 MP main sensor, although the marketing brief lacks specific megapixel details.//1//R&D-1024
        $$ Water Resistance: The IP68 rating specified is not met by the R&D's IP67 rating. The marketing brief does not address water resistance.//-1//R&D-1024
        $$ Operating System: The requirement of XOS v5.0 or higher is met by R&D's use of XOS v5.1. The marketing brief does not specify the operating system.//1//R&D-1024
        $$ Wireless Charging: The specification's requirement for wireless charging is not met in the R&D design and is not mentioned in the marketing brief.//-1//R&D-1024
        '''
        },
        {"role": "user", "content": f'''{text}'''}
    ]
   
    resp = get_response(messages, 0.01, "gpt-3.5-turbo-16k", max_tokens=count_max_tokens(specifications))

    log["reasoning_prompt_tokens"] = resp.usage.prompt_tokens
    log["reasoning_response_tokens"] =  resp.usage.completion_tokens

    resp = resp.choices[0].message.content

    log["reasoning_response"] = resp
    
    resp = parseData(resp, source_map)

    log["reasoning_parsed_response"] = resp

    return resp

def get_reasoning_summary(key_points, total_factors, positive_factors, log ={}):
    print("Key Points-->", key_points)
    
    messages=[
                {"role": "system", "content": f'''You are a helpful assistant to do summarization.'''},
                {"role": "user", "content": f'''Summarize the key findings based on the compliances (✓), non-compliances (X) and neutral(*) factors. 
                 Summarization should be in three paragraphs for each of compliances, non compliances and neutral factors. The contents of the paragraphs should be a top view of the key findings rather than mentioning all the points.
                 Provide it with proper formatting in HTML snippet wrapped in a div tag, exclude everything else other than the html snippet, it must start with a div tag.
                
                {key_points}'''
                },
            ]

    resp = ""
    log["reasoning_summary_prompt"] = messages[1]["content"]
     
    resp  = get_response(messages, 0.01, "gpt-4-turbo")
    log["reasoning_summary_prompt_token"] = resp.usage.prompt_tokens
    log["reasoning_summary_response_token"] =  resp.usage.completion_tokens

    resp = resp.choices[0].message.content
    
    log["reasoning_summary_response"] = resp

    # print(resp)
    return resp


def key_points_builder(report_path):
    with open(report_path, 'r', encoding ='utf8') as json_file:
        report = json.load(json_file)
    
    key_points = ""
    for x in report["details"]:
        for y in x["reasoning"]:
            if y["evalution"] == 1:
                key_points += "- " + y["content"] + "\n"
    
    return key_points

def execute(report_id="813dbab9-aa60-4d27-bf9a-87271a1d168d"):
    report_path = os.path.join('specs', "f865fd98-f2e7-4c71-9854-f9868f282e93", f'''{report_id}.json''')
    key_points = key_points_builder(report_path)
    reasoning_summary = get_reasoning_summary(key_points, 0, 0)
    print(reasoning_summary)


def reasoning_with_criteria(source, specifications, criteria):
    pass