import os,sys, json, re, time
import asyncio
from models import Project, Requirement
from services.data_handler import DataManager
from services.claude_ai_service import ClaudeService
from services.report_manager import ReportManager
from services.update_backend_status import BackendStatusUpdater
from services.generate_question_field import QuestionGenerator
from services.handle_new_qmp_source_expansion import QMPSourceExtractor
from services.synonym_expansion import SynonymGenerator
from services.groq_ai_service import GroqService
from services.fast_apis_service import FastAPIs
from flask_socketio import emit
from init import app
from pydantic import BaseModel, Field, field_validator
from typing import List, Union
import json


class QMPQueryHandler:
    def __init__(self, socket_manager, is_test):
         # Get the path to the parent directory
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

        env_file_path = os.path.join(parent_dir, 'env.json')
        with open(env_file_path, 'r') as f:
            self.env_data = json.load(f)

        self.data_manager = DataManager()
        self.claude_client = ClaudeService()
        self.groq_client = GroqService()
        self.fast_apis = FastAPIs()
        self.backend_status_updater = BackendStatusUpdater()
        self.question_generator = QuestionGenerator()
        self.qmp_source_extractor = QMPSourceExtractor()
        self.synonym_service = SynonymGenerator()
        self.is_test = is_test
        self.socket_manager = socket_manager

    def add_event(self, request_id, event_name, data):
        # return
        self.socket_manager.emit_to_client(request_id, event_name, data, '/qmp')

    def process_query(self, req_id, other_project_ids):
        asyncio.run(self.answer_question_v1_1(req_id, other_project_ids))

    async def format_question_text(self, question_text):
        prompt = f"""
            You are an **EPC (Engineering, Procurement, and Construction) Project Engineer** assistant working for **Worley** or **Fulor**.
            Your task is to extract all **queries (questions)** from the provided RFIs text while maintaining their exact wording and meaning.
            Return the extracted questions below format.

            <INSTRUCTIONS>
                - Identify and extract **all** questions from the given RFIs text.
                - Do **not** paraphrase, modify, or interpret the extracted questions—return them exactly as written.
                - Ensure strict adherence to the **output format** provided.
                - Sometimes it can be a simple RFIs.
            </INSTRUCTIONS>

            <EXAMPLE>
                "Is there any update on the procurement of heat exchangers, Share the final approval status of the project plan. Have all site workers undergone the necessary HSE training?"
            <EXAMPLE>
            <OUTPUT FORMAT>
                <Question>Is there any update on the procurement of heat exchangers?</Question>
                <Question>Share the final approval status of the project plan.</Question>
                <Question>Have all site workers undergone the necessary HSE training?</Question>
            <OUTPUT FORMAT>

            <EXAMPLE>
                "Can you confirm the final dimensions of the pressure vessel? What are the maximum allowable tolerances for the welding joints?
                Has the structural integrity analysis been completed for the load-bearing beams?"
            <EXAMPLE>
            <OUTPUT FORMAT>
                <Question>Can you confirm the final dimensions of the pressure vessel?</Question>
                <Question>What are the maximum allowable tolerances for the welding joints?</Question>
                <Question>Has the structural integrity analysis been completed for the load-bearing beams?</Question>
            <OUTPUT FORMAT>

            NOTE: Strictly follow the outut format.
            NOTE: Do not paraphrase or change the extracted text.
            NOTE: Just extract queries from the RFIs text, but dont change meaning.

            Process the following RFIs text below:
            <RFIs>
                "{question_text}"
            </RFIs>


        NOTE: IF the input text does not contain any questions or RFIs (e.g., it's just a greeting like "Hello"), simply return the exact text in this format:
                <OUTPUT FORMAT>
                <Question>[exact input text]</Question>
                <OUTPUT FORMAT>
                Do NOT provide any explanations or commentary - just return the text in the format above.
"""
        # completion = self.fast_apis.generate_completion(
        #     [{"role": "user", "content": prompt}],
        #     'groq',
        #     'llama3-70b-8192'
        # )

        completion = await self.claude_client.generate_message_agent_sonnet_new(
            [{"role": "user", "content": prompt}]
        )
        print('formatted question: ', completion)

        if completion:
            # Regex pattern to extract content inside <Source_answer> tags, including multiline content
            pattern = r'<Question>(.*?)<\/Question>'
            # matches = re.findall(pattern, completion, re.DOTALL)
            matches = re.findall(pattern, completion.content[0].text, re.DOTALL)
            if matches:
                return [match.strip() for match in matches]

            # If no matches found, try one more time
            print('No matches found in first attempt, retrying...')
            # completion = self.fast_apis.generate_completion(
            #     [{"role": "user", "content": prompt}],
            #     'groq',
            #     'llama3-70b-8192'
            # )
            completion = await self.claude_client.generate_message_agent_sonnet_new(
                [{"role": "user", "content": prompt}]
            )
            if completion:
                # matches = re.findall(pattern, completion, re.DOTALL)
                matches = re.findall(pattern, completion.content[0].text, re.DOTALL)
                if matches:
                    return [match.strip() for match in matches]

        return None


    async def process_question(self, idx, sub_question, project_id, project_data, requirement_id, max_retries=3):
        """Process a single question."""
        start_query = time.time()
        attempts = 0
        highlight_class = 'highlight'

        print('i am subquestion: ', sub_question)

        # Generate similar questions to improve search
        synonyms = await self.synonym_service.generate_synonym(f"{sub_question}\n Description: {sub_question}")

        # Combine all matches into a single string separated by '; '
        similarQuestion = [f"{name.strip()} - {description.strip()}"
                for name, description in re.findall(r"Name:\s*(.+?)\n\s*Description:\s*(.+?)(?:\n|</SYNONYMS>)", synonyms, re.DOTALL)]
        print("Synonyms Questions generated successfully...: ", similarQuestion)


        while attempts < max_retries:
            try:

                print('i am here...')
                self.add_event(requirement_id, 'progress_message', {
                    'event_type': 'QMP',
                    'request_id': requirement_id,
                    'data': {'status': 'extracting_sources', 'message': f"Processing request. Crafting response now. Please wait.", 'query_idx': idx}
                })

                # Extract sources
                print('now trying to extract data using v2...')
                # print(project_id, [sub_question], similarQuestion)
                # return
                sources = await self.data_manager.extract_data_v2(project_id, [sub_question], similarQuestion, 5)
                self.add_event(requirement_id, 'progress_message', {
                    'event_type': 'QMP',
                    'request_id': requirement_id,
                    'data': {'status': 'generating_answer', 'message': f"Processing inquiry {idx}. Thinking. Please wait.", 'query_idx': idx}
                })

                # output_directory = 'output_json_files'  # Specify your desired output directory
                # os.makedirs(output_directory, exist_ok=True)  # Create the directory if it doesn't exist
                # # Dump sources into individual JSON files
                # output_file_path = os.path.join(output_directory, f"sources_query_{idx}.json")
                # with open(output_file_path, 'w') as json_file:
                #     json.dump(sources, json_file, indent=4)  # Write the sources to the JSON file

                answer = await self.get_answer_claude(sources[0]['source_list'], sub_question)
                # feedback_text = await self.extract_feedback_content(answer)

                # print('i am feedback answer: ', answer)

                # if feedback_text is not None:
                #     # highlight_class = 'highlight-red'
                #     highlight_class = 'highlight'
                #     print('now checking with expansion agent...')
                #     await self.qmp_source_extractor.extract_sources(self.related_project_ids, sub_question)
                #     sources2 = self.qmp_source_extractor.get_response()
                #     print('top expand sources generated...')
                #     if sources2[0]['source_list']:
                #         [answer2, prompt] = await self.get_answer_claude_v2(sources2[0]['source_list'], sub_question, answer)
                #     else:
                #         answer2 = None
                # else:
                #     answer2 = None

                final_answer = answer
                final_source = sources

                # if answer2:
                #     print(f"revised answer in query {idx} : ", answer2)

                #     # Extract the flag from answer2
                #     comparison_flag = await self.extract_comparison_flag(answer2)
                #     print('Comparison flag:', comparison_flag)

                #     # Decide which answer to use
                #     if comparison_flag == "USE_EXISTING_ANSWER":
                #         final_answer = answer
                #         final_source = sources
                #         print("Using existing answer.")
                #     elif comparison_flag == "USE_NEW_ANSWER":
                #         final_answer = answer2
                #         final_source = sources2
                #         print("Using new answer.")
                #     elif comparison_flag == "MERGE_BOTH_ANSWERS":
                #         final_answer = await self.merge_answers(
                #             await self.extract_answer_content(answer),
                #             await self.extract_answer_content(answer2)
                #         )
                #         final_source = self.merge_sources(sources, sources2)
                #         print("Merging both answers.")
                #     else:
                #         final_answer = answer
                #         final_source = sources
                #         print("Defaulting to existing answer due to flag extraction failure.")
                # else:
                #     print(f"main answer in query {idx} : ", answer)
                #     final_answer = answer
                #     final_source = sources
                #     print("Expansion failed or feedback was None. Using existing answer.")


                self.add_event(requirement_id, 'progress_message', {
                    'event_type': 'QMP',
                    'request_id': requirement_id,
                    'data': {'status': 'formatting_sources', 'message': f"Refining the details for inquiry {idx}. Double checking our answers. Please wait.", 'query_idx': idx}
                })


                # print('i amfeedback text: ', feedback_text)

                source_list = []
                index = 0
                # section_ids = await self.extract_all_answer_sectionIds(final_answer)
                section_ids = final_answer['SectionIds']
                print('section ids: ', section_ids)
                # section_ids_content = await self.extract_source_answer_content(final_answer)
                section_ids_headers = final_answer['SourceAnswerHeaders']
                section_ids_content = final_answer['SourceAnswerContents']
                # print('section ids content: ', section_ids_content)
                # answer = await self.extract_answer_content(final_answer)
                answer = final_answer['Answer']

                # print('section ids below in sources sufficient: ', section_ids)

                # First, format and highlight the sources that are explicitly referenced in the answer
                for section_id in section_ids:
                    # section_id = section_id.strip()
                    if section_id:
                        print('section id is this: ', section_id)
                        # format_text = await self.groq_ai_clean_and_format_sources(section_ids_content[index])
                        format_text = section_ids_headers[index] + '<br/>' + section_ids_content[index]
                        # format_text = await self.claude_ai_clean_and_format_sources(section_ids_content[index])

                        print('completed the groq formating, now removing new line...')
                        # cleaned_text = self.remove_newlines(format_text)
                        cleaned_text = format_text
                        print('now appending to source map...')
                        # print(final_source[0]['source_map'])
                        final_source[0]['source_map'][section_id]['content'] = f"<div class='{highlight_class}'>" + cleaned_text + "</div>"
                        print('this is brakpoint..')
                        source_list.append(final_source[0]['source_map'][section_id])
                        index += 1

                # Now add all sources to the source_list, not just the ones referenced in the answer
                # This ensures all sources are sent to the frontend
                for source_id, source in final_source[0]['source_map'].items():
                    # Skip sources that are already in the source_list (the ones referenced in the answer)
                    if source_id in section_ids:
                        continue
                    # Add the remaining sources without highlighting
                    source_list.append(source)


                if 'SOURCES SUFFICIENT' in final_answer['Feedback']:
                    # Format and clean answer and sources
                    # print('i finished groq clean format source....')

                    log = self.report_manager.load_report()
                    # print('i am the log loaded.....')

                    # log['answer'][idx - 1] = (
                    #     f"<h3 style='font-weight: bold; font-size: 18.67px; color: #333;'>Clarification {idx}: {sub_question}</h3>"
                    #     f"<div class='highlight'><p style='font-size: 14.67px;'>{await self.groq_ai_clean_and_format_text(answer, project_data['entity_type'])}</p></div><br/>"
                    # )
                    # log['answer'][idx - 1] = (
                    #     f"<h3 style='font-weight: bold; font-size: 18.67px; color: #333;'>Clarification {idx}: {sub_question}</h3>"
                    #     f"<div class='highlight'><p style='font-size: 14.67px;'>{await self.claude_ai_clean_and_format_text(answer, project_data['entity_type'])}</p></div><br/>"
                    # )

                    log['answer'][idx - 1] = (
                        f"<h3 style='font-weight: bold; font-size: 18.67px; color: #333;'>Clarification {idx}: {sub_question}</h3>"
                        f"<div class='highlight'><p style='font-size: 14.67px;'>{answer}</p></div><br/>"
                    )

                    print('loaded answer log')

                    log["sources"][idx - 1]  = source_list
                    log["bid_title"] = project_data['name']
                    log["question"][idx - 1] = f"Query #{idx}: {sub_question}"
                    log["reference"][idx - 1] = [source.get("content", "") for source in source_list]
                    log["tender_clarification_round"][idx - 1] = f"Round #{idx}"

                elif 'SOURCES INSUFFICIENT' in final_answer['Feedback']:

                    # print('i amfeedback text in insufficient sources: ', feedback_text)
                    log = self.report_manager.load_report()

                    # recommendation_text = await self.extract_recommendation_content(final_answer)

                    log['answer'][idx - 1] = (
                        f"<h3 style='font-weight: bold; font-size: 18.67px; color: #333;'>Clarification {idx}: {sub_question}</h3>"
                        f"<div class='highlight'><p style='font-size: 14.67px;'>{final_answer['Answer'] + ' <br/> ' + final_answer['Recommendation']}</p></div><br/>"
                    )

                    # Always use source_list if it's not empty
                    if source_list:
                        log["sources"][idx - 1] = source_list
                        log["reference"][idx - 1] = [source.get("content", "") for source in source_list]
                    else:
                        # If no sources were found at all, use the recommendation as a fallback
                        log["reference"][idx - 1] = final_answer['Recommendation']
                        log["sources"][idx - 1] = {
                            "content": final_answer['Recommendation'],
                            "id": "",
                            "matched_content": "",
                            "page_number": "1",
                            "score": 0.0,
                            "section_id": "",
                            "source": "",
                            "title": ""
                            }

                    log["bid_title"] = project_data['name']
                    log["question"][idx - 1] = f"Query #{idx}: {sub_question}"
                    log["tender_clarification_round"][idx - 1] = f"Round #{idx}"


                self.add_event(requirement_id, 'in_progress_event', {
                    'event_type': 'QMP',
                    'request_id': requirement_id,
                    'data': log
                })

                end_query = time.time()
                print(f"Time taken to complete query {idx}: {end_query - start_query:.2f} seconds")

                await self.update_log_and_status(log)
                break  # Exit loop on success

            except Exception as e:
                print(e)
                attempts += 1
                print(f"Attempt {attempts} failed for Query {idx}: {e}")
                if attempts >= max_retries:
                    log = self.report_manager.load_report()
                    log['answer'][idx - 1] = f"Query #{idx} {sub_question}: Failed to retrieve answer due to error after {max_retries} attempts."
                    log["sources"][idx - 1]  = f"Query #{idx} {sub_question}: Failed to retrieve sources due to error after {max_retries} attempts."
                    log["question"][idx - 1]  = f"Query #{idx}: {sub_question}"
                    log["reference"][idx - 1]  = []
                    log["tender_clarification_round"][idx - 1]  = f"Round #{idx}"
                    self.add_event(requirement_id, 'error_event', {
                        'event_type': 'QMP',
                        'request_id': requirement_id,
                        'data': {'status': 'error', 'message': f"Oops! Something went wrong for Query {idx} after {max_retries} attempts.", 'query_idx': idx, 'error': str(e)}
                    })


    async def answer_question_v1_1(self, requirement_id, related_project_ids):
        print('Started the answer method...')
        requirement = Requirement.get_single(requirement_id)

        # Check if the requirement status is already done
        if requirement['status'] == 'done':
            print(f"Requirement with id: {requirement_id} is already done. Exiting...")
            return

        self.requirement_id = requirement_id
        self.related_project_ids = related_project_ids
        Requirement.update(requirement_id, tried=requirement['tried'] + 1)

        # print('i am the raw query: ', requirement['criteria'])

        question_list = await self.format_question_text(requirement['criteria'])

        print('new formatted question list: ', question_list)

        project_id = requirement['project_id']
        project_data = Project.get_single(project_id)
        self.report_manager = ReportManager(self.env_data.get('DATA_DIR'), project_id, requirement_id)
        # Initialize shared response structure
        tasks = []
        # Create initial report
        initial_log = {
            "answer": [f"Pending Clarification {count}" for count in range(1, len(question_list) + 1)],
            "sources": [[]] * len(question_list),
            "bid_number": "0023419",
            "bid_title": "",
            "question": [f"Pending Clarification {count}" for count in range(1, len(question_list) + 1)],
            "reference": [[]] * len(question_list),
            "raiser_by": "Please Complete",
            "responsible": "Please Complete",
            "status": "Please Complete",
            "tender_clarification_round": [f"Pending Clarification {count}" for count in range(1, len(question_list) + 1)]
        }
        self.report_manager.create_initial_report(initial_log)

        for idx, sub_question in enumerate(question_list,  start=1):
            if not sub_question.strip():
                continue
            tasks.append(asyncio.create_task(self.process_question(idx, sub_question, project_id,project_data, requirement_id)))

        # Run tasks concurrently
        await asyncio.gather(*tasks)
        log = self.report_manager.load_report()
        print(f"Latest NEW LOG :{log}")
        # Emit final completion event
        self.add_event(requirement_id, 'completed_event', {
            'event_type': 'QMP',
            'request_id': requirement_id,
            'data': log
        })
        Requirement.update(requirement_id, status='done')

    async def update_log_and_status(self, log):
        print('\n\n now updating qmp log with new log data...\n\n')

        # Update the log with new data
        self.report_manager.update_log(log)
        # if not self.is_test:
        #     self.backend_status_updater.update_history_status(self.requirement_id, 2)

    def extract_subarray(self, section_ids, potential_source_index):
        print('This is section Ids: ',section_ids)
        # Ensure the indices are within bounds and extract the corresponding elements
        return [section_ids[i] for i in potential_source_index if 0 <= i < len(section_ids)]

    async def extract_comparison_flag(self, answer2):
        print('answer2 for comparison:', answer2)
        # Assuming the flag is included in the answer response in a specific format
        match = re.search(r"<COMPARISON_FLAG>(.*?)</COMPARISON_FLAG>", answer2, re.IGNORECASE)

        if match:
            match_response = match.group(1).strip()
            return match_response
        return "USE_EXISTING_ANSWER"  # Default to existing answer if flag extraction fails


    async def merge_answers(self, existing_answer, new_answer):
        prompt = '''
            <EXISTING_ANSWER>
                {existing_answer}
            </EXISTING_ANSWER>
            <NEW_ANSWER>
                {new_answer}
            </NEW_ANSWER>
            <ROLE>
                You are an **EPC (Engineering, Procurement, and Construction) Project Engineer** assistant working for **Worley** or **Fulor**. Your responses should reflect the knowledge, expertise, and decision-making processes typical of a seasoned professional in this role, ensuring alignment with industry practices and the responsibilities associated with EPC project management.
            </ROLE>
            <TASK>
                - Carefully analyze both the <EXISTING_ANSWER> and <NEW_ANSWER>.
                - Seamlessly merge them into a single, well-structured response without redundancy.
                - Ensure that the response is **direct, confident, and authoritative**.
                - Present the information as a knowledgeable EPC engineer, reflecting years of industry expertise.
                - Ensure a smooth and natural flow, so users cannot distinguish if multiple responses were combined.
                - Do not include any unnecessary disclaimers, apologies, or uncertainty.
                - Use clear and precise language with industry-standard terminology.
                - Remove any inconsistencies or contradictions between the two answers, choosing the most accurate details.
            </TASK>
            <ANSWER FORMAT>
                Provide the **merged response** in a clear and professional manner.

                **Final Answer:**
                <answer>
                    [Merged answer here, structured logically and confidently]
                </answer>
            </ANSWER FORMAT>
            <IMPORTANT>
                - The answer should be **professionally formatted** with logical structuring.
                - Avoid excessive paragraph spacing—keep the response **concise and dense with information**.
                - The response must feel **authoritative**—it should not read as an AI-generated response.
                - The final answer should be **comprehensive and fully self-contained**.
            </IMPORTANT>
        '''

        formatted_prompt = prompt.format(existing_answer=existing_answer, new_answer=new_answer)

        completion = await self.claude_client.generate_message_agent_sonnet_new(
            [{"role": "user", "content": formatted_prompt}]
        )
        return f"<answer>{(completion.content[0].text)}</answer>"

    def merge_sources(self, existing_source, new_source):
        merged_source = existing_source.copy()
        merged_source[0]['source_list'].extend(new_source[0]['source_list'])
        merged_source[0]['source_map'].update(new_source[0]['source_map'])
        return merged_source

    def format_sources(self, sources):
        formatted_text = 'Sources:\n'
        for idx, source in enumerate(sources, 1):
            if source.get("content"):
                source_title = ".".join(source.get("source", "").split('/')[-1].split('.')[:-1])
                formatted_text += f"Document {idx}:"
                if source_title:
                    formatted_text += f" {source_title}"
                if source['section_id']:
                    formatted_text += f" (Section ID: {source['section_id']})"
                page_number = source.get("page_number", " ")  # Default to "N/A" if page_number is not found
                if page_number:
                    formatted_text += f" (Page: {page_number})"
                formatted_text += f"\n\n{source['content']}\n\n"

        print('this is the formatted sources: ', formatted_text)
        return formatted_text

    async def get_answer_claude(self, sources, question):
        print('now in get answer claude....')
        prompt = '''
            <SOURCES>
                {sources}
            </SOURCES>
            <QUESTION>
                {question}
            </QUESTION>
            <ROLE>
                Provide a detailed and professional response using the provided sources, ensuring alignment with industry standards in EPC project management. Avoid adopting a personal perspective or stating your role in the response.
                Your responses should reflect the knowledge, expertise, and decision-making processes typical of a professional in this role.
            </ROLE>
            <TASK>
                - Analyze the <SOURCES> to answer the <QUESTION>.
                - Use only the provided sources to formulate your answer.
                - Ignore any irrelevant sources.
                - Cite the sources used in your answer by mentioning their document names or titles.
                - Do not quote the sources directly; instead, paraphrase and cite them.
                - Provide a convincing answer in one or more paragraphs.
                - Include as many relevant details as possible.
                - If multiple values exist for a query demanding a singular value, mention all of them.
                - Present information confidently without apologizing.
                - Extract concise and relevant subtexts or paragraphs from the content of each <Section_Ids> used in the answer.
                - Include only the portions that directly support the answer, maintaining logical flow and context.
                - Exclude irrelevant or redundant information.
                - Separate the extracted content for each <Section_Ids> with $$$$$.
                - Arrange the extracted content in the <Source_answer> tag, following the order of the <Section_Ids> used.
                - Each section should start with a bold title formatted as:
                    <h3 style="font-weight: bold; font-size: 16px;">Document Name: <span style="font-style: italic; font-weight: normal;">(<document_name>)</span>, Section <section_number> <span style="font-style: italic; font-weight: normal;">(<title_of_section>)</span>, Page <page_number></h3>
                - Strictly pick the `section_id` key, from the sources, do not pick the `id` key.
                - Each content in the Source_answer_headers & the Source_answer_contents should be seperated by a HTML break.
                - For empty sources add a message saying "No sources found" insstead of empty string
            </TASK>
            <ANSWER FORMAT>
                Provide your answer in the following format:
                <answer>
                Your detailed answer here, citing sources as needed.
                </answer>
                <Section_Ids>
                    section_id1, section_id2, ...
                </Section_Ids>
                <Source_answer_headers>
                        <h3 style="font-weight: bold; font-size: 16px;>
                            Document Name: <span style="font-style: italic; font-weight: normal;">(`document_name`)</span>,
                            Section `section_number` <span style="font-style: italic; font-weight: normal;">(`title_of_section`)</span>,
                            Page `page_number`
                        </h3>
                        <h3 style="font-weight: bold; font-size: 16px;>
                            Document Name: <span style="font-style: italic; font-weight: normal;">(`document_name_2`)</span>,
                            Section `section_number_2` <span style="font-style: italic; font-weight: normal;">(`title_of_section_2`)</span>,
                            Page `page_number_2`
                        </h3>
            </Source_answer_headers>
                <Source_answer_contents>
                    "Extracted subtext from section `section_id1` relevant to the answer."<br/>
                    "Extracted subtext from section `section_id2` relevant to the answer."
                </Source_answer_contents>

            </ANSWER FORMAT>
            <FEEDBACK>
                - If the provided sources has accuracy of 50% and above to fully answer the question, return: **"FLAG: SOURCES SUFFICIENT"**.
                - If the sources are not enough and has low accuracy, return: **"FLAG: SOURCES INSUFFICIENT"**.
            </FEEDBACK>
            <RECOMMENDATION>
                - If the sources are insufficient, return: **"Consult an EPC Engineer for further clarification and industry-specific insights."**
                - If the sources are sufficient, return: **"No additional consultation needed."**
            </RECOMMENDATION>
            <IMPORTANT>
                - Each content in the Source_answer_headers & the Source_answer_contents should be seperated by a HTML break
                - Do not mention section IDs in the answer part.
                - A section_id should be picked only from the `section_Id` key, so pick the `section_Id` key and not the `id` key
                - Include section IDs used directly or indirectly in your answer only in the <Section_Ids> tag.
                - Sort the section IDs according to their appearance in your answer.
                - Your answer should strictly be in font size equivalent to 11pt on word document.
                - The spacing between any paragraphs or points in your answer should be minimal and not contain any newline(`\n`)
                - The <FEEDBACK> section should contain only one of the two flags and nothing else.
                - Each content in the Source_answer_headers & the Source_answer_contents should be seperated by a HTML break
                - (Strictly) Do not state your role in the response.
            </IMPORTANT>

        NOTE, THIS IS VERY CRITICAL: If no relevant sources are found for the query OR if the found sources don't relate to the query in any meaningful way, respond with a properly formatted HTML message indicating this situation.
        example query : "Hello"
                    RESPONSE FORMAT THIS SCENARIO:
                    <answer>
                        Unfortunately, I couldn't find any information about "[insert user's query]" in the available documentation. Would you like me to help you search for something else?
                    </answer>

                    ALTERNATIVE FORMATS (feel free to use these or create similar variations):
                    <answer>
                        The documentation doesn't contain specific information related to "[insert user's query]". Would you like me to check for related topics instead?
                    </answer>

                    <answer>
                        While I found some sources, they don't contain relevant information about "[insert user's query]". Please let me know if you'd like to refine your search.
                    </answer>

                    IMPORTANT:
                    - Include the user's actual query in your response
                    - Keep responses concise and helpful
                    - Don't mention what search process was used
                    - Don't apologize unnecessarily
                    - Maintain the HTML format with the <answer> tags
        '''
        formatted_prompt = [{"role": "user", "content": prompt.format(sources=sources, question=question)}]
        print('done formatting prompt...')
        class ResponseInfo(BaseModel):
            Answer: str = Field(description="The main response generated, structured as a well-reasoned and professional answer.")
            Recommendation: str = Field(description="Indicates whether additional consultation is needed based on source sufficiency.")
            Feedback: str = Field(description="Indicates whether the provided sources were sufficient or insufficient.")
            SourceAnswerHeaders: List[str] = Field(description="List of headers for the referenced source documents.")
            SourceAnswerContents: List[str] = Field(description="List of extracted content from source documents relevant to the query.")
            SectionIds: List[str] = Field(description="List of section IDs from the sources used in the answer.")

            @field_validator("SourceAnswerHeaders", "SourceAnswerContents", "SectionIds", mode="before")
            @classmethod
            def ensure_list(cls, value: Union[str, List[str]]) -> List[str]:
                """Ensures the field is always a list, converting stringified lists or <br/> separated strings."""

                # Debugging: Print the raw value before validation
                print(f"Validating field with raw value: {repr(value)} (Type: {type(value)})")

                if isinstance(value, list):
                    print(f"✅ Value is already a valid list")
                    return value  # Already a list, return as-is

                if isinstance(value, str):
                    # Attempt JSON parsing if the string is a stringified list
                    try:
                        parsed_value = json.loads(value)
                        if isinstance(parsed_value, list):
                            print(f"🔄 Converted stringified list to valid list")
                            return parsed_value
                    except json.JSONDecodeError:
                        print(f"⚠️ JSON parsing failed, checking for <br/> separator...")

                    # If JSON parsing fails, split using "<br/>" as a delimiter
                    if "<br/>" in value:
                        split_values = [v.strip() for v in value.split("<br/>") if v.strip()]
                        print(f"🔄 Split string using <br/>")
                        return split_values

                    # If it's just a single string without a separator, wrap it in a list
                    print(f"🔄 Wrapped single string into list")
                    return [value]

                # If an unexpected type is received, raise an error
                raise ValueError(f"❌ Expected a list or string, but received {type(value)}: {value}")


        completion = await self.claude_client.generate_message_agent_sonnet_v2(ResponseInfo, formatted_prompt, 0.01)
        if completion and hasattr(completion.content[0], 'input'):
            response = completion.content[0].input
            master_response = ResponseInfo(**response)


        print('received response from claude formatted...')
        source_answer_header  =  master_response.SourceAnswerHeaders
        SourceAnswerContents  =  master_response.SourceAnswerContents
        #source_answer_header  = [item.lstrip('"\\').rstrip('"') for item in master_response.SourceAnswerHeaders]
        #SourceAnswerContents  = [item.lstrip('"\\').rstrip('"') for item in master_response.SourceAnswerContents]

        print(f"New source answer : {source_answer_header}")
        print(f"New answer content : {SourceAnswerContents}")
        new_response = {
            'Answer': master_response.Answer,
            'SourceAnswerHeaders': source_answer_header,
            'SourceAnswerContents': SourceAnswerContents,
            'SectionIds': master_response.SectionIds,
            'Recommendation': master_response.Recommendation,
            'Feedback': master_response.Feedback,
        }
        print('new response by claude: ', json.dumps(new_response))
        return new_response

        # completion = await self.claude_client.generate_message_agent_sonnet_new(
        #     [{"role": "user", "content": formatted_prompt}]
        # )
        # return (completion.content[0].text, sources)

    async def get_answer_claude_v2(self, sources, question, existing_answer):
        prompt = '''
            <SOURCES>
                {sources}
            </SOURCES>
            <QUESTION>
                {question}
            </QUESTION>
            <EXISTING_ANSWER>
                {existing_answer}
            </EXISTING_ANSWER>
            <ROLE>
                You are an **EPC (Engineering, Procurement, and Construction) Project Engineer** assistant working for **Worley** or **Fulor**. Your responses should reflect the knowledge, expertise, and decision-making processes typical of a professional in this role, ensuring alignment with industry practices and the responsibilities associated with EPC project management.
            </ROLE>
            <TASK>
                - Analyze the <SOURCES> to answer the <QUESTION>.
                - Use only the provided sources to formulate your answer.
                - Ignore any irrelevant sources.
                - Cite the sources used in your answer by mentioning their document names or titles.
                - Do not quote the sources directly; instead, paraphrase and cite them.
                - Provide a convincing answer in one or more paragraphs.
                - Include as many relevant details as possible.
                - If multiple values exist for a query demanding a singular value, mention all of them.
                - Present information confidently without apologizing.
                - Extract concise and relevant subtexts or paragraphs from the content of each <Section_Ids> used in the answer.
                - Include only the portions that directly support the answer, maintaining logical flow and context.
                - Exclude irrelevant or redundant information.
                - Separate the extracted content for each <Section_Ids> with $$$$$.
                - Arrange the extracted content in the <Source_answer> tag, following the order of the <Section_Ids> used.
                - Each section should start with a bold title formatted as:
                    <h3 style="font-weight: bold; font-size: 16px;">Document Name: <span style="font-style: italic; font-weight: normal;">(<document_name>)</span>, Section <section_number> <span style="font-style: italic; font-weight: normal;">(<title_of_section>)</span>, Page <page_number></h3>
                - Strictly pick the `section_id` key, from the sources, do not pick the `id` key.
            </TASK>
            <ANSWER FORMAT>
                Provide your answer in the following format:
                <answer>
                Your detailed answer here, citing sources as needed.
                </answer>
                <Section_Ids>
                    section_id1, section_id2, ...
                </Section_Ids>
                <Source_answer>
                    <h3 style="font-weight: bold; font-size: 16px;">Document Name: <span style="font-style: italic; font-weight: normal;">(<document_name>)</span>, Section <section_number> <span style="font-style: italic; font-weight: normal;">(<title_of_section>)</span>, Page <page_number></h3>
                    The extracted sub text or paragraphs from the contents of each section_ids you've listed above.
                    $$$$$
                </Source_answer>
            </ANSWER FORMAT>
            <ANSWER_COMPARISON>
                - Compare the <EXISTING_ANSWER> and the newly generated answer based on:
                    - Completeness: Does it fully address the question?
                    - Accuracy: Does it align correctly with the sources?
                    - Relevance: Is it directly answering the question without unnecessary details?
                    - Clarity: Is it well-structured and easy to understand?
                - Based on this evaluation, determine which answer should be used.
            </ANSWER_COMPARISON>
            <ANSWER_COMPARISON_FLAG>
                - If the existing answer is better, return: **USE_EXISTING_ANSWER**
                - If the new answer is better, return: **USE_NEW_ANSWER**
                - If both answers provide useful information but neither is complete alone, return: **MERGE_BOTH_ANSWERS**
            </ANSWER_COMPARISON_FLAG>
            <FEEDBACK>
                - If the answer provided does not meet expectations or additional information is required, include a detailed note about the inaccuracies or the missing elements.
                - If the SOURCES provided meet expectation then the feedback section should not be in the returned response.

                    Detailed notes on inaccuracies or missing elements.
            </FEEDBACK>
            <IMPORTANT>
                - Do not mention section IDs in the answer part.
                - A section_id should be picked only from the `section_Id` key, so pick the `section_Id` key and not the `id` key
                - Include section IDs used directly or indirectly in your answer only in the <Section_Ids> tag.
                - Sort the section IDs according to their appearance in your answer.
                - Your answer should strictly be in font size equivalent to 11pt on word document.
                - The **ANSWER_COMPARISON_FLAG** should always be present in the response.
                - The spacing between any paragraphs or points in your answer should be minimal and not contain any newline(`\n`)
                - The Feedback section should strictly be added only if provided SOURCES does not give enough answer for the query.
            </IMPORTANT>
        '''

        formatted_prompt = prompt.format(sources=sources, question=question, existing_answer=existing_answer)

        completion = await self.claude_client.generate_message_agent_sonnet_new(
            [{"role": "user", "content": formatted_prompt}]
        )
        return (completion.content[0].text, sources)


    async def ai_clean_and_format_text(self, content, type):
        prompt = '''
            <HTML_CONTENT>
            {content}
            </HTML_CONTENT>
            <DOCUMENT_TYPE>
            {type}
            </DOCUMENT_TYPE>
            <TASK>
                You are an **EPC (Engineering, Procurement, and Construction) Project Engineer** assistant working for **Worley** or **Fulor**. Your task is to take the given HTML and replace any bad symbol:
                1. If any symbol is not properly shown or displayed then you should fix it by replacing it with the actual right symbol so it can be well displayed when viewed.
                2. Format the text correctly for readability. The response should not be choked up or too dense. Improve the formatting for readability by adjusting line breaks, spacing, and overall text structure where necessary.
                3. When referring to the documentation, use **'The Project Documentation'** if the document type is "project" and **'The Tender Documentation'** if the document type is "tender."
            </TASK>

            <IMPORTANT>
                - Formatting of the text should not change the meaning or initial writeup.
                - Formatting structure must be professional.
                - Remove any newline(\n) symbol noticed.
                - All spacing between any paragraphs or points should be minimal and not contain any newline(`\n`) character
                - Ensure the response does not contain extra explanations, apologies, or acknowledgments.
                - Respond with confidence and authority, like an experienced engineer with years of expertise.
            </IMPORTANT>

            <OUTPUT_FORMAT>
                Do not include any explanations or additional text outside of the given or formatted content.
            </OUTPUT_FORMAT>
            '''

        formatted_prompt = prompt.format(content=content, type=type)
        completion = await self.claude_client.generate_message_agent_haiku(
            [{"role": "user", "content": formatted_prompt}]
        )

        return completion.content[0].text

    async def claude_ai_clean_and_format_text(self, content, type):
        prompt = '''
            <HTML_CONTENT>
            {content}
            </HTML_CONTENT>
            <DOCUMENT_TYPE>
            {type}
            </DOCUMENT_TYPE>
            <TASK>
                You are an **EPC (Engineering, Procurement, and Construction) Project Engineer** assistant working for **Worley** or **Fulor**. Your task is to:
                1. Fix any improperly displayed symbols in the given HTML content.
                2. Improve the text's readability by formatting it correctly (line breaks, spacing, etc.), ensuring professionalism and minimal paragraph spacing.
                3. When referring to the documentation, use **'The Project Documentation'** if the document type is "project" and **'The Tender Documentation'** if the document type is "tender."
                4. Avoid any phrases like "EPC Project Engineer" or similar roles, and remove them.
            </TASK>

           <IMPORTANT>
                - Do not include any additional text, explanations, or preambles in your response.
                - Only return the reformatted text. No headings, context, or introductory phrases like "Here is the reformatted text."
                - Ensure the response does not contain extra explanations, apologies, or acknowledgments.
                - Respond with confidence and authority, like an experienced engineer with years of expertise.
            </IMPORTANT>
            <OUTPUT_FORMAT>
                Only return the formatted text, nothing else.
            </OUTPUT_FORMAT>
            '''
        formatted_prompt = prompt.format(content=content, type=type)
        try:
            completion = await self.claude_client.generate_message_agent_haiku(
                [{"role": "user", "content": formatted_prompt}]
            )
            if completion and hasattr(completion.content[0], 'text'):
                return completion.content[0].text

        except Exception as e:
            completion = await self.claude_client.generate_message_agent_haiku(
                [{"role": "user", "content": formatted_prompt}]
            )
            if completion and hasattr(completion.content[0], 'text'):
                return completion.content[0].text

    async def groq_ai_clean_and_format_text(self, content, type):
        prompt = '''
            <HTML_CONTENT>
            {content}
            </HTML_CONTENT>
            <DOCUMENT_TYPE>
            {type}
            </DOCUMENT_TYPE>
            <TASK>
                You are an **EPC (Engineering, Procurement, and Construction) Project Engineer** assistant working for **Worley** or **Fulor**. Your task is to:
                1. Fix any improperly displayed symbols in the given HTML content.
                2. Improve the text's readability by formatting it correctly (line breaks, spacing, etc.), ensuring professionalism and minimal paragraph spacing.
                3. When referring to the documentation, use **'The Project Documentation'** if the document type is "project" and **'The Tender Documentation'** if the document type is "tender."
                4. Avoid any phrases like "EPC Project Engineer" or similar roles, and remove them.
            </TASK>

           <IMPORTANT>
                - Do not include any additional text, explanations, or preambles in your response.
                - Only return the reformatted text. No headings, context, or introductory phrases like "Here is the reformatted text."
                - Ensure the response does not contain extra explanations, apologies, or acknowledgments.
                - Respond with confidence and authority, like an experienced engineer with years of expertise.
            </IMPORTANT>
            <OUTPUT_FORMAT>
                Only return the formatted text, nothing else.
            </OUTPUT_FORMAT>
            '''
        formatted_prompt = prompt.format(content=content, type=type)
        try:
            completion = self.fast_apis.generate_completion(
                [{"role": "user", "content": formatted_prompt}],
                'groq',
                'llama3-70b-8192'
            )
            if completion:
                return completion

        except Exception as e:
            completion = await self.claude_client.generate_message_agent_haiku(
                [{"role": "user", "content": formatted_prompt}]
            )
            if completion and hasattr(completion.content[0], 'text'):
                return completion.content[0].text

    async def extract_all_answer_index(self, text):
        # Define the regex pattern to match all <WEIGHTED_SCORE> tags and their content
        pattern = r'<Source_Index_Number>(.*?)<\/Source_Index_Number>'
        match = re.findall(pattern, text)
        result = []

        if len(match) > 0:
            numbers = match[0].split(",")
            result.extend([int(num.strip()) for num in numbers if num.strip().isdigit()])

        return result

    async def extract_answer_content(self, text):
        # Regex pattern to match content within <answer> tags
        pattern = r'<answer>(.*?)<\/answer>'
        match = re.search(pattern, text, re.DOTALL)
        if match:
            return match.group(1).strip()
        return None

    async def extract_feedback_content(self, text):
        # print('given text to extract feedback: ', text)
        # Regex pattern to match content within <answer> tags
        pattern = r'<FEEDBACK>(.*?)<\/FEEDBACK>'
        match = re.search(pattern, text, re.DOTALL)
        if match:
            return match.group(1).strip()
        return None

    async def extract_recommendation_content(self, text):
        pattern = r'<RECOMMENDATION>(.*?)<\/RECOMMENDATION>'
        match = re.search(pattern, text, re.DOTALL)
        if match:
            return match.group(1).strip()
        return None

    async def extract_all_answer_sectionIds(self, text):
        # print('this is text: ', text)
        # Adjust regex pattern to account for potential whitespace around the tags
        pattern = r'<Section_Ids>\s*(.*?)\s*<\/Section_Ids>'
        match = re.findall(pattern, text)
        result = []

        if len(match) > 0:
            ids = match[0].split(",")
            result.extend([id.strip() for id in ids if id.strip()])  # Ensure no leading/trailing spaces

        return result

    async def extract_source_answer_content(self, text):
        # Regex pattern to extract content inside <Source_answer> tags, including multiline content
        pattern = r'<Source_answer>(.*?)<\/Source_answer>'

        match = re.search(pattern, text, re.DOTALL)

        # print('this is match: ', match)
        if match:
            source_content = match.group(1).strip()
            sources = [content.strip() for content in source_content.split('$$$$$') if content.strip()]
            return sources

        return []

    def convert_newlines_to_br(self, input_text):
        # Replace occurrences of more than two consecutive newlines with a single <br>
        input_text = re.sub(r'\n{3,}', '<br/>', input_text)

        # Replace exactly two consecutive newlines with two <br> tags
        input_text = re.sub(r'\n{2}', '<br/><br/>', input_text)

        # Replace any single newline with one <br> tag
        input_text = re.sub(r'\n', '<br/>', input_text)

        return input_text

    def remove_newlines(self, input_text):
        # Remove all newline characters from the input text
        return input_text.replace('\n', '')


    async def claude_ai_clean_and_format_sources(self, sources):
        print('sources in format agent: ', sources)
        prompt = '''
            <SOURCES_CONTENT>
                {sources}
            </SOURCES_CONTENT>
            <TASK>
                You are an **EPC (Engineering, Procurement, and Construction) Project Engineer** assistant working for **Worley** or **Fulor**. Your task is to format text into the following professional structure using HTML with **Tailwind CSS styling**:

                1. If any symbol is not properly shown or displayed, fix it by replacing it with the correct symbol for proper rendering.
                2. Improve readability by adding appropriate line breaks, spacing, and structure to avoid dense formatting.
                3a. The content of each section should appear below its title, indented properly to enhance clarity.
                3b. All the content should be strictly in *italic form*, and each subsection should have a **bold title** but remain in *italic format*.
                4. Symbols, special characters, or formatting issues should be corrected while preserving the original meaning.
                5. Ensure that all formatting is **professional and visually appealing**.
                6. Infer missing page numbers or section titles based on context; otherwise, use placeholders like `"Page X"` or `"Section X"`.
                7. Do not modify the **top heading text** inside the `<h3>` tag.

                <STYLE_REQUIREMENTS>
                    - Apply **Tailwind CSS** classes to style the output for **enhanced readability and aesthetics**.
                    - Text should have a **consistent font size, spacing, and structure**.
                    - All section headings (`<h3>`) should use `text-lg font-bold text-gray-900 mb-2` for emphasis.
                    - Italicized content should use `italic text-gray-700` for clarity.
                    - Subsection titles should be bold using `font-semibold text-gray-800`.
                    - Use `leading-relaxed` to maintain a comfortable **line height**.
                    - Ensure proper **padding (`p-2`) and margins (`mb-4`)** for spacing.
                    - Keep the layout **responsive and structured** for professional presentation.
                </STYLE_REQUIREMENTS>

                <IMPORTANT>
                    - Preserve the **original meaning** of the text.
                    - Focus on **clarity, structure, and professional formatting**.
                    - All subheadings must not exceed **11pt equivalent in a Word document**.
                    - Do not include any **extra text, explanations, or acknowledgments**.
                    - Respond with **confidence and precision**, like a seasoned EPC engineer.
                    - **Return only the formatted text**—no additional descriptions or preambles.
                    - Do not add any intro like saying `Here is the formatted Text`
                    - Write the response as Human Engineer as possible.
                </IMPORTANT>

                <OUTPUT_FORMAT>
                    Only return the formatted text with Tailwind CSS, nothing else.
                    Do not include explanations or additional text outside the formatted content.

                    <h3 class="text-lg font-bold text-gray-900 mb-2">Document Name:
                        <span class="italic font-normal">(<document_name>)</span>,
                        Section <section_number>
                        <span class="italic font-normal">(<title_of_section>)</span>,
                        Page <page_number>
                    </h3>

                    <div class="text-[14.6px] leading-relaxed text-gray-700 p-2 mb-4">
                        <content_of_the_section>
                    </div>
                </OUTPUT_FORMAT>
            '''
        formatted_prompt = prompt.format(sources=sources)
        try:
            completion = await self.claude_client.generate_message_agent_haiku(
                [{"role": "user", "content": formatted_prompt}]
            )

            if completion and hasattr(completion.content[0], 'text'):
                return completion.content[0].text
            # completion = self.fast_apis.generate_completion(
            #     [{"role": "user", "content": formatted_prompt}],
            #     'groq',
            #     'llama3-70b-8192'
            # )
            # if completion:
            #     print('completed formatted sources groq...')
            #     return completion.replace('\n', '')

        except Exception as e:
            completion = await self.claude_client.generate_message_agent_haiku(
                [{"role": "user", "content": formatted_prompt}]
            )
            if completion and hasattr(completion.content[0], 'text'):
                return completion.content[0].text


    async def groq_ai_clean_and_format_sources(self, sources):
        print('sources in format agent: ', sources)
        prompt = '''
            <SOURCES_CONTENT>
                {sources}
            </SOURCES_CONTENT>
            <TASK>
                You are an **EPC (Engineering, Procurement, and Construction) Project Engineer** assistant working for **Worley** or **Fulor**. Your task is to format text into the following professional structure using HTML with **Tailwind CSS styling**:

                1. If any symbol is not properly shown or displayed, fix it by replacing it with the correct symbol for proper rendering.
                2. Improve readability by adding appropriate line breaks, spacing, and structure to avoid dense formatting.
                3a. The content of each section should appear below its title, indented properly to enhance clarity.
                3b. All the content should be strictly in *italic form*, and each subsection should have a **bold title** but remain in *italic format*.
                4. Symbols, special characters, or formatting issues should be corrected while preserving the original meaning.
                5. Ensure that all formatting is **professional and visually appealing**.
                6. Infer missing page numbers or section titles based on context; otherwise, use placeholders like `"Page X"` or `"Section X"`.
                7. Do not modify the **top heading text** inside the `<h3>` tag.
                8. Replace all line breaks with HTML `<br>` elements, ensuring **balanced and structured formatting**.
                9. Ensure the response **does not contain any newline (`\n`) characters**, replacing them with appropriate HTML formatting.

                <STYLE_REQUIREMENTS>
                    - Apply **Tailwind CSS** classes to style the output for **enhanced readability and aesthetics**.
                    - Text should have a **consistent font size, spacing, and structure**.
                    - All section headings (`<h3>`) should use `text-lg font-bold text-gray-900 mb-2` for emphasis.
                    - Italicized content should use `italic text-gray-700` for clarity.
                    - Subsection titles should be bold using `font-semibold text-gray-800`.
                    - Use `leading-relaxed` to maintain a comfortable **line height**.
                    - Ensure proper **padding (`p-2`) and margins (`mb-4`)** for spacing.
                    - Keep the layout **responsive and structured** for professional presentation.
                </STYLE_REQUIREMENTS>

                <IMPORTANT>
                    - Preserve the **original meaning** of the text.
                    - Focus on **clarity, structure, and professional formatting**.
                    - All subheadings must not exceed **11pt equivalent in a Word document**.
                    - Do not include any **extra text, explanations, or acknowledgments**.
                    - Respond with **confidence and precision**, like a seasoned EPC engineer.
                    - **Return only the formatted text**—no additional descriptions or preambles.
                </IMPORTANT>

                <OUTPUT_FORMAT>
                    Only return the formatted text with Tailwind CSS, nothing else.

                    <h3 class="text-lg font-bold text-gray-900 mb-2">Document Name:
                        <span class="italic font-normal">(<document_name>)</span>,
                        Section <section_number>
                        <span class="italic font-normal">(<title_of_section>)</span>,
                        Page <page_number>
                    </h3>

                    <div class="text-[14.6px] leading-relaxed text-gray-700 p-2 mb-4">
                        <content_of_the_section>
                    </div>
                </OUTPUT_FORMAT>
            '''
        formatted_prompt = prompt.format(sources=sources)
        class FormatText(BaseModel):
            formatted_text: str

        try:
            completion = self.groq_client.extract_data(
                FormatText,
                [{"role": "user", "content": formatted_prompt}],
                'llama3-70b-8192'
            )
            if completion:
                print('completed formatted sources groq...')
                print(completion)
                return completion

        except Exception as e:
            print('groq failed now trying claude...')
            completion = await self.claude_client.generate_message_agent_haiku(
                [{"role": "user", "content": formatted_prompt}]
            )
            if completion and hasattr(completion.content[0], 'text'):
                return completion.content[0].text



    async def handle_query(self):
        with app.app_context():
            pending = Requirement.get_by(status='pending', type='qmp')
            if len(pending) >= 5:
                print('exiting qmp...')
                return

            related_project_ids = [
            '8cae6c9c-cf8f-4eb2-bb93-a0bb3d5c1e72',
                '1a0e8f66-5d87-41f9-bb93-1cb655f4a0f9',
                '1c31374d-4a4a-4a39-a0ee-372d99575ceb'
            ]
            return await self.answer_question_v1_1('23bccbb3-118a-4fe8-9367-77839bd89a96', related_project_ids)

        # files = sorted(Requirement.get_by(status='idle', type='qmp'), key=lambda x: x['tried'])
        files = sorted([item for item in Requirement.get_by(status='idle', type='qmp') if item['tried'] < 2], key=lambda x: x['tried'])

        # print('files to be handled...')
        # print(files)
        if len(files):
            if files[0]['tried'] < 2:
                await self.answer_question_v1_1(files[0]['id'])
        else:
            print("No Files In Queue!")

        return

if __name__ == "__main__":
    processor = QMPQueryHandler("", True)
    asyncio.run(processor.handle_query())
