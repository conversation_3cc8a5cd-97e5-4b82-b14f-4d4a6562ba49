def clean_price(price):
    """Cleans price string by removing commas and converting to float/int, handles None."""
    if price is None:
        return None
    try:
        # Remove commas and convert to float, then to int if it's a whole number
        cleaned = float(str(price).replace(',', ''))
        return int(cleaned) if cleaned == int(cleaned) else cleaned
    except ValueError:
        return str(price)  # Return original value if conversion fails (e.g., "TBA", "N/A", "Not Included", "2x5500")

def format_currency(price, currency="USD"):
    """Formats a number as currency string."""
    if price is None:
        return ""
    if isinstance(price, str):
        return price  # Return the string as is if it's not a number
    # Check if the price is a float with decimal places, then format appropriately
    if isinstance(price, float) and price != int(price):
        return f"{currency} {price:,.2f}"
    return f"{currency} {int(price):,}"


def generate_commercial_adjudication(data):
    """Generate detailed pricing table for Attachment 2."""
    bidders = set()
    equipment_details = {}

    # First pass to collect all bidders and structure
    for eq_id, eq_data in data['results'].items():
        equipment_name = eq_data['equipment_name']
        equipment_details[equipment_name] = {}
        
        for item in eq_data['data']:
            item_name = item['item_name'].replace('_', ' ').title()
            equipment_details[equipment_name][item_name] = {}
            
            for price_info in item['prices']:
                bidder = price_info['bidder']
                bidders.add(bidder)
                if bidder not in equipment_details[equipment_name][item_name]:
                    equipment_details[equipment_name][item_name][bidder] = {
                        'price': clean_price(price_info['unit_price']),
                        'quantity': '1',
                        'comment': price_info.get('comment', '')
                    }

    sorted_bidders = sorted(list(bidders))
    
    html_output = f"""<div align="center" style="margin: 0 auto; text-align: center;">
        <h2 align="center" style="color: #2c5aa0; border-bottom: 2px solid #2c5aa0; padding-bottom: 10px;">
            Attachment 2 – Commercial Adjudication
        </h2>
        <table border="1" cellpadding="4" cellspacing="0" style="margin: 20px auto; border-collapse: collapse; border: 1px solid #ddd; width: auto; min-width: 800px;">
            <thead>
                <tr>
                    <th bgcolor="#f2f2f2" style="border: 1px solid #ddd; padding: 8px; width: 200px;">Item Description</th>"""

    # Add bidder columns with sub-columns for price, quantity, and comment
    for bidder in sorted_bidders:
        html_output += f"""
                    <th bgcolor="#f2f2f2" style="border: 1px solid #ddd; padding: 8px; width: 120px;" colspan="3">{bidder}</th>"""
    
    html_output += """
                </tr>
                <tr>
                    <th bgcolor="#f2f2f2" style="border: 1px solid #ddd; padding: 8px;"></th>"""
    
    # Add sub-columns for each bidder
    for _ in sorted_bidders:
        html_output += """
                    <th bgcolor="#f2f2f2" style="border: 1px solid #ddd; padding: 8px; width: 40px;">Price</th>
                    <th bgcolor="#f2f2f2" style="border: 1px solid #ddd; padding: 8px; width: 40px;">Qty</th>
                    <th bgcolor="#f2f2f2" style="border: 1px solid #ddd; padding: 8px; width: 40px;">Comment</th>"""
    
    html_output += """
                </tr>
            </thead>
            <tbody>"""

    # Generate rows for each equipment and its items
    for eq_name in sorted(equipment_details.keys()):
        # Equipment row
        html_output += f"""
                <tr>
                    <td bgcolor="#e0e0e0" style="border: 1px solid #ddd; padding: 8px;"><b>{eq_name}</b></td>"""
        
        for bidder in sorted_bidders:
            price_info = equipment_details[eq_name].get('Main Equipment Price', {}).get(bidder, {})
            price = format_currency(price_info.get('price'))
            qty = price_info.get('quantity', '1')
            comment = price_info.get('comment', '')
            html_output += f"""
                    <td style="border: 1px solid #ddd; padding: 8px;">{price}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">{qty}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">{comment}</td>"""
        
        html_output += """
                </tr>"""
        
        # Items rows
        for item_name, item_data in equipment_details[eq_name].items():
            if item_name != 'Main Equipment Price':  # Skip the main equipment price as it's already shown
                html_output += f"""
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px;">{item_name}</td>"""
                
                for bidder in sorted_bidders:
                    price_info = item_data.get(bidder, {})
                    price = format_currency(price_info.get('price'))
                    qty = price_info.get('quantity', '1')
                    comment = price_info.get('comment', '')
                    html_output += f"""
                    <td style="border: 1px solid #ddd; padding: 8px;">{price}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">{qty}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">{comment}</td>"""
                
                html_output += """
                </tr>"""

    html_output += """
            </tbody>
        </table>
    </div>"""
    return html_output

if __name__ == "__main__":
    import json
    with open('report.json', 'r') as f:
        data = json.load(f)
    # Generate both HTML contents
    html_content1 = generate_commercial_adjudication(data)
    

    # Save to files
    with open("page1.html", "w") as f:
        f.write(html_content1)

    with open("page2.html", "w") as f:
        f.write(html_content2)

    print("Generated page1.html and page2.html successfully!")