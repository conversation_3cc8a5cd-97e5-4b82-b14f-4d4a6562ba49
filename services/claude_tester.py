import anthropic
import os, sys
import json
import random
import asyncio

class ClaudeTester:
    def __init__(self):
        # Get the path to the parent directory
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # Load environment variables from env.json file in the parent directory
        env_file_path = os.path.join(parent_dir, 'env.json')

        claude_keys = ['CLAUDE_API_KEY', 'CLAUDE_API_KEY_2']
        
        # Select a random API key
        selected_api_key = random.choice(claude_keys)
        
        # Load environment variables from env.json file
        with open(env_file_path, 'r') as f:
            env_data = json.load(f)
        
        # Set up ClaudeAI client
        self.client = anthropic.Anthropic(api_key=env_data.get(selected_api_key, ''))

        # Map deployments to models
        self.deployments_map = {
            "Claude 3 Sonnet": "claude-3-sonnet-20240229",
        }

        # Use Claude 3 Sonnet model for testing
        self.model_id = self.deployments_map["Claude 3 Sonnet"]
    
    def fetch_from_file(self, file_path='test_samples.json'):
        # Check if the file exists
        if os.path.exists(file_path):
            with open(file_path, 'r') as file:
                try:
                    # Load and return the JSON data from the file
                    data = json.load(file)
                    return data
                except json.JSONDecodeError:
                    print("Error: Could not decode JSON data.")
                    return None
        else:
            print(f"Error: File {file_path} does not exist.")
            return None


    async def test_model(self, messages, use_temperature=True, values=[0.1, 0.2, 0.3, 0.5, 0.7, 0.9], top_k=3, max_tokens=4096):
        results = []
        for value in values:
            try:
                if use_temperature:
                    print(f"Testing with temperature: {value}")
                    response = self.client.messages.create(
                        model=self.model_id,
                        temperature=value,
                        max_tokens=max_tokens,
                        messages=messages
                    )
                else:
                    print(f"Testing with top_p: {value}")
                    response = self.client.messages.create(
                        model=self.model_id,
                        top_p=value,
                        top_k=top_k,
                        max_tokens=max_tokens,
                        messages=messages
                    )
                
                results.append({
                    "value": value,
                    "type": "temperature" if use_temperature else "top_p",
                    "response": response.content[0].text
                })
            except Exception as e:
                print(f"Error generating message with value {value}: {e}")
                results.append({
                    "value": value,
                    "type": "temperature" if use_temperature else "top_p",
                    "response": None,
                    "error": str(e)
                })

        return results


# Example usage:
async def run_test(prompt):
    tester = ClaudeTester()

    test_data = tester.fetch_from_file()

    # print(prompt)

    for data in test_data:
        # print('i am data: ', data.keys())

        source_text = data['source_document_text']
        text = data['retrieved_texts']
        concatenated_criteria = f"{data['criteria']['name']} \n Description: {data['criteria']['description']}\n"

        # Create a copy of the prompt
        messages = prompt.copy()
        messages.append({
            "role": "user",
            "content": f"<SOURCE_DOCUMENT>\n{source_text}</SOURCE_DOCUMENT>\n<UPLOADED_DOCUMENT>\n{text}</UPLOADED_DOCUMENT>\n<Question>\n{concatenated_criteria}\n</Question>"
        })


        # print(prompt)
        # sys.exit()
        
        # Test with temperature values
        results1 = await tester.test_model(messages, use_temperature=True, values=[0.1, 0.2, 0.3, 0.5])
        
        # Test with top_p values
        # results2 = await tester.test_model(prompt, use_temperature=False, values=[0.85, 0.9, 0.95], top_k=3)
        
        # Combine results
        combined_results = {
            "criteria": concatenated_criteria,
            "temperature_tests": results1
            # "top_p_tests": results2
        }
        
        # Write results to a file
        output_file = "claude_test_results.json"
        # Check if the file exists and load existing data
        if os.path.exists(output_file):
            with open(output_file, "r") as f:
                try:
                    existing_data = json.load(f)
                except json.JSONDecodeError:
                    # If the file is empty or corrupted, initialize an empty dictionary
                    existing_data = {}
        else:
            existing_data = {}
        
        # Update existing data with the new combined results
        if "results" not in existing_data:
            existing_data["results"] = []

        existing_data["results"].append(combined_results)

        # Write the updated data back to the file
        with open(output_file, "w") as f:
            json.dump(existing_data, f, indent=4)
        
        print(f"Results written to {output_file}")


prompt = [
    {
        "role": "user",
        "content": """
            As an AI Industrial Engineer Evaluator assistant, 
            
            <Instruction>
                STEP1A:Your First task is to go through both the UPLOADED_DOCUMENT & the SOURCE_DOCUMENT to understand them, so you know if any part of the document can be used to support the Questions given.
                Understand the documents so you can be an expert in it, and then decide if the knowledge can be used to give answers to the Question.
                                    
                Rules for STEP1:
                - Search for sections or paragraphs that may contain relevant information related to each Question.
                - Look for keywords or phrases that indicate alignment with the Question.
                - Ensure that all relevant sections are thoroughly examined to avoid missing any information.

                STEP1B: After understanding the document, compare the UPLOADED_DOCUMENT against the SOURCE_DOCUMENT.
                - The SOURCE_DOCUMENT is assumed to be the correct and master reference document.
                - The UPLOADED_DOCUMENT should be evaluated based on how well it aligns with and matches the information in the SOURCE_DOCUMENT.
                
                STEP2: Your next task is to Evaluate the text based on the following Rules: 
                Note: The text should be scored based on the following:
                <SCORE INTERPRETATION>
                    - Use the following criteria for scoring:
                    - 0-20%: Non-compliant or no response – Objectives not met – Non-Compliant
                    - 21%-40%: Inadequate Response – Response inconclusive
                    - 41%-59%: Minimal Compliance – Objectives partially met
                    - 60%-80%: Higher Partial Compliance – Most objectives met
                    - 81%-100%: Full Compliance – Objectives fully met or exceeded
                </SCORE INTERPRETATION>

                Rules for STEP2:
                - Evaluate each section identified in STEP1 against the Question provided.
                - Always be careful before scoring an evaluation as 0% because sometimes it might have some part of information that supports the question.
                - Apply the scoring Question strictly based on the alignment of the text with the given Questions.
                - Compare the UPLOADED_DOCUMENT to the SOURCE_DOCUMENT, and consider the context and relevance of the text to the Question.
    
        
                STEP3: Response should follow the format below strictly:
                
                **Evaluation Format:**
                - <Evaluation>(Required)</Evaluation>
                - <Score>(Required, integer)</Score>
                - <Reason>(Required)</Reason>
                - <Reference>(Required)</Reference>
            </Instruction>
            <IMPORTANT>
                - The Content should contain exactly the full text which you evaluated, without any addition or subtraction.
                - Do not summarize or paraphrase the text in the content key, it should be returned as given(provided).
                - Most-times the questions asked in the Question is tricky, so you have to understand the document as an industrial engineer.
                - You have to ensure you understand the UPLOADED_DOCUMENT & SOURCE_DOCUMENT perfectly before answering, just like an industrial engineer would do.
                - Also lookout for any suggestions, examples or any form of statement that supports the Questions.
                - We need every single statement no matter how small that could help the Questions.
                - The Reason should be detailed & comprehensive enough.
                - If the UPLOADED_DOCUMENT doesnt contain partial or any information that can be used to compare against the SOURCE_DOCUMENT then the evaluation is - 0-20%: Non-compliant or no response
                - The reference is a text from the source document, not your description of comparison. The reference is just a text you pick as a strong point from the source document & it should be returned as is from the document.
            </IMPORTANT>
        """
    },
    {
        "role": "assistant",
        "content": "okay!"
    },
    {
        "role": "user",
        "content": """
            <SOURCE_DOCUMENT>
                **Warranty Details**: The warranty covers all major components and labor for at least 24 months. In cases where parts fail due to manufacturing defects, replacements are provided within 24 months.
            </SOURCE_DOCUMENT>
            <UPLOADED_DOCUMENT>
                The warranty provided covers parts for 24 months, but labor is only covered for 18 months, which may lead to higher repair costs.
            </UPLOADED_DOCUMENT>
            <Question>
                Title: Warranty Compliance
                Description: Does the proposed warranty meet the required specifications for both parts and labor?
                Weight: 10%
            </Question>
        """
    },
    {
        "role": "assistant",
        "content": """
            <output>
                <Evaluation>Non-compliant</Evaluation>
                <Score>15%</Score>
                <Reason>1. Warranty Compliance: The document specifies 18 months for labor coverage, which does not meet the required 24-month warranty for both parts and labor.</Reason>
                <Reference>**Warranty Details**: The warranty covers all major components and labor for at least 24 months. In cases where parts fail due to manufacturing defects, replacements are provided within 24 months.</Reference>
            </output>
        """
    },
    {
        "role": "user",
        "content": """
            <SOURCE_DOCUMENT>
                The system components must withstand pressure between 150 PSI and 300 PSI to ensure safety and reliability under operational conditions.
            </SOURCE_DOCUMENT>
            <UPLOADED_DOCUMENT>
                The provided documentation specifies the following pressure ratings: Component A can handle 140 PSI to 310 PSI, Component B can handle 160 PSI to 280 PSI, and Component C can handle 150 PSI to 290 PSI.
            </UPLOADED_DOCUMENT>
            <Question>
                Title: 2. Pressure Rating Compliance
                Description: Do the pressure ratings of the components meet the required specifications?
                Weight: 10%
            </Question>
        """
    },
    {
        "role": "assistant",
        "content": """
            <output>
                <Evaluation>Inadequate Response</Evaluation>
                <Score>35%</Score>
                <Reason>2. Pressure Rating Compliance: The pressure ratings for the components partially meet the specified range of 150 PSI to 300 PSI. While Component C fully complies, Components A and B do not meet the lower or upper bounds, respectively, resulting in inadequate compliance with the pressure rating criteria.</Reason>
                <Reference>The system components must withstand pressure between 150 PSI and 300 PSI to ensure safety and reliability under operational conditions.</Reference>
            </output>
        """
    },
    {
        "role": "user",
        "content": """
            <SOURCE_DOCUMENT>
                Maintenance should be conducted semi-annually to ensure optimal performance and longevity of the equipment.
            </SOURCE_DOCUMENT>
            <UPLOADED_DOCUMENT>
                The equipment will undergo maintenance every 6 months, aligning with industry best practices to prevent any operational downtime.
            </UPLOADED_DOCUMENT>
            <Question>
                Title: 3. Maintenance Schedule
                Description: Does the proposed maintenance interval comply with the required frequency?
                Weight: 8%
            </Question>
        """
    },
    {
        "role": "assistant",
        "content": """
            <output>
                <Evaluation>Full Compliance</Evaluation>
                <Score>90%</Score>
                <Reason>3. Maintenance Schedule: The text specifies a maintenance interval of every 6 months, which aligns with the semi-annual requirement.</Reason>
                <Reference>Maintenance should be conducted semi-annually to ensure optimal performance and longevity of the equipment.</Reference>
            </output>
        """
    },
    {
        "role": "user",
        "content": """
            <SOURCE_DOCUMENT>
                All materials must adhere to ISO 9001 standards to ensure quality and durability, critical in the construction phase.
            </SOURCE_DOCUMENT>
            <UPLOADED_DOCUMENT>
                The construction materials will follow recognized global standards, ensuring durability and quality in line with industry expectations.
            </UPLOADED_DOCUMENT>
            <Question>
                Title: 4. Material Standards Compliance
                Description: Does the proposal confirm compliance with ISO 9001 standards?
                Weight: 15%
            </Question>
        """
    },
    {
        "role": "assistant",
        "content": """
            <output>
                <Evaluation>Minimal Compliance</Evaluation>
                <Score>45%</Score>
                <Reason>4. Material Standards Compliance: The response mentions global standards but does not explicitly confirm compliance with ISO 9001, leading to partial compliance.</Reason>
                <Reference>All materials must adhere to ISO 9001 standards to ensure quality and durability, critical in the construction phase.</Reference>
            </output>
        """
    },
    {
        "role": "user",
        "content": """
            <SOURCE_DOCUMENT>
                Sewage storage should have a capacity of at least 0.1 cubic meters to ensure it meets the requirements during peak usage.
            </SOURCE_DOCUMENT>
            <UPLOADED_DOCUMENT>
                The tank has a capacity of 0.1 cubic meters, sufficient for sewage storage and handling during periods of high demand.
            </UPLOADED_DOCUMENT>
            <Question>
                Title: 8. Capacity Compliance
                Description: Does the proposed tank capacity meet the required specifications?
                Weight: 12%
            </Question>
        """
    },
    {
        "role": "assistant",
        "content": """
            <output>
                <Evaluation>Full Compliance</Evaluation>
                <Score>100%</Score>
                <Reason>8. Capacity Compliance: The provided tank capacity of 0.1 cubic meters meets the required capacity for sewage storage.</Reason>
                <Reference>Sewage storage should have a capacity of at least 0.1 cubic meters to ensure it meets the requirements during peak usage.</Reference>
            </output>
        """
    },
    {
        "role": "user",
        "content": """
            <SOURCE_DOCUMENT>
                The system should respond immediately to user commands, ensuring a seamless and efficient user experience.
            </SOURCE_DOCUMENT>
            <UPLOADED_DOCUMENT>
                Users can expect the system to react within seconds to their commands, making the interaction smooth and efficient.
            </UPLOADED_DOCUMENT>
            <Question>
                Title: 4. Response Time Compliance
                Description: Does the system's response time meet the immediacy required?
                Weight: 10%
            </Question>
        """
    },
    {
        "role": "assistant",
        "content": """
            <output>
                <Evaluation>Minimal Compliance</Evaluation>
                <Score>50%</Score>
                <Reason>4. Response Time Compliance: The term 'within seconds' suggests a slight delay, which may not fully align with the expectation of 'immediate' response, leading to partial compliance.</Reason>
                <Reference>The system should respond immediately to user commands, ensuring a seamless and efficient user experience.</Reference>
            </output>
        """
    }
]


# To run the test, call the async function like this:
asyncio.run(run_test(prompt))