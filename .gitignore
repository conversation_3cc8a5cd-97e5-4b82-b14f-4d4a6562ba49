# Ignore virtual environment
venv/
groq_keys.json
cerebras_keys.json
# Ignore Python cache files
__pycache__/

# Ignore Jupyter Notebook checkpoints
.ipynb_checkpoints/

# Ignore log files
*.log
test.py

# Ignore data directory
data/

# Ignore all .docx and .pdf files
*.docx
*.pdf
test_keys.py

# Ignore all files in specs/ directory
specs/*
chronobid/*

# Except for scp_engr_doc.json in specs/ directory
!specs/scp_engr_doc.json
!specs/Aienergy.discipline.json
!specs/Aienergy.discipline.criteria.json

db.sqlite3
services/lab.html
env.json

api.json

.DS_Store
_venv
testing_grounds.py
claude_messages.json
