"""Added status column to bid_file model

Revision ID: 15af6a5219d5
Revises: 64e5bd5161df
Create Date: 2025-02-27 20:04:10.045848

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '15af6a5219d5'
down_revision = '64e5bd5161df'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('bid_files', schema=None) as batch_op:
        batch_op.add_column(sa.Column('status', sa.String(length=255), nullable=False))
        batch_op.create_index(batch_op.f('ix_bid_files_status'), ['status'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('bid_files', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_bid_files_status'))
        batch_op.drop_column('status')

    # ### end Alembic commands ###
