import cohere
import numpy as np
import os, json
import time

class CohereService:
    def __init__(self):
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # Load environment variables from env.json file in the parent directory
        env_file_path = os.path.join(parent_dir, 'env.json')

        
        # Load environment variables from env.json file
        with open(env_file_path, 'r') as f:
            env_data = json.load(f)

        self.cohere_client = cohere.Client(api_key=env_data.get('COHERE_API_KEY', ''))

    def embed_texts(self, texts, input_type, model="embed-english-v3.0"):
        response = self.cohere_client.embed(
            texts=texts,
            input_type=input_type,
            model=model
        )
        embeddings = response.embeddings
        return np.asarray(embeddings)
    
    def embed_texts_as_list(self, texts, input_type, model="embed-english-v3.0"):
        for attempt in range(3):
            try:
                response = self.cohere_client.embed(
                    texts=texts,
                    input_type=input_type,
                    model=model
                )
                embeddings = response.embeddings
                return embeddings
            except Exception as e:
                print(f"Attempt {attempt + 1} failed: {e}")
                time.sleep(2)
        else:
            raise RuntimeError("Cohere API failed after 3 retries.")

    def search_similar(self, query, chunked_text, num_of_result = 4):
        
        # Embed the documents (chunked text) with input type 'search_document'
        doc_embeddings = self.embed_texts(chunked_text, input_type="search_document")

        # Embed the query with input type 'search_query'
        query_embedding = self.embed_texts([query], input_type="search_query")[0]

        # Compute the dot product between query embedding and document embeddings
        scores = np.dot(query_embedding, doc_embeddings.T)

        # Find the highest scores
        max_idx = np.argsort(-scores)

        num_indices = min(len(chunked_text), num_of_result)
        # best_texts = [chunked_text[max_idx[i]] for i in range(num_indices)]
        # Retrieve the top results and their scores
        best_results = [
            {"text": chunked_text[max_idx[i]], "score": scores[max_idx[i]]}
            for i in range(num_indices)
        ]

        merged_texts_with_scores = []
        for i in range(0, len(best_results), 2):
            merged_text = " ".join([r["text"] for r in best_results[i:i+2]])
            avg_score = sum([r["score"] for r in best_results[i:i+2]]) / len(best_results[i:i+2])
            merged_texts_with_scores.append({"text": merged_text, "score": avg_score})
            
        return merged_texts_with_scores


# if __name__ == "__main__":
#     cohere_service = CohereService()
#     res = cohere_service.embed_texts_as_list(["Hello, world!", "xyz"], input_type="search_query")
#     print(len(res[0]))