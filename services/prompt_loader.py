import json
from services.evaluation_templates import EvaluationTemplates

class PromptLoader:
    def __init__(self):
        # self.filepath = "claude_prompts.json"
        self.templates = EvaluationTemplates()
        # self.prompts = self._load_prompts()

    def _load_prompts(self):
        """Loads the JSON file and returns the prompt data."""
        try:
            with open(self.filepath, 'r') as file:
                return json.load(file)
        except FileNotFoundError:
            print(f"Error: File {self.filepath} not found.")
            return {}
        except json.JSONDecodeError:
            print("Error: Failed to decode JSON.")
            return {}
    
    def get_prompt(self, key, injections=None):
        """Returns the full prompt array with optional replacements."""
        # prompt_data = self.prompts.get(key)
        prompt_data = self.templates.get_template(key)
        if not prompt_data:
            raise ValueError(f"Prompt with key '{key}' not found.")

        if injections:
            prompt_content = prompt_data[0].format(**injections)
        else:
            prompt_content = prompt_data
        
        return prompt_content

if __name__ == "__main__":
    ploader = PromptLoader()
    print(ploader.get_prompt("cbp_get_weakness_section"))