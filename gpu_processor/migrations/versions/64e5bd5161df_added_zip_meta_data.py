"""added zip meta data

Revision ID: 64e5bd5161df
Revises: a6f1982f3617
Create Date: 2025-02-26 16:08:09.386807

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '64e5bd5161df'
down_revision = 'a6f1982f3617'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('bid_files', schema=None) as batch_op:
        batch_op.add_column(sa.Column('path', sa.Text(), nullable=True))

    with op.batch_alter_table('bids', schema=None) as batch_op:
        batch_op.add_column(sa.Column('zip_metadata', sa.Text(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('bids', schema=None) as batch_op:
        batch_op.drop_column('zip_metadata')

    with op.batch_alter_table('bid_files', schema=None) as batch_op:
        batch_op.drop_column('path')

    # ### end Alembic commands ###
