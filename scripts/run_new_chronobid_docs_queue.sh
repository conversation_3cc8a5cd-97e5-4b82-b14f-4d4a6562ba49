#!/bin/bash

# Log file for debugging
LOGFILE=/home/<USER>/dev-ds-oil-and-gas/logs/chronobid_docs_queue.log

VENV_PATH="/home/<USER>/dev-ds-oil-and-gas/venv/bin/activate"
source "$VENV_PATH"

# Log the date and time of script execution
echo "Running script at $(date)" >> $LOGFILE

# Navigate to the project directory
cd /home/<USER>/dev-ds-oil-and-gas || { echo "Failed to change directory to /home/<USER>/dev-ds-oil-and-gas"; exit 1; }


# Run the Python module
python3 -m services.handle_new_chronobid_documents >> $LOGFILE 2>&1

echo "Finished script at $(date)" >> $LOGFILE


