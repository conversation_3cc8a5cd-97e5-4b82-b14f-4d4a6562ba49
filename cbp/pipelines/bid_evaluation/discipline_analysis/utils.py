def transform_by_discipline(data):
    # Initialize the result dictionary
    result = {}
    
    # Iterate through each equipment
    for equipment in data:
        equipment_name = equipment['equipment_name']
        
        # Iterate through each category
        for category in equipment['categories']:
            # Iterate through each item in the category
            for item in category['items']:
                discipline = item['discipline']
                
                # Initialize discipline if not exists
                if discipline not in result:
                    result[discipline] = {}
                
                # Initialize equipment if not exists under this discipline
                if equipment_name not in result[discipline]:
                    result[discipline][equipment_name] = {}
                
                # Initialize category if not exists under this equipment
                if category['name'] not in result[discipline][equipment_name]:
                    result[discipline][equipment_name][category['name']] = []
                
                # Add the item to the category
                result[discipline][equipment_name][category['name']].append({
                    'name': item['name'],
                    'unit': item['unit']
                })
    
    return result

def construct_discipline_queries(transformed_data):
    """
    Constructs search queries for each discipline based on the transformed data.
    Each query will include technical specifications and relevant information for the discipline.
    
    Args:
        transformed_data (dict): The transformed data from transform_by_discipline function
        
    Returns:
        dict: A dictionary mapping each discipline to its constructed query
    """
    discipline_queries = {}
    
    for discipline, equipment_data in transformed_data.items():
        # Start with the discipline name as base query
        query_parts = [f"Technical specifications and requirements for {discipline}"]
        
        # Add equipment-specific information
        for equipment_name, categories in equipment_data.items():
            query_parts.append(f"\nEquipment: {equipment_name}")
            
            # Add category and item details
            for category_name, items in categories.items():
                query_parts.append(f"\nCategory: {category_name}")
                for item in items:
                    item_query = f"- {item['name']}"
                    if item.get('unit'):
                        item_query += f" (Unit: {item['unit']})"
                    query_parts.append(item_query)
        
        # Combine all parts into a single query for the discipline
        discipline_queries[discipline] = "\n".join(query_parts)
    
    return discipline_queries

# Example usage:
from test import data
transformed_data = transform_by_discipline(data)
print(transformed_data)

# transformed_data = transform_by_discipline(data)
# queries = construct_discipline_queries(transformed_data)
# for discipline, query in queries.items():
#     print(f"\nDiscipline: {discipline}")
#     print("Query:")
#     print(query) 