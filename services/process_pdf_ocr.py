from gevent import monkey; monkey.patch_all()
import gevent
from gevent.pool import Pool

import aiohttp
import os
import json
import asyncio
import requests  # Added for get_text_from_image_with_ocr_sync function
import time # Added for retry delay
import requests.exceptions # Added for specific exception handling
from bs4 import BeautifulSoup


# Get the path to the parent directory
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables from env.json file in the parent directory
env_file_path = os.path.join(parent_dir, 'env.json')
with open(env_file_path, 'r') as f:
    env_data = json.load(f)
    
async def process_df_with_ocr_support(file_path: str):
    #TODO should be sync, just leaving it async for now
    res = await process_pdf_async(file_path)
    return {'text': res}

async def process_df_with_ocr_support_(file_path: str) -> dict:
    """
    Sends a PDF file to the GPU server for OCR processing asynchronously.

    Args:
        file_path (str): The path to the PDF file to be processed.

    Returns:
        dict: The response data from the server, or an error message if the request fails.
    """
    url = f"{env_data['GPU_SERVER_URL']}/process_pdf_with_ocr_support"
    
    try:
        async with aiohttp.ClientSession() as session:
            with open(file_path, 'rb') as f:
                form_data = aiohttp.FormData()
                form_data.add_field('file', f, filename=os.path.basename(file_path), content_type='application/pdf')
                
                # Set timeout to 300 seconds (5 minutes)
                timeout = aiohttp.ClientTimeout(total=900, connect=60) 
                async with session.post(url, data=form_data, timeout=timeout) as response:
                    response.raise_for_status()  # Raises exception for HTTP errors
                    data = await response.json()
                    # write to file
                    with open('response.json', 'w') as f:
                        json.dump(data, f)
                    return data  # Return the data on success
    except aiohttp.ClientError as e:
        print(f"❌ Error: {e}")
        return {"error": f"❌ Error: {e}"}  # Return error message
    except Exception as ex:
        print(f"⚠️ Unexpected error: {ex}")
        return {"error": f"⚠️ Unexpected error: {ex}"}  # Return unexpected error message
    
   
   
async def get_text_from_image_with_ocr(file_path: str) -> dict:
    """
    Sends a PDF file to the GPU server for OCR processing asynchronously.

    Args:
        file_path (str): The path to the PDF file to be processed.

    Returns:
        dict: The response data from the server, or an error message if the request fails.
    """
    url = f"{env_data['GPU_SERVER_URL']}/run_image_ocr"
    
    try:
        async with aiohttp.ClientSession() as session:
            with open(file_path, 'rb') as f:
                form_data = aiohttp.FormData()
                form_data.add_field('file', f, filename=os.path.basename(file_path), content_type='image/png')
                
                # Set timeout to 300 seconds (5 minutes)
                timeout = aiohttp.ClientTimeout(total=900, connect=60) 
                async with session.post(url, data=form_data, timeout=timeout) as response:
                    response.raise_for_status()  # Raises exception for HTTP errors
                    data = await response.json()
                    # write to file
                    with open('response.json', 'w') as f:
                        json.dump(data, f)
                    return data  # Return the data on success
    except aiohttp.ClientError as e:
        print(f"❌ Error: {e}")
        return {"error": f"❌ Error: {e}"}  # Return error message
    except Exception as ex:
        print(f"⚠️ Unexpected error: {ex}")
        return {"error": f"⚠️ Unexpected error: {ex}"}  # Return unexpected error message
    
    
import os
import fitz  # PyMuPDF
import gc
import time
import asyncio
import json
import random
# from paddleocr import PaddleOCR

# Initialize PaddleOCR (using CPU to avoid memory issues)
def initialize_ocr():
    return None

parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
GROQ_KEYS_PATH = os.path.join(parent_dir,"groq_keys.json")

def load_groq_api_keys(file_path=GROQ_KEYS_PATH):
    """
    Load Groq API keys from a JSON file
    Returns a list of available API keys
    """
    available_keys = []
    
    try:
        # Load the JSON file
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        # Extract API keys from the JSON structure
        for key_name, key_info in data.items():
            if isinstance(key_info, dict) and 'api_key' in key_info:
                if key_info['api_key'].startswith('gsk_'):
                    available_keys.append(key_info['api_key'])
    
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"Error loading Groq API keys from {file_path}: {e}")
    
    return available_keys

def get_random_groq_api_key(file_path=GROQ_KEYS_PATH):
    """
    Get a random Groq API key from the available keys in the JSON file
    Returns a single API key or None if no keys are available
    """
    keys = load_groq_api_keys(file_path)
    if keys:
        return random.choice(keys)
    return None

def check_text_coherence_with_llm(text_sample, api_key=None, keys_file=GROQ_KEYS_PATH):
    """
    Use Groq LLM to determine if text is coherent or garbled
    Returns "COHERENT" if text is coherent, "GARBLED" if garbled, None if error
    """
    print("DEBUG: STARTING LLM COHERENCE CHECK")
    
    # If no API key provided, get a random one from the file
    if not api_key:
        api_key = get_random_groq_api_key(keys_file)
        
    if not api_key:
        print("ERROR: NO GROQ API KEY FOUND")
        return None
    
    try:
        from groq import Groq
        client = Groq(api_key=api_key)
        print("DEBUG: GROQ CLIENT INITIALIZED")
        
        # Prepare the prompt
        prompt = f"""
        Analyze the following text sample from a PDF document and determine if it appears to be 
        properly encoded, coherent human-readable text OR if it appears to be garbled/improperly encoded text.
        
        TEXT SAMPLE:
        {text_sample[:500]}  # Limit to first 500 chars

        Important:
        - The text sample must not be empty if it is empty, respond with 'GARBLED'.
        - The text sameple must contain at least two sentences of human language not just random characters. If it does not, respond with 'GARBLED'.

        If the text is readable human language which can be of any language such as Arabic, English, French, etc., respond with 'COHERENT'.
        If the text is garbled characters, encoding issues or not readable, respond with 'GARBLED'.
        Respond with only one word, either 'COHERENT' or 'GARBLED'.
        """
        
        chat_completion = client.chat.completions.create(
            messages=[
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            model="llama-3.1-8b-instant",  # Using a fast, small model for speed
            temperature=0
        )
        print("DEBUG: LLM API CALL COMPLETED")
        
        result = chat_completion.choices[0].message.content.strip()
        print(f"DEBUG: LLM RESULT: {result}")
        return result.upper()
        
    except Exception as e:
        print(f"ERROR: LLM COHERENCE CHECK FAILED:", str(e))
        return None

def detect_page_characteristics(page, doc):
    """
    Analyze a PDF page to determine its characteristics:
    - Whether it contains text
    - How many images it contains
    - Whether it's likely a scanned document
    - Other relevant features for processing decision
    """
    raw_blocks = page.get_text("rawdict")["blocks"]

    has_valid_unicode = False
    has_non_unicode_text = False
    has_text = False
    non_printable_chars = 0
    printable_chars = 0
    text_blocks = []
    
    for block in raw_blocks:
        if block["type"] != 0:
            continue
        block_text = ""
        for line in block.get("lines", []):
            for span in line.get("spans", []):
                text = span.get("text", "")
                block_text += text
                has_text = True
                
        if block_text.strip():
            text_blocks.append(block_text.strip())

    images = page.get_images(full=True)
    image_count = len(images)

    xobjects = page.get_xobjects()
    has_xobjects = len(xobjects) > 0

    return {
        "has_text": has_text,
        "image_count": image_count,
        "likely_scanned": not has_text and image_count > 0,
        "text_blocks": len(text_blocks),
        "text_content": text_blocks,
        "has_xobjects": has_xobjects
    }

def extract_text_from_page(page):
    """Extract text directly from a text-based PDF page"""
    # Try different text extraction methods
    methods = ["text", "blocks", "words", "html"]
    
    for method in methods:
        text = page.get_text(method)
        if text and len(text) > 20:  # If we got meaningful text
            if method == "blocks":
                # Clean up block text (often has better paragraph structure)
                try:
                    return "\n".join([block[4] for block in text if len(block) > 4])
                except:
                    # If block structure doesn't match expectation, try next method
                    continue
            elif method == "words":
                # Reconstruct text from words
                try:
                    words = [word[4] for word in text]
                    return " ".join(words)
                except:
                    continue
            elif method == "html":
                # Extract just the text from HTML
                try:
                    from bs4 import BeautifulSoup
                    soup = BeautifulSoup(text, 'html.parser')
                    return soup.get_text(separator="\n")
                except:
                    # If BeautifulSoup fails, fall back to raw text
                    continue
            else:
                # Regular text extraction
                return text
    
    # If all methods fail to extract meaningful text
    return ""

def extract_text_from_page_v2(page):
    # Method 0: Table Extraction (Prioritize for structured data)
    try:
        tables = page.find_tables()
        if tables:
            extracted_table_text = []
            for table in tables:
                table_data = table.extract()
                if table_data:
                    for row in table_data:
                        extracted_table_text.append("\t".join(row)) # Join cells with tabs
            
            final_table_text = "\n".join(extracted_table_text).strip()
            if final_table_text and len(final_table_text) > 20:
                return final_table_text
    except Exception:
        pass # Fallback to other methods if table extraction fails or yields no meaningful text

    # Method 1: Blocks
    try:
        blocks = page.get_text("blocks", flags=fitz.TEXT_PRESERVE_LIGATURES | fitz.TEXT_PRESERVE_WHITESPACE)
        if blocks:
            blocks.sort(key=lambda block: (block[1], block[0]))

            extracted_text = []
            for block in blocks:
                if block[6] == 0:
                    text_content = block[4].strip()
                    if text_content:
                        extracted_text.append(text_content)

            if extracted_text and len("".join(extracted_text)) > 20:
                return "\n".join(extracted_text)
    except Exception:
        pass

    # Method 2: Raw Text
    try:
        text_raw = page.get_text("text", flags=fitz.TEXT_PRESERVE_LIGATURES | fitz.TEXT_PRESERVE_WHITESPACE)
        if text_raw and len(text_raw) > 20:
            return text_raw.strip()
    except Exception:
        pass

    # Method 3: Words
    try:
        words = page.get_text("words", flags=fitz.TEXT_PRESERVE_LIGATURES | fitz.TEXT_PRESERVE_WHITESPACE)
        if words:
            words.sort(key=lambda w: (w[6], w[0]))

            reconstructed_text = []
            current_line = -1
            for word_info in words:
                if word_info[6] != current_line:
                    if current_line != -1:
                        reconstructed_text.append("\n")
                    current_line = word_info[6]
                reconstructed_text.append(word_info[4] + " ")

            final_text = "".join(reconstructed_text).strip()
            if final_text and len(final_text) > 20:
                return final_text
    except Exception:
        pass

    # Method 4: HTML
    try:
        html_text = page.get_text("html", flags=fitz.TEXT_PRESERVE_LIGATURES | fitz.TEXT_PRESERVE_WHITESPACE)
        if html_text and len(html_text) > 20:
            soup = BeautifulSoup(html_text, 'html.parser')
            cleaned_text = soup.get_text(separator="\n").strip()
            if cleaned_text and len(cleaned_text) > 20:
                return cleaned_text
    except Exception:
        pass

    return ""

def process_page_with_ocr(page_num, page, temp_dir):
    """Process a page using OCR"""
    print(f"DEBUG: STARTING OCR PROCESSING FOR PAGE {page_num + 1}")
    page_text = []
    pix = None
    
    try:
        # Convert page to image
        pix = page.get_pixmap(matrix=fitz.Matrix(1.5, 1.5))
        img_path = os.path.join(temp_dir, f"temp_page_{page_num}.png")
        pix.save(img_path)
        # Release pixmap memory immediately after saving
        del pix
        pix = None
        print("DEBUG: PAGE CONVERTED TO IMAGE SUCCESSFULLY")
        
        # Run OCR on GPU Server
        result = get_text_from_image_with_ocr_sync(img_path)
        page_text = result['text'] or []
        print("DEBUG: OCR COMPLETED")
        
      
    except Exception as e:
        print(f"ERROR: OCR PROCESSING FAILED FOR PAGE {page_num + 1}:", str(e))
        page_text.append(f"")
    finally:
        # Clean up resources in finally block to ensure it happens
        if pix:
            del pix
        
        # Clean up temp image
        try:
            if os.path.exists(img_path):
                os.remove(img_path)
                print("DEBUG: TEMPORARY IMAGE FILE REMOVED")
        except Exception as e:
            print("WARNING: FAILED TO REMOVE TEMPORARY IMAGE:", str(e))
        
        # Force garbage collection
        gc.collect()
        
    # Join all text from this page
    if page_text:
        if isinstance(page_text, list):
            result_text = "\n".join(page_text)
        else:
            result_text = page_text
    else:
        result_text = "NO TEXT EXTRACTED"
        print("WARNING: NO TEXT EXTRACTED FROM OCR")
        
    return result_text

# This function runs as an async coroutine
async def process_page_async(page_num, pdf_path, temp_dir="temp_images", use_llm=True, groq_keys_file="groq_keys.json"):
    """Process a single page from the PDF with intelligent method selection using async"""
    print("DEBUG: STARTING PAGE PROCESSING FOR PAGE", page_num + 1)
    page_start_time = time.time()
    
    # Create temp directory if it doesn't exist
    os.makedirs(temp_dir, exist_ok=True)
    
    # Initialize OCR for this task
    try:
        print("DEBUG: SUCCESSFULLY INITIALIZED OCR")
    except Exception as e:
        print("ERROR: FAILED TO INITIALIZE OCR:", str(e))
        return page_num, f"ERROR: Failed to initialize OCR - {str(e)}", "error"
    
    # We'll run the CPU-intensive parts in a thread pool
    loop = asyncio.get_running_loop()
    
    # Each task needs its own PyMuPDF document instance
    try:
        # Run in thread pool to avoid blocking event loop
        def open_pdf_and_get_page():
            pdf = fitz.open(pdf_path)
            page = pdf[page_num]
            return pdf, page
            
        pdf, page = await loop.run_in_executor(None, open_pdf_and_get_page)
        print("DEBUG: SUCCESSFULLY OPENED PDF AND LOADED PAGE", page_num + 1)
    except Exception as e:
        print("ERROR: FAILED TO OPEN PDF OR LOAD PAGE:", str(e))
        # Clean up OCR
        gc.collect()
        return page_num, f"ERROR: Failed to open PDF or load page - {str(e)}", "error"
    
    process_method = ""
    result_text = ""
    
    # Get page characteristics
    try:
        # Run in thread pool to avoid blocking event loop
        characteristics = await loop.run_in_executor(
            None, 
            lambda: detect_page_characteristics(page, pdf)
        )
        print("DEBUG: PAGE CHARACTERISTICS:", characteristics)
    except Exception as e:
        print("ERROR: FAILED TO DETECT PAGE CHARACTERISTICS:", str(e))
        # Clean up resources
        pdf.close()
        gc.collect()
        return page_num, f"ERROR: Failed to detect page characteristics - {str(e)}", "error"
    
    # Decision tree for processing method
    if characteristics["likely_scanned"]:
        print("DEBUG: USING OCR METHOD - PAGE APPEARS TO BE SCANNED")
        process_method = "OCR (scanned)"
        try:
            # Run OCR in thread pool
            result_text = await loop.run_in_executor(
                None,
                lambda: process_page_with_ocr(page_num, page, temp_dir)
            )
            print("DEBUG: OCR PROCESSING COMPLETED SUCCESSFULLY")
        except Exception as e:
            print("ERROR: OCR PROCESSING FAILED:", str(e))
            result_text = f"ERROR: OCR processing failed - {str(e)}"
    elif not characteristics["has_text"] and characteristics["image_count"] == 0:
        print("DEBUG: USING DIRECT EXTRACTION - BLANK/SIMPLE PAGE")
        process_method = "direct (blank/simple)"
        try:
            # Run extraction in thread pool
            result_text = await loop.run_in_executor(
                None,
                lambda: extract_text_from_page(page)
            )
            print("DEBUG: DIRECT EXTRACTION COMPLETED")
        except Exception as e:
            print("ERROR: DIRECT EXTRACTION FAILED:", str(e))
            result_text = f"ERROR: Direct extraction failed - {str(e)}"
    else:
        print("DEBUG: ATTEMPTING DIRECT EXTRACTION FIRST")
        try:
            # Run extraction in thread pool
            extracted_text = await loop.run_in_executor(
                None,
                lambda: extract_text_from_page(page)
            )
            print("DEBUG: DIRECT EXTRACTION ATTEMPTED")
            
            if use_llm and extracted_text:
                print("DEBUG: PERFORMING LLM COHERENCE CHECK")
                # Run LLM check in thread pool
                coherence = await loop.run_in_executor(
                    None,
                    lambda: check_text_coherence_with_llm(
                        extracted_text, 
                        keys_file=groq_keys_file
                    )
                )
                print("DEBUG: LLM COHERENCE RESULT:", coherence)
                
                if coherence == "COHERENT":
                    print("DEBUG: USING DIRECT EXTRACTION - LLM VERIFIED COHERENT")
                    process_method = "direct (LLM verified)"
                    result_text = extracted_text
                elif coherence == "GARBLED":
                    print("DEBUG: FALLING BACK TO OCR - LLM DETECTED GARBLED TEXT")
                    process_method = "OCR (fallback - garbled)"
                    # Run OCR in thread pool
                    result_text = await loop.run_in_executor(
                        None,
                        lambda: process_page_with_ocr(page_num, page, temp_dir)
                    )
                else:
                    print("DEBUG: FALLING BACK TO OCR - LLM ERROR")
                    process_method = "OCR (fallback - LLM error)"
                    # Run OCR in thread pool
                    result_text = await loop.run_in_executor(
                        None,
                        lambda: process_page_with_ocr(page_num, page, temp_dir)
                    )
            else:
                if characteristics["has_text"] and not characteristics["likely_scanned"]:
                    print("DEBUG: USING DIRECT EXTRACTION - NO LLM CHECK")
                    process_method = "direct (no LLM)"
                    result_text = extracted_text
                else:
                    print("DEBUG: USING OCR - NO TEXT DETECTED")
                    process_method = "OCR (fallback - no text)"
                    # Run OCR in thread pool
                    result_text = await loop.run_in_executor(
                        None,
                        lambda: process_page_with_ocr(page_num, page, temp_dir)
                    )
        except Exception as e:
            print("ERROR: PROCESSING FAILED:", str(e))
            result_text = f"ERROR: Processing failed - {str(e)}"
    
    # Clean up resources
    try:
        # Clean up PDF resources
        page = None  # Release page reference
        pdf.close()
        del pdf
        print("DEBUG: PDF CLOSED AND CLEANED UP SUCCESSFULLY")
        
        # Force garbage collection
        gc.collect()
        print("DEBUG: GARBAGE COLLECTION PERFORMED")
    except Exception as e:
        print("WARNING: CLEANUP FAILED:", str(e))
    
    page_time = time.time() - page_start_time
    print(f"DEBUG: PAGE {page_num + 1} PROCESSING COMPLETE - METHOD: {process_method} - TIME: {page_time:.2f}s")
    
    return page_num, result_text, process_method

async def process_pdf_async(pdf_path, max_concurrency=None, use_llm=True, groq_keys_file=GROQ_KEYS_PATH):
    """
    Process a PDF file asynchronously with concurrent handling of pages using asyncio
    """
    start_time = time.time()
    
    # Determine concurrency level (default to CPU count or provided value)
    if max_concurrency is None:
        import os
        max_concurrency = min(os.cpu_count() or 4, 4)  # Limit to avoid excessive resource usage
    
    # Open the PDF to get page count
    pdf = fitz.open(pdf_path)
    total_pages = len(pdf)
    pdf.close()
    
    print(f"Processing PDF with {total_pages} pages using max concurrency of {max_concurrency}")
    print(f"LLM text coherence check: {'ENABLED' if use_llm else 'DISABLED'}")
    
    # Create a temporary directory for images
    temp_dir = "temp_images"
    import os
    os.makedirs(temp_dir, exist_ok=True)
    
    # Track stats
    method_counts = {
        "OCR": 0,
        "direct": 0
    }
    
    # Create tasks for all pages
    tasks = [
        process_page_async(page_num, pdf_path, temp_dir, use_llm, groq_keys_file)
        for page_num in range(total_pages)
    ]
    
    # Process pages with concurrency control using semaphore
    semaphore = asyncio.Semaphore(max_concurrency)
    
    async def process_with_semaphore(task):
        async with semaphore:
            return await task
    
    # Wrap tasks with semaphore
    limited_tasks = [process_with_semaphore(task) for task in tasks]
    
    # Process pages concurrently and gather results
    results = []
    for task in asyncio.as_completed(limited_tasks):
        try:
            page_num, page_text, process_method = await task
            results.append((page_num, page_text))
            
            # Update stats
            if "OCR" in process_method:
                method_counts["OCR"] += 1
            else:
                method_counts["direct"] += 1
                
            # Print progress update
            print(f"Completed page {page_num + 1}/{total_pages} using {process_method}")
                
        except Exception as e:
            page_num = len(results)  # Approximation if we can't get the actual page number
            print(f"\nError in async task: {str(e)}")
            results.append((page_num, f"ERROR: {str(e)}"))
    
    # Clean up temp directory
    try:
        if os.path.exists(temp_dir) and not os.listdir(temp_dir):  # If directory is empty
            os.rmdir(temp_dir)
    except:
        pass
    
    # Sort results by page number
    results.sort(key=lambda x: x[0])
    page_texts = [text for _, text in results]
    
    # Print stats
    total_time = time.time() - start_time
    print(f"\nProcessing complete!")
    print(f"Total pages processed: {total_pages}")
    print(f"Pages using OCR: {method_counts['OCR']}")
    print(f"Pages using direct extraction: {method_counts['direct']}")
    print(f"Total processing time: {total_time:.2f}s")
    print(f"Average time per page: {total_time/total_pages:.2f}s")
    
    return page_texts



def process_pdf(pdf_path, max_concurrency=4, use_llm=True, groq_keys_file=GROQ_KEYS_PATH):
    """
    Process a PDF file with intelligent method selection for each page
    """
    try:
        # Try to get current running loop
        loop = asyncio.get_running_loop()
    except RuntimeError:
        # No running loop, create one
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    
    return loop.run_until_complete(
        process_pdf_async(
            pdf_path,
            max_concurrency=max_concurrency,
            use_llm=use_llm,
            groq_keys_file=groq_keys_file
        )
    )


# Helper function to run the async code
def process_pdf_(pdf_path, max_concurrency=4, use_llm=True, groq_keys_file=GROQ_KEYS_PATH):
    """
    Process a PDF file with intelligent method selection for each page
    
    Args:
        pdf_path (str): Path to the PDF file
        max_concurrency (int): Maximum number of concurrent tasks
        use_llm (bool): Whether to use LLM for text coherence verification
        groq_keys_file (str): Path to file containing Groq API keys
        
    Returns:
        list: Extracted text for each page
    """
    return asyncio.run(process_pdf_async(
        pdf_path, 
        max_concurrency=max_concurrency,
        use_llm=use_llm,
        groq_keys_file=groq_keys_file
    ))


def get_text_from_image_with_ocr_sync(file_path: str, retries: int = 3, backoff_factor: float = 0.5) -> dict:
    """
    Sends an image file to the GPU server for OCR processing synchronously,
    with retry logic on failure.

    Args:
        file_path (str): The path to the image file to be processed.
        retries (int): The maximum number of retry attempts.
        backoff_factor (float): The factor to determine the delay between retries (delay = backoff_factor * (2 ** attempt)).

    Returns:
        dict: The response data from the server, or an error message if the request fails after all retries.
    """
    urls = [f"https://v4zcxz0n5vla5o-5140.proxy.runpod.net/run_image_ocr", f"https://v4zcxz0n5vla5o-5130.proxy.runpod.net/run_image_ocr"]
    last_exception = None

    for attempt in range(retries):
        url = random.choice(urls)
        try:
            with open(file_path, 'rb') as f:
                files = {'file': (os.path.basename(file_path), f, 'image/png')}

                response = requests.post(url, files=files, timeout=600)
                response.raise_for_status()  # Raises HTTPError for bad responses (4xx or 5xx)

                data = response.json()
                return data  # Return the data on success

        except requests.exceptions.RequestException as e: # Catch specific requests errors
            last_exception = e
            if attempt < retries - 1:
                delay = backoff_factor * (2 ** attempt)
                time.sleep(delay)
        except Exception as ex: # Catch other potential errors during file handling or JSON parsing
             last_exception = ex
             if attempt < retries - 1:
                 delay = backoff_factor * (2 ** attempt)
                 time.sleep(delay)

    # If loop completes without returning, all retries failed
    error_message = f"❌ Error after {retries} attempts: {last_exception}"
    return {"error": error_message}

def process_page_sync(page_num, pdf_path, temp_dir="temp_images", use_llm=True, groq_keys_file=GROQ_KEYS_PATH):
    """Process a single page from the PDF with intelligent method selection using sync code"""
    print("DEBUG: STARTING PAGE PROCESSING FOR PAGE", page_num + 1)
    page_start_time = time.time()
    
    # Create temp directory if it doesn't exist
    os.makedirs(temp_dir, exist_ok=True)
    
    # Initialize OCR for this task
    try:
        print("DEBUG: SUCCESSFULLY INITIALIZED OCR")
    except Exception as e:
        print("ERROR: FAILED TO INITIALIZE OCR:", str(e))
        return page_num, f"ERROR: Failed to initialize OCR - {str(e)}", "error"
    
    # Each task needs its own PyMuPDF document instance
    try:
        pdf = fitz.open(pdf_path)
        page = pdf[page_num]
        # print(f'\n\n\nThis is the page: {page}\n\n\n')
        print("DEBUG: SUCCESSFULLY OPENED PDF AND LOADED PAGE", page_num + 1)
    except Exception as e:
        print("ERROR: FAILED TO OPEN PDF OR LOAD PAGE:", str(e))
        # Clean up OCR
        gc.collect()
        return page_num, f"ERROR: Failed to open PDF or load page - {str(e)}", "error"
    
    process_method = ""
    result_text = ""
    
    # Get page characteristics
    try:
        characteristics = detect_page_characteristics(page, pdf)
        print("DEBUG: PAGE CHARACTERISTICS:", characteristics)
    except Exception as e:
        print("ERROR: FAILED TO DETECT PAGE CHARACTERISTICS:", str(e))
        # Clean up resources
        pdf.close()
        gc.collect()
        return page_num, f"ERROR: Failed to detect page characteristics - {str(e)}", "error"
    
    # Decision tree for processing method
    if characteristics["likely_scanned"]:
        print("DEBUG: USING OCR METHOD - PAGE APPEARS TO BE SCANNED")
        process_method = "OCR (scanned)"
        try:
            result_text = process_page_with_ocr(page_num, page, temp_dir)
            print("DEBUG: OCR PROCESSING COMPLETED SUCCESSFULLY")
        except Exception as e:
            print("ERROR: OCR PROCESSING FAILED:", str(e))
            result_text = f"ERROR: OCR processing failed - {str(e)}"
    elif not characteristics["has_text"] and characteristics["image_count"] == 0:
        print("DEBUG: USING DIRECT EXTRACTION - BLANK/SIMPLE PAGE")
        process_method = "direct (blank/simple)"
        try:
            result_text = extract_text_from_page(page)
            print("DEBUG: DIRECT EXTRACTION COMPLETED")
        except Exception as e:
            print("ERROR: DIRECT EXTRACTION FAILED:", str(e))
            result_text = f"ERROR: Direct extraction failed - {str(e)}"
    else:
        print("DEBUG: ATTEMPTING DIRECT EXTRACTION FIRST")
        try:
            extracted_text = extract_text_from_page_v2(page)
            # print(f'\n\n\nThis is the extracted text: {extracted_text}\n\n\n')
            print("DEBUG: DIRECT EXTRACTION ATTEMPTED")
            
            if use_llm and extracted_text:
                print("DEBUG: PERFORMING LLM COHERENCE CHECK")
                coherence = check_text_coherence_with_llm(
                    extracted_text, 
                    keys_file=groq_keys_file
                )
                print("DEBUG: LLM COHERENCE RESULT:", coherence)
                
                if coherence == "COHERENT":
                    print("DEBUG: USING DIRECT EXTRACTION - LLM VERIFIED COHERENT")
                    process_method = "direct (LLM verified)"
                    result_text = extracted_text
                elif coherence == "GARBLED":
                    print("DEBUG: FALLING BACK TO OCR - LLM DETECTED GARBLED TEXT")
                    process_method = "OCR (fallback - garbled)"
                    result_text = process_page_with_ocr(page_num, page, temp_dir)
                else:
                    print("DEBUG: FALLING BACK TO OCR - LLM ERROR")
                    process_method = "OCR (fallback - LLM error)"
                    result_text = process_page_with_ocr(page_num, page, temp_dir)
            else:
                if characteristics["has_text"] and not characteristics["likely_scanned"]:
                    print("DEBUG: USING DIRECT EXTRACTION - NO LLM CHECK")
                    process_method = "direct (no LLM)"
                    result_text = extracted_text
                else:
                    print("DEBUG: USING OCR - NO TEXT DETECTED")
                    process_method = "OCR (fallback - no text)"
                    result_text = process_page_with_ocr(page_num, page, temp_dir)
        except Exception as e:
            print("ERROR: PROCESSING FAILED:", str(e))
            result_text = f"ERROR: Processing failed - {str(e)}"
    
    # Clean up resources
    try:
        # Clean up PDF resources
        page = None  # Release page reference
        pdf.close()
        del pdf
        print("DEBUG: PDF CLOSED AND CLEANED UP SUCCESSFULLY")
        
        # Force garbage collection
        gc.collect()
        print("DEBUG: GARBAGE COLLECTION PERFORMED")
    except Exception as e:
        print("WARNING: CLEANUP FAILED:", str(e))
    
    page_time = time.time() - page_start_time
    print(f"DEBUG: PAGE {page_num + 1} PROCESSING COMPLETE - METHOD: {process_method} - TIME: {page_time:.2f}s")
    
    return page_num, result_text, process_method

def process_pdf_with_greenlet(pdf_path, max_concurrency=None, use_llm=True, groq_keys_file=GROQ_KEYS_PATH):
    """
    Process a PDF file synchronously with concurrent handling of pages using gevent
    """
    
    
    start_time = time.time()
    
    # Determine concurrency level (default to CPU count or provided value)
    if max_concurrency is None:
        max_concurrency = min(os.cpu_count() or 4, 4)  # Limit to avoid excessive resource usage
    
    # Open the PDF to get page count
    pdf = fitz.open(pdf_path)
    total_pages = len(pdf)
    pdf.close()
    
    print(f"Processing PDF with {total_pages} pages using max concurrency of {max_concurrency}")
    print(f"LLM text coherence check: {'ENABLED' if use_llm else 'DISABLED'}")
    
    # Create a temporary directory for images
    temp_dir = "temp_images"
    os.makedirs(temp_dir, exist_ok=True)
    
    # Track stats
    method_counts = {
        "OCR": 0,
        "direct": 0
    }
    
    # Process pages with gevent Pool for concurrency
    results = []
    pool = Pool(max_concurrency)
    
    def process_page_wrapper(page_num):
        try:
            page_num, page_text, process_method = process_page_sync(
                page_num, pdf_path, temp_dir, use_llm, groq_keys_file
            )
            
            # Update stats
            if "OCR" in process_method:
                method_counts["OCR"] += 1
            else:
                method_counts["direct"] += 1
                
            # Print progress update
            print(f"Completed page {page_num + 1}/{total_pages} using {process_method}")
            
            return page_num, page_text
            
        except Exception as e:
            print(f"\nError processing page {page_num + 1}: {str(e)}")
            return page_num, f"ERROR: {str(e)}"
    
    # Submit all tasks to the pool
    jobs = [pool.spawn(process_page_wrapper, page_num) for page_num in range(total_pages)]
    
    # Wait for all jobs to complete
    gevent.joinall(jobs)
    
    # Collect results
    for job in jobs:
        if job.value is not None:
            page_num, page_text = job.value
            results.append((page_num, page_text))
    
    # Clean up temp directory
    try:
        if os.path.exists(temp_dir) and not os.listdir(temp_dir):  # If directory is empty
            os.rmdir(temp_dir)
    except:
        pass
    
    # Sort results by page number
    results.sort(key=lambda x: x[0])
    page_texts = [text for _, text in results]
    
    # Print stats
    total_time = time.time() - start_time
    print(f"\nProcessing complete!")
    print(f"Total pages processed: {total_pages}")
    print(f"Pages using OCR: {method_counts['OCR']}")
    print(f"Pages using direct extraction: {method_counts['direct']}")
    print(f"Total processing time: {total_time:.2f}s")
    print(f"Average time per page: {total_time/total_pages:.2f}s")
    
    return page_texts

def process_pdf_with_ocr_support(file_path: str):
    """
    Synchronous version of process_df_with_ocr_support
    """
    res = process_pdf_with_greenlet(file_path, max_concurrency=10)
    return {'text': res}


# if __name__ == "__main__":
    # file_path = os.path.join(parent_dir, "enco.pdf")
    # print(file_path)
    # start_time = time.time()
    # print(f"Starting PDF processing at {time.strftime('%Y-%m-%d %H:%M:%S')}")
    # res = process_pdf_with_ocr_support(file_path)
    # with open('response.json', 'w') as f:
    #     json.dump(res, f)
    # print(res)
    # end_time = time.time()
    # print(f"PDF processing completed in {end_time - start_time:.2f} seconds")