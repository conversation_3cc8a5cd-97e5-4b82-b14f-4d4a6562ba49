from extensions import celery
from services.zip_file_processor import ZIPFileProcessor
import os
import uuid
from models import BidFile, Bids
from socket_instance import socket_instance
import time
import traceback

@celery.task(bind=True, max_retries=3, name="task.file_processor_tasks_bid_socket.process_files_bid_task_with_socket")
def process_files_bid_task_with_socket(self, bid_id, file_path, req_id=None):
    """
    Process a single bid file with socket-based progress tracking.
    
    Args:
        bid_id (str): ID of the bid
        file_path (str): Path to the file to process
        req_id (str, optional): Request ID for socket communication
    
    Returns:
        str: Status message
    """
    try:
        # Get socket manager instance
        socket_manager = socket_instance.get_instance()
        file_name = os.path.basename(file_path)
        
        # If request ID is provided, emit a test event
        if req_id and socket_manager:
            socket_manager.emit_to_client(
                req_id,
                'test_event',
                {'message': f'Starting bid file processing for {file_name}'},
                namespace='/bid_processing'
            )
            
            # Emit processing started event
            socket_manager.emit_to_client(
                req_id,
                'processing_started',
                {
                    'event_type': 'bid_processing',
                    'request_id': req_id,
                    'data': {
                        'message': f"Starting to process bid file: {file_name}",
                        'bid_id': bid_id,
                        'file_name': file_name
                    }
                },
                namespace='/bid_processing'
            )
        
        print(f'Starting to process bid file: {file_name}')
        
        # Process the file
        try:
            # Update progress at 25%
            if req_id and socket_manager:
                socket_manager.emit_to_client(
                    req_id,
                    'progress_message',
                    {
                        'event_type': 'bid_processing',
                        'request_id': req_id,
                        'data': {
                            'status': 'processing',
                            'message': f"Analyzing file structure: {file_name}",
                            'progress': 25,
                            'bid_id': bid_id,
                            'file_name': file_name
                        }
                    },
                    namespace='/bid_processing'
                )
            
            file_processor = ZIPFileProcessor(bid_id, file_path)
            
            # Update progress at 50%
            if req_id and socket_manager:
                socket_manager.emit_to_client(
                    req_id,
                    'progress_message',
                    {
                        'event_type': 'bid_processing',
                        'request_id': req_id,
                        'data': {
                            'status': 'processing',
                            'message': f"Extracting content from file: {file_name}",
                            'progress': 50,
                            'bid_id': bid_id,
                            'file_name': file_name
                        }
                    },
                    namespace='/bid_processing'
                )
            
            # Process the file
            file_processor.process_files()
            
            # Update progress at 100%
            if req_id and socket_manager:
                socket_manager.emit_to_client(
                    req_id,
                    'progress_message',
                    {
                        'event_type': 'bid_processing',
                        'request_id': req_id,
                        'data': {
                            'status': 'processing',
                            'message': f"Finalizing processing: {file_name}",
                            'progress': 90,
                            'bid_id': bid_id,
                            'file_name': file_name
                        }
                    },
                    namespace='/bid_processing'
                )
            
            # Get bid name for the completion message
            bid_name = "this bid"
            try:
                bid = Bids.get_single(bid_id)
                if bid and 'name' in bid:
                    bid_name = bid['name']
            except Exception as e:
                print(f"Error getting bid name: {e}")
            
            # Emit completion event
            if req_id and socket_manager:
                socket_manager.emit_to_client(
                    req_id,
                    'completed_event',
                    {
                        'event_type': 'bid_processing',
                        'request_id': req_id,
                        'data': {
                            'status': 'success',
                            'message': f"Successfully processed {file_name} for {bid_name}",
                            'bid_id': bid_id,
                            'file_name': file_name
                        }
                    },
                    namespace='/bid_processing'
                )
            
            print(f"Successfully processed bid file: {file_name}")
            return f"Successfully processed bid file: {file_name}"
            
        except Exception as e:
            error_msg = f"Error processing bid file {file_name}: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            
            # Emit error event
            if req_id and socket_manager:
                socket_manager.emit_to_client(
                    req_id,
                    'error_event',
                    {
                        'event_type': 'bid_processing',
                        'request_id': req_id,
                        'data': {
                            'status': 'error',
                            'message': f"Error processing file {file_name}",
                            'error': str(e),
                            'bid_id': bid_id,
                            'file_name': file_name
                        }
                    },
                    namespace='/bid_processing'
                )
            
            raise e
            
    except Exception as e:
        print(f"Error during bid file processing: {e}")
        traceback.print_exc()
        return str(e)


@celery.task(bind=True, max_retries=3, name="task.file_processor_tasks_bid_socket.process_files_bid_bulk_task_with_socket")
def process_files_bid_bulk_task_with_socket(self, bid_id, file_paths, req_id=None):
    """
    Process multiple bid files in bulk with socket-based progress tracking.
    
    Args:
        bid_id (str): ID of the bid
        file_paths (list): List of file paths to process
        req_id (str, optional): Request ID for socket communication
    
    Returns:
        str: Status message
    """
    try:
        # Get socket manager instance
        socket_manager = socket_instance.get_instance()
        total_files = len(file_paths)
        
        # If request ID is provided, emit a test event
        if req_id and socket_manager:
            socket_manager.emit_to_client(
                req_id,
                'test_event',
                {'message': f'Starting bulk bid processing for {total_files} files'},
                namespace='/bid_processing'
            )
            
            # Emit processing started event
            socket_manager.emit_to_client(
                req_id,
                'processing_started',
                {
                    'event_type': 'bid_processing',
                    'request_id': req_id,
                    'data': {
                        'message': f"Starting to process {total_files} bid files in bulk",
                        'bid_id': bid_id,
                        'total_files': total_files
                    }
                },
                namespace='/bid_processing'
            )
        
        print(f'Starting to process {total_files} bid files in bulk...')
        processed_count = 0
        failed_count = 0
        successful_files = []
        failed_files = []
        
        # Get bid name for messages
        bid_name = "this bid"
        try:
            bid = Bids.get_single(bid_id)
            if bid and 'name' in bid:
                bid_name = bid['name']
        except Exception as e:
            print(f"Error getting bid name: {e}")
        
        for index, file in enumerate(file_paths):
            bid_file_id = file["file_id"]
            file_path = file["file_path"]
            file_name = os.path.basename(file_path)
            file_extension = os.path.splitext(file_path)[1].lstrip('.')
            current_file = index + 1
            
            # Emit progress update for current file
            if req_id and socket_manager:
                socket_manager.emit_to_client(
                    req_id,
                    'progress_message',
                    {
                        'event_type': 'bid_processing',
                        'request_id': req_id,
                        'data': {
                            'status': 'processing',
                            'message': f"Processing file {current_file} of {total_files}: {file_name}",
                            'progress': (current_file - 0.5) / total_files * 100,
                            'bid_id': bid_id,
                            'file_name': file_name,
                            'current_file': current_file,
                            'total_files': total_files
                        }
                    },
                    namespace='/bid_processing'
                )
            
            # Set status to pending before processing
            try:
                print(f"Updating file {file_name} status: pending")
                file_data = {
                    'name': file_name,
                    'bid_id': bid_id,
                    'status': "pending",
                    'file_type': file_extension,
                }
                BidFile.update(bid_file_id, **file_data)
                
                # Process the file
                if os.path.exists(file_path):
                    file_processor = ZIPFileProcessor(bid_id, file_path)
                    file_processor.process_files()
                    processed_count += 1
                    status = 'done'
                    successful_files.append({
                        'file_id': bid_file_id,
                        'file_name': file_name
                    })
                else:
                    print(f"File not found: {file_path}")
                    failed_count += 1
                    status = 'failed'
                    failed_files.append({
                        'file_id': bid_file_id,
                        'file_name': file_name,
                        'error': 'File not found'
                    })
            except Exception as e:
                print(f"Error processing file {file_path}: {e}")
                failed_count += 1
                status = 'failed'
                failed_files.append({
                    'file_id': bid_file_id,
                    'file_name': file_name,
                    'error': str(e)
                })
                
            # Update status after processing (success or failure)
            try:
                print(f"Updating file {file_name} status: {status}")
                file_data = {
                    'name': file_name,
                    'bid_id': bid_id,
                    'status': status,
                    'file_type': file_extension,
                }
                BidFile.update(bid_file_id, **file_data)
            except Exception as db_err:
                print(f"Error saving BidFile for {file_name}: {db_err}")
            
            # Emit progress update after processing current file
            if req_id and socket_manager:
                file_status = 'success' if status == 'done' else 'error'
                file_message = f"Successfully processed" if status == 'done' else f"Failed to process"
                
                socket_manager.emit_to_client(
                    req_id,
                    'file_complete',
                    {
                        'event_type': 'bid_processing',
                        'request_id': req_id,
                        'data': {
                            'status': file_status,
                            'message': f"{file_message} file {current_file} of {total_files}: {file_name}",
                            'progress': (current_file / total_files * 100),
                            'bid_id': bid_id,
                            'file_name': file_name,
                            'current_file': current_file,
                            'total_files': total_files
                        }
                    },
                    namespace='/bid_processing'
                )
        
        # Prepare summary message
        if failed_count == 0:
            if total_files == 1:
                summary_msg = f"Your file was successfully processed for {bid_name}."
            else:
                summary_msg = f"All {total_files} files were successfully processed for {bid_name}."
            summary_status = "success"
        elif processed_count == 0:
            if total_files == 1:
                summary_msg = f"We couldn't process your file for {bid_name}. It may be corrupted or in an unsupported format."
            else:
                summary_msg = f"We couldn't process any of your {total_files} files for {bid_name}. They may be corrupted or in unsupported formats."
            summary_status = "error"
        else:
            if failed_count == 1:
                failure_text = "1 file couldn't be processed"
            else:
                failure_text = f"{failed_count} files couldn't be processed"
            
            if processed_count == 1:
                success_text = "1 file was successfully processed"
            else:
                success_text = f"{processed_count} files were successfully processed"
            
            summary_msg = f"{success_text} and {failure_text} for {bid_name}."
            summary_status = "partial"
        
        print(f"Bulk processing completed. {summary_msg}")
        
        # Emit completion event
        if req_id and socket_manager:
            # Create a more user-friendly list of failed files if any
            failed_files_messages = []
            if failed_count > 0:
                for failed_file in failed_files:
                    failed_files_messages.append(f"• '{failed_file['file_name']}' - Unable to process")
            
            socket_manager.emit_to_client(
                req_id,
                'completed_event',
                {
                    'event_type': 'bid_processing',
                    'request_id': req_id,
                    'data': {
                        'status': summary_status,
                        'message': summary_msg,
                        'total_files': total_files,
                        'successful_files': processed_count,
                        'failed_files': failed_count,
                        'failed_files_messages': failed_files_messages,
                        'success_details': successful_files,
                        'failure_details': failed_files,
                        'bid_id': bid_id
                    }
                },
                namespace='/bid_processing'
            )
        
        return f"Bulk processing completed. Successfully processed: {processed_count}, Failed: {failed_count}"
    except Exception as e:
        print(f"Error during bulk bid file processing: {e}")
        traceback.print_exc()
        
        # Emit error event
        if req_id and socket_manager:
            socket_manager.emit_to_client(
                req_id,
                'error_event',
                {
                    'event_type': 'bid_processing',
                    'request_id': req_id,
                    'data': {
                        'status': 'error',
                        'message': "We encountered an unexpected problem while processing your files. Please try again or contact support if the issue persists.",
                        'technical_error': str(e),
                        'bid_id': bid_id
                    }
                },
                namespace='/bid_processing'
            )
        
        return str(e)