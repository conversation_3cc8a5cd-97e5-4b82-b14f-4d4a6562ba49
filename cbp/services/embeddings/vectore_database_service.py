import os
import json
from uuid import UUID, uuid4
from typing import List, <PERSON><PERSON>, Optional
from langchain_cohere import CohereEmbeddings
import faiss
from langchain_community.docstore.in_memory import InMemoryDocstore
from langchain_community.vectorstores import FAISS
from langchain_core.documents import Document
import asyncio
import time
import aiohttp
from typing import List, Tuple
import requests

class VectorDatabaseService:
    """Service for managing vector embeddings and similarity search operations."""
    
    EMBEDDING_MODEL = "embed-english-v3.0"
    VECTOR_DB_NAME = "aienergy_vec_db"
    
    def __init__(self, max_concurrent_tasks: int = 10):
        self._embeddings_service = None
        self._vector_database = None
        self._concurrent_task_limiter = asyncio.Semaphore(max_concurrent_tasks)
        self._load_environment_config()

    def _load_environment_config(self) -> None:
        """Loads environment configuration from JSON file."""
        parent_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
        env_file_path = os.path.join(parent_dir, 'env.json')
        
        with open(env_file_path, 'r') as config_file:
            env_config = json.load(config_file)
            
        os.environ["COHERE_API_KEY"] = env_config.get("COHERE_API_KEY", "")
        self._gpu_server_url =  env_config.get("GPU_SERVER_URL", "https://v4zcxz0n5vla5o-5130.proxy.runpod.net")

    def _initialize_vector_store(self) -> None:
        """Initializes the FAISS vector store with Cohere embeddings."""
        self._embeddings_service = CohereEmbeddings(model=self.EMBEDDING_MODEL)
       
        try:
            self._vector_database = FAISS.load_local(
                self.VECTOR_DB_NAME,
                self._embeddings_service,
                allow_dangerous_deserialization=True
            )
        except Exception as e:
            print(f"Failed to initialize FAISS vector store: {e}")
            self._vector_database = None

    async def insert_documents(self, document_contents: List[str], file_name: str, request_id: str) -> None:
        """
        Inserts documents into the vector store asynchronously.

        Args:
            document_contents: List of document content to be embedded
            file_name: Source file name for metadata
            request_id: Request identifier for document grouping
        """
        self._initialize_vector_store()
        
        documents = [
            Document(
                page_content=content,
                metadata={"source": file_name, "request_id": request_id}
            ) for content in document_contents
        ]
        document_ids = [uuid4() for _ in document_contents]

        asyncio.create_task(self._store_documents_async(documents, document_ids))

    async def _store_documents_async(self, documents: List[Document], document_ids: List[UUID]) -> None:
        """
        Stores documents in the vector database and persists changes asynchronously.
        
        Args:
            documents: List of documents to store
            document_ids: Corresponding unique identifiers for the documents
        """
        store_start_time = time.time()
        self._vector_database.add_documents(documents=documents, ids=document_ids)
        store_duration = time.time() - store_start_time
        print(f"Document storage completed in {store_duration:.2f} seconds for {len(documents)} documents")

        persist_start_time = time.time()
        self._vector_database.save_local(self.VECTOR_DB_NAME)
        persist_duration = time.time() - persist_start_time
        print(f"Vector database persistence completed in {persist_duration:.2f} seconds")

    async def search_documents(self, search_queries: List[str], request_id: str, num_results: int = 5) -> List[List[Tuple[str, float]]]:
        """
        Performs semantic search against the vector database.

        Args:
            search_queries: List of search queries to process
            request_id: Request identifier for filtering results
            num_results: Number of top results to return per query

        Returns:
            List of results for each query, where each result contains content and similarity score
        """
        return await self._execute_gpu_search(search_queries, request_id, num_results)
    
    async def _execute_gpu_search(self, search_queries: List[str], request_id: str, num_results: int) -> List[List[Tuple[str, float]]]:
        """
        Executes search queries on GPU server for improved performance.

        Args:
            search_queries: List of search queries to process
            request_id: Request identifier for filtering
            num_results: Number of top results to return per query

        Returns:
            List of results per query, each containing content and similarity score
        """
        url = f"{self._gpu_server_url}/search_tender_requirement"
        request_payload = {
            "search_queries": search_queries,
            "request_id": request_id,
            "k": num_results
        }

        try:
            timeout_config = aiohttp.ClientTimeout(
                total=240,
                connect=60,
                sock_read=150,
                sock_connect=60 
            )
            async with aiohttp.ClientSession(timeout=timeout_config) as session:
                async with session.post(url, json=request_payload, headers={'Content-Type': 'application/json'}) as response:
                    response_text = await response.text()
                    
                    if response.status == 200:
                        response_data = json.loads(response_text)
                        if response_data.get("success"):
                            return [
                                [(item["content"], item["score"]) for item in query_results]
                                for query_results in response_data["message"]["results"]
                            ]
                        raise RuntimeError(f"GPU server responded with failure: {response_data}")
                    raise RuntimeError(f"GPU server error: {response.status} - {response_text}")
        except asyncio.TimeoutError:
            raise RuntimeError("GPU server request timed out")
        except Exception as e:
            raise RuntimeError(f"Error during GPU search: {str(e)}")
    
    def _execute_gpu_search_sync(self, search_queries: List[str], request_id: str, num_results: int) -> List[List[Tuple[str, float]]]:
        """
        Executes search queries on GPU server for improved performance.

        Args:
            search_queries: List of search queries to process
            request_id: Request identifier for filtering
            num_results: Number of top results to return per query

        Returns:
            List of results per query, each containing content and similarity score
        """
        url = f"{self._gpu_server_url}/search_tender_requirement"
        request_payload = {
            "search_queries": search_queries,
            "request_id": request_id,
            "k": num_results
        }

        try:
            response = requests.post(
                url,
                json=request_payload,
                headers={'Content-Type': 'application/json'},
                timeout=(60, 150)  # (connect timeout, read timeout)
            )
            
            if response.status_code == 200:
                response_data = response.json()
                if response_data.get("success"):
                    return [
                        [(item["content"], item["score"]) for item in query_results]
                        for query_results in response_data["message"]["results"]
                    ]
                raise RuntimeError(f"GPU server responded with failure: {response_data}")
            raise RuntimeError(f"GPU server error: {response.status_code} - {response.text}")
            
        
        except requests.Timeout:
            raise RuntimeError("GPU server request timed out")
        except Exception as e:
            raise RuntimeError(f"Error during GPU search: {str(e)}")
        
    async def get_top_documents(self, request_id: str) -> List[List[Tuple[str, float]]]:
        """
        Retrieves top 3 most relevant documents from GPU server.

        Args:
            request_id: Request identifier for filtering

        Returns:
            List of document groups, each containing content and relevance score
        """
        url = f"{self._gpu_server_url}/get_top_3_pages_documents"
        query_params = {"request_id": request_id}

        try:
            timeout_config = aiohttp.ClientTimeout(total=180, connect=60)
            async with aiohttp.ClientSession(timeout=timeout_config) as session:
                async with session.get(url, params=query_params, headers={'Content-Type': 'application/json'}) as response:
                    response_text = await response.text()
                    
                    if response.status == 200:
                        response_data = json.loads(response_text)
                        if response_data.get("success"):
                            return response_data["message"]["results"]
                        raise RuntimeError(f"GPU server responded with failure: {response_data}")
                    raise RuntimeError(f"GPU server error: {response.status} - {response_text}")
        except asyncio.TimeoutError:
            raise RuntimeError("GPU server request timed out")
        except Exception as e:
            raise RuntimeError(f"Error during top document retrieval: {str(e)}")

    async def _legacy_similarity_search(self, search_query: str, request_id: str, num_results: int):
        """
        Performs legacy asynchronous similarity search with scoring.
        
        This is a legacy method kept for compatibility.
        """
        return self._vector_database.similarity_search_with_score(
            search_query,
            k=num_results,
            filter={"request_id": request_id},
        )
