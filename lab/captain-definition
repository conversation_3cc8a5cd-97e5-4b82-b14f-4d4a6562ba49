{"schemaVersion": 2, "dockerfilePath": "./Dockerfile", "volumes": [{"volumeName": "data-volume", "containerPath": "/app/data", "hostPath": "/home/<USER>/ds-oil-and-gas/data"}, {"volumeName": "specs-volume", "containerPath": "/app/specs", "hostPath": "/home/<USER>/ds-oil-and-gas/specs"}, {"volumeName": "chronobid-volume", "containerPath": "/app/chronobid", "hostPath": "/home/<USER>/ds-oil-and-gas/chronobid"}, {"volumeName": "db-volume", "containerPath": "/app/db.sqlite3", "hostPath": "/home/<USER>/ds-oil-and-gas/db.sqlite3"}], "buildContext": ".", "context": ".", "exposedPorts": {"8000/tcp": {}}, "portBinding": {}, "start": {"web": "gunicorn -w 4 -b 0.0.0.0:5120 app:app"}, "appVersionsToRetain": 5, "hasPersistentData": "no", "envVars": {"FLASK_ENV": "production"}, "app": "flask-app", "appName": "Flask App"}