"""Add discipline to requirement table

Revision ID: 1fbf9c532c00
Revises: 700216b0a1f4
Create Date: 2024-08-22 17:56:01.023271

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1fbf9c532c00'
down_revision = '700216b0a1f4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('requirement', schema=None) as batch_op:
        batch_op.add_column(sa.Column('discipline', sa.String(length=255), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('requirement', schema=None) as batch_op:
        batch_op.drop_column('discipline')

    # ### end Alembic commands ###
