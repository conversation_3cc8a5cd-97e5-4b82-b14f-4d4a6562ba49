"""added templates for clients

Revision ID: 8893287e3963
Revises: 2ca40ce63b78
Create Date: 2025-01-22 22:21:47.878574

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '8893287e3963'
down_revision = '2ca40ce63b78'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('document_numbering_template',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('client', sa.String(length=255), nullable=False),
    sa.Column('template', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('document_numbering_template', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_document_numbering_template_client'), ['client'], unique=False)

    op.create_table('report_template',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('client', sa.String(length=255), nullable=False),
    sa.Column('intro_template', sa.Text(), nullable=True),
    sa.Column('body_template', sa.Text(), nullable=True),
    sa.Column('conclusion_template', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('report_template', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_report_template_client'), ['client'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('report_template', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_report_template_client'))

    op.drop_table('report_template')
    with op.batch_alter_table('document_numbering_template', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_document_numbering_template_client'))

    op.drop_table('document_numbering_template')
    # ### end Alembic commands ###
