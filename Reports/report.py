from models import ReportTemplate
import uuid

class Reports:
    def __init__(self):
        pass

    def create(self, client, intro_template, body_template, conclusion_template):
        create_kwargs = {
            'id': str(uuid.uuid4()),  # Generate a new unique ID
            'client': client,
            'intro_template': intro_template,
            'body_template': body_template,
            'conclusion_template': conclusion_template
        }
        # Call the create method of ReportTemplate
        return ReportTemplate.create(**create_kwargs)

    def update(self, id, intro_template=None, body_template=None, conclusion_template=None):
        update_kwargs = {}
        if intro_template:
            update_kwargs['intro_template'] = intro_template
        if body_template:
            update_kwargs['body_template'] = body_template
        if conclusion_template:
            update_kwargs['conclusion_template'] = conclusion_template
        
        # Call the update method of ReportTemplate
        return ReportTemplate.update(id, **update_kwargs)

    def delete(self, id):
        # Call the delete method of ReportTemplate
        return ReportTemplate.delete_by(id=id)

    def list(self, **filters):
        # Call the get_by method of ReportTemplate
        return ReportTemplate.get_by(**filters)

    def view(self, id):
        # Call the get_single method of ReportTemplate
        return ReportTemplate.get_single(id)
