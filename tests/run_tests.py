import os
import asyncio
import json

# Import the necessary classes
from tests import ChronobidProcessorManager, SpecsComplyProcessorManager, QuestionAnswerTester

# Directory containing sample PDF files and questions JSON file
DATA_DIR = 'tests/data'
QUESTIONS_DATA = os.path.join(DATA_DIR, 'questions.json')
CHRONOBID_DATA = os.path.join(DATA_DIR, 'chronobid_data.json')
SPECS_DATA = os.path.join(DATA_DIR, 'specs_data.json')

# Function to create requirement records for Specs tests
async def run_specs_tests():    
    # Load specs test data
    with open(SPECS_DATA, 'r') as file:
        specs_data = json.load(file)
    
    processor_manager = SpecsComplyProcessorManager(specs_data)
    processor_manager.run()


# Function to create requirement records for Chronobid tests
async def run_chronobid_tests():    
    # Load chronobid test data
    with open(CHRONOBID_DATA, 'r') as file:
        chronobid_data = json.load(file)
    
    processor_manager = ChronobidProcessorManager(chronobid_data)
    processor_manager.run()


# Function to run question-answer tests and write responses to a file
def run_question_answer_tests(questions_data):
    # Load questions data
    with open(QUESTIONS_DATA, 'r') as file:
        questions_data = json.load(file)
    
    response_file = ""

    tester = QuestionAnswerTester(questions_data, response_file)
    tester.run()



# Main function to run all tests
async def main():
    await run_specs_tests()
    await run_chronobid_tests()
    run_question_answer_tests()


if __name__ == '__main__':
    asyncio.run(main())
