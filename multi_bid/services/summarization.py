from transformers import AutoModelForCausalLM, AutoTokenizer, pipeline
# from auto_gptq import exllama_set_max_input_length
import torch
import os
import asyncio
import time
import random
from time import sleep
from openai import AzureOpenAI, OpenAI
import json
class SummarizationAssistant:
    def __init__(self):
        # Get the path to the parent directory
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # Load environment variables from env.json file in the parent directory
        env_file_path = os.path.join(parent_dir, 'env.json')
        print(env_file_path)
        with open(env_file_path, 'r') as f:
            env_data = json.load(f)

        # Initialize AzureOpenAI client
        self.azure_client = AzureOpenAI(
            azure_endpoint=env_data.get("AZURE_ENDPOINT"),
            api_key=env_data.get("AZURE_API_KEY"),
            api_version=env_data.get("AZURE_API_VERSION")
        )

        # Define the mapping of deployments
        self.deployments_map = {
            "gpt-3.5-turbo-16k": ["ai_energy-3-16k-1"],
            "gpt-4-turbo": ["ai_energy-4-turbo-1"],
            "gpt-3.5-turbo": ["ai_energy-3-turbo"]
        }

        # Define the model name or path
        self.model_name_or_path = 'TheBloke/Mistral-7B-Instruct-v0.1-GPTQ'

    def summarization(self, text):
        model = AutoModelForCausalLM.from_pretrained(self.model_name_or_path,
                                                     torch_dtype=torch.float16,
                                                     device_map="cuda:0",
                                                     revision="main")

        tokenizer = AutoTokenizer.from_pretrained(self.model_name_or_path, use_fast=True)

        # model = exllama_set_max_input_length(model, 4096)
        model = ""

        prompt_template='''
            ### System Prompt
            Based on the text input from the user, produce a comprehensive yet succinct summary. Extract the primary themes and central messages, and represent them in a coherent manner spanning 2-3 sentences. Ensure that the summary encapsulates the essence and intention of the original content, making it accessible for users unfamiliar with the full text. 
            ### User Message: 
            4.12 Interface Management 
            Refer to the “PROJECT CONTROL” section of EXHIBIT - P – Administration  Instructions & Project Control. 
            CONTRACTOR shall acknowledge the COMPANY Interface Management  Procedure (Project1 INTERFACE CONTROL PROCEDURE – Ref. Project1-Company-PMT 
            0000-PM-PRO-0029), the Interface Register (INTERFACE REGISTER – Ref. NA 
            company-PMT-0812-PM-REG-0006-1), the Interface Alignment sheets (Project1  INTERFACE ALIGNMENT SHEETS – Ref. MUL-company-Project1 -0812-MD-IAS-0001 ) and  shall actively participate in the interface alignment process as required by  COMPANY.
            ### Assistant: the document instructs that reference should be made to the “PROJECT CONTROL” section of EXHIBIT - P – Administration Instructions & Project Control for guidelines on interface management within the project. The CONTRACTOR is mandated to acknowledge and comply with the COMPANY Interface Management Procedure, denoted by specific reference numbers, including adhering to the Interface Register and the Interface Alignment Sheets. Active participation in the interface alignment process is required, ensuring alignment and coordination as per the COMPANY's stipulated procedures and standards.
        '''
            
        prompt_template += f'''
            ### User Message
            {text}
            ### Assistant
        '''

        # Now proceed with your inference
        input_ids = tokenizer(prompt_template, return_tensors='pt').input_ids.cuda()
        output = model.generate(inputs=input_ids, temperature=0, max_new_tokens=512)

        pipe = pipeline(
            "text-generation",
            model=model,
            tokenizer=tokenizer,
            max_new_tokens=4096,
            temperature=0,
            top_p=0.95,
            repetition_penalty=1.15,
            pad_token_id=tokenizer.eos_token_id,
            return_full_text=False
        )

        return pipe(prompt_template)[0]['generated_text']

    def openai_summarization(self, text, temperature=0.01, model="gpt-3.5-turbo", steps=5):
        messages = [
            {"role": "system", "content": """
            Based on the text input from the user, produce a comprehensive yet succinct summary.
            Extract the primary themes and central messages, and represent them in a coherent manner spanning 2-3 sentences.
            Ensure that the summary encapsulates the essence and intention of the original content, making it accessible for users unfamiliar with the full text. For example:
            ### User Message: 
            4.12 Interface Management 
            Refer to the “PROJECT CONTROL” section of EXHIBIT - P – Administration  Instructions & Project Control. 
            CONTRACTOR shall acknowledge the COMPANY Interface Management  Procedure (Project1 INTERFACE CONTROL PROCEDURE – Ref. Project1-Company-PMT 
            0000-PM-PRO-0029), the Interface Register (INTERFACE REGISTER – Ref. NA 
            company-PMT-0812-PM-REG-0006-1), the Interface Alignment sheets (Project1  INTERFACE ALIGNMENT SHEETS – Ref. MUL-company-Project1 -0812-MD-IAS-0001 ) and  shall actively participate in the interface alignment process as required by  COMPANY.
            ### Assistant: 
            the document instructs that reference should be made to the “PROJECT CONTROL” section of EXHIBIT - P – Administration Instructions & Project Control for guidelines on interface management within the project. The CONTRACTOR is mandated to acknowledge and comply with the COMPANY Interface Management Procedure, denoted by specific reference numbers, including adhering to the Interface Register and the Interface Alignment Sheets. Active participation in the interface alignment process is required, ensuring alignment and coordination as per the COMPANY's stipulated procedures and standards.
        """},
            {"role": "user", "content": text}
        ]

        while steps > 0:
            steps -= 1
            try:
                completion = self.azure_client.chat.completions.create(
                    model=random.choice(self.deployments_map[model]),
                    temperature=temperature,
                    messages=messages
                )
                return completion.choices[0].message.content
            except Exception as e:
                print(e)
                time.sleep(30)


# Usage example:
# if __name__ == "__main__":
#     summarization_assistant = SummarizationAssistant()
#     text = "Your text goes here."
#     summary = summarization_assistant.summarization(text)
#     print(summary)
#     openai_summary = summarization_assistant.openai_summarization(text)
#     print(openai_summary)
