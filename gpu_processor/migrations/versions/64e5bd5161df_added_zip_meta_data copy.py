"""added zip meta data

Revision ID: 64e5bd5161df
Revises: a6f1982f3617
Create Date: 2025-02-26 16:08:09.386807

"""
from alembic import op
import sqlalchemy as sa
import sqlalchemy.engine.reflection as reflection


# revision identifiers, used by Alembic.
revision = '64e5bd5161df'
down_revision = 'a6f1982f3617'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('bid_files', schema=None) as batch_op:
        if not column_exists('bids', 'zip_metadata'):
            return

    with op.batch_alter_table('bids', schema=None) as batch_op:
        if not column_exists('bids', 'zip_metadata'):
            return

    # ### end Alembic commands ###

def column_exists(table_name, column_name):
    """Check if a column exists before attempting to drop it."""
    bind = op.get_bind()
    inspector = reflection.Inspector.from_engine(bind)
    columns = [col["name"] for col in inspector.get_columns(table_name)]
    return column_name in columns


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('bids', schema=None) as batch_op:
        if column_exists('bids', 'zip_metadata'):
            batch_op.drop_column('zip_metadata')

    with op.batch_alter_table('bid_files', schema=None) as batch_op:
        if column_exists('bids', 'zip_metadata'):
            batch_op.drop_column('path')

    # ### end Alembic commands ###
