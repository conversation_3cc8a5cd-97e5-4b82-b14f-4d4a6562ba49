from typing import Dict, List, Any, Optional
import json
from dataclasses import dataclass
from services.faiss_embedding import FaissEmbedding
from services.data_handler import DataManager
from services.claude_ai_service import ClaudeService
from services.prompt_loader import PromptLoader


@dataclass
class EquipmentEvaluation:
    """Data class for equipment evaluation results"""
    discipline: str
    equipment: str
    evaluation: str


@dataclass
class BidderAnalysis:
    """Data class for complete bidder analysis results"""
    bidder_name: str
    analysis: List[EquipmentEvaluation]


class DisciplineAnalysisPipeline:
    """
    A comprehensive pipeline for analyzing bidder data across disciplines and equipment.
    
    This class processes bidder data, transforms it by discipline, generates search queries,
    retrieves relevant documents, and performs AI-powered analysis for each discipline-equipment combination.
    """
    
    def __init__(self, 
                 project_id: str,
                 claude_keys: List[str] = None,
                 default_claude_key: str = "CLAUDE_API_CBP",
                 temperature: float = 0.001):
        """
        Initialize the pipeline with required services and configuration.
        
        Args:
            project_id (str): The project ID for document retrieval
            claude_keys (List[str]): List of Claude API keys
            default_claude_key (str): Default Claude API key to use
            temperature (float): Temperature setting for AI analysis
        """
        self.project_id = project_id
        self.temperature = temperature
        
        # Initialize services
        self.faiss_embedding = FaissEmbedding()
        self.data_manager = DataManager()
        self.claude_client = ClaudeService(
            claude_keys=claude_keys or ['CLAUDE_API_CBP'], 
            default_key=default_claude_key
        )
        self.prompt_loader = PromptLoader()
        
        # Common technical terms and standards by discipline
        self.discipline_context = {
            'Mechanical': ['ASME', 'API', 'ISO', 'pump', 'valve', 'pipeline', 'pressure', 'flow', 'temperature', 'material', 'corrosion', 'maintenance'],
            'Electrical': ['IEC', 'IEEE', 'NEC', 'voltage', 'current', 'power', 'cable', 'transformer', 'switchgear', 'protection', 'earthing'],
            'Instrumentation & Control': ['ISA', 'IEC', 'sensor', 'transmitter', 'control', 'automation', 'calibration', 'accuracy', 'signal', 'protocol'],
            'Civil': ['ACI', 'ASTM', 'concrete', 'steel', 'foundation', 'structure', 'load', 'design', 'construction', 'safety'],
            'Process': ['API', 'ASME', 'flow', 'pressure', 'temperature', 'control', 'safety', 'operation', 'maintenance', 'efficiency'],
            'Commercial': ['cost', 'pricing', 'delivery', 'warranty', 'terms', 'conditions', 'schedule', 'logistics'],
            'Quality': ['ISO', 'ASTM', 'testing', 'inspection', 'certification', 'compliance', 'standards', 'quality assurance'],
            'General': ['specifications', 'requirements', 'standards', 'documentation', 'compliance']
        }

    def transform_by_discipline(self, data: List[Dict[str, Any]]) -> Dict[str, Dict[str, Dict[str, List[Dict[str, str]]]]]:
        """
        Transform the input data structure to organize by discipline -> equipment -> category -> items.
        
        Args:
            data (List[Dict]): Raw bidder data containing equipment categories and items
            
        Returns:
            Dict: Transformed data organized by discipline
        """
        result = {}
        
        # Iterate through each equipment
        for equipment in data:
            equipment_name = equipment['equipment_name']
            
            # Iterate through each category
            for category in equipment['categories']:
                # Iterate through each item in the category
                for item in category['items']:
                    discipline = item['discipline']
                    
                    # Initialize discipline if not exists
                    if discipline not in result:
                        result[discipline] = {}
                    
                    # Initialize equipment if not exists under this discipline
                    if equipment_name not in result[discipline]:
                        result[discipline][equipment_name] = {}
                    
                    # Initialize category if not exists under this equipment
                    if category['name'] not in result[discipline][equipment_name]:
                        result[discipline][equipment_name][category['name']] = []
                    
                    # Add the item to the category
                    result[discipline][equipment_name][category['name']].append({
                        'name': item['name'],
                        'unit': item['unit']
                    })
        
        return result

    def construct_discipline_queries(self, transformed_data: Dict[str, Any]) -> Dict[str, Dict[str, str]]:
        """
        Construct enhanced search queries for each discipline and equipment combination.
        
        Args:
            transformed_data (Dict): The transformed data from transform_by_discipline
            
        Returns:
            Dict: A dictionary mapping disciplines to equipment-specific queries
        """
        discipline_queries = {}
        
        for discipline, equipment_data in transformed_data.items():
            # Initialize discipline dictionary if not exists
            if discipline not in discipline_queries:
                discipline_queries[discipline] = {}
                
            # Get discipline-specific context
            context_terms = self.discipline_context.get(discipline, [])
            
            # Add equipment-specific information with enhanced context
            for equipment_name, categories in equipment_data.items():
                # Initialize equipment query parts
                query_parts = [
                    f"Technical specifications, standards, and requirements for {equipment_name} in {discipline} engineering",
                    f"Industry standards and best practices for {equipment_name} {discipline} systems",
                    f"Quality assurance and compliance requirements for {equipment_name}"
                ]
                
                # Add relevant technical terms and standards
                if context_terms:
                    query_parts.append(f"\nRelevant standards and technical terms: {', '.join(context_terms)}")
                
                # Add category and item details with enhanced context
                for category_name, items in categories.items():
                    query_parts.extend([
                        f"\nCategory: {category_name}",
                        f"Technical requirements and specifications for {category_name}"
                    ])
                    
                    for item in items:
                        # Create detailed item query with multiple aspects
                        item_details = [
                            f"- {item['name']}",
                            f"  * Specification requirements",
                            f"  * Quality standards",
                            f"  * Performance criteria"
                        ]
                        
                        if item.get('unit'):
                            item_details.extend([
                                f"  * Measurement unit: {item['unit']}",
                                f"  * Acceptable ranges and tolerances"
                            ])
                        
                        query_parts.extend(item_details)
                    
                    # Add relationship context
                    query_parts.append(f"Interdependencies and integration requirements for {category_name} items")
                
                # Add general quality and safety requirements
                query_parts.extend([
                    "\nQuality Assurance Requirements:",
                    "- Material specifications and certifications",
                    "- Testing and inspection requirements",
                    "- Documentation and compliance records",
                    "\nSafety and Environmental Considerations:",
                    "- Safety standards and regulations",
                    "- Environmental impact requirements",
                    "- Risk assessment and mitigation"
                ])
                
                # Store the query for this equipment under the discipline
                discipline_queries[discipline][equipment_name] = "\n".join(query_parts)
        
        return discipline_queries

    def generate_analysis_discipline(self, 
                                   source_docs: Any, 
                                   uploaded_docs: Any, 
                                   discipline: str, 
                                   equipment: str) -> str:
        """
        Generate analysis for a specific discipline and equipment using LLM.
        
        Args:
            source_docs: Source documents for analysis
            uploaded_docs: Uploaded bidder documents
            discipline (str): The discipline being analyzed
            equipment (str): The equipment being analyzed
            
        Returns:
            str: Generated analysis text
        """
        # Create prompt for LLM
        prompt = self.prompt_loader.get_prompt('cbp_report_discipline_analysis', {
            "discipline": discipline,
            "equipment": equipment,
            "source_text": source_docs,
            "text": uploaded_docs
        })
        
        # Prepare messages for LLM
        messages = [{"role": "user", "content": prompt}]
        
        try:
            # Generate response using Claude
            response = self.claude_client.generate_message_agent_sonnet_sync(
                messages=messages,
                temperature=self.temperature,
                max_tokens=4096
            )
            
            # Parse and return the response
            response_text = response.content[0].text
            print(f"Analysis generated for {discipline} - {equipment}")
            return response_text
            
        except Exception as e:
            print(f"Error generating analysis for {discipline} - {equipment}: {e}")
            return f"Error generating analysis: {str(e)}"

    def process_single_bidder(self, 
                            bidder_name: str, 
                            bidder_data: List[Dict[str, Any]], 
                            bid_id: str) -> BidderAnalysis:
        """
        Process a single bidder's data and generate complete analysis.
        
        Args:
            bidder_name (str): Name of the bidder
            bidder_data (List[Dict]): Bidder's equipment data
            bid_id (str): Bidder's ID for document retrieval
            
        Returns:
            BidderAnalysis: Complete analysis results for the bidder
        """
        print(f"Processing bidder: {bidder_name}")
        
        # Step 1: Transform data by discipline
        transformed_data = self.transform_by_discipline(bidder_data)
        print(f"Transformed data into {len(transformed_data)} disciplines")
        
        # Step 2: Construct discipline queries
        discipline_queries = self.construct_discipline_queries(transformed_data)
        print(f"Generated queries for {len(discipline_queries)} disciplines")
        
        # Step 3: Process each discipline and equipment
        evaluations = []
        
        for discipline, equipment_queries in discipline_queries.items():
            print(f"Processing discipline: {discipline}")
            
            for equipment, query in equipment_queries.items():
                print(f"  Processing equipment: {equipment}")
                
                try:
                    # Retrieve documents
                    uploaded_docs = self.faiss_embedding.search_sync(
                        query=[query], 
                        request_id=bid_id, 
                        k=20
                    )
                    
                    source_docs = self.data_manager.extract_data_v2_sync(
                        self.project_id, 
                        [query], 
                        [], 
                        20
                    )
                    
                    # Generate analysis
                    evaluation = self.generate_analysis_discipline(
                        source_docs=source_docs,
                        uploaded_docs=uploaded_docs,
                        discipline=discipline,
                        equipment=equipment
                    )
                    
                    # Store evaluation
                    evaluations.append(EquipmentEvaluation(
                        discipline=discipline,
                        equipment=equipment,
                        evaluation=evaluation
                    ))
                    
                except Exception as e:
                    print(f"Error processing {discipline} - {equipment}: {e}")
                    evaluations.append(EquipmentEvaluation(
                        discipline=discipline,
                        equipment=equipment,
                        evaluation=f"Error during analysis: {str(e)}"
                    ))
        
        return BidderAnalysis(
            bidder_name=bidder_name,
            analysis=evaluations
        )

    def process_multiple_bidders(self, 
                               bidders_data: List[Dict[str, Any]]) -> List[BidderAnalysis]:
        """
        Process multiple bidders' data.
        
        Args:
            bidders_data (List[Dict]): List of bidder data containing:
                - bidder_name: str
                - data: List[Dict] (equipment data)
                - bid_id: str
                
        Returns:
            List[BidderAnalysis]: Analysis results for all bidders
        """
        results = []
        
        for bidder_info in bidders_data:
            try:
                result = self.process_single_bidder(
                    bidder_name=bidder_info['bidder_name'],
                    bidder_data=bidder_info['data'],
                    bid_id=bidder_info['bid_id']
                )
                results.append(result)
                
            except Exception as e:
                print(f"Error processing bidder {bidder_info.get('bidder_name', 'Unknown')}: {e}")
                # Add error result
                results.append(BidderAnalysis(
                    bidder_name=bidder_info.get('bidder_name', 'Unknown'),
                    analysis=[EquipmentEvaluation(
                        discipline="Error",
                        equipment="Error",
                        evaluation=f"Failed to process bidder: {str(e)}"
                    )]
                ))
        
        return results

    def export_results_to_dict(self, results: List[BidderAnalysis]) -> List[Dict[str, Any]]:
        """
        Export analysis results to dictionary format.
        
        Args:
            results (List[BidderAnalysis]): Analysis results
            
        Returns:
            List[Dict]: Results in dictionary format
        """
        return [
            {
                "bidder_name": result.bidder_name,
                "analysis": [
                    {
                        "discipline": eval.discipline,
                        "equipment": eval.equipment,
                        "evaluation": eval.evaluation
                    }
                    for eval in result.analysis
                ]
            }
            for result in results
        ]

    def export_results_to_json(self, 
                             results: List[BidderAnalysis], 
                             filename: Optional[str] = None) -> str:
        """
        Export analysis results to JSON format.
        
        Args:
            results (List[BidderAnalysis]): Analysis results
            filename (Optional[str]): Optional filename to save JSON
            
        Returns:
            str: JSON string of results
        """
        dict_results = self.export_results_to_dict(results)
        json_str = json.dumps(dict_results, indent=2, ensure_ascii=False)
        
        if filename:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(json_str)
            print(f"Results exported to {filename}")
        
        return json_str


# Example usage:
if __name__ == "__main__":
    # Example data structure for testing
    from test import data
    data =  [   {
            "categories": [

                {
                    "items": [
                        {
                            "discipline": "Piping",
                            "name": "Underflow discharge pipe diameter ",
                            "unit": "mm"
                        },
                        {
                            "discipline": "Piping",
                            "name": "Knife Gate Valve - Diameter ",
                            "unit": "mm"
                        },
                        {
                            "discipline": "Piping",
                            "name": "Flange Specifications :",
                            "unit": ""
                        },
                        {
                            "discipline": "Piping",
                            "name": "Type of piping connections",
                            "unit": ""
                        },
                        {
                            "discipline": "Piping",
                            "name": "Pressure Gauge ",
                            "unit": ""
                        },
                        {
                            "discipline": "Piping",
                            "name": "Overflow discharge pipe diameter ",
                            "unit": "mm"
                        },
                        {
                            "discipline": "Piping",
                            "name": "Feed distributor pipe diameter",
                            "unit": "mm"
                        },
                        {
                            "discipline": "Piping",
                            "name": "Knife Gate Valve - Model",
                            "unit": ""
                        }
                    ],
                    "name": "Piping"
                },
                {
                    "items": [
                        {
                            "discipline": "General",
                            "name": "Quotation #",
                            "unit": ""
                        },
                        {
                            "discipline": "General",
                            "name": "Vendor Name",
                            "unit": ""
                        },
                        {
                            "discipline": "General",
                            "name": "Model",
                            "unit": ""
                        },
                        {
                            "discipline": "General",
                            "name": "Service",
                            "unit": ""
                        },
                        {
                            "discipline": "General",
                            "name": "% of Components to be Assembled on Site",
                            "unit": ""
                        },
                        {
                            "discipline": "General",
                            "name": "Number of cyclones",
                            "unit": ""
                        },
                        {
                            "discipline": "General",
                            "name": "Number of unit(s)",
                            "unit": ""
                        },
                        {
                            "discipline": "General",
                            "name": "Equipment type",
                            "unit": ""
                        },
                        {
                            "discipline": "General",
                            "name": "Warranty (commissioning/shipping)",
                            "unit": ""
                        }
                    ],
                    "name": "Description of Equipment"
                }
            ],
            "equipment_name": "Cyclone Cluster ",
            "id": "da996048-ec98-42cd-b402-3f698e705960"
        },

        
   ]
    sample_bidder_data = [
        {
            "bidder_name": "SAMUEL",
            "bid_id": "6aa67866-913e-42a8-97cc-b0b7d662241e",
            "data": data
        }
    ]
    

    # Initialize pipeline
    pipeline = DisciplineAnalysisPipeline(
        project_id="c8af6072-0e65-4065-babb-35142b959320"
    )
    
    
    # Process single bidder
    # result = pipeline.process_single_bidder(
    #     bidder_name="SAMUEL",
    #     bidder_data=data,  # Your data variable
    #     bid_id="6aa67866-913e-42a8-97cc-b0b7d662241e"
    # )
    
    # Process multiple bidders
    results = pipeline.process_multiple_bidders(sample_bidder_data)
    
    # Export results
    json_output = pipeline.export_results_to_json(results, "bidder_analysis_results.json")