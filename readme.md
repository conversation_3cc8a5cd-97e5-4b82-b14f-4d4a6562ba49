# Project Setup Guide

This document provides a step-by-step guide for setting up the project environment and running the application. Follow each step carefully to ensure proper configuration.

---

## 1. Create and Activate a Python Virtual Environment
To isolate dependencies and manage the project efficiently, create and activate a Python virtual environment:

1. **Create the virtual environment**:
   ```bash
   python -m venv venv
   ```
2. **Activate the virtual environment**:
   - **On Windows**:
     ```bash
     .\venv\Scripts\activate
     ```
   - **On macOS/Linux**:
     ```bash
     source venv/bin/activate
     ```

---

## 2. Install Required Dependencies
Install all necessary packages and libraries specified in the `requirements.txt` file:

```bash
pip install -r requirements.txt
```

---

## 3. Configure Environment Variables
Set up environment variables to ensure smooth operation:
1. Copy the contents of `env.example.json`.
2. Paste and configure them into a new file named `env.json`.

---

## 4. Create Required Directories
Ensure the following directories are created in the project root to store application-specific data:

- `chronobid`
- `specs`
- `data`

---

## 5. Run Database Migrations
Apply database migrations to configure the database schema:

```bash
flask db upgrade
```

---

## 6. Configure Crontab for Scheduled Tasks
To automate recurring tasks:
1. Copy the content from `scripts/template.cron`.
2. Add it to the server's crontab configuration file.

---

## 7. Provide Document Management and Numbering Details
To ensure compatibility with your engineering processes:
1. Share your **Engineering Document Numbering System** to integrate with the application.
2. Provide your **Report Templates**, including:
   - **Introductory Template**
   - **Body Template**
   - **Conclusion/Recommendation Template**

---

## 8. Document Storage Information
Clarify how your organization manages documents:
- Specify if they are stored on **Google Drive**, **AWS S3 Buckets**, or any other private document management system.
- Provide necessary access details for integration.

---

## 9. Start the Application
Run the application with **Gunicorn** for efficient server performance:

```bash
gunicorn --bind 0.0.0.0:5120 app:app \
    --worker-class eventlet \
    --timeout 120 \
    --workers 4 \
    --access-logfile /home/<USER>/dev-ds-oil-and-gas/logs/server.log \
    --error-logfile /home/<USER>/dev-ds-oil-and-gas/logs/server.log \
    --capture-output \
    --log-level debug \
    --max-requests 200 \
    --max-requests-jitter 30 \
    --daemon
```

After starting the application, ensure the cron service is active:

```bash
service cron start
```

---

## 10. Start the Celery Worker
To start a Celery worker, use the following command:

```bash
celery -A init.celery worker --loglevel=info
```

### Deploying Celery as a Daemon
To run the Celery worker as a daemon, follow these steps:

1. **Edit the Celery Service File**: Open the Celery service file for editing:

   ```bash
   sudo nano /etc/systemd/system/celery.service
   ```

2. **Ensure the User and Group are Set to `azureuser`**: In the `[Service]` section of the file, make sure the User and Group are both set to `azureuser` as shown below:

   ```ini
   [Unit]
   Description=Celery Worker
   After=network.target

   [Service]
   User=azureuser
   Group=azureuser
   WorkingDirectory=/home/<USER>/dev-ds-oil-and-gas
   ExecStart=/home/<USER>/dev-ds-oil-and-gas/venv/bin/celery -A init.celery worker --loglevel=info
   Restart=always

   [Install]
   WantedBy=multi-user.target
   ```

3. **Reload systemd Configuration**: After editing the file, reload the systemd configuration to apply the changes:

   ```bash
   sudo systemctl daemon-reload
   ```

4. **Restart Celery**: Restart the Celery service to run it under the `azureuser`:

   ```bash
   sudo systemctl restart celery
   ```

---

## 11. DRA Server Re-deployment Guide

### Connect to DRA VPN
Ensure you're connected to the DRA VPN before proceeding.

### Deploy Main App
1. **SSH to Main Server**:
   ```bash
   ssh ryan_wong_aienergy@**********
   ```
2. **Navigate to Project Directory**:
   ```bash
   cd dev-ds-oil-and-gas
   ```
3. **Pull Latest Changes**:
   ```bash
   git pull origin dev-deploy
   ```
4. **Restart Flask App**:
   ```bash
   ./scripts/restart_server_dev.sh
   ```
5. **Check App Logs**:
   ```bash
   tail -f logs/server.log
   ```
6. **Restart File Worker**:
   ```bash
   sudo systemctl restart dramatiq-file
   ```
7. **Check Worker Logs**:
   ```bash
   journalctl -u dramatiq-file -f
   ```

### Re-deploy Task Workers
1. **SSH to Worker Server**:
   ```bash
   ssh ryan_wong_aienergy@**********
   ```
2. **Navigate to Project Directory**:
   ```bash
   cd dev-ds-oil-and-gas
   ```
3. **Pull Latest Changes**:
   ```bash
   git pull origin dev-deploy
   ```
4. **Restart Dramatiq Service**:
   ```bash
   sudo systemctl restart dramatiq
   ```
5. **Check Worker Logs**:
   ```bash
   journalctl -u dramatiq -f
   ```

---

## 12. DRA Server Troubleshooting Guide

### Service Status Checks
1. **RabbitMQ** (runs on main server):
   - Ports: 5672, 15672
   - Check status:
     ```bash
     sudo systemctl status rabbitmq-server
     ```

2. **Redis**:
   - Port: 6379
   - Check status:
     ```bash
     sudo systemctl status redis-server
     ```

3. **MySQL Server** (runs on worker server):
   - Check status:
     ```bash
     sudo systemctl status mysql
     ```

### Common Issues and Solutions
1. **Too Many Database Connections**:
   - Check the processlist:
     ```bash
     mysql -u root -p -e "SHOW PROCESSLIST;"
     ```
   - Solution: Reduce wait timeout in MySQL config:
     ```bash
     sudo nano /etc/mysql/my.cnf
     ```
     Add/Modify:
     ```ini
     [mysqld]
     wait_timeout = 60
     interactive_timeout = 60
     ```

2. **Can't Access DRA Domain**:
   - Update hosts file:
     ```bash
     sudo nano /etc/hosts
     ```
   - Add these entries:
     ```bash
     ********** ainrg.draglobal.com
     ********** ainrgds.draglobal.com
     ```

---

## Notes
- Ensure all configurations are thoroughly tested in a development environment before deploying to production.


For further assistance, contact the project team or refer to the documentation.

