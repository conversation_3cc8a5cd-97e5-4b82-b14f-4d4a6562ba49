"""Add exist_bid field to requirement table

Revision ID: 0e7efda7c2d3
Revises: dc12cc86881e
Create Date: 2025-02-28 21:51:39.038343

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '0e7efda7c2d3'
down_revision = 'dc12cc86881e'
branch_labels = None
depends_on = None


def upgrade():
    with op.batch_alter_table('requirement', schema=None) as batch_op:
        batch_op.add_column(sa.Column('exist_bid', sa.Integer(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('requirement', schema=None) as batch_op:
        batch_op.drop_column('exist_bid')
    # ### end Alembic commands ###
