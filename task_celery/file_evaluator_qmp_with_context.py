from extensions import celery
from socket_instance import socket_instance
from services.handle_new_qmp_query import QMPQueryHandler
from retriever import retrieve_docs
from llm import call_llm
from models import Requirement, Answer

@celery.task(bind=True, max_retries=3, name="task_celery.file_evaluator_qmp_with_context.evaluate_qmp_with_context_task")
def evaluate_qmp_with_context_task(
    self, req_id, parent_request_id, highlighted_text, 
    other_project_ids, exist_bid=False, bid_id=None, break_cache=False
):
    try:
        socket_manager = socket_instance.get_instance()

        # Retrieve the parent requirement (original question)
        parent_req = Requirement.get(id=parent_request_id)
        base_question = parent_req.criteria  # The original question

        # Step 1: Retrieve documents based on other project IDs, existing bid, etc.
        docs = retrieve_docs(
            project_id=parent_req.project_id,
            other_project_ids=other_project_ids,
            exist_bid=exist_bid,
            bid_id=bid_id,
            break_cache=break_cache
        )

        # Step 2: Construct the new prompt with the original question and the highlighted text
        prompt = build_prompt(
            question=base_question,
            docs=docs,
            extra_context=highlighted_text
        )
        
        # Step 3: Call the LLM with the new prompt
        refined_answer = call_llm(prompt)

        # Step 4: Save the refined answer (you can save it under the new req_id for tracking purposes)
        Answer.create(
            request_id=req_id,
            parent_request_id=parent_request_id,
            text=refined_answer
        )

        print('QMP with context evaluation completed successfully.')
        return "QMP with context evaluation completed successfully."
    except Exception as e:
        print(f"Error during QMP with context evaluation: {e}")
        return str(e)

# Helper function to build the prompt for the LLM
def build_prompt(question, docs, extra_context):
    """
    Builds the final prompt for the LLM by combining the base question,
    documents (context), and additional highlighted text.
    
    Args:
        question (str): The original question from the user or system.
        docs (list): A list of documents or context retrieved based on the query.
        extra_context (str): The highlighted text to be used as additional context.
        
    Returns:
        str: The final prompt to be passed to the LLM.
    """
    # Combine the original question with documents and highlighted context
    prompt = f"Question: {question}\n"

    # Add the documents as context
    prompt += "Context:\n"
    for doc in docs:
        prompt += f"- {doc}\n"
    
    # Add the highlighted context (if provided)
    if extra_context:
        prompt += f"\nHighlighted Context: {extra_context}\n"
    
    prompt += "\nAnswer the question based on the above context."
    
    return prompt
