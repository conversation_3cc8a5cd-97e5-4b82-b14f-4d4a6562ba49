<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload and Listen</title>
    <script src="https://cdn.socket.io/4.0.0/socket.io.min.js"></script>
</head>
<body>
    <h1>Upload File and Listen to Server</h1>
    <form id="upload-form">
        <!-- <input type="text" id="project_id" placeholder="Enter Project ID" required>
        <input type="text" id="question" placeholder="Enter Question" required>
        <input type="text" id="other_project_ids" placeholder="Other Project IDs (comma-separated)"> -->
        <button type="submit">Submit</button>
        <button id="reconnect-btn" type="button">Reconnect</button>
    </form>

    <h2>Socket Messages</h2>
    <ul id="socket-messages"></ul>

    <script>
        const API_URL = "https://5drzz86ndf11nv-5120.proxy.runpod.net/test_celery"; // Update to match backend URL
        const SOCKET_NAMESPACE = "https://5drzz86ndf11nv-5120.proxy.runpod.net/processing_files"; // Update if needed

        const form = document.getElementById("upload-form");
        const messagesList = document.getElementById("socket-messages");
        let socket;
        let isReconnecting = false;
        let requestId;

        const socketOptions = {
            transports: ['websocket', 'polling'],
            reconnection: true,
            reconnectionAttempts: 5,
            reconnectionDelay: 1000
        };

        document.getElementById("reconnect-btn").addEventListener("click", () => {
            triggerReconnection(requestId);
        });

        form.addEventListener("submit", async (e) => {
            e.preventDefault();

            // const project_id = document.getElementById("project_id").value.trim();
            // const question = document.getElementById("question").value.trim();
            // const other_project_ids = document.getElementById("other_project_ids").value.split(',').map(id => id.trim());

            // if (!project_id || !question) {
            //     alert("Project ID and Question are required!");
            //     return;
            // }

            try {
                requestId = 'e2b82b05-d7cc-42c7-8b73-a4c087205062'

                // const payload = {
                //     project_id,
                //     question,
                //     other_project_ids
                // };

                // const response = await fetch(API_URL, {
                //     method: "POST",
                //     headers: { "Content-Type": "application/json" },
                //     body: JSON.stringify(payload),
                // });
                // const response = await fetch(API_URL + `?request_id=${requestId}`, {
                //     method: "GET", // Change to GET request
                //     headers: { "Content-Type": "application/json" },
                // });

                // // Handle the response
                // if (response.ok) {
                //     const data = await response.json();
                //     console.log("Response data:", data);
                // } else {
                //     console.error("Error fetching data:", response.statusText);
                //     return
                // }

                // if (!response.ok) {
                //     throw new Error(`API error: ${response.status}`);
                // }

                // const data = await response.json();
                // requestId = data.request_id; // Store actual request ID
                // console.log("Received request_id:", requestId);
                connectToSocket(requestId);
            } catch (error) {
                console.error("Error during API request:", error);
                alert("Failed to send the request. Check the console for details.");
            }
        });

        function connectToSocket(requestId) {
            if (socket) socket.close();

            socket = io(SOCKET_NAMESPACE, socketOptions);

            socket.on("connect", () => {
                console.log("Connected to socket server");

                socket.emit("join", { request_id: requestId, is_reconnect: isReconnecting });
                console.log("Join event emitted:", { request_id: requestId, is_reconnect: isReconnecting });
            });

            socket.on("progress_message", (data) => {
                console.log("Received progress message:", data);
                const li = document.createElement("li");
                li.textContent = JSON.stringify(data);
                messagesList.appendChild(li);
            });

            socket.on("disconnect", () => {
                console.log("Disconnected from socket server");
                isReconnecting = true;
            });

            socket.on("connect_error", (error) => {
                console.error("Socket connection error:", error);
                isReconnecting = true;
            });

            socket.on("leave", (data) => {
                console.log("User left the room:", data);
                
                // Optional: Display a message in the UI
                const li = document.createElement("li");
                li.textContent = `User ${data.user_id} has left the room`;
                messagesList.appendChild(li);
                
                // Handle any necessary cleanup
                isReconnecting = false;
            });

        }

        function triggerReconnection(requestId) {
            if (!requestId) {
                console.error("No request ID available for reconnection");
                return;
            }
            isReconnecting = true;
            console.log("Reconnecting to socket...");
            connectToSocket(requestId);
        }
    </script>
</body>
</html>
