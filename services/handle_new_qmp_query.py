import gevent
from gevent import monkey
monkey.patch_all()

import os,sys, json, re, time
import asyncio
from models import Project, Requirement, BidFile
from services.data_handler import DataManager
from services.claude_ai_service import ClaudeService
from services.report_manager import ReportManager
from services.update_backend_status import BackendStatusUpdater
from services.generate_question_field import QuestionGenerator
from services.handle_new_qmp_source_expansion import QMPSourceExtractor
from services.synonym_expansion import SynonymGenerator
from services.groq_ai_service import GroqService
from services.fast_apis_service import FastAPIs
from services.faiss_embedding import FaissEmbedding
from flask_socketio import emit
# from init import app
from pydantic import BaseModel, Field, field_validator
from typing import List, Union
import json
import statistics


class QMPQueryHandler:
    def __init__(self, socket_manager, is_test, language="English"):
         # Get the path to the parent directory
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

        env_file_path = os.path.join(parent_dir, 'env.json')
        with open(env_file_path, 'r') as f:
            self.env_data = json.load(f)

        self.data_manager = DataManager()
        self.claude_client = ClaudeService()
        self.groq_client = GroqService()
        self.fast_apis = FastAPIs()
        self.backend_status_updater = BackendStatusUpdater()
        self.question_generator = QuestionGenerator()
        self.qmp_source_extractor = QMPSourceExtractor()
        self.synonym_service = SynonymGenerator()
        self.is_test = is_test
        self.socket_manager = socket_manager
        self.should_query_existing_bid = False
        self.bid_id = None
        self.faiss_processor = FaissEmbedding()
        self.break_cache = False
        self.quoted_text = ""
        self.follow_up_query = ""
        self.is_follow_up_query = False
        self.language = language

    def set_should_query_existing_bid(self, should_query_existing_bid):
        self.should_query_existing_bid = should_query_existing_bid

    def set_bid_id(self, bid_id):
        self.bid_id = bid_id

    def can_query_existing_bid(self):
        return self.should_query_existing_bid and self.bid_id

    def set_break_cache(self, break_cache):
        self.break_cache = break_cache

    def should_break_cache(self):
        return self.break_cache

    def add_event(self, request_id, event_name, data):
        # return
        self.socket_manager.emit_to_client(request_id, event_name, data, '/qmp')

    def process_query(self, req_id, other_project_ids):
        asyncio.run(self.answer_question_v1_1(req_id, other_project_ids))

    def process_query_sync(self, req_id, other_project_ids):
        from init import app
        print('checkpoint QMP QMPQueryHandler in sync')

        def _run_async():
            try:
                # Ensure each greenlet has an application context
                with app.app_context():
                    return self.answer_question_v1_1_sync(req_id, other_project_ids)
            except Exception as e:
                print(f"Error in process_query greenlet: {e}")
                raise

        # Spawn and return the greenlet
        return gevent.spawn(_run_async)

    async def format_question_text(self, question_text):
        prompt = f"""
            You are an **EPC (Engineering, Procurement, and Construction) Project Engineer** assistant working for **Worley** or **Fluor**.
            Your task is to extract all **queries (questions)** from the provided RFIs text while maintaining their exact wording, meaning, and necessary context.
            Return the extracted questions in the specified format.

            <INSTRUCTIONS>
                - Identify and extract **all** questions from the given RFIs text.
                - Do **not** paraphrase or reinterpret the extracted questions—preserve their exact meaning.
                - When handling compound questions with dependencies, ensure each extracted question retains all necessary context.
                - For dependent questions, include relevant information from previous context to make each question independently understandable.
                - Ensure strict adherence to the **output format** provided.
                - Sometimes it can be a simple RFI with no questions, in which case present the entire text as a single question.
            </INSTRUCTIONS>

            <EXAMPLE>
                "Pipes within 50mm under spec of A2 is connected with cast iron pipe fitting, why there is quantity of cut in and branch weld? What's the detailed wall thickness of 65—150mm pipes and fittings? and what's the fitting pressure? Wall thickness of all 200~700mm pipes and fittings is 4.5mm, why large diameter pipes and fittings is with so small wall thickness?"
            <EXAMPLE>
            <OUTPUT FORMAT>
                <Question>For pipes within 50mm under spec of A2 connected with cast iron pipe fitting, why is there quantity of cut in and branch weld?</Question>
                <Question>What's the detailed wall thickness of 65-150mm pipes and fittings?</Question>
                <Question>What's the fitting pressure for the 65-150mm pipes and fittings?</Question>
                <Question>Wall thickness of all 200~700mm pipes and fittings is 4.5mm, why are large diameter pipes and fittings with so small wall thickness?</Question>
            </OUTPUT FORMAT>

            <EXAMPLE>
                "Can you confirm the final dimensions of the pressure vessel? What are the maximum allowable tolerances for the welding joints?
                Has the structural integrity analysis been completed for the load-bearing beams?"
            <EXAMPLE>
            <OUTPUT FORMAT>
                <Question>Can you confirm the final dimensions of the pressure vessel?</Question>
                <Question>What are the maximum allowable tolerances for the welding joints?</Question>
                <Question>Has the structural integrity analysis been completed for the load-bearing beams?</Question>
            </OUTPUT FORMAT>

            <SPECIAL RULES>
                1. If the input text does not contain any questions or RFIs (e.g., it's just a greeting like "Hello"), simply return the exact text in this format:
                   <OUTPUT FORMAT>
                   <Question>[exact input text]</Question>
                   </OUTPUT FORMAT>
                   Do NOT provide any explanations or commentary - just return the text in the format above.

                2. CRITICAL: If dealing with compound or dependent questions, ensure each extracted question contains sufficient context to be understood independently. Add relevant information from previous sentences/questions when necessary to maintain meaning. Never split a question if doing so would lose important context.

                3. For questions connected by "and" or similar conjunctions, determine if they're truly separate questions or if they form a single meaningful query. If they form a single complex query, keep them together. If they're separate topics, split them while preserving context.

                <EXAMPLE>
                "We have two types of separated dividers, one is to use divided pages to separate the sections of the tender which is integrated in one file, and the other one is to use the folder to separate the sections of the Tender which is integrated in one file, and the other one is to use the folder to separate the sections of the Tender (one folder is a section), please confirm which one meet your requirement"
                <EXAMPLE>

                <OUTPUT FORMAT>
                <Question>"We have two types of separated dividers, one is to use divided pages to separate the sections of the tender which is integrated in one file, and the other one is to use the folder to separate the sections of the Tender which is integrated in one file, and the other one is to use the folder to separate the sections of the Tender (one folder is a section), please confirm which one meet your requirement"<Question>
                <OUTPUT FORMAT>


                4. If a statement provides information that is required for understanding a subsequent question, incorporate that information into the question.

                5. The response should show all the reference information relevant to the query. The sources should also show that information.

                6. When information is present in multiple files, ensure you reference all the files where the information is.

                7. If the query contains keywords that are not found in any of the documents, interpret the query in the context of the uploaded documents and provide reasoning based on that.

                8. Use as many relevant resources as possible and reference all. Don't just stick to one or a few when there are more containing the information.
            </SPECIAL RULES>

            Process the following RFIs text below:
            <RFIs>
                "{question_text}"
            </RFIs>
        """
        # completion = self.fast_apis.generate_completion(
        #     [{"role": "user", "content": prompt}],
        #     'groq',
        #     [{"role": "user", "content": prompt}],
        #     'groq',
        #     'llama3-70b-8192'
        # )

        completion = await self.claude_client.generate_message_agent_sonnet_new(
            [{"role": "user", "content": prompt}]
        )
        # print('formatted question: ', completion)
        print('format question usage: ', completion.usage)

        if completion:
            # Regex pattern to extract content inside <Source_answer> tags, including multiline content
            pattern = r'<Question>(.*?)<\/Question>'
            # matches = re.findall(pattern, completion, re.DOTALL)
            matches = re.findall(pattern, completion.content[0].text, re.DOTALL)
            if matches:
                return [match.strip() for match in matches]

            # If no matches found, try one more time
            print('No matches found in first attempt, retrying...')
            # completion = self.fast_apis.generate_completion(
            #     [{"role": "user", "content": prompt}],
            #     'groq',
            #     'llama3-70b-8192'
            # )
            completion = await self.claude_client.generate_message_agent_sonnet_new(
                [{"role": "user", "content": prompt}]
            )
            if completion:
                print('format question usage: ', completion.usage)
                # matches = re.findall(pattern, completion, re.DOTALL)
                matches = re.findall(pattern, completion.content[0].text, re.DOTALL)
                if matches:
                    return [match.strip() for match in matches]

        return None

    def format_question_text_sync(self, question_text):
        prompt = f"""
             You are an **EPC (Engineering, Procurement, and Construction) Project Engineer** assistant working for **Worley** or **Fluor**.
            Your task is to extract all **queries (questions)** from the provided RFIs text while maintaining their exact wording, meaning, and necessary context.
            Return the extracted questions in the specified format.

            <INSTRUCTIONS>
                - Identify and extract **all** questions from the given RFIs text.
                - Do **not** paraphrase or reinterpret the extracted questions—preserve their exact meaning.
                - When handling compound questions with dependencies, ensure each extracted question retains all necessary context.
                - For dependent questions, include relevant information from previous context to make each question independently understandable.
                - Ensure strict adherence to the **output format** provided.
                - Sometimes it can be a simple RFI with no questions, in which case present the entire text as a single question.
            </INSTRUCTIONS>

            <EXAMPLE>
                "Pipes within 50mm under spec of A2 is connected with cast iron pipe fitting, why there is quantity of cut in and branch weld? What's the detailed wall thickness of 65—150mm pipes and fittings? and what's the fitting pressure? Wall thickness of all 200~700mm pipes and fittings is 4.5mm, why large diameter pipes and fittings is with so small wall thickness?"
            <EXAMPLE>
            <OUTPUT FORMAT>
                <Question>For pipes within 50mm under spec of A2 connected with cast iron pipe fitting, why is there quantity of cut in and branch weld?</Question>
                <Question>What's the detailed wall thickness of 65-150mm pipes and fittings?</Question>
                <Question>What's the fitting pressure for the 65-150mm pipes and fittings?</Question>
                <Question>Wall thickness of all 200~700mm pipes and fittings is 4.5mm, why are large diameter pipes and fittings with so small wall thickness?</Question>
            </OUTPUT FORMAT>

            <EXAMPLE>
                "Can you confirm the final dimensions of the pressure vessel? What are the maximum allowable tolerances for the welding joints?
                Has the structural integrity analysis been completed for the load-bearing beams?"
            <EXAMPLE>
            <OUTPUT FORMAT>
                <Question>Can you confirm the final dimensions of the pressure vessel?</Question>
                <Question>What are the maximum allowable tolerances for the welding joints?</Question>
                <Question>Has the structural integrity analysis been completed for the load-bearing beams?</Question>
            </OUTPUT FORMAT>

            <SPECIAL RULES>
                1. If the input text does not contain any questions or RFIs (e.g., it's just a greeting like "Hello"), simply return the exact text in this format:
                   <OUTPUT FORMAT>
                   <Question>[exact input text]</Question>
                   </OUTPUT FORMAT>
                   Do NOT provide any explanations or commentary - just return the text in the format above.

                2. CRITICAL: If dealing with compound or dependent questions, ensure each extracted question contains sufficient context to be understood independently. Add relevant information from previous sentences/questions when necessary to maintain meaning. Never split a question if doing so would lose important context.

                3. For questions connected by "and" or similar conjunctions, determine if they're truly separate questions or if they form a single meaningful query. If they form a single complex query, keep them together. If they're separate topics, split them while preserving context.

                <EXAMPLE>
                "We have two types of separated dividers, one is to use divided pages to separate the sections of the tender which is integrated in one file, and the other one is to use the folder to separate the sections of the Tender which is integrated in one file, and the other one is to use the folder to separate the sections of the Tender (one folder is a section), please confirm which one meet your requirement"
                <EXAMPLE>

                <OUTPUT FORMAT>
                <Question>"We have two types of separated dividers, one is to use divided pages to separate the sections of the tender which is integrated in one file, and the other one is to use the folder to separate the sections of the Tender which is integrated in one file, and the other one is to use the folder to separate the sections of the Tender (one folder is a section), please confirm which one meet your requirement"<Question>
                <OUTPUT FORMAT>


                4. If a statement provides information that is required for understanding a subsequent question, incorporate that information into the question.

                5. The response should show all the reference information relevant to the query. The sources should also show that information.

                6. When information is present in multiple files, ensure you reference all the files where the information is.

                7. If the query contains keywords that are not found in any of the documents, interpret the query in the context of the uploaded documents and provide reasoning based on that.

                8. Use as many relevant resources as possible and reference all. Don't just stick to one or a few when there are more containing the information.
            </SPECIAL RULES>

            Process the following RFIs text below:
            <RFIs>
                "{question_text}"
            </RFIs>
        """
        # completion = self.fast_apis.generate_completion(
        #     [{"role": "user", "content": prompt}],
        #     'groq',
        #     [{"role": "user", "content": prompt}],
        #     'groq',
        #     'llama3-70b-8192'
        # )

        completion = self.claude_client.generate_message_agent_sonnet_sync(
            [{"role": "user", "content": prompt}]
        )
        # print('formatted question: ', completion)
        print('format question usage: ', completion.usage)
        # dramatiq_logger.info('format question usage:  %s', completion.usage)

        if completion:
            # Regex pattern to extract content inside <Source_answer> tags, including multiline content
            pattern = r'<Question>(.*?)<\/Question>'
            # matches = re.findall(pattern, completion, re.DOTALL)
            matches = re.findall(pattern, completion.content[0].text, re.DOTALL)
            if matches:
                return [match.strip() for match in matches]

            # If no matches found, try one more time
            print('No matches found in first attempt, retrying...')

            completion = self.claude_client.generate_message_agent_sonnet_sync(
                [{"role": "user", "content": prompt}]
            )
            if completion:
                print('format question usage: ', completion.usage)
                # matches = re.findall(pattern, completion, re.DOTALL)
                matches = re.findall(pattern, completion.content[0].text, re.DOTALL)
                if matches:
                    return [match.strip() for match in matches]

        return None



    async def process_question(self, idx, sub_question, project_id, project_data, requirement_id, max_retries=3, original_question="", quoted_text="", follow_up_query=""):
        """Process a single question."""
        start_query = time.time()
        attempts = 0
        highlight_class = 'highlight'

        # print('i am subquestion: ', sub_question)

        # Generate similar questions to improve search
        synonyms = await self.synonym_service.generate_synonym(f"{sub_question}\n Description: {sub_question}")

        # Combine all matches into a single string separated by '; '
        similarQuestion = [f"{name.strip()} - {description.strip()}"
                for name, description in re.findall(r"Name:\s*(.+?)\n\s*Description:\s*(.+?)(?:\n|</SYNONYMS>)", synonyms, re.DOTALL)]
        # print("Synonyms Questions generated successfully...: ", similarQuestion)


        while attempts < max_retries:
            try:

                # print('i am here...')
                self.add_event(requirement_id, 'progress_message', {
                    'event_type': 'QMP',
                    'request_id': requirement_id,
                    'data': {'status': 'extracting_sources', 'message': f"Processing request. Crafting response now. Please wait.", 'query_idx': idx}
                })

                # Extract sources
                # print('now trying to extract data using v2...')
                # print(project_id, [sub_question], similarQuestion)
                # return
                if self.can_query_existing_bid():
                    # print('now trying to query existing bid...')
                    search_queries = [sub_question] + similarQuestion
                    sources = await self.faiss_processor.search(search_queries, self.bid_id, 20)
                    # format the sources
                    final_results = [{
                        "source_map": {},
                        "source_list": [],
                        "questions": [sub_question]
                    }]
                    sources_list = []
                    source_map = {}
                    for i, source in enumerate(sources[0]):
                        source_map[i] = source[0]
                        sources_list.append({
                            "id": i,
                            "content": source[0],
                            "score": source[1],
                            "section_id": i,
                            "source": "",
                            "title": "",
                            "page_number": None
                        })
                    final_results[0]['source_map'] = source_map
                    final_results[0]['source_list'] = sources_list
                    sources = final_results
                    # print('this is sources: ', sources)
                else:
                    sources = await self.data_manager.extract_data_v2(project_id, [sub_question], similarQuestion, 65)
                    # print('this is sources: ', sources)

                self.add_event(requirement_id, 'progress_message', {
                    'event_type': 'QMP',
                    'request_id': requirement_id,
                    'data': {'status': 'generating_answer', 'message': f"Processing inquiry {idx}. Thinking. Please wait.", 'query_idx': idx}
                })

                # output_directory = 'output_json_files'  # Specify your desired output directory
                # os.makedirs(output_directory, exist_ok=True)  # Create the directory if it doesn't exist
                # # Dump sources into individual JSON files
                # output_file_path = os.path.join(output_directory, f"sources_query_{idx}.json")
                # with open(output_file_path, 'w') as json_file:
                #     json.dump(sources, json_file, indent=4)  # Write the sources to the JSON file

                answer = await self.get_answer_claude(sources[0]['source_list'], sub_question, original_question=original_question, quoted_text=quoted_text, follow_up_query=follow_up_query)
                # feedback_text = await self.extract_feedback_content(answer)

                # print('i am feedback answer: ', answer)

                # if feedback_text is not None:
                #     # highlight_class = 'highlight-red'
                #     highlight_class = 'highlight'
                #     print('now checking with expansion agent...')
                #     await self.qmp_source_extractor.extract_sources(self.related_project_ids, sub_question)
                #     sources2 = self.qmp_source_extractor.get_response()
                #     print('top expand sources generated...')
                #     if sources2[0]['source_list']:
                #         [answer2, prompt] = await self.get_answer_claude_v2(sources2[0]['source_list'], sub_question, answer)
                #     else:
                #         answer2 = None
                # else:
                #     answer2 = None

                final_answer = answer
                final_source = sources

                # if answer2:
                #     print(f"revised answer in query {idx} : ", answer2)

                #     # Extract the flag from answer2
                #     comparison_flag = await self.extract_comparison_flag(answer2)
                #     print('Comparison flag:', comparison_flag)

                #     # Decide which answer to use
                #     if comparison_flag == "USE_EXISTING_ANSWER":
                #         final_answer = answer
                #         final_source = sources
                #         print("Using existing answer.")
                #     elif comparison_flag == "USE_NEW_ANSWER":
                #         final_answer = answer2
                #         final_source = sources2
                #         print("Using new answer.")
                #     elif comparison_flag == "MERGE_BOTH_ANSWERS":
                #         final_answer = await self.merge_answers(
                #             await self.extract_answer_content(answer),
                #             await self.extract_answer_content(answer2)
                #         )
                #         final_source = self.merge_sources(sources, sources2)
                #         print("Merging both answers.")
                #     else:
                #         final_answer = answer
                #         final_source = sources
                #         print("Defaulting to existing answer due to flag extraction failure.")
                # else:
                #     print(f"main answer in query {idx} : ", answer)
                #     final_answer = answer
                #     final_source = sources
                #     print("Expansion failed or feedback was None. Using existing answer.")


                self.add_event(requirement_id, 'progress_message', {
                    'event_type': 'QMP',
                    'request_id': requirement_id,
                    'data': {'status': 'formatting_sources', 'message': f"Refining the details for inquiry {idx}. Double checking our answers. Please wait.", 'query_idx': idx}
                })


                # print('i amfeedback text: ', feedback_text)

                source_list = []
                index = 0
                # section_ids = await self.extract_all_answer_sectionIds(final_answer)
                section_ids = final_answer['SectionIds']
                # print('section ids: ', section_ids)
                # section_ids_content = await self.extract_source_answer_content(final_answer)
                section_ids_headers = final_answer['SourceAnswerHeaders']
                section_ids_content = final_answer['SourceAnswerContents']
                # print('section ids content: ', section_ids_content)
                # answer = await self.extract_answer_content(final_answer)
                answer = final_answer['Answer']

                # print('section ids below in sources sufficient: ', section_ids)

                """

                for section_id in section_ids:
                    # section_id = section_id.strip()
                    if section_id:
                        # print('section id is this: ', section_id)
                        # format_text = await self.groq_ai_clean_and_format_sources(section_ids_content[index])
                        format_text = section_ids_headers[index] + '<br/>' + section_ids_content[index]
                        # format_text = await self.claude_ai_clean_and_format_sources(section_ids_content[index])

                        # print('completed the groq formating, now removing new line...')
                        # cleaned_text = self.remove_newlines(format_text)
                        cleaned_text = format_text
                        # print('now appending to source map...')
                        # print('this is final source map: ', final_source[0]['source_map'])
                        # print('this is source list: ', final_source[0]['source_list'])
                        # Convert section_id to integer since source_map uses integer keys
                         # Handle both numeric and string section IDs


                        if isinstance(section_id, str) and not section_id.isdigit() and isinstance(final_source[0]['source_list'], list):
                            # If it's a string section ID, use it directly
                            # final_source[0]['source_list'][section_id]['content'] = f"<div class='{highlight_class}'>" + cleaned_text + "</div>"
                            # find source_list item with "section_id" is section_id
                            for item in final_source[0]['source_list']:
                                if item['section_id'] == section_id:
                                    item['content'] = f"<div class='{highlight_class}'>" + cleaned_text + "</div>"
                                    final_source[0]['source_list'] = item
                                    final_source[0]['source_list'][section_id] = {
                                        "content": f"<div class='{highlight_class}'>" + cleaned_text + "</div>"
                                    }

                        else:
                            # If it's numeric or can be converted to a number, use integer index
                            section_id =  section_id if isinstance(section_id, str) and not section_id.isdigit() else int(section_id)
                            if section_id not in final_source[0]['source_list'] or 'section_id' not in final_source[0]['source_list'][section_id]:
                                final_source[0]['source_list'][section_id] = {
                                    "section_id": section_id,
                                    "content": f"<div class='{highlight_class}'>" + cleaned_text + "</div>"
                                }
                                continue
                            final_source[0]['source_list'][section_id]['content'] = f"<div class='{highlight_class}'>" + cleaned_text + "</div>"
                        # final_source[0]['source_list'][section_id]['content'] = f"<div class='{highlight_class}'>" + cleaned_text + "</div>"
                        # print('this is brakpoint..')
                        source_list.append(final_source[0]['source_list'][section_id])
                        index += 1
                        """

                        # UPDATED SOURCE ISSUE

                for section_id in section_ids:
                    if section_id:
                        header = section_ids_headers[index]
                        content = section_ids_content[index]
                        format_text = header + '<br/>' + content
                        cleaned_text = format_text

                        # Enhanced metadata extraction from header
                        doc_name = ""
                        title = ""
                        page_number = None
                        document_type = None
                        discipline = None
                        document_number = None
                        document_references = []

                        # Improved regex patterns for more reliable extraction
                        doc_match = re.search(r'Document Name:.*?<span.*?>\((.+?)\)</span>', header)
                        if doc_match:
                            doc_name = doc_match.group(1).strip()
                        else:
                            # Try alternative pattern
                            alt_doc_match = re.search(r'Document(?:\s+Name)?:\s*([^,<>\(\)]+)', header)
                            if alt_doc_match:
                                doc_name = alt_doc_match.group(1).strip()

                        title_match = re.search(r'Section.*?<span.*?>\((.+?)\)</span>', header)
                        if title_match:
                            title = title_match.group(1).strip()
                        else:
                            # Try alternative pattern
                            alt_title_match = re.search(r'Section[:\s]+([^,<>\(\)]+)', header)
                            if alt_title_match:
                                title = alt_title_match.group(1).strip()

                        page_match = re.search(r'Page\s+(\d+)', header)
                        if page_match:
                            try:
                                page_number = int(page_match.group(1))
                            except ValueError:
                                page_number = None

                        # Extract document type if available
                        doc_type_match = re.search(r'Document Type:\s*([^,<>\(\)]+)', header)
                        if doc_type_match:
                            document_type = doc_type_match.group(1).strip()

                        # Extract discipline if available
                        discipline_match = re.search(r'Discipline:\s*([^,<>\(\)]+)', header)
                        if discipline_match:
                            discipline = discipline_match.group(1).strip()

                        # Extract document number if available
                        doc_num_match = re.search(r'Document Number:\s*([^,<>\(\)]+)', header)
                        if doc_num_match:
                            document_number = doc_num_match.group(1).strip()

                        # Normalize section_id to int if possible
                        try:
                            section_id_int = int(section_id)
                        except (ValueError, TypeError):
                            section_id_int = section_id  # Keep as-is if not convertible

                        # Get additional metadata from the chunk if available
                        matched_item = None
                        for i, item in enumerate(final_source[0]['source_list']):
                            if str(item.get('section_id', '')) == str(section_id):
                                matched_item = item
                                break

                        if matched_item:
                            # Use existing metadata if available
                            if not doc_name and matched_item.get('source'):
                                doc_name = matched_item.get('source')
                            if not title and matched_item.get('title'):
                                title = matched_item.get('title')
                            if page_number is None and matched_item.get('page_number'):
                                page_number = matched_item.get('page_number')
                            if not document_type and matched_item.get('document_type'):
                                document_type = matched_item.get('document_type')
                            if not discipline and matched_item.get('discipline'):
                                discipline = matched_item.get('discipline')
                            if not document_number and matched_item.get('document_number'):
                                document_number = matched_item.get('document_number')
                            if not document_references and matched_item.get('document_references'):
                                document_references = matched_item.get('document_references')

                            # Update the matched item with new content and enhanced metadata
                            matched_item['content'] = f"<div class='{highlight_class}'>" + cleaned_text + "</div>"

                            # Update metadata if extracted from header
                            if doc_name:
                                matched_item['source'] = doc_name
                            if title:
                                matched_item['title'] = title
                            if page_number is not None:
                                matched_item['page_number'] = page_number
                            if document_type:
                                matched_item['document_type'] = document_type
                            if discipline:
                                matched_item['discipline'] = discipline
                            if document_number:
                                matched_item['document_number'] = document_number
                            if document_references:
                                matched_item['document_references'] = document_references

                            source_list.append(matched_item)
                        else:
                            # Create a new source item if no match found
                            source_list.append({
                                "section_id": section_id_int,
                                "content": f"<div class='{highlight_class}'>" + cleaned_text + "</div>",
                                "id": "",
                                "score": 0.0,
                                "source": doc_name,  # Use extracted doc_name
                                "title": title,      # Use extracted title
                                "page_number": page_number if page_number is not None else 1,
                                "document_type": document_type,
                                "discipline": discipline,
                                "document_number": document_number,
                                "document_references": document_references
                            })

                        index += 1

                        #  UPDATED SOURCE ISSUE




                if 'SOURCES SUFFICIENT' in final_answer['Feedback']:
                    # Format and clean answer and sources
                    log = self.report_manager.load_report()

                    # Ensure all sources have proper metadata
                    for source in source_list:
                        if not source.get('source'):
                            # Try to extract document name from content if available
                            doc_match = re.search(r'Document Name:.*?<span.*?>\((.+?)\)</span>', source.get('content', ''))
                            if doc_match:
                                source['source'] = doc_match.group(1).strip()
                            else:
                                alt_doc_match = re.search(r'Document(?:\s+Name)?:\s*([^,<>\(\)]+)', source.get('content', ''))
                                if alt_doc_match:
                                    source['source'] = alt_doc_match.group(1).strip()
                                else:
                                    source['source'] = "Unknown Document"

                        if not source.get('title'):
                            # Try to extract title from content if available
                            title_match = re.search(r'Section.*?<span.*?>\((.+?)\)</span>', source.get('content', ''))
                            if title_match:
                                source['title'] = title_match.group(1).strip()
                            else:
                                alt_title_match = re.search(r'Section[:\s]+([^,<>\(\)]+)', source.get('content', ''))
                                if alt_title_match:
                                    source['title'] = alt_title_match.group(1).strip()
                                else:
                                    source['title'] = "Unknown Section"

                        if not source.get('page_number'):
                            # Try to extract page number from content
                            page_match = re.search(r'Page\s+(\d+)', source.get('content', ''))
                            if page_match:
                                try:
                                    source['page_number'] = int(page_match.group(1))
                                except ValueError:
                                    source['page_number'] = 1
                            else:
                                source['page_number'] = 1

                    log['answer'][idx - 1] = (
                        f"<h3 style='font-weight: bold; font-size: 18.67px; color: #333;'>Clarification {idx}: {sub_question}</h3>"
                        f"<div class='highlight'><p style='font-size: 14.67px;'>{answer}</p></div><br/>"
                    )

                    log["sources"][idx - 1] = source_list
                    log["bid_title"] = project_data['name']
                    log["question"][idx - 1] = f"Query #{idx}: {sub_question}"
                    log["reference"][idx - 1] = [source.get("content", "") for source in source_list]
                    log["tender_clarification_round"][idx - 1] = f"Round #{idx}"

                elif 'SOURCES INSUFFICIENT' in final_answer['Feedback']:
                    log = self.report_manager.load_report()

                    if idx - 1 >= 0 and idx - 1 < len(log['answer']):
                        log['answer'][idx - 1] = (
                            f"<h3 style='font-weight: bold; font-size: 18.67px; color: #333;'>Clarification {idx}: {sub_question}</h3>"
                            f"<div class='highlight'><p style='font-size: 14.67px;'>{final_answer['Answer'] + ' <br/> ' + final_answer['Recommendation']}</p></div><br/>"
                        )

                    # Check if source_list is not empty, otherwise fallback to Recommendation
                    if source_list:
                        # Ensure all sources have proper metadata
                        for source in source_list:
                            if not source.get('source'):
                                # Try to extract document name from content if available
                                doc_match = re.search(r'Document Name:.*?<span.*?>\((.+?)\)</span>', source.get('content', ''))
                                if doc_match:
                                    source['source'] = doc_match.group(1).strip()
                                else:
                                    alt_doc_match = re.search(r'Document(?:\s+Name)?:\s*([^,<>\(\)]+)', source.get('content', ''))
                                    if alt_doc_match:
                                        source['source'] = alt_doc_match.group(1).strip()
                                    else:
                                        source['source'] = "Unknown Document"

                            if not source.get('title'):
                                # Try to extract title from content if available
                                title_match = re.search(r'Section.*?<span.*?>\((.+?)\)</span>', source.get('content', ''))
                                if title_match:
                                    source['title'] = title_match.group(1).strip()
                                else:
                                    alt_title_match = re.search(r'Section[:\s]+([^,<>\(\)]+)', source.get('content', ''))
                                    if alt_title_match:
                                        source['title'] = alt_title_match.group(1).strip()
                                    else:
                                        source['title'] = "Unknown Section"

                            if not source.get('page_number'):
                                # Try to extract page number from content
                                page_match = re.search(r'Page\s+(\d+)', source.get('content', ''))
                                if page_match:
                                    try:
                                        source['page_number'] = int(page_match.group(1))
                                    except ValueError:
                                        source['page_number'] = 1
                                else:
                                    source['page_number'] = 1

                        log["sources"][idx - 1] = source_list
                        log["reference"][idx - 1] = [source.get("content", "") for source in source_list]
                    else:
                        # Ensure the index exists in the arrays before assignment
                        while len(log["reference"]) < idx:
                            log["reference"].append("")

                        while len(log["sources"]) < idx:
                            log["sources"].append({})

                        # Create a more informative fallback source when no sources are found
                        log["reference"][idx - 1] = final_answer['Recommendation']
                        log["sources"][idx - 1] = {
                            "content": final_answer['Recommendation'],
                            "id": "",
                            "matched_content": "",
                            "page_number": 1,
                            "score": 0.0,
                            "section_id": "",
                            "source": "No Source Found",
                            "title": "Recommendation"
                        }

                    log["bid_title"] = project_data['name']
                    log["question"][idx - 1] = f"Query #{idx}: {sub_question}"
                    log["tender_clarification_round"][idx - 1] = f"Round #{idx}"

                # right before add_event

                self.add_event(requirement_id, 'in_progress_event', {
                    'event_type': 'QMP',
                    'request_id': requirement_id,
                    'data': log
                })

                end_query = time.time()
                print(f"Time taken to complete query {idx}: {end_query - start_query:.2f} seconds")

                await self.update_log_and_status(log)
                break  # Exit loop on success

            except Exception as e:
                import traceback
                print(f"Error occurred: {str(e)}")
                traceback.print_exc()
                print("Full stack trace printed above")
                attempts += 1
                print(f"Attempt {attempts} failed for Query {idx}: {e}")
                if attempts >= max_retries:
                    log = self.report_manager.load_report()
                    log['answer'][idx - 1] = f"Query #{idx} {sub_question}: Failed to retrieve answer due to error after {max_retries} attempts."
                    log["sources"][idx - 1]  = f"Query #{idx} {sub_question}: Failed to retrieve sources due to error after {max_retries} attempts."
                    log["question"][idx - 1]  = f"Query #{idx}: {sub_question}"
                    log["reference"][idx - 1]  = []
                    log["tender_clarification_round"][idx - 1]  = f"Round #{idx}"
                    self.add_event(requirement_id, 'error_event', {
                        'event_type': 'QMP',
                        'request_id': requirement_id,
                        'data': {'status': 'error', 'message': f"Oops! Something went wrong for Query {idx} after {max_retries} attempts.", 'query_idx': idx, 'error': str(e)}
                    })

    def process_question_sync(self, idx, sub_question, project_id, project_data, requirement_id, max_retries=3, original_question="", quoted_text="", follow_up_query="", language="English"):
        """Process a single question."""
        start_query = time.time()
        attempts = 0
        highlight_class = 'highlight'

        # print('i am subquestion: ', sub_question)

        # Generate similar questions to improve search
        synonyms = self.synonym_service.generate_synonym_sync(f"{sub_question}\n Description: {sub_question}")

        # Combine all matches into a single string separated by '; '
        similarQuestion = [f"{name.strip()} - {description.strip()}"
                for name, description in re.findall(r"Name:\s*(.+?)\n\s*Description:\s*(.+?)(?:\n|</SYNONYMS>)", synonyms, re.DOTALL)][:3]
        # print("Synonyms Questions generated successfully...: ", similarQuestion)


        while attempts < max_retries:
            try:

                # print('i am here...')
                self.add_event(requirement_id, 'progress_message', {
                    'event_type': 'QMP',
                    'request_id': requirement_id,
                    'data': {'status': 'extracting_sources', 'message': f"Processing request. Crafting response now. Please wait.", 'query_idx': idx}
                })

                # Extract sources
                # print('now trying to extract data using v2...')
                # print(project_id, [sub_question], similarQuestion)
                # return
                if self.can_query_existing_bid() and not self.is_follow_up_query:
                    print('now trying to query existing bid...')
                    search_queries = [sub_question] + similarQuestion
                    sources = self.faiss_processor.search_sync(search_queries, self.bid_id, 80)
                    
                    # format the sources
                    final_results = [{
                        "source_map": {},
                        "source_list": [],
                        "questions": [sub_question]
                    }]
                    sources_list = []
                    source_map = {}
                    for i, source in enumerate(sources[0]):
                        source_map[i] = source[0]
                        sources_list.append({
                            "id": i,
                            "content": source[0],
                            "score": source[1],
                            "section_id": i,
                            "source": source[2],
                            "page_number": source[3]
                        })
                    final_results[0]['source_map'] = source_map
                    final_results[0]['source_list'] = sources_list
                    sources = final_results
                    # print('this is sources: ', sources)
                elif not self.is_follow_up_query:
                    if self.package_id:
                        sources = self.data_manager.extract_data_v2_sync(self.package_id, [sub_question], similarQuestion, 45)
                    elif self.vendor_id:
                        sources = self.data_manager.extract_data_v2_sync(self.vendor_id, [sub_question], similarQuestion, 45)
                    elif self.package_id:
                        sources = self.data_manager.extract_data_v2_sync(self.package_id, [sub_question], similarQuestion, 45)
                    else:
                        sources = self.data_manager.extract_data_v2_sync(project_id, [sub_question], similarQuestion, 45)
                    # print('this is sources: ', sources)
                    
                
                if self.is_follow_up_query:
                    print('now trying to merge sources for follow-up query...')
                    # project source
                    project_sources = None if not self.project_id else self.data_manager.extract_data_v2_sync(self.project_id, [sub_question, self.follow_up_query, quoted_text], similarQuestion, 25)
                    # package source
                    package_sources = None if not self.package_id else self.data_manager.extract_data_v2_sync(self.package_id, [sub_question, self.follow_up_query, quoted_text], similarQuestion, 25)
                    # engineer source
                    engineer_sources = None if not self.engineer_id else self.data_manager.extract_data_v2_sync(self.engineer_id, [sub_question, self.follow_up_query, quoted_text], similarQuestion, 25)
                    # Vendor source
                    vendor_sources = None if not self.vendor_id else self.data_manager.extract_data_v2_sync(self.vendor_id, [sub_question, self.follow_up_query, quoted_text], similarQuestion, 25)
                    # bid source
                    bid_sources_raw = None if not self.bid_id else self.faiss_processor.search_sync([sub_question, self.follow_up_query, quoted_text], self.bid_id, 50)

                    # Call the new function to merge sources
                    if project_sources:
                        print(f'has_project_source: {len(project_sources)}')
                    if package_sources:
                        print(f'has_package_source: {len(package_sources)}')
                    if bid_sources_raw:
                        print(f'has_bid_source: {len(bid_sources_raw)}')
                        
                    sources = self._merge_followup_sources(project_sources, package_sources, engineer_sources, vendor_sources, bid_sources_raw, sub_question)

                    original_question = self.follow_up_query


                self.add_event(requirement_id, 'progress_message', {
                    'event_type': 'QMP',
                    'request_id': requirement_id,
                    'data': {'status': 'generating_answer', 'message': f"Processing inquiry {idx}. Thinking. Please wait.", 'query_idx': idx}
                })

                bid_files = self.get_bid_files(self.bid_id) if self.bid_id else []
                project_files = self.get_project_files(self.project_id) if self.project_id else []

                package_files = self.get_project_files(self.package_id) if self.package_id else []
                engineer_files = self.get_project_files(self.engineer_id) if self.engineer_id else []
                vendor_files = self.get_project_files(self.vendor_id) if self.vendor_id else []

                combined_file_names = {
                    'bid_files': bid_files,
                    'project_files': project_files,
                    'package_files': package_files,
                    'engineer_files': engineer_files,
                    'vendor_files': vendor_files
                }


                answer = self.get_answer_claude_sync(sources[0]['source_list'], sub_question, original_question=original_question, quoted_text=quoted_text, follow_up_query=follow_up_query, file_names=combined_file_names, language=language)

                

                final_answer = answer
                final_source = sources



                self.add_event(requirement_id, 'progress_message', {
                    'event_type': 'QMP',
                    'request_id': requirement_id,
                    'data': {'status': 'formatting_sources', 'message': f"Refining the details for inquiry {idx}. Double checking our answers. Please wait.", 'query_idx': idx}
                })



                source_list = []
                section_ids = final_answer['SectionIds']
                section_ids_headers = final_answer['SourceAnswerHeaders']
                section_ids_content = final_answer['SourceAnswerContents']
                answer = final_answer['Answer']

                # print('section ids below in sources sufficient: ', section_ids)

                for i, section_id in enumerate(section_ids):
                    if section_id:
                        try:
                            header = section_ids_headers[i]
                            content = section_ids_content[i]
                        except IndexError:
                            continue
                        format_text = header + '<br/>' + content
                        cleaned_text = format_text

                        # Extract metadata from header
                        doc_name = ""
                        title = ""
                        page_number = None
                        document_type = None
                        discipline = None
                        document_number = None
                        document_references = []

                        # Parse header to extract document name, title, and page number
                        doc_match = re.search(r'Document Name:.*?<span.*?>\((.+?)\)</span>', header)
                        if doc_match:
                            doc_name = doc_match.group(1)

                        title_match = re.search(r'Section.*?<span.*?>\((.+?)\)</span>', header)
                        if title_match:
                            title = title_match.group(1)

                        page_match = re.search(r'Page\s+(\d+)', header)
                        if page_match:
                            try:
                                page_number = int(page_match.group(1))
                            except ValueError:
                                page_number = None

                        # Normalize section_id to int if possible
                        try:
                            section_id_int = int(section_id)
                        except (ValueError, TypeError):
                            section_id_int = section_id  # Keep as-is if not convertible

                        # Get additional metadata from the chunk if available
                        matched_item = None
                        for i, item in enumerate(final_source[0]['source_list']):
                            if str(item.get('section_id', '')) == str(section_id):
                                matched_item = item
                                break

                        if matched_item:
                            # Use existing metadata if available
                            if not doc_name and matched_item.get('source'):
                                doc_name = matched_item.get('source')
                            if not title and matched_item.get('title'):
                                title = matched_item.get('title')
                            if page_number is None and matched_item.get('page_number'):
                                page_number = matched_item.get('page_number')
                            if not document_type and matched_item.get('document_type'):
                                document_type = matched_item.get('document_type')
                            if not discipline and matched_item.get('discipline'):
                                discipline = matched_item.get('discipline')
                            if not document_number and matched_item.get('document_number'):
                                document_number = matched_item.get('document_number')
                            if not document_references and matched_item.get('document_references'):
                                document_references = matched_item.get('document_references')
                            

                            # Update the matched item with new content
                            matched_item['content'] = f"<div class='{highlight_class}'>" + cleaned_text + "</div>"

                            # Update metadata if extracted from header
                            if doc_name:
                                matched_item['source'] = doc_name
                            if title:
                                matched_item['title'] = title
                            if page_number is not None:
                                matched_item['page_number'] = page_number
                            if document_type:
                                matched_item['document_type'] = document_type
                            if discipline:
                                matched_item['discipline'] = discipline
                            if document_number:
                                matched_item['document_number'] = document_number
                            if document_references:
                                matched_item['document_references'] = document_references

                            source_list.append(matched_item)
                        else:
                            # Create a new source item if no match found
                            source_list.append({
                                "section_id": section_id_int,
                                "content": f"<div class='{highlight_class}'>" + cleaned_text + "</div>",
                                "id": "",
                                "score": 0.0,
                                "source": doc_name,  # Use extracted doc_name
                                "title": title,      # Use extracted title
                                "page_number": page_number if page_number is not None else 1,
                                "document_type": document_type,
                                "discipline": discipline,
                                "document_number": document_number,
                                "document_references": document_references
                            })

        

                        #  UPDATED SOURCE ISSUE




                if 'SOURCES SUFFICIENT' in final_answer['Feedback']:
                    # Format and clean answer and sources
                    # print('i finished groq clean format source....')

                    log = self.report_manager.load_report()
                    # print('i am the log loaded.....')

                    # log['answer'][idx - 1] = (
                    #     f"<h3 style='font-weight: bold; font-size: 18.67px; color: #333;'>Clarification {idx}: {sub_question}</h3>"
                    #     f"<div class='highlight'><p style='font-size: 14.67px;'>{await self.groq_ai_clean_and_format_text(answer, project_data['entity_type'])}</p></div><br/>"
                    # )
                    # log['answer'][idx - 1] = (
                    #     f"<h3 style='font-weight: bold; font-size: 18.67px; color: #333;'>Clarification {idx}: {sub_question}</h3>"
                    #     f"<div class='highlight'><p style='font-size: 14.67px;'>{await self.claude_ai_clean_and_format_text(answer, project_data['entity_type'])}</p></div><br/>"
                    # )

                    log['answer'][idx - 1] = (
                        f"<h3 style='font-weight: bold; font-size: 18.67px; color: #333;'>Clarification {idx}: {sub_question}</h3>"
                        f"<div class='highlight'><p style='font-size: 14.67px;'>{answer}</p></div><br/>"
                    )

                    # print('loaded answer log')

                    # Ensure all sources have proper metadata
                    for source in source_list:
                        if not source.get('source') or source.get('source') == "Unknown Document":
                            # Try to extract document name from content if available
                            doc_match = re.search(r'Document Name:.*?<span.*?>\((.+?)\)</span>', source.get('content', ''))
                            if doc_match:
                                source['source'] = doc_match.group(1).strip()
                            else:
                                alt_doc_match = re.search(r'Document(?:\s+Name)?:\s*([^,<>\(\)]+)', source.get('content', ''))
                                if alt_doc_match:
                                    source['source'] = alt_doc_match.group(1).strip()
                                else:
                                    source['source'] = "Unknown Document"

                        if not source.get('title'):
                            # Try to extract title from content if available
                            title_match = re.search(r'Section.*?<span.*?>\((.+?)\)</span>', source.get('content', ''))
                            if title_match:
                                source['title'] = title_match.group(1).strip()
                            else:
                                alt_title_match = re.search(r'Section[:\s]+([^,<>\(\)]+)', source.get('content', ''))
                                if alt_title_match:
                                    source['title'] = alt_title_match.group(1).strip()
                                else:
                                    source['title'] = "Unknown Section"

                        if not source.get('page_number'):
                            # Try to extract page number from content if available
                            page_match = re.search(r'Page\s+(\d+)', source.get('content', ''))
                            if page_match:
                                try:
                                    source['page_number'] = int(page_match.group(1))
                                except ValueError:
                                    source['page_number'] = 1
                            else:
                                source['page_number'] = 1

                    log["sources"][idx - 1] = source_list
                    log["bid_title"] = project_data['name']
                    log["question"][idx - 1] = f"Query #{idx}: {sub_question}"
                    log["reference"][idx - 1] = [source.get("content", "") for source in source_list]
                    log["tender_clarification_round"][idx - 1] = f"Round #{idx}"

                    # For follow-up queries, include the quoted text in the response
                    if quoted_text:
                        log["quoted_text"] = quoted_text
                        log["follow_up_query"] = follow_up_query

                elif 'SOURCES INSUFFICIENT' in final_answer['Feedback']:

                    # print('i amfeedback text in insufficient sources: ', feedback_text)
                    log = self.report_manager.load_report()
                    # print('this is log: ', log)
                    # print('this is idx: ', idx)

                    # recommendation_text = await self.extract_recommendation_content(final_answer)

                    if idx - 1 >= 0 and idx - 1 < len(log['answer']):
                        log['answer'][idx - 1] = (
                            f"<h3 style='font-weight: bold; font-size: 18.67px; color: #333;'>Clarification {idx}: {sub_question}</h3>"
                            f"<div class='highlight'><p style='font-size: 14.67px;'>{final_answer['Answer'] + ' <br/> ' + final_answer['Recommendation']}</p></div><br/>"
                        )

                    # Check if source_list is not empty, otherwise fallback to Recommendation
                    if source_list:
                        # Ensure all sources have proper metadata
                        for source in source_list:
                            if not source.get('source') or source.get('source') == "Unknown Document":
                                # Try to extract document name from content if available
                                doc_match = re.search(r'Document Name:.*?<span.*?>\((.+?)\)</span>', source.get('content', ''))
                                if doc_match:
                                    source['source'] = doc_match.group(1).strip()
                                else:
                                    # Try alternative pattern for document name extraction
                                    alt_doc_match = re.search(r'Document(?:\s+Name)?:\s*([^,<>\(\)\n]+)', source.get('content', ''), re.IGNORECASE)
                                    if alt_doc_match:
                                        source['source'] = alt_doc_match.group(1).strip()
                                    else:
                                        # Try to extract from filename if available
                                        filename_match = re.search(r'(?:file|document)[:\s]+([A-Za-z0-9_\-\.]+\.[A-Za-z0-9]+)', source.get('content', ''), re.IGNORECASE)
                                        if filename_match:
                                            source['source'] = filename_match.group(1).strip()
                                        else:
                                            # Use the original filename if available
                                            source['source'] = source.get('source', "Unknown Document")

                            if not source.get('title'):
                                # Try to extract title from content if available
                                title_match = re.search(r'Section.*?<span.*?>\((.+?)\)</span>', source.get('content', ''))
                                if title_match:
                                    source['title'] = title_match.group(1).strip()
                                else:
                                    # Try alternative pattern for section title extraction
                                    alt_title_match = re.search(r'Section(?:\s+\w+)?:\s*([^,<>\(\)\n]+)', source.get('content', ''), re.IGNORECASE)
                                    if alt_title_match:
                                        source['title'] = alt_title_match.group(1).strip()
                                    else:
                                        source['title'] = "Unknown Section"

                            if not source.get('page_number'):
                                # Try to extract page number from content if available
                                page_match = re.search(r'Page\s+(\d+)', source.get('content', ''))
                                if page_match:
                                    try:
                                        source['page_number'] = int(page_match.group(1))
                                    except ValueError:
                                        source['page_number'] = 1
                                else:
                                    source['page_number'] = 1

                        log["sources"][idx - 1] = source_list
                        log["reference"][idx - 1] = [source.get("content", "") for source in source_list]
                    else:
                         # Ensure the index exists in the arrays before assignment
                        while len(log["reference"]) < idx:
                            log["reference"].append("")

                        while len(log["sources"]) < idx:
                            log["sources"].append({})

                        log["reference"][idx - 1] = final_answer['Recommendation']
                        log["sources"][idx - 1] = {
                            "content": final_answer['Recommendation'],
                            "id": "",
                            "matched_content": "",
                            "page_number": "1",
                            "score": 0.0,
                            "section_id": "",
                            "source": "Recommendation",
                            "title": "AI Recommendation"
                            }

                    log["bid_title"] = project_data['name']
                    log["question"][idx - 1] = f"Query #{idx}: {sub_question}"
                    log["tender_clarification_round"][idx - 1] = f"Round #{idx}"

                    # For follow-up queries, include the quoted text in the response
                    if quoted_text:
                        log["quoted_text"] = quoted_text
                        log["follow_up_query"] = follow_up_query

                # right before add_event

                self.add_event(requirement_id, 'in_progress_event', {
                    'event_type': 'QMP',
                    'request_id': requirement_id,
                    'data': log
                })

                end_query = time.time()
                print(f"Time taken to complete query {idx}: {end_query - start_query:.2f} seconds")

                self.update_log_and_status_sync(log)
                break  # Exit loop on success

            except Exception as e:
                import traceback
                print(f"Error occurred: {str(e)}")
                traceback.print_exc()
                print("Full stack trace printed above")
                attempts += 1
                print(f"Attempt {attempts} failed for Query {idx}: {e}")
                if attempts >= max_retries:
                    log = self.report_manager.load_report()
                    log['answer'][idx - 1] = f"Query #{idx} {sub_question}: Failed to retrieve answer due to error after {max_retries} attempts."
                    log["sources"][idx - 1]  = f"Query #{idx} {sub_question}: Failed to retrieve sources due to error after {max_retries} attempts."
                    log["question"][idx - 1]  = f"Query #{idx}: {sub_question}"
                    log["reference"][idx - 1]  = []
                    log["tender_clarification_round"][idx - 1]  = f"Round #{idx}"
                    self.add_event(requirement_id, 'error_event', {
                        'event_type': 'QMP',
                        'request_id': requirement_id,
                        'data': {'status': 'error', 'message': f"Oops! Something went wrong for Query {idx} after {max_retries} attempts.", 'query_idx': idx, 'error': str(e)}
                    })


    async def answer_question_v1_1(self, requirement_id, related_project_ids):
        try:
            requirement = Requirement.get_single(requirement_id)
            # print('this is requirement: ', requirement)
        except Exception as e:
            import traceback
            print(f"Error occurred: {str(e)}")
            traceback.print_exc()
            # print("Full stack trace printed above")
            return

        # Check if the requirement status is already done
        if requirement['status'] == 'done':
            # print(f"Requirement with id: {requirement_id} is already done. Exiting...")
            return

        self.requirement_id = requirement_id
        self.related_project_ids = related_project_ids
        Requirement.update(requirement_id, tried=requirement['tried'] + 1)


        # do query expansion
        question_list = await self.format_question_text(requirement['criteria'])

        #ADD LANGUAGE
        # print('new formatted question list: ', question_list)

        project_id = requirement['project_id']
        if self.can_query_existing_bid():
            project_data = {"name": ""}
            project_id = self.bid_id
        else:
            project_data = Project.get_single(project_id)

        if not self.should_break_cache():
            # pull all the existing answers from the report
            reports = ReportManager.get_all_query_reports(self.env_data.get('DATA_DIR'), project_id, self.bid_id)
            # Find the first report that has the same criteria as the requirement
            for report in reports:
                if report['criteria'] == requirement['criteria']:
                    # return the report
                    self.add_event(requirement_id, 'completed_event', {
                        'event_type': 'QMP',
                        'request_id': requirement_id,
                        'data': report
                    })
                    Requirement.update(requirement_id, status='done')
                    return


        self.report_manager = ReportManager(self.env_data.get('DATA_DIR'), project_id, requirement_id, self.bid_id)
        # Initialize shared response structure
        tasks = []
        # Create initial report
        initial_log = {
            "criteria": requirement['criteria'],
            "answer": [f"Pending Clarification {count}" for count in range(1, len(question_list) + 1)],
            "sources": [[]] * len(question_list),
            "bid_number": "00" if not self.bid_id else self.bid_id,
            "bid_title": "",
            "question": [f"Pending Clarification {count}" for count in range(1, len(question_list) + 1)],
            "reference": [[]] * len(question_list),
            "raiser_by": "Please Complete",
            "responsible": "Please Complete",
            "status": "Please Complete",
            "tender_clarification_round": [f"Pending Clarification {count}" for count in range(1, len(question_list) + 1)],
            "quoted_text": self.quoted_text if hasattr(self, 'quoted_text') and self.quoted_text else "",
            "follow_up_query": self.follow_up_query if hasattr(self, 'follow_up_query') and self.follow_up_query else ""
        }
        self.report_manager.create_initial_report(initial_log)

        for idx, sub_question in enumerate(question_list,  start=1):
            if not sub_question.strip():
                continue
            tasks.append(asyncio.create_task(self.process_question(
                idx,
                sub_question,
                project_id,
                project_data,
                requirement_id,
                original_question=requirement['criteria'],
                quoted_text=self.quoted_text,
                follow_up_query=self.follow_up_query,
                language=self.language
            )))

        # Run tasks concurrently
        await asyncio.gather(*tasks)
        log = self.report_manager.load_report()
        # print(f"Latest NEW LOG :{log}")
        # Emit final completion event
        self.add_event(requirement_id, 'completed_event', {
            'event_type': 'QMP',
            'request_id': requirement_id,
            'data': log
        })
        Requirement.update(requirement_id, status='done')
         # call completion status to done
        self.backend_status_updater.update_qmp_completion_status(requirement_id, log)

    def answer_question_v1_1_sync(self, requirement_id, related_project_ids):
        try:
            requirement = Requirement.get_single(requirement_id)
            # print('this is requirement: ', requirement)
        except Exception as e:
            import traceback
            print(f"Error occurred: {str(e)}")
            traceback.print_exc()
            # print("Full stack trace printed above")
            return

        # Check if the requirement status is already done
        if requirement['status'] == 'done':
            # print(f"Requirement with id: {requirement_id} is already done. Exiting...")
            return

        self.requirement_id = requirement_id
        self.related_project_ids = related_project_ids
        Requirement.update(requirement_id, tried=requirement['tried'] + 1)
        # do query expansion

        if self.is_follow_up_query:
            question_list = self.format_question_text_sync(self.follow_up_query)
        else:
            question_list = self.format_question_text_sync(requirement['criteria'])
        # print('new formatted question list: ', question_list)

        project_id = requirement['project_id']

        package_id = requirement['package_id'] if requirement['package_id'] else None
        engineer_id = requirement['engineer_id'] if requirement['engineer_id'] else None
        vendor_id = requirement['vendor_id'] if requirement['vendor_id'] else None

        self.package_id = package_id

        self.project_id = project_id
        self.engineer_id = engineer_id
        self.vendor_id = vendor_id

        if self.can_query_existing_bid():
            project_data = {"name": ""}
            project_id = self.bid_id
        else:
            try:
                project_data = Project.get_single(project_id)
            except Exception as e:
                project_data = {"name": ""}
                print(f"Error occurred: {str(e)}")
                # print("Full stack trace printed above")

        if not self.should_break_cache():
            # pull all the existing answers from the report
            reports = ReportManager.get_all_query_reports(self.env_data.get('DATA_DIR'), project_id, self.bid_id)
            # Find the first report that has the same criteria as the requirement
            for report in reports:
                if report['criteria'] == requirement['criteria']:
                    # return the report
                    self.add_event(requirement_id, 'completed_event', {
                        'event_type': 'QMP',
                        'request_id': requirement_id,
                        'data': report
                    })
                    Requirement.update(requirement_id, status='done')
                    return


        self.report_manager = ReportManager(self.env_data.get('DATA_DIR'), project_id, requirement_id, self.bid_id)
        # Initialize shared response structure
        tasks = []
        # Create initial report
        initial_log = {
            "criteria": requirement['criteria'],
            "answer": [f"Pending Clarification {count}" for count in range(1, len(question_list) + 1)],
            "sources": [[]] * len(question_list),
            "bid_number": "00" if not self.bid_id else self.bid_id,
            "bid_title": "",
            "question": [f"Pending Clarification {count}" for count in range(1, len(question_list) + 1)],
            "reference": [[]] * len(question_list),
            "raiser_by": "Please Complete",
            "responsible": "Please Complete",
            "status": "Please Complete",
            "tender_clarification_round": [f"Pending Clarification {count}" for count in range(1, len(question_list) + 1)],
            "quoted_text": self.quoted_text if hasattr(self, 'quoted_text') and self.quoted_text else "",
            "follow_up_query": self.follow_up_query if hasattr(self, 'follow_up_query') and self.follow_up_query else ""
        }
        self.report_manager.create_initial_report(initial_log)

        for idx, sub_question in enumerate(question_list,  start=1):
            if not sub_question.strip():
                continue
            tasks.append(gevent.spawn(self.process_question_sync(
                idx,
                sub_question,
                project_id,
                project_data,
                requirement_id,
                original_question=requirement['criteria'],
                quoted_text=self.quoted_text,
                follow_up_query=self.follow_up_query,
                language=self.language
            )))

        # Run tasks concurrently
        gevent.joinall(tasks)
        log = self.report_manager.load_report()
        # print(f"Latest NEW LOG :{log}")
        # Emit final completion event
        self.add_event(requirement_id, 'completed_event', {
            'event_type': 'QMP',
            'request_id': requirement_id,
            'data': log
        })
        Requirement.update(requirement_id, status='done')
         # call completion status to done
        self.backend_status_updater.update_qmp_completion_status(requirement_id, log)


    async def update_log_and_status(self, log):
        print('\n\n now updating qmp log with new log data...\n\n')

        # Update the log with new data
        self.report_manager.update_log(log)
        # if not self.is_test:
        #     self.backend_status_updater.update_history_status(self.requirement_id, 2)

    def update_log_and_status_sync(self, log):
        print('\n\n now updating qmp log with new log data...\n\n')
        self.report_manager.update_log(log)

    def extract_subarray(self, section_ids, potential_source_index): 
        print('This is section Ids: ',section_ids)
        # Ensure the indices are within bounds and extract the corresponding elements
        return [section_ids[i] for i in potential_source_index if 0 <= i < len(section_ids)]

    async def extract_comparison_flag(self, answer2):
        print('answer2 for comparison:', answer2)
        # Assuming the flag is included in the answer response in a specific format
        match = re.search(r"<COMPARISON_FLAG>(.*?)</COMPARISON_FLAG>", answer2, re.IGNORECASE)

        if match:
            match_response = match.group(1).strip()
            return match_response
        return "USE_EXISTING_ANSWER"  # Default to existing answer if flag extraction fails


    async def merge_answers(self, existing_answer, new_answer):
        prompt = '''
            <EXISTING_ANSWER>
                {existing_answer}
            </EXISTING_ANSWER>
            <NEW_ANSWER>
                {new_answer}
            </NEW_ANSWER>
            <ROLE>
                You are an **EPC (Engineering, Procurement, and Construction) Project Engineer** assistant working for **Worley** or **Fulor**. Your responses should reflect the knowledge, expertise, and decision-making processes typical of a seasoned professional in this role, ensuring alignment with industry practices and the responsibilities associated with EPC project management.
            </ROLE>
            <TASK>
                - Carefully analyze both the <EXISTING_ANSWER> and <NEW_ANSWER>.
                - Seamlessly merge them into a single, well-structured response without redundancy.
                - Ensure that the response is **direct, confident, and authoritative**.
                - Present the information as a knowledgeable EPC engineer, reflecting years of industry expertise.
                - Ensure a smooth and natural flow, so users cannot distinguish if multiple responses were combined.
                - Do not include any unnecessary disclaimers, apologies, or uncertainty.
                - Use clear and precise language with industry-standard terminology.
                - Remove any inconsistencies or contradictions between the two answers, choosing the most accurate details.
            </TASK>
            <ANSWER FORMAT>
                Provide the **merged response** in a clear and professional manner.

                **Final Answer:**
                <answer>
                    [Merged answer here, structured logically and confidently]
                </answer>
            </ANSWER FORMAT>
            <IMPORTANT>
                - The answer should be **professionally formatted** with logical structuring.
                - Avoid excessive paragraph spacing—keep the response **concise and dense with information**.
                - The response must feel **authoritative**—it should not read as an AI-generated response.
                - The final answer should be **comprehensive and fully self-contained**.
            </IMPORTANT>
        '''

        formatted_prompt = prompt.format(existing_answer=existing_answer, new_answer=new_answer)

        completion = await self.claude_client.generate_message_agent_sonnet_new(
            [{"role": "user", "content": formatted_prompt}]
        )
        return f"<answer>{(completion.content[0].text)}</answer>"

    def merge_sources(self, existing_source, new_source):
        merged_source = existing_source.copy()
        merged_source[0]['source_list'].extend(new_source[0]['source_list'])
        merged_source[0]['source_map'].update(new_source[0]['source_map'])
        return merged_source

    def format_sources(self, sources):
        formatted_text = 'Sources:\n'
        for idx, source in enumerate(sources, 1):
            if source.get("content"):
                source_title = ".".join(source.get("source", "").split('/')[-1].split('.')[:-1])
                formatted_text += f"Document {idx}:"
                if source_title:
                    formatted_text += f" {source_title}"
                if source['section_id']:
                    formatted_text += f" (Section ID: {source['section_id']})"
                page_number = source.get("page_number", " ")  # Default to "N/A" if page_number is not found
                if page_number:
                    formatted_text += f" (Page: {page_number})"
                formatted_text += f"\n\n{source['content']}\n\n"

        print('this is the formatted sources: ', formatted_text)
        return formatted_text

    def set_quoted_text(self, quoted_text):
        self.quoted_text = quoted_text

    def set_follow_up_query(self, follow_up_query):
        self.follow_up_query = follow_up_query
        if follow_up_query is not None and follow_up_query != "":
            self.is_follow_up_query = True

    def log_source_details(self, sources, label=""):
        """Log detailed information about sources to help diagnose issues."""
        with open("source_details_log.txt", "a") as log_file:
            log_file.write(f"\n\n--- SOURCE DETAILS LOG {label} ---\n")
            log_file.write(f"Number of sources: {len(sources)}\n")

            for idx, source in enumerate(sources):
                log_file.write(f"\n=== SOURCE {idx+1} ===\n")
                log_file.write(f"Source: {source.get('source', 'None')}\n")
                log_file.write(f"Title: {source.get('title', 'None')}\n")
                log_file.write(f"Page Number: {source.get('page_number', 'None')}\n")
                log_file.write(f"Section ID: {source.get('section_id', 'None')}\n")
                log_file.write(f"Document Type: {source.get('document_type', 'None')}\n")
                log_file.write(f"Discipline: {source.get('discipline', 'None')}\n")
                log_file.write(f"Document Number: {source.get('document_number', 'None')}\n")

                if 'content' in source:
                    log_file.write("\n--- CONTENT ---\n")
                    log_file.write(source['content'][:1000])  # Log first 1000 chars of content
                    if len(source['content']) > 1000:
                        log_file.write("\n... (content truncated) ...\n")
                    else:
                        log_file.write("\n--- END CONTENT ---\n")

                if 'matched_content' in source:
                    log_file.write("\n--- MATCHED CONTENT ---\n")
                    log_file.write(source['matched_content'][:500])  # Log first 500 chars of matched content
                    if len(source['matched_content']) > 500:
                        log_file.write("\n... (matched content truncated) ...\n")
                    else:
                        log_file.write("\n--- END MATCHED CONTENT ---\n")

                log_file.write("\n")

            log_file.write("--- END SOURCE DETAILS LOG ---\n")

    def convert_ascii_table_to_html(self, table_lines):
        """Convert ASCII table lines to HTML table format."""
        if not table_lines:
            return ""

        # Log the input table lines for debugging
        with open("table_conversion_log.txt", "a") as log_file:
            log_file.write("\n\n--- TABLE CONVERSION INPUT ---\n")
            log_file.write("\n".join(table_lines))
            log_file.write("\n--- END INPUT ---\n")

        # Clean up table lines - remove any leading/trailing whitespace
        table_lines = [line.strip() for line in table_lines if line.strip()]

        # Find header separator line (contains -+- or ---)
        header_sep_index = -1
        for i, line in enumerate(table_lines):
            if '-+-' in line or '---' in line or line.count('-') > 3:
                header_sep_index = i
                break

        if header_sep_index == -1 or header_sep_index == 0:
            # No proper header found, treat all as body
            has_header = False
            body_start = 0
            # If no header separator found but first line has pipes, treat it as header
            if len(table_lines) > 0 and '|' in table_lines[0] and table_lines[0].count('|') > 1:
                has_header = True
        else:
            has_header = True
            body_start = header_sep_index + 1

        # Start building HTML table with improved styling
        html_table = '<table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0; font-family: Arial, sans-serif;">\n'

        # Process header if exists
        if has_header:
            header_line = table_lines[0] if header_sep_index == -1 else table_lines[header_sep_index - 1]
            header_cells = [cell.strip() for cell in header_line.split('|')]
            # Remove empty cells at start/end if they exist
            if header_cells and not header_cells[0]:
                header_cells.pop(0)
            if header_cells and not header_cells[-1]:
                header_cells.pop()

            html_table += '  <thead>\n    <tr>\n'
            for cell in header_cells:
                html_table += f'      <th style="padding: 8px; text-align: left; border: 1px solid #ddd; background-color: #f2f2f2; font-weight: bold;">{cell}</th>\n'
            html_table += '    </tr>\n  </thead>\n'

        # Process body
        html_table += '  <tbody>\n'
        body_start_idx = 1 if (header_sep_index == -1 and has_header) else body_start
        for i in range(body_start_idx, len(table_lines)):
            line = table_lines[i]
            # Skip separator lines and empty lines
            if '-+-' in line or '---' in line or not '|' in line or line.strip() == '':
                continue

            cells = [cell.strip() for cell in line.split('|')]
            # Remove empty cells at start/end if they exist
            if cells and not cells[0]:
                cells.pop(0)
            if cells and not cells[-1]:
                cells.pop()

            # Skip rows with no content
            if not cells:
                continue

            # Add row with alternating background for better readability
            row_style = ' style="background-color: #f9f9f9;"' if i % 2 == 0 else ''
            html_table += f'    <tr{row_style}>\n'

            for cell in cells:
                # Handle empty cells
                cell_content = cell if cell.strip() else '&nbsp;'
                html_table += f'      <td style="padding: 8px; text-align: left; border: 1px solid #ddd;">{cell_content}</td>\n'

            html_table += '    </tr>\n'

        html_table += '  </tbody>\n</table>'

        # Log the output HTML for debugging
        with open("table_conversion_log.txt", "a") as log_file:
            log_file.write("\n--- TABLE CONVERSION OUTPUT ---\n")
            log_file.write(html_table)
            log_file.write("\n--- END OUTPUT ---\n")

        return html_table

    async def get_answer_claude(self, sources, question, original_question="", quoted_text="", follow_up_query = ""):
        # Log source details before processing
        # self.log_source_details(sources, "BEFORE_PROCESSING")

        # Create a log file for document metadata extraction
        with open("document_metadata_log.txt", "a") as log_file:
            log_file.write("\n\n--- DOCUMENT METADATA EXTRACTION START ---\n")
            log_file.write(f"Processing {len(sources)} sources\n")
            log_file.write(f"Question: {question}\n")

        # Preprocess sources to ensure they have proper metadata
        for idx, source in enumerate(sources):
            with open("document_metadata_log.txt", "a") as log_file:
                log_file.write(f"\n--- SOURCE {idx+1} ---\n")
                log_file.write(f"Initial source metadata: {source.get('source', 'None')}\n")
                log_file.write(f"Initial title: {source.get('title', 'None')}\n")
                log_file.write(f"Initial page_number: {source.get('page_number', 'None')}\n")
                log_file.write(f"Has content: {'Yes' if 'content' in source else 'No'}\n")
                log_file.write(f"Has matched_content: {'Yes' if 'matched_content' in source else 'No'}\n")
                if 'content' in source:
                    log_file.write(f"Content preview: {source['content'][:200]}...\n")

            # Extract document name from content if available and source is missing
            if (not source.get('source') or source.get('source') == "" or source.get('source') == "Unknown Document") and 'content' in source:
                # Try to extract document name from content
                doc_match = re.search(r'Document(?:\s+Name)?[:\s]+([^,<>\(\)\n]+)', source.get('content', ''), re.IGNORECASE)
                if doc_match:
                    source['source'] = doc_match.group(1).strip()
                    with open("document_metadata_log.txt", "a") as log_file:
                        log_file.write(f"Extracted document name from content: {source['source']}\n")
                elif 'matched_content' in source:
                    # Try to extract from matched_content if available
                    doc_match = re.search(r'Document(?:\s+Name)?[:\s]+([^,<>\(\)\n]+)', source.get('matched_content', ''), re.IGNORECASE)
                    if doc_match:
                        source['source'] = doc_match.group(1).strip()
                        with open("document_metadata_log.txt", "a") as log_file:
                            log_file.write(f"Extracted document name from matched_content: {source['source']}\n")

                # If still no document name, try to find any filename pattern
                if not source.get('source') or source.get('source') == "" or source.get('source') == "Unknown Document":
                    # Try to find filename pattern
                    filename_match = re.search(r'(?:file|document)[:\s]+([A-Za-z0-9_\-\.]+\.[A-Za-z0-9]+)', source.get('content', ''), re.IGNORECASE)
                    if filename_match:
                        source['source'] = filename_match.group(1).strip()
                        with open("document_metadata_log.txt", "a") as log_file:
                            log_file.write(f"Extracted filename: {source['source']}\n")

                    # Try to extract from first line if it looks like a document name
                    if not source.get('source') or source.get('source') == "" or source.get('source') == "Unknown Document":
                        content_lines = source.get('content', '').split('\n')
                        if content_lines and len(content_lines[0]) > 5 and len(content_lines[0]) < 100:
                            # Check if first line looks like a document name (no special chars, reasonable length)
                            first_line = content_lines[0].strip()
                            if re.match(r'^[A-Za-z0-9_\-\.\s]+$', first_line) and not re.search(r'unknown|section|page|document', first_line, re.IGNORECASE):
                                source['source'] = first_line
                                with open("document_metadata_log.txt", "a") as log_file:
                                    log_file.write(f"Using first line as document name: {source['source']}\n")

                    # Try to extract from section_id if available
                    if (not source.get('source') or source.get('source') == "" or source.get('source') == "Unknown Document") and source.get('section_id'):
                        section_id = source.get('section_id')
                        if isinstance(section_id, str) and '_' in section_id:
                            # Extract document name from section_id if it follows a pattern like "doc_name_section_123"
                            doc_parts = section_id.split('_')
                            if len(doc_parts) >= 2:
                                potential_doc_name = '_'.join(doc_parts[:-1])  # Take all parts except the last one
                                if len(potential_doc_name) > 3:  # Ensure it's not too short
                                    source['source'] = potential_doc_name
                                    with open("document_metadata_log.txt", "a") as log_file:
                                        log_file.write(f"Extracted document name from section_id: {source['source']}\n")

            # Default document name if still not found
            if not source.get('source') or source.get('source') == "":
                source['source'] = "Unknown Document"
                with open("document_metadata_log.txt", "a") as log_file:
                    log_file.write("Using default document name: Unknown Document\n")

            # Ensure source has a proper title
            if not source.get('title') or source.get('title') == "":
                # Try to extract title from content
                if 'content' in source:
                    title_match = re.search(r'(?:Section|Title)[:\s]+([^,<>\(\)\n]+)', source.get('content', ''), re.IGNORECASE)
                    if title_match:
                        source['title'] = title_match.group(1).strip()
                        with open("document_metadata_log.txt", "a") as log_file:
                            log_file.write(f"Extracted title: {source['title']}\n")
                    else:
                        # Use first line of content as title if no title found
                        content_lines = source.get('content', '').split('\n')
                        if content_lines and len(content_lines[0]) > 5 and len(content_lines[0]) < 100:
                            source['title'] = content_lines[0].strip()
                            with open("document_metadata_log.txt", "a") as log_file:
                                log_file.write(f"Using first line as title: {source['title']}\n")
                        else:
                            source['title'] = "Unknown Section"
                            with open("document_metadata_log.txt", "a") as log_file:
                                log_file.write("Using default title: Unknown Section\n")
                else:
                    source['title'] = "Unknown Section"
                    with open("document_metadata_log.txt", "a") as log_file:
                        log_file.write("No content available, using default title: Unknown Section\n")

            # Ensure source has a proper page number
            if not source.get('page_number'):
                # Try to extract page number from content
                if 'content' in source:
                    page_match = re.search(r'Page\s+(\d+)', source.get('content', ''), re.IGNORECASE)
                    if page_match:
                        try:
                            source['page_number'] = int(page_match.group(1))
                            with open("document_metadata_log.txt", "a") as log_file:
                                log_file.write(f"Extracted page number: {source['page_number']}\n")
                        except ValueError:
                            source['page_number'] = 1
                            with open("document_metadata_log.txt", "a") as log_file:
                                log_file.write("Invalid page number, using default: 1\n")
                    else:
                        source['page_number'] = 1
                        with open("document_metadata_log.txt", "a") as log_file:
                            log_file.write("No page number found, using default: 1\n")
                else:
                    source['page_number'] = 1
                    with open("document_metadata_log.txt", "a") as log_file:
                        log_file.write("No content available, using default page number: 1\n")

            # Log final metadata
            with open("document_metadata_log.txt", "a") as log_file:
                log_file.write(f"Final source metadata: {source.get('source', 'None')}\n")
                log_file.write(f"Final title: {source.get('title', 'None')}\n")
                log_file.write(f"Final page_number: {source.get('page_number', 'None')}\n")

            # Ensure content is properly formatted
            if 'content' in source and isinstance(source['content'], str):
                # Remove any problematic characters
                source['content'] = source['content'].replace('\u0000', '')

                # Format tables with HTML table tags if they exist
                # Look for table-like structures and convert them to HTML tables
                if '|' in source['content']:
                    # Log the content for debugging
                    with open("table_detection_log.txt", "a") as log_file:
                        log_file.write(f"\n\n--- CONTENT WITH POTENTIAL TABLES ---\n")
                        log_file.write(source['content'][:1000])
                        log_file.write("\n--- END CONTENT ---\n")

                    # Simple approach: If we see a line with multiple pipe characters,
                    # treat it as a potential table and convert it
                    lines = source['content'].split('\n')
                    potential_table_lines = []
                    in_table = False
                    new_content = []

                    for line in lines:
                        # Check if this line looks like a table row (has multiple pipe characters)
                        if '|' in line and line.count('|') >= 2:
                            # This might be a table row
                            if not in_table:
                                in_table = True
                            potential_table_lines.append(line)
                        else:
                            # Not a table row
                            if in_table and potential_table_lines:
                                # We were in a table, but now we're not
                                # Convert the collected table lines to HTML
                                html_table = self.convert_ascii_table_to_html(potential_table_lines)
                                new_content.append(html_table)
                                potential_table_lines = []
                                in_table = False
                            new_content.append(line)

                    # Handle any remaining table lines
                    if in_table and potential_table_lines:
                        html_table = self.convert_ascii_table_to_html(potential_table_lines)
                        new_content.append(html_table)

                    # Update the content
                    source['content'] = '\n'.join(new_content)

                    # Log the updated content for debugging
                    with open("table_detection_log.txt", "a") as log_file:
                        log_file.write(f"\n\n--- UPDATED CONTENT WITH HTML TABLES ---\n")
                        log_file.write(source['content'][:1000])
                        log_file.write("\n--- END UPDATED CONTENT ---\n")

        prompt = '''
            <SOURCES>
                {sources}
            </SOURCES>
            <QUESTION>
                {question}
            </QUESTION>
            <ORIGINAL_QUESTION>
                {original_question}
            </ORIGINAL_QUESTION>
            <QUOTED_TEXT>
                {quoted_text}
            </QUOTED_TEXT>
            <FOLLOW_UP_QUERY>
                {follow_up_query}
            </FOLLOW_UP_QUERY>
            <ROLE>
                Provide a detailed and professional response using the provided sources, ensuring alignment with industry standards in EPC project management. Avoid adopting a personal perspective or stating your role in the response.
                Your responses should reflect the knowledge, expertise, and decision-making processes typical of a professional in this role.
            </ROLE>
            <TASK>
                - Analyze the <SOURCES> to answer the <QUESTION> which was derived from the <ORIGINAL_QUESTION>.
                - Use only the provided sources to formulate your answer.
                - Ignore any irrelevant sources.
                - Cite the sources used in your answer by mentioning their document names or titles.
                - IMPORTANT: Always use the exact document name from the source metadata (the 'source' field) in your citations.
                - When available, include document type, discipline, and document number in your citations.
                - If document references are available in the metadata, mention them when relevant to the answer.
                - For each source you use, make sure to clearly identify the document name, section, and page number.
                - Do not quote the sources directly; instead, paraphrase and cite them.
                - Provide a convincing answer in one or more paragraphs.
                - Include as many relevant details as possible.
                - If multiple values exist for a query demanding a singular value, mention all of them.
                - Present information confidently without apologizing.
                - Extract concise and relevant subtexts or paragraphs from the content of each <Section_Ids> used in the answer.
                - Include only the portions that directly support the answer, maintaining logical flow and context.
                - Exclude irrelevant or redundant information.
                - Separate the extracted content for each <Section_Ids> with $$$$$.
                - Arrange the extracted content in the <Source_answer> tag, following the order of the <Section_Ids> used.
                - Each section should start with a bold title formatted as:
                    <h3 style="font-weight: bold; font-size: 16px;">Document Name: <span style="font-style: italic; font-weight: normal;">(<document_name>)</span>, Section <section_number> <span style="font-style: italic; font-weight: normal;">(<title_of_section>)</span>, Page <page_number></h3>
                - If document type and discipline are available, include them in the header:
                    <h3 style="font-weight: bold; font-size: 16px;">Document Name: <span style="font-style: italic; font-weight: normal;">(<document_name>)</span>, Type: <document_type>, Discipline: <discipline>, Document Number: <document_number>, Section <section_number> <span style="font-style: italic; font-weight: normal;">(<title_of_section>)</span>, Page <page_number></h3>
                - Strictly pick the `section_id` key, from the sources, do not pick the `id` key.
                - Each content in the Source_answer_headers & the Source_answer_contents should be seperated by a HTML break.
                - For empty sources add a message saying "No sources found" instead of empty string
                - If the <QUOTED_TEXT> is not empty, then the answer should be in the context of the <QUOTED_TEXT>. In this case, also use the <FOLLOW_UP_QUERY> as the real question. The <ORIGINAL_QUESTION> just serves as a background.
                - For follow-up queries (when <QUOTED_TEXT> and <FOLLOW_UP_QUERY> are provided), ensure that:
                  1. All relevant sources are included in the response
                  2. Document names, sections, and page numbers are properly displayed
                  3. The response is properly formatted with clear attribution to sources
                  4. The selected text (<QUOTED_TEXT>) is used as context for answering the follow-up query
            </TASK>
            <ANSWER FORMAT>
                Provide your answer in the following format:
                <answer>
                Your detailed answer here, citing sources as needed.
                </answer>
                <Section_Ids>
                    section_id1, section_id2, ...
                </Section_Ids>
                <Source_answer_headers>
                        <h3 style="font-weight: bold; font-size: 16px;">
                            Document Name: <span style="font-style: italic; font-weight: normal;">(`document_name`)</span>,
                            Section `section_number` <span style="font-style: italic; font-weight: normal;">(`title_of_section`)</span>,
                            Page `page_number`
                        </h3>
                        <h3 style="font-weight: bold; font-size: 16px;">
                            Document Name: <span style="font-style: italic; font-weight: normal;">(`document_name_2`)</span>,
                            Section `section_number_2` <span style="font-style: italic; font-weight: normal;">(`title_of_section_2`)</span>,
                            Page `page_number_2`
                        </h3>
            </Source_answer_headers>
                <Source_answer_contents>
                    "Extracted subtext from section `section_id1` relevant to the answer."<br/>
                    "Extracted subtext from section `section_id2` relevant to the answer."
                </Source_answer_contents>

            </ANSWER FORMAT>
            <FEEDBACK>
                - If the provided sources has accuracy of 50% and above to fully answer the question, return: **"FLAG: SOURCES SUFFICIENT"**.
                - If the sources are not enough and has low accuracy, return: **"FLAG: SOURCES INSUFFICIENT"**.
            </FEEDBACK>
            <RECOMMENDATION>
                - If the sources are insufficient, return: **"Consult an EPC Engineer for further clarification and industry-specific insights."**
                - If the sources are sufficient, return: **"No additional consultation needed."**
            </RECOMMENDATION>
            <IMPORTANT>
                - Each content in the Source_answer_headers & the Source_answer_contents should be seperated by a HTML break
                - Do not mention section IDs in the answer part.
                - A section_id should be picked only from the `section_Id` key, so pick the `section_Id` key and not the `id` key
                - Include section IDs used directly or indirectly in your answer only in the <Section_Ids> tag.
                - Sort the section IDs according to their appearance in your answer.
                - Your answer should strictly be in font size equivalent to 11pt on word document.
                - The spacing between any paragraphs or points in your answer should be minimal and not contain any newline(`\n`)
                - The <FEEDBACK> section should contain only one of the two flags and nothing else.
                - Each content in the Source_answer_headers & the Source_answer_contents should be seperated by a HTML break
                - (Strictly) Do not state your role in the response.
                - Make sure all document names, section titles, and page numbers are clearly visible in the source headers.
                - CRITICAL: Always use the exact document name from the source metadata (the 'source' field) in your citations and headers.
                - For tables in the content, ensure they are properly formatted with HTML table tags.
            </IMPORTANT>

        NOTE, THIS IS VERY CRITICAL: If no relevant sources are found for the query OR if the found sources don't relate to the query in any meaningful way, respond with a properly formatted HTML message indicating this situation.
        example query : "Hello"
                    RESPONSE FORMAT THIS SCENARIO:
                    <answer>
                        Unfortunately, I couldn't find any information about "[insert user's query]" in the available documentation. Would you like me to help you search for something else?
                    </answer>

                    ALTERNATIVE FORMATS (feel free to use these or create similar variations):
                    <answer>
                        The documentation doesn't contain specific information related to "[insert user's query]". Would you like me to check for related topics instead?
                    </answer>

                    <answer>
                        While I found some sources, they don't contain relevant information about "[insert user's query]". Please let me know if you'd like to refine your search.
                    </answer>

                    IMPORTANT:
                    - Include the user's actual query in your response
                    - Keep responses concise and helpful
                    - Don't mention what search process was used
                    - Don't apologize unnecessarily
                    - Maintain the HTML format with the <answer> tags
        '''
        formatted_prompt = [{"role": "user", "content": prompt.format(sources=sources, question=question, original_question=original_question, quoted_text=quoted_text, follow_up_query=follow_up_query)}]
        # print('done formatting prompt...')
        class ResponseInfo(BaseModel):
            Answer: str = Field(description="The main response generated, structured as a well-reasoned and professional answer.")
            Recommendation: str = Field(description="Indicates whether additional consultation is needed based on source sufficiency.")
            Feedback: str = Field(description="Indicates whether the provided sources were sufficient or insufficient.")
            SourceAnswerHeaders: List[str] = Field(description="List of headers for the referenced source documents.")
            SourceAnswerContents: List[str] = Field(description="List of extracted content from source documents relevant to the query.")
            SectionIds: List[str] = Field(description="List of section IDs from the sources used in the answer.")

            @field_validator("SourceAnswerHeaders", "SourceAnswerContents", "SectionIds", mode="before")
            @classmethod
            def ensure_list(cls, value: Union[str, List[str]]) -> List[str]:
                """Ensures the field is always a list, converting stringified lists or <br/> separated strings."""

                # Debugging: Print the raw value before validation
                print(f"Validating field with raw value: {repr(value)} (Type: {type(value)})")

                if isinstance(value, list):
                    print(f"✅ Value is already a valid list")
                    return value  # Already a list, return as-is

                if isinstance(value, str):
                    # Attempt JSON parsing if the string is a stringified list
                    try:
                        parsed_value = json.loads(value)
                        if isinstance(parsed_value, list):
                            print(f"🔄 Converted stringified list to valid list")
                            return parsed_value
                    except json.JSONDecodeError:
                        print(f"⚠️ JSON parsing failed, checking for <br/> separator...")

                    # If JSON parsing fails, try splitting using either "<br/>" or "<br>" as delimiters
                    if "<br/>" in value or "<br>" in value:
                        # Replace both types of breaks with a consistent delimiter
                        normalized_value = value.replace("<br/>", "|||").replace("<br>", "|||")
                        split_values = [v.strip() for v in normalized_value.split("|||") if v.strip()]
                        print(f"🔄 Split string using <br/> or <br>")
                        return split_values

                    # If it's just a single string without a separator, wrap it in a list
                    print(f"🔄 Wrapped single string into list")
                    return [value]

                # If an unexpected type is received, raise an error
                raise ValueError(f"❌ Expected a list or string, but received {type(value)}: {value}")


        completion = await self.claude_client.generate_message_agent_sonnet_v2(ResponseInfo, formatted_prompt, 0.01)
        if completion and hasattr(completion, 'content'):
            for content_item in completion.content:
                if hasattr(content_item, 'input'):
                    response = content_item.input
                    master_response = ResponseInfo(**response)
                    break


        # print('received response from claude formatted...')
        source_answer_header  =  master_response.SourceAnswerHeaders
        SourceAnswerContents  =  master_response.SourceAnswerContents
        #source_answer_header  = [item.lstrip('"\\').rstrip('"') for item in master_response.SourceAnswerHeaders]
        #SourceAnswerContents  = [item.lstrip('"\\').rstrip('"') for item in master_response.SourceAnswerContents]

        # print(f"New source answer : {source_answer_header}")
        # print(f"New answer content : {SourceAnswerContents}")
        new_response = {
            'Answer': master_response.Answer,
            'SourceAnswerHeaders': source_answer_header,
            'SourceAnswerContents': SourceAnswerContents,
            'SectionIds': master_response.SectionIds,
            'Recommendation': master_response.Recommendation,
            'Feedback': master_response.Feedback,
        }
        # print('new response by claude: ', json.dumps(new_response))

        # Log source details after all processing is complete
        # self.log_source_details(sources, "AFTER_ALL_PROCESSING")

        # Log the final answer for debugging
        with open("final_answer_log.txt", "a") as log_file:
            log_file.write("\n\n--- FINAL ANSWER ---\n")
            log_file.write(f"Question: {question}\n\n")
            log_file.write(f"Answer: {new_response['Answer'][:1000]}...\n")
            log_file.write(f"Section IDs: {new_response['SectionIds']}\n")
            log_file.write(f"Feedback: {new_response['Feedback']}\n")
            log_file.write("--- END FINAL ANSWER ---\n")

        return new_response

        # completion = await self.claude_client.generate_message_agent_sonnet_new(
        #     [{"role": "user", "content": formatted_prompt}]
        # )
        # return (completion.content[0].text, sources)

    def get_answer_claude_sync(self, sources, question, original_question="", quoted_text="", follow_up_query = "", file_names=None, language="English"):

        prompt = '''
            <SOURCES>
                {sources}
            </SOURCES>
            <FILE_NAMES>
                {file_names}
            </FILE_NAMES>
            <QUESTION>
                {question}
            </QUESTION>
            <ORIGINAL_QUESTION>
                {original_question}
            </ORIGINAL_QUESTION>
            <QUOTED_TEXT>
                {quoted_text}
            </QUOTED_TEXT>
            <FOLLOW_UP_QUERY>
                {follow_up_query}
            </FOLLOW_UP_QUERY>

            Provide a detailed and professional response using the provided sources, ensuring alignment with industry standards in EPC project management. Avoid adopting a personal perspective or stating your role in the response.

            Guidelines:
            - Answer in the {language} language.
            - Analyze the sources to answer the question which was derived from the original question
            - There are different sources, one for bids, one for package, and for projects. Each with their prefixes in the section_id, bid, pkg, and proj in the section_id of sources. Ensure to use the relevant source for the question
            - When there is more than one source, ensure you show the source and type of source, either bid, package, or project
            - Use only the provided sources to formulate your answer
            - Ignore any irrelevant sources
            - IMPORTANT: Always use the exact document name from the source metadata (the 'source' field) in your citations
            - When available, include document type, discipline, and document number in your citations
            - If document references are available in the metadata, mention them when relevant to the answer
            - For each source you use, make sure to clearly identify the document name and page number
            - Do in-line citations for the sources used in the answer (document name and page - xxxx_xxxx_xxxx.pdf - p.xxxx)
            - Provide a convincing answer in one or more paragraphs
            - Include as many relevant details as possible
            - If multiple values exist for a query demanding a singular value, mention all of them
            - Present information confidently without apologizing
            - Extract texts or paragraphs from the content used in the answer
            - Include all the portions of the source content that directly support the answer, maintaining logical flow and context
            - Exclude irrelevant or redundant information
            - Separate the extracted content with $$$$$
            - Each content should start with a bold title formatted as:
                <h3 style="font-weight: bold; font-size: 16px;">
                Document Name: <span style="font-style: italic; font-weight: normal;">(`source`)</span>, 
                Section: <span style="font-style: italic; font-weight: normal;">(`section gotten from source content heading, title, or hierarchical numbering system`)</span>, 
                Page: <span style="font-style: italic; font-weight: normal;">(`page_number`)</span>
                </h3>
            - If document type and discipline are available, include them in the header:
                <h3 style="font-weight: bold; font-size: 16px;">
                    Document Name: <span style="font-style: italic; font-weight: normal;">(`document_name_2`)</span>, 
                    Type: <document_type>, 
                    Discipline: <discipline>, 
                    Document Number: <document_number>, 
                    Page: <page_number>
                </h3>
            - Each content in the Source_answer_headers & the Source_answer_contents should be separated by a HTML break <br>
            - For empty sources add a message saying "No sources found" instead of empty string
            - For each source, ensure you show at least three lines of content
            - Do not repeat sources
            - If the user asks questions related to files, ensure you display the full file name from the FILE_NAMES tag
            - Remove any unnecessary spacing in the content and sources
            - If the question contains words like compare, Ensure you compare the relevant sources and make the comparison
            - If the question contains words like list, ensure you list the relevant sources and make the list
            - Never include the same source in the <Source_answer_headers> and <Source_answer_contents> tags.
            - Never use any source that look like table of contents.
            - If there is more than one type of source, ensure you show sources from all types of files, not only one
            - If the user asks any question related to files, such as how many files are in this bid, ensure you use the file names from the FILE_NAMES tag, and use the correct file type for the answer
            - If the QUOTED_TEXT is not empty, then the answer should be in the context of the QUOTED_TEXT. In this case, also use the FOLLOW_UP_QUERY as the real question. The ORIGINAL_QUESTION just serves as a background
            - For follow-up queries (when QUOTED_TEXT and FOLLOW_UP_QUERY are provided), ensure that:
                1. All relevant sources are included in the response
                2. Document names and page numbers are properly displayed
                3. The response is properly formatted with clear attribution to sources
                4. The selected text (QUOTED_TEXT) is used as context for answering the follow-up query

            <ROLE>
                Provide a detailed and professional response using the provided sources, ensuring alignment with industry standards in EPC project management. Avoid adopting a personal perspective or stating your role in the response.
                Your responses should reflect the knowledge, expertise, and decision-making processes typical of a professional in this role.
                
            </ROLE>
            <TASK>
                - Analyze the <SOURCES> to answer the <QUESTION> which was derived from the <ORIGINAL_QUESTION>.
                - There are different sources, one for bids, one for package, and for projecs. Each with their prefixes in the section_id, bid, pkg, and proj in the section_id of <SOURCES>. Ensure to use the relevant source for the question.
                - When there is more than one source, ensure you show the source and type of source, either bid, package, or project.
                - Use only the provided sources to formulate your answer.
                - Maintain the terminology and language used in the sources.
                - Ignore any irrelevant sources.
                - Cite the sources used in your answer by mentioning their document names (i.e. xxxxx_xxxx_xxxx.pdf) and or titles. 
                - IMPORTANT: Always use the exact document name from the source metadata (the 'source' field) in your citations.
                - When available, include document type, discipline, and document number in your citations.
                - If document references are available in the metadata, mention them when relevant to the answer.
                - For each source you use, make sure to clearly identify the document name, section (if present), and page number.
                - Do in-line citations for the sources used in the answer (document name and page - xxxx_xxxx_xxxx.pdf - p.xxxx).
                - Provide an answer in one or more paragraphs.
                - Include only relevant details.
                - If multiple values exist for a query demanding a singular value, mention all of them.
                - Present information confidently without apologizing.
                - Extract texts or paragraphs from the content of each <Section_Ids> used in the answer.
                - Include all the portions of the source content that directly support the answer, maintaining logical flow and context, if it seems incomplete, you can add starting or ending ellipses to indicate possible continuation.
                - Exclude irrelevant or redundant information.
                - Separate the extracted content for each <Section_Ids> with $$$$$.
                - Arrange the extracted content in the <Source_answer> tag, following the order of the <Section_Ids> used.
                - Each section should start with a bold title formatted as:
                    <h3 style="font-weight: bold; font-size: 16px;">Document Name: <span style="font-style: italic; font-weight: normal;">(<document_name>)</span>, Section <section_number> <span style="font-style: italic; font-weight: normal;">(<title_of_section>)</span>, Page <page_number></h3>
                - If document type and discipline are available, include them in the header:
                    <h3 style="font-weight: bold; font-size: 16px;">Document Name: <span style="font-style: italic; font-weight: normal;">(<document_name>)</span>, Type: <document_type>, Discipline: <discipline>, Document Number: <document_number>, Section <section_number> <span style="font-style: italic; font-weight: normal;">(<title_of_section>) [inferred from the content instead of a programmatic section number]</span>, Page <page_number></h3>
                - Strictly pick the `section_id` key, from the sources, do not pick the `id` key.
                - Each content in the Source_answer_headers & the Source_answer_contents should be seperated by a HTML break.
                - For empty sources add a message saying "No sources found" instead of empty string
                - For each source, ensure you show at least three lines of content.
                - If the user asks questions related to files, ensure you display the full file name from the <FILE_NAMES> tag.
                - Remove any unnecessary spacing in the content and sources.
                - If the question contains words like compare, Ensure you compare the relevant sources and make the comparison. Such as comparing the bid files with the package files or project files.
                - If the question contains words like list, ensure you list the relevant sources and make the list. Such as listing the bid files with the package files or project files.
                - If there is more than one type of source in <SOURCES>, ensure you show sources from all types of files, not only one.
                - If the user asks any question related to files, such as how many files are in this bid, ensure you use the file names from the <FILE_NAMES> tag, and use the correct file type for the answer, such as bid_files for bids, package_files for packages, and project_files for projects.
                - If the <QUOTED_TEXT> is not empty, then the answer should be in the context of the <QUOTED_TEXT>. In this case, also use the <FOLLOW_UP_QUERY> as the real question. The <ORIGINAL_QUESTION> gave the context for the <QUOTED_TEXT>.
                - For follow-up queries (when <QUOTED_TEXT> and <FOLLOW_UP_QUERY> are provided), ensure that:
                  1. All relevant sources are included in the response
                  2. Document names, sections, and page numbers are properly displayed
                  3. The response is properly formatted with clear attribution to sources
                  4. The selected text (<QUOTED_TEXT>) is used as context for answering the follow-up query
            </TASK>
            <ANSWER FORMAT>
                Provide your answer in the following format:
                <answer>
                Your detailed answer here with in-line citations of sources by filename and page number if present as needed.
                </answer>

                <Section_Ids>
                    [section_id1, section_id2, ...] from <SOURCES> used in <answer>
                </Section_Ids>

                <Source_answer_headers>
                        <h3 style="font-weight: bold; font-size: 16px;">
                            Document Name: <span style="font-style: italic; font-weight: normal;">(`source`)</span>, Section: <span style="font-style: italic; font-weight: normal;">(`section gotten from source content heading, title, or hierarchical numbering system`)</span>, Page: <span style="font-style: italic; font-weight: normal;">(`page_number`)</span>
                        </h3> <br/>
                        <h3 style="font-weight: bold; font-size: 16px;">
                            Document Name: <span style="font-style: italic; font-weight: normal;">(`source`)</span>, Section: <span style="font-style: italic; font-weight: normal;">(`section gotten from source content heading, title, or hierarchical numbering system`)</span>, Page: <span style="font-style: italic; font-weight: normal;">(`page_number`)</span>
                        </h3> <br/>
                        Ensure you get the Document Name, Section, and Page Number from <SOURCES> used in <answer>.
                        - Document Name is supposed to be a filename including extension so present it as it is (xxxxx_xxx_xxx.pdf).
                        - Section : Pull section from the source content, if you identify a heading, title or hierarchical numbering system that is a section title from <SOURCES>
                        - Page Number is supposed to be a page number so present it as it is. 
                        - Ensure a consistent format for all document names, section titles, and page numbers.
                        - Ensure you return the answer as a list of strings.
                        - Ensure sections are gotten from the <SOURCES> and not from the <FILE_NAMES>.
                        - Ensure you show all sources.
                        - Ensure you show the type of source, either bid, package, or project.
                        - Remove any unnecessary spacing in the content and sources.
                        - Do not display any weird characters in the content and sources.
                        - Ensure you return the HTML in the correct format.
                </Source_answer_headers>
                <Source_answer_contents>
                    "Extracted subtext from section `section_id1` relevant to the answer from <SOURCES> used in <answer>" <br/>
                    "Extracted subtext from section `section_id2` relevant to the answer from <SOURCES> used in <answer>."
                    - Format table with HTML table tags you think should be a table.
                    - Ensure the subtext is at least three lines long and maximum of four lines. and gotten from <SOURCES>.
                    - Ensure multiple type of sources are shown, e.g bid, package, and project gotten from the section_id prefix in <SOURCES>.
                    - Ensure you show all sources used.
                    - Ensure you return the answer as a list of strings.
                    - Ensure you segment words that gets joined due to OCR errors Example: thefluidissohighlytoxicthatasingleexposureto should be segmented into: the fluid is so highly toxic that a single exposure to.
                    - If the question is related to files, ensure you use the file names from the <FILE_NAMES> tag, and use the correct file type for the answer, such as bid_files for bids, package_files for packages, and project_files for projects.
                    - Do not display any weird characters in the content and sources.
                    - Do not display '|' in the content and sources.
                </Source_answer_contents>
                
                Note: the <Section_Ids>, <Source_answer_headers>, and <Source_answer_contents> must be in the same order. we assume that the order of the section_ids is the same as the order of the source_answer_headers and source_answer_contents when extracting the answer.

            </ANSWER FORMAT>
            <FEEDBACK>
                - If the provided sources has accuracy of 50% and above to fully answer the question, return: **"FLAG: SOURCES SUFFICIENT"**.
                - If the sources are not enough and has low accuracy, return: **"FLAG: SOURCES INSUFFICIENT"**.
            </FEEDBACK>
            <RECOMMENDATION>
                - If the sources are insufficient, return: **"Consult an EPC Engineer for further clarification and industry-specific insights."**
                - If the sources are sufficient, return: **"No additional consultation needed."**
            </RECOMMENDATION>
            <IMPORTANT>
                - Each content in the Source_answer_headers & the Source_answer_contents should be seperated by a HTML break
                - Return the section ID as a list of strings.
                - Do not mention section IDs in the answer part.
                - A section_id should be picked only from the `section_Id` key, so pick the `section_Id` key and not the `id` key
                - Include section IDs used directly or indirectly in your answer only in the <Section_Ids> tag.
                - Sort the section IDs according to their appearance in your answer.
                - Document Name is supposed to be a filename including extension so present it as it is.
                - Your answer should strictly be in font size equivalent to 11pt on word document.
                - The spacing between any paragraphs or points in your answer should be minimal and not contain any newline(`\n`)
                - The <FEEDBACK> section should contain only one of the two flags and nothing else.
                - Each content in the Source_answer_headers & the Source_answer_contents should be seperated by a HTML break
                - (Strictly) Do not state your role in the response.
                - Make sure all document names, section titles, and page numbers are clearly visible in the source headers.
                - CRITICAL: Always use the exact document name from the source metadata (the 'source' field) in your citations and headers.
                - For tables in the content, ensure they are properly formatted with HTML table tags.
                - Do not mention any tags in the response or that it was derived for any tag
                - Show all sources.
                - ONLY USE <FILES> IF THE QUESTION ASKS, FOR EXAMPLE, "HOW MANY FILES ARE IN THIS BID?" OR "HOW MANY FILES ARE IN THIS PACKAGE?".
                - Ensure the number of section_ids, source_answer_headers, and source_answer_contents are the same.
                - When there are more than one source type in the <SOURCES>, ensure you show what type of source it is, e.g bid source, pakcage source, project source.
                - Never repeat the same source in the <Source_answer_headers> and <Source_answer_contents> tags.
            </IMPORTANT>

        NOTE, THIS IS VERY CRITICAL: If no relevant sources are found for the query OR if the found sources don't relate to the query in any meaningful way, respond with a properly formatted HTML message indicating this situation.
        example query : "Hello"
                    RESPONSE FORMAT THIS SCENARIO:
                    <answer>
                        Unfortunately, I couldn't find any information about "[insert user's query]" in the available documentation. Would you like me to help you search for something else?
                    </answer>

                    ALTERNATIVE FORMATS (feel free to use these or create similar variations):
                    <answer>
                        The documentation doesn't contain specific information related to "[insert user's query]". Would you like me to check for related topics instead?
                    </answer>

                    <answer>
                        While I found some sources, they don't contain relevant information about "[insert user's query]". Please let me know if you'd like to refine your search.
                    </answer>

                    IMPORTANT:
                    - Include the user's actual query in your response
                    - Keep responses concise and helpful
                    - Don't mention what search process was used
                    - Don't apologize unnecessarily
                    - Maintain the HTML format with the <answer> tags
                    - Answer in the {language} language.
        '''

        # Convert sources to a JSON string to avoid format issues with curly braces in content
        # sources_str = json.dumps(sources, indent=2) # Using indent for potential debugging

       
        for i, source in enumerate(sources):
            print(f"Source {i+1}:")
            print(f"  Section ID: {source.get('section_id', 'N/A')}")
            print(f"  Source: {source.get('source', 'N/A')}")
            print(f"  Score: {source.get('score', 'N/A')}")
            print("-" * 40)

        # Step 3: Filter sources
        sources = self.filter_top_k_sources_by_median(sources, k=30, median_threshold=0.65)
        print("Sources being used for answer generation:")
        for i, source in enumerate(sources):
            print(f"Source {i+1}:")
            print(f"  Section ID: {source.get('section_id', 'N/A')}")
            print(f"  Source: {source.get('source', 'N/A')}")
            print(f"  Score: {source.get('score', 'N/A')}")
            print("-" * 40)
        
        sources = [{
            'section_id': source.get('section_id', ''), 
            'source': source.get('source', ''), 
            'page_number': source.get('page_number', ''),
            'content': source.get('content', ''),
            'matched_content': source.get('matched_content', ''),
            'title': source.get('title', ''),
            } for source in sources]
        
        formatted_prompt_content = prompt.format(
            sources=sources, 
            question=question,
            original_question=original_question,
            quoted_text=quoted_text,
            follow_up_query=follow_up_query,
            file_names=file_names,
            language=language
        )
        formatted_prompt = [{"role": "user", "content": formatted_prompt_content}]

        # Define the ResponseInfo model (same as async version)
        class ResponseInfo(BaseModel):
            Answer: str = Field(description="The main response generated, structured as a well-reasoned and professional answer.")
            Recommendation: str = Field(description="Indicates whether additional consultation is needed based on source sufficiency.")
            Feedback: str = Field(description="Indicates whether the provided sources were sufficient or insufficient.")
            SourceAnswerHeaders: List[str] = Field(description="List of headers for the referenced source documents.")
            SourceAnswerContents: List[str] = Field(description="List of extracted content from source documents relevant to the query.")
            SectionIds: List[str] = Field(description="List of section IDs from the sources used in the answer.")

            @field_validator("SourceAnswerHeaders", "SourceAnswerContents", mode="before")
            @classmethod
            def ensure_list(cls, value: Union[str, List[str]]) -> List[str]:
                """Ensures the field is always a list, converting stringified lists or <br/> separated strings."""

                # Debugging: Print the raw value before validation
                print(f"Validating field with raw value: {repr(value)} (Type: {type(value)})")

                if isinstance(value, list):
                    print(f"✅ Value is already a valid list")
                    return value  # Already a list, return as-is

                if isinstance(value, str):
                    # Attempt JSON parsing if the string is a stringified list
                    try:
                        print(f'trying to parse the value...{value}')
                        parsed_value = json.loads(value)
                        if isinstance(parsed_value, list):
                            print(f"🔄 Converted stringified list to valid list")
                            return parsed_value
                    except json.JSONDecodeError:
                        print(f"⚠️ JSON parsing failed, checking for <br/> separator...")

                    # If JSON parsing fails, try splitting using either "<br/>" or "<br>" as delimiters
                    if "<br/>" in value or "<br>" in value:
                        # Replace both types of breaks with a consistent delimiter
                        normalized_value = value.replace("<br/>", "|||").replace("<br>", "|||")
                        split_values = [v.strip() for v in normalized_value.split("|||") if v.strip()]
                        print(f"🔄 Split string using <br/> or <br>")
                        return split_values

                    # If it's just a single string without a separator, wrap it in a list
                    print(f"🔄 Wrapped single string into list")
                    return [value]

                # If an unexpected type is received, raise an error
                raise ValueError(f"❌ Expected a list or string, but received {type(value)}: {value}")

            # Ensure section_ids is a list
            @field_validator("SectionIds", mode="before")
            @classmethod
            def ensure_section_ids_is_list(cls, value: Union[str, List[str]]) -> List[str]:
                if isinstance(value, list):
                    return value
                if isinstance(value, str):
                    try:
                        parsed_value = json.loads(value)
                        if isinstance(parsed_value, list):
                            return parsed_value
                    except json.JSONDecodeError:
                        print(f"❌ JSON parsing failed, checking for <br/> separator...")
                        
                    if "," in value:
                        split_values = [v.strip() for v in value.split(",") if v.strip()]
                        print(f"🔄 Split string using comma")
                        return split_values

                    if "<br/>" in value:
                        split_values = [v.strip() for v in value.split("<br/>") if v.strip()]
                        print(f"🔄 Split string using <br/>")
                        return split_values
                    return [value]
                        
                


        completion = self.claude_client.generate_message_agent_sonnet_v2_sync(ResponseInfo, formatted_prompt, 0.01)
        if completion and hasattr(completion, 'content'):
            for content_item in completion.content:
                if hasattr(content_item, 'input'):
                    response = content_item.input
                    master_response = ResponseInfo(**response)
                    break


        # print('received response from claude formatted...')
        source_answer_header  =  master_response.SourceAnswerHeaders
        SourceAnswerContents  =  master_response.SourceAnswerContents
        #source_answer_header  = [item.lstrip('"\\').rstrip('"') for item in master_response.SourceAnswerHeaders]
        #SourceAnswerContents  = [item.lstrip('"\\').rstrip('"') for item in master_response.SourceAnswerContents]

        # print(f"New source answer : {source_answer_header}")
        # print(f"New answer content : {SourceAnswerContents}")
        new_response = {
            'Answer': master_response.Answer,
            'SourceAnswerHeaders': source_answer_header,
            'SourceAnswerContents': SourceAnswerContents,
            'SectionIds': master_response.SectionIds,
            'Recommendation': master_response.Recommendation,
            'Feedback': master_response.Feedback,
        }
        print('new response by claude: ', json.dumps(new_response))

        # Log source details after all processing is complete
        # self.log_source_details(sources, "AFTER_ALL_PROCESSING_SYNC")

        # Log the final answer for debugging
        # with open("final_answer_log.txt", "a") as log_file:
        #     log_file.write("\n\n--- FINAL ANSWER (SYNC) ---\n")
        #     log_file.write(f"Question: {question}\n\n")
        #     log_file.write(f"Answer: {new_response['Answer'][:1000]}...\n")
        #     log_file.write(f"Section IDs: {new_response['SectionIds']}\n")
        #     log_file.write(f"Feedback: {new_response['Feedback']}\n")
        #     log_file.write("--- END FINAL ANSWER ---\n")

        return new_response

        # completion = await self.claude_client.generate_message_agent_sonnet_new(
        #     [{"role": "user", "content": formatted_prompt}]
        # )
        # return (completion.content[0].text, sources)


    async def get_answer_claude_v2(self, sources, question, existing_answer):
        prompt = '''
            <SOURCES>
                {sources}
            </SOURCES>
            <QUESTION>
                {question}
            </QUESTION>
            <EXISTING_ANSWER>
                {existing_answer}
            </EXISTING_ANSWER>
            <ROLE>
                You are an **EPC (Engineering, Procurement, and Construction) Project Engineer** assistant working for **Worley** or **Fulor**. Your responses should reflect the knowledge, expertise, and decision-making processes typical of a professional in this role, ensuring alignment with industry practices and the responsibilities associated with EPC project management.
            </ROLE>
            <TASK>
                - Analyze the <SOURCES> to answer the <QUESTION>.
                - Use only the provided sources to formulate your answer.
                - Ignore any irrelevant sources.
                - Cite the sources used in your answer by mentioning their document names or titles.
                - When available, include document type, discipline, and document number in your citations.
                - If document references are available in the metadata, mention them when relevant to the answer.
                - Do not quote the sources directly; instead, paraphrase and cite them.
                - Provide a convincing answer in one or more paragraphs.
                - Include as many relevant details as possible.
                - If multiple values exist for a query demanding a singular value, mention all of them.
                - Present information confidently without apologizing.
                - Extract concise and relevant subtexts or paragraphs from the content of each <Section_Ids> used in the answer.
                - Include only the portions that directly support the answer, maintaining logical flow and context.
                - Exclude irrelevant or redundant information.
                - Separate the extracted content for each <Section_Ids> with $$$$$.
                - Arrange the extracted content in the <Source_answer> tag, following the order of the <Section_Ids> used.
                - Each section should start with a bold title formatted as:
                    <h3 style="font-weight: bold; font-size: 16px;">Document Name: <span style="font-style: italic; font-weight: normal;">(<document_name>)</span>, Section <section_number> <span style="font-style: italic; font-weight: normal;">(<title_of_section>)</span>, Page <page_number></h3>
                - If document type and discipline are available, include them in the header:
                    <h3 style="font-weight: bold; font-size: 16px;">Document Name: <span style="font-style: italic; font-weight: normal;">(<document_name>)</span>, Type: <document_type>, Discipline: <discipline>, Document Number: <document_number>, Section <section_number> <span style="font-style: italic; font-weight: normal;">(<title_of_section>)</span>, Page <page_number></h3>
                - Strictly pick the `section_id` key, from the sources, do not pick the `id` key.
            </TASK>
            <ANSWER FORMAT>
                Provide your answer in the following format:
                <answer>
                Your detailed answer here, citing sources as needed.
                </answer>
                <Section_Ids>
                    section_id1, section_id2, ...
                </Section_Ids>
                <Source_answer>
                    <h3 style="font-weight: bold; font-size: 16px;">Document Name: <span style="font-style: italic; font-weight: normal;">(<document_name>)</span>, Section <section_number> <span style="font-style: italic; font-weight: normal;">(<title_of_section>)</span>, Page <page_number></h3>
                    The extracted sub text or paragraphs from the contents of each section_ids you've listed above.
                    $$$$$
                </Source_answer>
            </ANSWER FORMAT>
            <ANSWER_COMPARISON>
                - Compare the <EXISTING_ANSWER> and the newly generated answer based on:
                    - Completeness: Does it fully address the question?
                    - Accuracy: Does it align correctly with the sources?
                    - Relevance: Is it directly answering the question without unnecessary details?
                    - Clarity: Is it well-structured and easy to understand?
                - Based on this evaluation, determine which answer should be used.
            </ANSWER_COMPARISON>
            <ANSWER_COMPARISON_FLAG>
                - If the existing answer is better, return: **USE_EXISTING_ANSWER**
                - If the new answer is better, return: **USE_NEW_ANSWER**
                - If both answers provide useful information but neither is complete alone, return: **MERGE_BOTH_ANSWERS**
            </ANSWER_COMPARISON_FLAG>
            <FEEDBACK>
                - If the answer provided does not meet expectations or additional information is required, include a detailed note about the inaccuracies or the missing elements.
                - If the SOURCES provided meet expectation then the feedback section should not be in the returned response.

                    Detailed notes on inaccuracies or missing elements.
            </FEEDBACK>
            <IMPORTANT>
                - Do not mention section IDs in the answer part.
                - A section_id should be picked only from the `section_Id` key, so pick the `section_Id` key and not the `id` key
                - Include section IDs used directly or indirectly in your answer only in the <Section_Ids> tag.
                - Sort the section IDs according to their appearance in your answer.
                - Your answer should strictly be in font size equivalent to 11pt on word document.
                - The **ANSWER_COMPARISON_FLAG** should always be present in the response.
                - The spacing between any paragraphs or points in your answer should be minimal and not contain any newline(`\n`)
                - The Feedback section should strictly be added only if provided SOURCES does not give enough answer for the query.
            </IMPORTANT>
        '''

        formatted_prompt = prompt.format(sources=sources, question=question, existing_answer=existing_answer)

        completion = await self.claude_client.generate_message_agent_sonnet_new(
            [{"role": "user", "content": formatted_prompt}]
        )
        return (completion.content[0].text, sources)


    async def ai_clean_and_format_text(self, content, type):
        prompt = '''
            <HTML_CONTENT>
            {content}
            </HTML_CONTENT>
            <DOCUMENT_TYPE>
            {type}
            </DOCUMENT_TYPE>
            <TASK>
                You are an **EPC (Engineering, Procurement, and Construction) Project Engineer** assistant working for **Worley** or **Fulor**. Your task is to take the given HTML and replace any bad symbol:
                1. If any symbol is not properly shown or displayed then you should fix it by replacing it with the actual right symbol so it can be well displayed when viewed.
                2. Format the text correctly for readability. The response should not be choked up or too dense. Improve the formatting for readability by adjusting line breaks, spacing, and overall text structure where necessary.
                3. When referring to the documentation, use **'The Project Documentation'** if the document type is "project" and **'The Tender Documentation'** if the document type is "tender."
            </TASK>

            <IMPORTANT>
                - Formatting of the text should not change the meaning or initial writeup.
                - Formatting structure must be professional.
                - Remove any newline(\n) symbol noticed.
                - All spacing between any paragraphs or points should be minimal and not contain any newline(`\n`) character
                - Ensure the response does not contain extra explanations, apologies, or acknowledgments.
                - Respond with confidence and authority, like an experienced engineer with years of expertise.
            </IMPORTANT>

            <OUTPUT_FORMAT>
                Do not include any explanations or additional text outside of the given or formatted content.
            </OUTPUT_FORMAT>
            '''

        formatted_prompt = prompt.format(content=content, type=type)
        completion = await self.claude_client.generate_message_agent_haiku(
            [{"role": "user", "content": formatted_prompt}]
        )

        return completion.content[0].text

    async def claude_ai_clean_and_format_text(self, content, type):
        prompt = '''
            <HTML_CONTENT>
            {content}
            </HTML_CONTENT>
            <DOCUMENT_TYPE>
            {type}
            </DOCUMENT_TYPE>
            <TASK>
                You are an **EPC (Engineering, Procurement, and Construction) Project Engineer** assistant working for **Worley** or **Fulor**. Your task is to:
                1. Fix any improperly displayed symbols in the given HTML content.
                2. Improve the text's readability by formatting it correctly (line breaks, spacing, etc.), ensuring professionalism and minimal paragraph spacing.
                3. When referring to the documentation, use **'The Project Documentation'** if the document type is "project" and **'The Tender Documentation'** if the document type is "tender."
                4. Avoid any phrases like "EPC Project Engineer" or similar roles, and remove them.
            </TASK>

           <IMPORTANT>
                - Do not include any additional text, explanations, or preambles in your response.
                - Only return the reformatted text. No headings, context, or introductory phrases like "Here is the reformatted text."
                - Ensure the response does not contain extra explanations, apologies, or acknowledgments.
                - Respond with confidence and authority, like an experienced engineer with years of expertise.
            </IMPORTANT>
            <OUTPUT_FORMAT>
                Only return the formatted text, nothing else.
            </OUTPUT_FORMAT>
            '''
        formatted_prompt = prompt.format(content=content, type=type)
        try:
            completion = await self.claude_client.generate_message_agent_haiku(
                [{"role": "user", "content": formatted_prompt}]
            )
            if completion and hasattr(completion.content[0], 'text'):
                return completion.content[0].text

        except Exception as e:
            completion = await self.claude_client.generate_message_agent_haiku(
                [{"role": "user", "content": formatted_prompt}]
            )
            if completion and hasattr(completion.content[0], 'text'):
                return completion.content[0].text

    async def groq_ai_clean_and_format_text(self, content, type):
        prompt = '''
            <HTML_CONTENT>
            {content}
            </HTML_CONTENT>
            <DOCUMENT_TYPE>
            {type}
            </DOCUMENT_TYPE>
            <TASK>
                You are an **EPC (Engineering, Procurement, and Construction) Project Engineer** assistant working for **Worley** or **Fulor**. Your task is to:
                1. Fix any improperly displayed symbols in the given HTML content.
                2. Improve the text's readability by formatting it correctly (line breaks, spacing, etc.), ensuring professionalism and minimal paragraph spacing.
                3. When referring to the documentation, use **'The Project Documentation'** if the document type is "project" and **'The Tender Documentation'** if the document type is "tender."
                4. Avoid any phrases like "EPC Project Engineer" or similar roles, and remove them.
            </TASK>

           <IMPORTANT>
                - Do not include any additional text, explanations, or preambles in your response.
                - Only return the reformatted text. No headings, context, or introductory phrases like "Here is the reformatted text."
                - Ensure the response does not contain extra explanations, apologies, or acknowledgments.
                - Respond with confidence and authority, like an experienced engineer with years of expertise.
            </IMPORTANT>
            <OUTPUT_FORMAT>
                Only return the formatted text, nothing else.
            </OUTPUT_FORMAT>
            '''
        formatted_prompt = prompt.format(content=content, type=type)
        try:
            completion = self.fast_apis.generate_completion(
                [{"role": "user", "content": formatted_prompt}],
                'groq',
                'llama3-70b-8192'
            )
            if completion:
                return completion

        except Exception as e:
            completion = await self.claude_client.generate_message_agent_haiku(
                [{"role": "user", "content": formatted_prompt}]
            )
            if completion and hasattr(completion.content[0], 'text'):
                return completion.content[0].text

    async def extract_all_answer_index(self, text):
        # Define the regex pattern to match all <WEIGHTED_SCORE> tags and their content
        pattern = r'<Source_Index_Number>(.*?)<\/Source_Index_Number>'
        match = re.findall(pattern, text)
        result = []

        if len(match) > 0:
            numbers = match[0].split(",")
            result.extend([int(num.strip()) for num in numbers if num.strip().isdigit()])

        return result

    async def extract_answer_content(self, text):
        # Regex pattern to match content within <answer> tags
        pattern = r'<answer>(.*?)<\/answer>'
        match = re.search(pattern, text, re.DOTALL)
        if match:
            return match.group(1).strip()
        return None

    async def extract_feedback_content(self, text):
        # print('given text to extract feedback: ', text)
        # Regex pattern to match content within <answer> tags
        pattern = r'<FEEDBACK>(.*?)<\/FEEDBACK>'
        match = re.search(pattern, text, re.DOTALL)
        if match:
            return match.group(1).strip()
        return None

    async def extract_recommendation_content(self, text):
        pattern = r'<RECOMMENDATION>(.*?)<\/RECOMMENDATION>'
        match = re.search(pattern, text, re.DOTALL)
        if match:
            return match.group(1).strip()
        return None

    async def extract_all_answer_sectionIds(self, text):
        # print('this is text: ', text)
        # Adjust regex pattern to account for potential whitespace around the tags
        pattern = r'<Section_Ids>\s*(.*?)\s*<\/Section_Ids>'
        match = re.findall(pattern, text)
        result = []

        if len(match) > 0:
            ids = match[0].split(",")
            result.extend([id.strip() for id in ids if id.strip()])  # Ensure no leading/trailing spaces

        return result

    async def extract_source_answer_content(self, text):
        # Regex pattern to extract content inside <Source_answer> tags, including multiline content
        pattern = r'<Source_answer>(.*?)<\/Source_answer>'

        match = re.search(pattern, text, re.DOTALL)

        # print('this is match: ', match)
        if match:
            source_content = match.group(1).strip()
            sources = [content.strip() for content in source_content.split('$$$$$') if content.strip()]
            return sources

        return []

    def convert_newlines_to_br(self, input_text):
        # Replace occurrences of more than two consecutive newlines with a single <br>
        input_text = re.sub(r'\n{3,}', '<br/>', input_text)

        # Replace exactly two consecutive newlines with two <br> tags
        input_text = re.sub(r'\n{2}', '<br/><br/>', input_text)

        # Replace any single newline with one <br> tag
        input_text = re.sub(r'\n', '<br/>', input_text)

        return input_text

    def remove_newlines(self, input_text):
        # Remove all newline characters from the input text
        return input_text.replace('\n', '')


    async def claude_ai_clean_and_format_sources(self, sources):
        print('sources in format agent: ', sources)
        prompt = '''
            <SOURCES_CONTENT>
                {sources}
            </SOURCES_CONTENT>
            <TASK>
                You are an **EPC (Engineering, Procurement, and Construction) Project Engineer** assistant working for **Worley** or **Fulor**. Your task is to format text into the following professional structure using HTML with **Tailwind CSS styling**:

                1. If any symbol is not properly shown or displayed, fix it by replacing it with the correct symbol for proper rendering.
                2. Improve readability by adding appropriate line breaks, spacing, and structure to avoid dense formatting.
                3a. The content of each section should appear below its title, indented properly to enhance clarity.
                3b. All the content should be strictly in *italic form*, and each subsection should have a **bold title** but remain in *italic format*.
                4. Symbols, special characters, or formatting issues should be corrected while preserving the original meaning.
                5. Ensure that all formatting is **professional and visually appealing**.
                6. Infer missing page numbers or section titles based on context; otherwise, use placeholders like `"Page X"` or `"Section X"`.
                7. Do not modify the **top heading text** inside the `<h3>` tag.

                <STYLE_REQUIREMENTS>
                    - Apply **Tailwind CSS** classes to style the output for **enhanced readability and aesthetics**.
                    - Text should have a **consistent font size, spacing, and structure**.
                    - All section headings (`<h3>`) should use `text-lg font-bold text-gray-900 mb-2` for emphasis.
                    - Italicized content should use `italic text-gray-700` for clarity.
                    - Subsection titles should be bold using `font-semibold text-gray-800`.
                    - Use `leading-relaxed` to maintain a comfortable **line height**.
                    - Ensure proper **padding (`p-2`) and margins (`mb-4`)** for spacing.
                    - Keep the layout **responsive and structured** for professional presentation.
                </STYLE_REQUIREMENTS>

                <IMPORTANT>
                    - Preserve the **original meaning** of the text.
                    - Focus on **clarity, structure, and professional formatting**.
                    - All subheadings must not exceed **11pt equivalent in a Word document**.
                    - Do not include any **extra text, explanations, or acknowledgments**.
                    - Respond with **confidence and precision**, like a seasoned EPC engineer.
                    - **Return only the formatted text**—no additional descriptions or preambles.
                    - Do not add any intro like saying `Here is the formatted Text`
                    - Write the response as Human Engineer as possible.
                </IMPORTANT>

                <OUTPUT_FORMAT>
                    Only return the formatted text with Tailwind CSS, nothing else.
                    Do not include explanations or additional text outside the formatted content.

                    <h3 class="text-lg font-bold text-gray-900 mb-2">Document Name:
                        <span class="italic font-normal">(<document_name>)</span>,
                        Section <section_number>
                        <span class="italic font-normal">(<title_of_section>)</span>,
                        Page <page_number>
                    </h3>

                    <div class="text-[14.6px] leading-relaxed text-gray-700 p-2 mb-4">
                        <content_of_the_section>
                    </div>
                </OUTPUT_FORMAT>
            '''
        formatted_prompt = prompt.format(sources=sources)
        try:
            completion = await self.claude_client.generate_message_agent_haiku(
                [{"role": "user", "content": formatted_prompt}]
            )

            if completion and hasattr(completion.content[0], 'text'):
                return completion.content[0].text
            # completion = self.fast_apis.generate_completion(
            #     [{"role": "user", "content": formatted_prompt}],
            #     'groq',
            #     'llama3-70b-8192'
            # )
            # if completion:
            #     print('completed formatted sources groq...')
            #     return completion.replace('\n', '')

        except Exception as e:
            completion = await self.claude_client.generate_message_agent_haiku(
                [{"role": "user", "content": formatted_prompt}]
            )
            if completion and hasattr(completion.content[0], 'text'):
                return completion.content[0].text


    async def groq_ai_clean_and_format_sources(self, sources):
        print('sources in format agent: ', sources)
        prompt = '''
            <SOURCES_CONTENT>
                {sources}
            </SOURCES_CONTENT>
            <TASK>
                You are an **EPC (Engineering, Procurement, and Construction) Project Engineer** assistant working for **Worley** or **Fulor**. Your task is to format text into the following professional structure using HTML with **Tailwind CSS styling**:

                1. If any symbol is not properly shown or displayed, fix it by replacing it with the correct symbol for proper rendering.
                2. Improve readability by adding appropriate line breaks, spacing, and structure to avoid dense formatting.
                3a. The content of each section should appear below its title, indented properly to enhance clarity.
                3b. All the content should be strictly in *italic form*, and each subsection should have a **bold title** but remain in *italic format*.
                4. Symbols, special characters, or formatting issues should be corrected while preserving the original meaning.
                5. Ensure that all formatting is **professional and visually appealing**.
                6. Infer missing page numbers or section titles based on context; otherwise, use placeholders like `"Page X"` or `"Section X"`.
                7. Do not modify the **top heading text** inside the `<h3>` tag.
                8. Replace all line breaks with HTML `<br>` elements, ensuring **balanced and structured formatting**.
                9. Ensure the response **does not contain any newline (`\n`) characters**, replacing them with appropriate HTML formatting.

                <STYLE_REQUIREMENTS>
                    - Apply **Tailwind CSS** classes to style the output for **enhanced readability and aesthetics**.
                    - Text should have a **consistent font size, spacing, and structure**.
                    - All section headings (`<h3>`) should use `text-lg font-bold text-gray-900 mb-2` for emphasis.
                    - Italicized content should use `italic text-gray-700` for clarity.
                    - Subsection titles should be bold using `font-semibold text-gray-800`.
                    - Use `leading-relaxed` to maintain a comfortable **line height**.
                    - Ensure proper **padding (`p-2`) and margins (`mb-4`)** for spacing.
                    - Keep the layout **responsive and structured** for professional presentation.
                </STYLE_REQUIREMENTS>

                <IMPORTANT>
                    - Preserve the **original meaning** of the text.
                    - Focus on **clarity, structure, and professional formatting**.
                    - All subheadings must not exceed **11pt equivalent in a Word document**.
                    - Do not include any **extra text, explanations, or acknowledgments**.
                    - Respond with **confidence and precision**, like a seasoned EPC engineer.
                    - **Return only the formatted text**—no additional descriptions or preambles.
                </IMPORTANT>

                <OUTPUT_FORMAT>
                    Only return the formatted text with Tailwind CSS, nothing else.

                    <h3 class="text-lg font-bold text-gray-900 mb-2">Document Name:
                        <span class="italic font-normal">(<document_name>)</span>,
                        Section <section_number>
                        <span class="italic font-normal">(<title_of_section>)</span>,
                        Page <page_number>
                    </h3>

                    <div class="text-[14.6px] leading-relaxed text-gray-700 p-2 mb-4">
                        <content_of_the_section>
                    </div>
                </OUTPUT_FORMAT>
            '''
        formatted_prompt = prompt.format(sources=sources)
        class FormatText(BaseModel):
            formatted_text: str

        try:
            completion = self.groq_client.extract_data(
                FormatText,
                [{"role": "user", "content": formatted_prompt}],
                'llama3-70b-8192'
            )
            if completion:
                print('completed formatted sources groq...')
                print(completion)
                return completion

        except Exception as e:
            print('groq failed now trying claude...')
            completion = await self.claude_client.generate_message_agent_haiku(
                [{"role": "user", "content": formatted_prompt}]
            )
            if completion and hasattr(completion.content[0], 'text'):
                return completion.content[0].text



    async def handle_query(self):
        from init import app
        with app.app_context():
            pending = Requirement.get_by(status='pending', type='qmp')
            if len(pending) >= 5:
                print('exiting qmp...')
                return

            related_project_ids = [
            '8cae6c9c-cf8f-4eb2-bb93-a0bb3d5c1e72',
                '1a0e8f66-5d87-41f9-bb93-1cb655f4a0f9',
                '1c31374d-4a4a-4a39-a0ee-372d99575ceb'
            ]
            return await self.answer_question_v1_1('23bccbb3-118a-4fe8-9367-77839bd89a96', related_project_ids)

        # files = sorted(Requirement.get_by(status='idle', type='qmp'), key=lambda x: x['tried'])
        files = sorted([item for item in Requirement.get_by(status='idle', type='qmp') if item['tried'] < 2], key=lambda x: x['tried'])

        # print('files to be handled...')
        # print(files)
        if len(files):
            if files[0]['tried'] < 2:
                await self.answer_question_v1_1(files[0]['id'])
        else:
            print("No Files In Queue!")

        return

    # Add this new method to the class
    def _merge_followup_sources(self, project_sources, package_sources, engineer_sources, vendor_sources, bid_sources_raw, sub_question):
        """Merges sources from project, package, and bid for follow-up queries."""
        print("MERGING SOURCES...   ")
        merged_sources = [{
            "source_map": {},
            "source_list": [],
            "questions": [sub_question]
        }]
        current_index = 0

        # Function to add sources to the merged list and map
        def add_to_merged(source_data, source_type_prefix=""):
            nonlocal current_index
            if source_data and 'source_list' in source_data[0]:
                for item in source_data[0]['source_list']:
                    # Ensure unique section_id or use index if needed
                    original_section_id = item.get('section_id')
                    # Prefix section_id to ensure uniqueness across source types if it's not already prefixed
                    if source_type_prefix and isinstance(original_section_id, (str, int)) and not str(original_section_id).startswith(source_type_prefix):
                         item['section_id'] = f"{source_type_prefix}_{original_section_id}"
                    else:
                         item['section_id'] = original_section_id if original_section_id is not None else f"{source_type_prefix}_{current_index}"

                    item['id'] = item.get('id', current_index) # Use existing or assign new id
                    merged_sources[0]['source_list'].append(item)

                    # Use the potentially updated section_id for the map key
                    map_key = item['section_id']
                    # Handle potential key collisions by appending index if necessary
                    collision_counter = 0
                    original_map_key = map_key
                    while map_key in merged_sources[0]['source_map']:
                        collision_counter += 1
                        map_key = f"{original_map_key}_collision_{collision_counter}"
                        item['section_id'] = map_key # Update section_id in the item as well if changed due to collision

                    merged_sources[0]['source_map'][map_key] = item.get('content', '') # Map key to content
                    current_index += 1

        # Add project sources
        if project_sources:
            add_to_merged(project_sources, "proj")

        # Add package sources
        if package_sources:
            add_to_merged(package_sources, "pkg")

        # Add vendor sources
        if vendor_sources:
            add_to_merged(vendor_sources, "vendor")

        # Add engineer sources
        if engineer_sources:
            add_to_merged(engineer_sources, "eng")



        # Format and add bid sources
        if bid_sources_raw and bid_sources_raw[0]: # Check if bid_sources_raw[0] is not empty
            bid_sources_formatted = [{
                "source_map": {},
                "source_list": [],
                "questions": [sub_question]
            }]
            bid_source_map = {}
            bid_sources_list = []
            # Note: bid_sources_raw[0] contains the list of [content, score] pairs
            for i, source_item in enumerate(bid_sources_raw[0]):
                # Use a specific prefix for bid sources
                section_id = f"bid_{i}"
                bid_source_map[section_id] = source_item[0]
                bid_sources_list.append({
                    "id": section_id, # Use section_id as id for simplicity here
                    "content": source_item[0],
                    "score": source_item[1],
                    "section_id": section_id,
                    "source": source_item[2], # Placeholder
                    "page_number": source_item[3]
                })
            bid_sources_formatted[0]['source_map'] = bid_source_map
            bid_sources_formatted[0]['source_list'] = bid_sources_list
            add_to_merged(bid_sources_formatted, "bid") # Pass "bid" prefix

        return merged_sources

    def get_project_files(self, project_id):
        # This method should return the project or package files based on the project_id
        # For now, let's return an empty list as a placeholder
        # Assuming you have the project_id

        # Fetch the project by ID
        project = Project.get_one_by(id=project_id)
        files_in_project = []
        if project:
            # Access the files related to the project
            files = project.files
            for file in files:
                  files_in_project.append(file.name)

        return files_in_project
    
    def get_bid_files(self, bid_id):
        # This method should return the bid files based on the bid_id
        # For now, let's return an empty list as a placeholder
        # Assuming you have the bid_id

        # Fetch the bid by ID
        bid = BidFile.get_by(bid_id=bid_id)
        files_in_bid = []
        if bid:
            # Access the files related to the bid
            for file in bid:
                  files_in_bid.append([file['name'], file['status']])

        return files_in_bid
    
    def filter_top_k_sources_by_median(self, sources, k=5, median_threshold=0.9):
        """
        Filters sources based on 90% of the median score and returns top-k by score.

        Args:
            sources (list): List of dicts with a 'score' key.
            k (int): Max number of sources to return.

        Returns:
            list: Top-k filtered and sorted sources.
        """
        valid_scores = [s.get('score', 0) for s in sources if s.get('score') is not None]

        if not valid_scores:
            return []

        median_score = statistics.median(valid_scores)
        threshold = median_threshold * median_score

        filtered = [s for s in sources if s.get('score', 0) >= threshold]

        return sorted(filtered, key=lambda s: s.get('score', 0), reverse=True)[:k]


if __name__ == "__main__":
    # processor = QMPQueryHandler("", True)
    # asyncio.run(processor.handle_query())
    
    processor = QMPQueryHandler(None, is_test=False)
    
    bid_id = "7b87d832-17f3-4f63-9ce9-6a8c127e4588"
    exist_bid = False
    break_cache = True
    quoted_text = "Consult an EPC Engineer for further clarification and industry-specific insights."
    follow_up_query = ""
    other_project_ids = []
    req_id = "27a75eb2-498c-4249-9f0d-3fc0787d62a3"
    
    # {"question":"Equipment specification",
    #  "exist_bid":false,
    #  "bid_id":"7b87d832-17f3-4f63-9ce9-6a8c127e4588",
    #  "project_id":"b80b409f-3887-420d-ab5a-6022388265e6",
    #  "package_id":"b3b8a2a9-838e-4439-891d-82d74094ad32",
    #  "other_project_ids":["e944336f-f49a-48f0-ae4d-cbaeeffde739","e677866a-0293-4d24-9ebe-e22a4b7d18f5","d75ab785-9c8b-4dc9-924e-f716381103bd","3d4e8dbe-9310-4500-ab7e-363a015535d5","4be7ee4b-7cdf-4d0a-80e5-89885b81e2a3","227af9c1-de62-4d85-b711-558f3ce122a8","a152907a-ba5d-4711-8a18-f5479f9af7da","60e9dccb-aa51-4a33-8d72-3ff0f0e36ef4","15a47ced-4290-427d-b7ed-d5ce0765157a","8af7f331-81e2-4868-8f4e-b9e4357a12c3","7f230375-5499-4646-87a4-647bf17b780a","2dd6a0aa-0d86-415c-9ac8-df3f6b632518","78fbb484-7f85-47ef-bbc8-5866bb6a8129","44752c4a-d15c-4bad-9aa3-20043a8a6e98","ecee921a-b020-4bea-9397-37956d996029","a5283b22-fcfd-4125-9e9a-9babfb056715","b73eab4c-be1c-4243-bb97-480fff373658","18f1c695-1e68-4857-8a0c-becd13a1fc4f","9f7100df-1219-4b9c-bf20-e97798c2a0b7","d4617a83-4078-4afa-8fd5-20849ce17b84","a0bfd990-f6d2-40af-a72a-a8fd64f76991","205934ec-62d8-477c-9df3-8b06c75f7b72","b14a613f-6370-47e2-b349-3279eb7a1e2d","438ead78-1c70-4b80-9305-88a68a335ec1","0151e9eb-6a91-4292-b44d-a6676817d74d","954753b5-8461-491b-a1fb-7b13e7f33ba9"],
    #  "quoted_text":"Consult an EPC Engineer for further clarification and industry-specific insights.",
    #  "follow_up_query":""}

    if bid_id:
        processor.set_should_query_existing_bid(exist_bid)
        processor.set_bid_id(bid_id)
    processor.set_break_cache(break_cache)
    processor.set_quoted_text(quoted_text)
    processor.set_follow_up_query(follow_up_query)

    greenlet = processor.process_query_sync(req_id, other_project_ids)
