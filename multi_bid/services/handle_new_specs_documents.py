import os
import sys
import json
import time
import asyncio
import re
from models import Requirement, Project
from services.reasoning_specs_comply import SpecsComplyReasoning
from services.process_document import FileProcessor
from services.data_handler import DataManager
from services.engr_doc_types_detection import EngrDocTypesDetection
from services.process_scp_dataset_1 import Dataset1Extractor
from services.process_scp_dataset_2 import Dataset2Extractor
from services.report_manager import ReportManager
from flask_socketio import emit
# from flask import current_app


class SpecsDocumentProcessor:
    def __init__(self, socket_manager, is_test=False):
        print('i am now called upon to process scp...')
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        env_file_path = os.path.join(parent_dir, 'env.json')

        with open(env_file_path, 'r') as f:
            self.env_data = json.load(f)
        
        self.socket_manager = socket_manager
        self.specs_reasoning = SpecsComplyReasoning()
        self.data_manager = DataManager()
        self.file_processor = FileProcessor()
        self.engr_doc_detector = EngrDocTypesDetection()
        self.dataset2_extractor = Dataset2Extractor(self.socket_manager)
        self.report_manager = None

    def process_scp(self, requirement_id):
        from flask import current_app
        asyncio.run(self.handle_single_new_document_v2(requirement_id))

    async def handle_single_new_document_v2(self, requirement_id):
        try:

            print('calling add event to send message to FE.')

            self.add_event(requirement_id , 'progress_message', {
                'event_type': 'SCP',
                'request_id': requirement_id ,
                'data': {'status': 'extracting_sources', 'message': f"Welcome to Specs Comply Assistant..."}
            })
            # return 

            print('i am now in scp class...')
            requirement = Requirement.get_single(requirement_id)
            
            # Check if the requirement status is already done
            if requirement['status'] == 'done':
                print(f"SCP Requirement with id: {requirement_id} is already done. Exiting...")
                return  # Exit if the requirement is already done
        

            await self._prepare_context(requirement)

            if requirement is None:
                self.handle_failed_request('', '', '')
            
            print(requirement)
            print('passed requirement successfully...')
            Requirement.update(requirement_id, status='pending')


            print('detect document category successfully.......')
            print(self.document_data)
            

            print('criterion.......')
            print(self.criterion)

            self.report_manager = ReportManager(
                self.env_data.get('SPECS_DIR'), self.project_id, self.requirement_id
            )
            self.report_manager.create_initial_report(self.initialize_log_structure(len(self.criterion)))
            log = self.report_manager.load_report()

            
            for count, criteria in enumerate(self.criterion, start=1):
                await self.process_single_criterion(criteria, count, log)
            
            # tasks = [
            #     asyncio.create_task(self.process_single_criterion(criteria, count, log))
            #     for count, criteria in enumerate(self.criterion, start=1)
            # ]

            # await asyncio.gather(*tasks)

            log['project_title'] = self.project['name']
            await self.specs_reasoning.get_claude_to_summarize_evaluate_v2(log)

            # return 
            #Todo: need to loop through the array and concat all items into the evaluate_summary key
            log['evaluate_summary']  = ''.join(log['evaluation_report_section']['evaluation_report'])
            log['evaluate_report'] = self.create_engineering_comments_table(
                log['evaluation_report_section']['engr_reference'],
                log['evaluation_report_section']['engr_comment']
            )

            # Emit final completion event
            self.add_event(self.requirement_id, 'completed_event', {
                'event_type': 'SCP',
                'request_id': self.requirement_id,
                'data': log
            })

            # self.save_report(log)
            self.report_manager.update_log(log)
            Requirement.update(requirement['id'], status='done')

        except Exception as e:
            print(f"Error returned: {e}")
            Requirement.update(requirement['id'], status='idle', tried=requirement["tried"] + 1)

    async def process_single_criterion(self, criteria, count, log):
        try:
            print('criteria: ', criteria)
            print('count : ', count)
            self.criteria = criteria
            print('i am log: ', log['criteria_array'])
            print('i am criterion: ', self.criterion)
            
            log['criteria_array'][count - 1] = self.criteria
            print(f"now insingle criteria {count} {self.criterion[count - 1]}....")
            # return
            self.add_event(self.requirement_id , 'progress_message', {
                'event_type': 'SCP',
                'request_id': self.requirement_id ,
                'data': {'status': 'extracting_sources', 'message': f"Starting analysis for Query {count}. Extracting relevant tender sources.", 'query_idx': count}
            })

            source_document_text = await self._extract_dataset1(self.criterion[count - 1], count, log)
            if source_document_text is None:
                self.handle_failed_request(self.requirement, "", "")
                return
            
            print(f'source document done for criteria {count} {self.criterion[count - 1]}....')
            # print(source_document_text)

            self.add_event(self.requirement_id , 'progress_message', {
                'event_type': 'SCP',
                'request_id': self.requirement_id ,
                'data': {'status': 'extracting_sources', 'message': f"Continuing analysis for Query {count}. Refining insights from the uploaded bid.", 'query_idx': count}
            })

            data = await self._extract_dataset2(self.criterion[count - 1], source_document_text, count, log)
            if not data:
                raise RuntimeError("Dataset 2 extraction failed")
            
            self.add_event(self.requirement_id , 'progress_message', {
                'event_type': 'SCP',
                'request_id': self.requirement_id ,
                'data': {'status': 'extracting_sources', 'message': f"Insights for Query {count} extracted. Preparing to analyze document chunks.", 'query_idx': count}
            })

            chunks = await self._get_chunks_to_process(count, log)
            log['chunk_to_pass'][count-1] = chunks

            print(f'i am here for criteria {count} {criteria}...')

            evaluate_text = f"<CRITERIA>Criteria {count}<CRITERIA><br/>"
            evaluate_text_2 = ""
            evaluate_reference = ""
            for evaluate in chunks:
                evaluate_text += f"""
                    <Evaluation>{evaluate['evaluation_data']['Evaluation']}</Evaluation><br/>
                    <Score>{evaluate['evaluation_data']['Score']}</Score><br/>
                    <Reason>{evaluate['evaluation_data']['Reason']}</Reason><br/>
                    <Reference>{evaluate['evaluation_data']['Reference']}</Reference><br/>
                """
                evaluate_text_2 += f"""
                    <Reason>{evaluate['evaluation_data']['Reason']}</Reason><br/>
                    <Reference>{evaluate['evaluation_data']['Reference']}</Reference><br/>
                """
                evaluate_reference += f"""
                    <Reason>{evaluate['evaluation_data']['Reason']}</Reason><br/>
                """
            
            print('now here also......')
            
            log['evaluation_report_section']['engr_reference'][count-1] = evaluate_reference

            # print('Chunk to pass: ', chunk_to_pass)
            print(f'now passing the criteria {count} {self.criterion[count - 1]}')
            await self.specs_reasoning.get_claude_to_give_non_compliance_comment(evaluate_text_2, self.criterion[count - 1], count, log)
            
            log['evaluate_summary_processing'][count-1] = f"{evaluate_text}<br/>"

            log['evaluate_summary'] += log['evaluation_report_section']['evaluation_report'][count-1]

            self.add_event(self.requirement_id, 'in_progress_event', {
                'event_type': 'SCP',
                'request_id': self.requirement_id,
                'data': log
            })

            self.report_manager.update_log(log)
            # self.save_report(log)
          

        except Exception as e:
            print(f"Error in process_single_criterion: {e}")
            Requirement.update(self.requirement['id'], status='idle', tried=self.requirement["tried"] + 1)

    def handle_failed_request(self, requirement, data, responses):
        report = {
            "total_factors": "",
            "positive_factors": "",
            "details": data,
            "reasoning_summary": "",
            "source_map": responses,
            "evaluate_summary": "The Source Document does not return any data based on the criteria provided."
        }
        report_path = os.path.join(
            self.env_data.get('DATA_DIR'),
            requirement.get("project_id", ""),
            f"{requirement.get('id', requirement)}.json"
        )
        self.save_json(report_path, report)

        if requirement:
            Requirement.update(requirement['id'], status='idle', tried=requirement["tried"] + 1)

    async def _extract_dataset1(self, criteria, count, log):
        extractor = Dataset1Extractor(criteria)
        return await extractor.get_dataset1(self.project_id, count, log)
    
    async def _extract_dataset2(self, criteria, source_text, count, log):
        return await self.dataset2_extractor.process_new_file_SpecsComply(
            criteria, source_text, count, self.requirement_id, log
        )

    async def _get_chunks_to_process(self, count, log):
        for key in ['strength_chunk', 'weak_chunk', 'risk_chunk']:
            print(f"this is the key: {key} and log: {log['evaluation_report_section'][key]} ")
            chunk_list = log['evaluation_report_section'].get(key, [])
            print(f"condition check: {chunk_list}")
            if len(chunk_list) > count - 1 and chunk_list[count - 1]:
                print('the chunklist: ', chunk_list[count - 1])
                return chunk_list[count - 1]
        # Return an empty list if no valid chunk is found
        return []
    
    def create_engineering_comments_table(self, engr_references, engr_comments):
        # print(f"engr comment: {engr_comments}")
        # print(f"engr_references: {engr_references}")
        # Create the HTML table
        html_table = """
        <table border="1">
            <tr>
                <th>Comment #</th>
                <th>Engineering Comment</th>
                <th>Reference</th>
                <th>Contractor Feedback</th>
            </tr>
        """
        
        # Add rows for each comment and reference
        for idx, (comment, reference) in enumerate(zip(engr_comments, engr_references), start=1):
            contractor_feedback = "????"

            html_table += f"""
                <tr>
                    <td>Item #{idx}</td>
                    <td>{comment}</td>
                    <td>{reference}</td>
                    <td>{contractor_feedback}</td>
                </tr>
            """
        
        # Close the table
        html_table += "</table>"
        
        return html_table

    def save_report(self, log):
        report_path = os.path.join(
            self.env_data.get('SPECS_DIR'),
            self.project_id,
            f"{self.requirement_id}.json"
        )
        print(f"Writing report to {report_path}")
        self.save_json(report_path, log)

    def save_json(self, path, data):
        with open(path, 'w', encoding='utf8') as json_file:
            json.dump(data, json_file, ensure_ascii=False)

    async def _prepare_context(self, requirement):
        self.requirement = requirement
        self.requirement_id = requirement["id"]
        self.requirement_name = requirement["name"]
        self.tender_title = self._remove_extension(self.requirement_name)
        self.tender_number = requirement["id"]
        self.bidder_name = '[BIDDER NAME]'
        self.project = Project.get_single(requirement["project_id"])
        self.project_id = requirement['project_id']
        self.file_path = os.path.join(self.env_data.get('SPECS_DIR'), self.project_id, self.requirement_name)
        self.document_data = await self.engr_doc_detector.detect_document_category_from_document_pages(
            self.requirement_id
        )
        self.criterion = self.document_data.get("document_formatted_criteria")[:6]
        self.document_number = self.document_data.get("document_number")
        self.document_discipline = self.document_data.get("document_discipline")
        self.document_deliverable = self.document_data.get("document_deliverable")
        
        print('i am the criterion: ', self.criterion)
        # sys.exit()
        # self.criterion = self.convert_criteria_to_dict(
        #     self.document_data.get(self.requirement['category'], {}).get("criteria", [])
        # )[:3]

    
    def initialize_log_structure(self, criteria_count):
        return {
            "criteria": "",
            "criteria_array": [""] * criteria_count,
            "reasoning_summary": [""] * criteria_count,
            "evaluate_summary": "",
            "evaluate_summary_intro": "",
            "evaluate_summary_code": "",
            "evaluate_summary_array": [""] * criteria_count,
            "evaluate_summary_processing": [""] * criteria_count,
            "project_title": "",
            "document_number": [],
            "document_discipline": [],
            "document_deliverable": [],
            "document_title": self._remove_extension(self.requirement_name),
            "chunk_to_pass": [""] * criteria_count,
            "evaluation_report_section": {
                "introduction": "",
                "strength_chunk": [""] * criteria_count,
                "weak_chunk": [""] * criteria_count,
                "risk_chunk": [""] * criteria_count,
                "engr_comment": [""] * criteria_count,
                "engr_reference": [""] * criteria_count,
                "contractor_feedback": "????",
                "strength_text": "",
                "weak_text": "",
                "risk_text": "",
                "detailed_evaluate_text": "",
                "recommendation_text": "",
                "strength_weak_risk_chunk_intro": {},
                "evaluation_report": [""] * criteria_count,
                "evaluation_result": ""
            }
        }

    async def process_single_request(self, request_id):
        await self.handle_single_new_document_v2(request_id)

    def convert_criteria_to_dict(self, criteria):
        criteria_list = [item.strip() for item in criteria[0].split(',')]
        weight = round(100 / len(criteria_list), 2)

        return [
            {"name": item, "description": item, "weight": weight} for item in criteria_list
        ]

    def _remove_extension(self, file_name):
        base_name = os.path.splitext(file_name)[0]
        pattern = r'_(\d{6})_merged$'        
        modified_name = re.sub(pattern, '', base_name)
        
        return modified_name
    
    def add_event(self, request_id, event_name, data):
        # return
        print('now emitting event for the client via scp..')
        self.socket_manager.emit_to_client(request_id, event_name, data, '/scp')

if __name__ == "__main__":
    processor = SpecsDocumentProcessor([], [])
    # asyncio.run(processor.queue())
    asyncio.run(processor.process_single_request('d11cf7c3-504f-44bc-a6f8-57877aef4005'))
