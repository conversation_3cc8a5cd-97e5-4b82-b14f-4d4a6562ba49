#!/bin/bash

echo "Checking for existing Celery workers..."
pkill -f "celery worker" && echo "✅ Stopped existing Celery workers." || echo "No existing Celery workers found."


# Check if <PERSON><PERSON> is running
echo "Checking if <PERSON><PERSON> is running..."
if ! redis-cli ping | grep -q "PONG"; then
    echo "Redis is NOT running. Attempting to start Redis manually..."
    
    # Start Redis in the background
    redis-server --daemonize yes
    
    sleep 2  # Give Redis time to start

    # Verify Redis started successfully
    if ! redis-cli ping | grep -q "PONG"; then
        echo "❌ Failed to start Redis. Exiting."
        exit 1
    fi
fi

echo "✅ Redis is running."

# Set number of workers and concurrency per worker
NUM_WORKERS=4  # Adjust based on GPU memory & workload
CONCURRENCY=4  # Adjust based on batch processing needs

echo "Starting $NUM_WORKERS Celery workers with concurrency of $CONCURRENCY each..."

# Start multiple workers with unique hostnames
for i in $(seq 1 $NUM_WORKERS); do
    celery -A init.celery worker --loglevel=info --concurrency=$CONCURRENCY \
        --pidfile="/workspace/gpu_processor/logs/celery-worker-$i.pid" \
        --logfile="/workspace/gpu_processor/logs/celery-worker-$i.log" \
        --hostname="worker-$i@%h" &
    echo "✅ Started Celery worker-$i@%h"
done

echo "✅ Celery worker started successfully!"
