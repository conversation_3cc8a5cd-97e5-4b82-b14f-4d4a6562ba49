import asyncio
from services.faiss_embedding import FaissEmbeddingGPU

async def main():
    request_id = '77170c18-feef-4e02-bd27-925b7b3805ae'
    
    faiss_instance = FaissEmbeddingGPU(request_id=request_id, batch_size=1000)

    # Get top 3 pages documents
    top_3_pages_docs = faiss_instance.get_top_3_pages_documents()
    
    # Format the results
    formatted_results = []
    for doc in top_3_pages_docs:
        formatted_results.append({
            'content': doc.page_content,
            'metadata': doc.metadata
        })

    # Return success response with formatted results
    print({
        'request_id': request_id,
        'document_count': len(top_3_pages_docs),
        'results': formatted_results
    })

# Run the async function
if __name__ == "__main__":
    results = asyncio.run(main())
