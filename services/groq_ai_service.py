import random
from groq import Groq
from services.groq_api_key_provider import GroqAPIKeyProvider
from pydantic import BaseModel
import instructor
import os, json
from models import LLMUsage

class GroqService:
    def __init__(self):

        self.api_key_provider = GroqAPIKeyProvider()
        self.parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.env_data = self._load_env()
        # Map deployments to models
        self.deployments_map = {
            "Llama 3 8B": "llama3-8b-8192",
            "Llama 3 70B": "llama3-70b-8192",
            "Llama 3.1 8B": "llama-3.1-8b-instant",
            "Llama 3.3 70B": "llama-3.3-70b-versatile"
        }

        self.model_ids = ["llama-3.1-8b-instant", "llama3-8b-8192"]

        # Select a default model
        self.selected_model = self.deployments_map["Llama 3 8B"]

    def _load_env(self):
        env_path = os.path.join(self.parent_dir, 'env.json')
        with open(env_path, 'r') as f:
            return json.load(f)
        
    def _record_usage(self, model_id, completion):
        """Helper method to record Groq API usage"""
        return
        try:
            LLMUsage.create(
                provider="groq",
                model=model_id,
                input_tokens=completion.usage.prompt_tokens,
                output_tokens=completion.usage.completion_tokens
            )
        except Exception as e:
            print(f"Error recording Groq usage: {e}")

    def generate_completion(self, messages, temperature=0.0001, max_tokens=1024, model=None, top_p=1.0, stream=False, stop=None):
        random.shuffle(self.model_ids)
        # if api_key:
        models_to_try = [model] if model else self.model_ids
        for model_id in models_to_try:
            api_key = self.api_key_provider.get_ready_api_key()
            self.client = Groq(api_key=api_key)
            print(model_id)
            try:
                completion = self.client.chat.completions.create(
                    model=model_id,
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    max_completion_tokens=max_tokens,
                    top_p=top_p,
                    stream=stream,
                    stop=stop,
                )
                
                if stream:
                    response = ""
                    for chunk in completion:
                        response += chunk.choices[0].delta.content or ""
                    return response
                else:
                    # Record usage before returning
                    
                    #load usage .txt and save it
                    print('=== Groq API Token Usage ===')
                    print(f'Model: {model_id}')
                    print(f'Prompt tokens: {completion.usage.prompt_tokens}')
                    print(f'Completion tokens: {completion.usage.completion_tokens}')
                    print(f'Total tokens: {completion.usage.total_tokens}')
                    print('==========================')
                    self._record_usage(model_id, completion)
                    return completion.choices[0].message.content
            except Exception as e:
                print(f"Error with model {model}: {e}")
        return None
    

    def extract_data(self, schema: BaseModel, messages, model=None):
        """
        Extract data using Groq with the specified schema and prompts.

        :param schema: Pydantic model specifying the response schema
        :param system_prompt: The system-level instruction for the Groq model
        :param user_prompt: The user-level input text to extract data from
        :return: An instance of the schema populated with extracted data
        """
        try:
            random.shuffle(self.model_ids)
            api_key = self.api_key_provider.get_ready_api_key()

            self.client = instructor.from_groq(Groq(api_key=self.env_data.get(api_key, '')), mode=instructor.Mode.JSON)
            models_to_try = [model] if model else self.model_ids
            for model_id in models_to_try:
                print(model_id)
                try:
                    completion = self.client.chat.completions.create(
                        model=model_id,
                        response_model=schema,
                        messages=messages,
                        temperature=0.01,
                    )
                    self._record_usage(model_id, completion)
                    return completion
                except Exception as e:
                    print(f"Error with model {model}: {e}")
        except Exception as e:
            print(f"Error generating completion: {e}")
