# /home/<USER>/file_processor_tasks.py
from extensions import celery
from socket_instance import socket_instance
from models import Requirement
from services.file_processor_cbp import CBPFileProcessor
import json

@celery.task(bind=True, max_retries=3, name="task_celery.file_processor_cbp_tasks.process_files_cbp_task")
def process_files_cbp_task(self, req_id, file_names_arr, existing_req):
    try:
        if existing_req:
            existing_req_names = json.loads(existing_req["name"])
            new_names = file_names_arr + existing_req_names
            Requirement.update(req_id, name=json.dumps(new_names))
        else:
            Requirement.update(req_id, name=json.dumps(file_names_arr))

        try:
            socket_manager = socket_instance.get_instance()

            
            # Test emit
            socket_manager.emit_to_client(
                req_id,
                'test_event',
                {'message': 'Test from Celery'},
                namespace='/processing_files'
            )
            print('starting to process files...')
            file_processor = CBPFileProcessor(socket_manager, req_id, existing_req, instant_file_names=file_names_arr)
            file_processor.process_files()
        except Exception as e:
            print(f"Error during file processing: {e}")

        return "File processing completed successfully."
    except Exception as e:
        print(f"Error during file processing: {e}")
        return str(e)
    

