
import eventlet
eventlet.monkey_patch(thread=False)
from init import app, socketio, socket_manager
from extensions import db, queues
from socket_instance import socket_instance

from flask import Flask, Response, request, jsonify, send_from_directory, send_file
from werkzeug.utils import secure_filename
from flask_socketio import So<PERSON><PERSON>
from models import Project, File, Requirement, Prompt, BidFile, Bids
from services.question_answer import QuestionA<PERSON>werer
from services.handle_new_qmp_query import Q<PERSON><PERSON><PERSON>yH<PERSON><PERSON>
from services.assistant_synchronization import AssistantSyncManager
from services.document_seeder import DocumentSeeder
from services.docx_to_pdf_v2 import DocxToPdf
from services.data_handler import DataManager
from services.file_merger import FileMerger
from services.docx_to_pdf_v2 import DocxToPdf
from services.file_processor_cbp import CBPFileProcessor
from services.file_processor_scp import SCPFileProcessor
from services.handle_cbp_report import GenerateReportCBP
from services.background_task_processor import BackgroundTaskProcessor
from services.engr_doc_types_detection import EngrDocTypesDetection
from services.parser_openai_parellel import OpenAIProcessor
# from services.handle_new_cbp_documents import ChronobidDocumentProcessor
# from services.handle_new_specs_documents import SpecsDocumentProcessor
# from task.file_evaluator_cbp_tasks import evaluate_files_cbp_task
# from task.file_evaluator_scp_tasks import evaluate_files_scp_task
# from task.file_upload_tasks import upload_files_task
# from task_redis.file_evaluator_cbp_tasks import evaluate_files_cbp_task
# from task_redis.file_evaluator_scp_tasks import evaluate_files_scp_task
# from task_redis.file_upload_tasks import upload_files_task
from task_redis.file_report_cbp_tasks import get_cbp_report_task
from namespaces.test_namespaces import TestNamespace
from namespaces.scp_namespaces import SCPNamespace
from namespaces.cbp_namespaces import CBPNamespace
from namespaces.qmp_namespaces import QMPNamespace
from namespaces.file_processing_namespaces import ProcessingFilesNamespace
from Reports.report import Reports
from DocumentNumber.documentnumber import DocumentNumbering
from services.faiss_embedding import FaissEmbedding
import sys, time
import random
import threading
import asyncio
import traceback
import json
import uuid
import os
from functools import wraps
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
from datetime import timedelta
from rq import Retry
from models import Users
import redis
from task_celery.test import test_task


allowed_origins = [
    '*',
    'null',
    'http://localhost:*',  # Allow all localhost ports
    'http://127.0.0.1:*',  # Allow all localhost IP ports
    'https://backend.aienergy-oilandgas.com',
    'https://app.aienergy-oilandgas.com',
    'http://127.0.0.1:5500'
]

# socketio.on_namespace(TestNamespace('/scp', socket_manager))

# socketio.on_namespace(TestNamespace('/test', socket_manager))
socketio.on_namespace(SCPNamespace('/scp', socket_manager))
socketio.on_namespace(CBPNamespace('/cbp', socket_manager))
socketio.on_namespace(QMPNamespace('/qmp', socket_manager))
socketio.on_namespace(ProcessingFilesNamespace('/processing_files', socket_manager))

REDIS_CLIENT = redis.StrictRedis(host='localhost', port=6379, db=0, decode_responses=True)

# Load environment variables from env.json file in the parent directory
env_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'env.json')
with open(env_file_path, 'r') as f:
    env_data = json.load(f)

UPLOAD_FOLDER = env_data.get("DATA_DIR")
CHRONOBID_FOLDER = env_data.get("CHRONOBID_DIR")
SPECS_FOLDER = env_data.get("SPECS_DIR")

ALLOWED_EXTENSIONS = {'doc', 'docx', 'pdf', 'txt', "csv", "xls", "xlsx"}

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['CHRONOBID_FOLDER'] = CHRONOBID_FOLDER
app.config['SPECS_FOLDER'] = SPECS_FOLDER
app.config['RABBITMQ_URL'] = env_data.get("RABBITMQ_URL")
app.config['CELERY_BROKER_URL'] = 'amqp://' + app.config['RABBITMQ_URL']
app.config['CELERY_RESULT_BACKEND'] = 'rpc://' + app.config['RABBITMQ_URL']


question_answerer = QuestionAnswerer()
data_manager = DataManager()
assistant_sync_manager = AssistantSyncManager()
document_seeder_processor = DocumentSeeder()
doc_to_pdf_processor = DocxToPdf()
reports = Reports()
document_numbering = DocumentNumbering()
engr_document_number_agent = EngrDocTypesDetection()
open_ai_processor = OpenAIProcessor()

main_event_loop = asyncio.new_event_loop()
loop_thread = threading.Thread(target=main_event_loop.run_forever, daemon=True)
loop_thread.start()


# Dictionary to store active clients keyed by their request IDs
clients = {}
client_joined = {}


# socket_manager = socket_instance.get_instance()
print(f"check3 Socket manager initialized: {socket_manager is not None}")


def async_route(f):
    @wraps(f)
    def wrapper(*args, **kwargs):
        return asyncio.run(f(*args, **kwargs))
    return wrapper

@app.route('/shutdown', methods=['POST'])
def shutdown():
    main_event_loop.call_soon_threadsafe(main_event_loop.stop)
    loop_thread.join()
    return "Event loop stopped."

@app.route('/health')
def home():
    return 'TEST OK!'

@app.route('/test_celery', methods=['GET'])
def test_celery():
    try:
        test_task.delay()
        return 'Celery task started'
    except Exception as e:
        return f'Error starting celery task: {str(e)}'

def success_response(message, data={}, status_code=200):
    return jsonify({"success": True, "message": message, "data": data}), status_code

def error_response(message, data={}, status_code=400):
    return jsonify({"success": False, "message": message, "data": data}), status_code

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/upload_1', methods=['POST'])
def upload_file_1():
    project_id = request.form.get('project_id')
    project_type = request.form.get('type')

    if not project_id:
        return error_response("Project ID is required")

    project = Project.get_by(id=project_id)
    print(project)
    if not project:
        return error_response("Invalid project_id")

    if 'file' not in request.files:
        return error_response("No file part")

    files = request.files.getlist('file')

    if not files:
        return error_response('No selected files', 400)

    if not all(allowed_file(file.filename) for file in files):
        return error_response('Some files are not allowed', 400)

    try:
        # dirtry = SPECS_FOLDER if project['entity_type'] != '' else UPLOAD_FOLDER
        dirtry = UPLOAD_FOLDER
        # dirtry = UPLOAD_FOLDER if project_type != 'scp' else SPECS_FOLDER
        file_dirtry = dirtry
        folder_path = os.path.join(dirtry , project_id)
        os.makedirs(folder_path, mode=0o775, exist_ok=True)

        file_id = str(uuid.uuid4())
        # Generate a random 6-digit number
        random_code = random.randint(100000, 999999)

        if len(files) > 1:
            # Merge files
            file_merger = FileMerger(folder_path)
            file_name = f"{project['name']}_{random_code}_merged.pdf"
            print('merged_file_name:', file_name)
            file_path = file_merger.merge_files(files, file_name)
        else:
            file_name = secure_filename(files[0].filename)
            temp_file_path = os.path.join(folder_path, file_name)

            if file_name.rsplit('.', 1)[1].lower() == 'pdf':
                file_path = temp_file_path
                files[0].save(file_path)
            else:
                files[0].save(temp_file_path)
                file_converter = DocxToPdf()
                file_path = file_converter.convert_docx_to_pdf_file_to_file(temp_file_path, folder_path)
                file_name = file_path.split('/')[-1]

        file_data = {
            'id': file_id,
            'name': file_name,
            'project_id': project_id,
            'file_dirtry': file_dirtry,
            'file_type': file_name.rsplit('.', 1)[1].lower()
        }

        file_save = File(**file_data)
        db.session.add(file_save)
        db.session.commit()

        return success_response(f'File successfully uploaded and saved to {file_path}')
    except Exception as e:
        traceback.print_exc()
        db.session.rollback()
        return error_response(f"Error saving file: {str(e)}", 500)

@app.route('/upload_tender', methods=['POST'])
def upload_tender_file():
    package_id = request.form.get('package_id')
    if not package_id:
        return error_response('package_id is required')

    package = Project.get_single(package_id)

    if not package:
        return error_response('Invalid package_id')

    if 'file' not in request.files:
        return error_response('No file part')

    files = request.files.getlist('file')

    if not files:
        return error_response('No selected files', 400)

    if not all(allowed_file(file.filename) for file in files):
        return error_response('Some files are not allowed', 400)

    try:
        folder_path = os.path.join(CHRONOBID_FOLDER, package_id)
        os.makedirs(folder_path, mode=0o775, exist_ok=True)
        file_id = str(uuid.uuid4())
        # Generate a random 6-digit number
        result = []
        file_ids = []
        all_success = True
        all_failed = True
        lock = threading.Lock()

        # Async file upload
        async def upload_file_task(file_itm):
            nonlocal all_success, all_failed
            file_id = str(uuid.uuid4())
            file_name = secure_filename(file_itm.filename)
            file_path = os.path.join(folder_path, file_name)
            try:
                print(f"Processing file: {file_name}")
                file_itm.save(file_path)
                file_extension = file_name.rsplit('.', 1)[-1].lower()

                pdf_conversion_status = 'done' if file_extension == 'pdf' else 'idle'

                file_data = {
                    'id': file_id,
                    'name': file_name,
                    'project_id': package_id,
                    'file_dirtry': CHRONOBID_FOLDER,
                    'file_type': file_extension,
                    'pdf_conversion_status': pdf_conversion_status
                }
                File.create(**file_data)
                with lock:
                    all_failed = False
                result.append({"success": True, "file": file_name})
                file_ids.append(file_id)
                print(f"Successfully uploaded: {file_name}")
            except Exception as e:
                with lock:
                    all_success = False
                result.append({"success": False, "file": file_name, "error": str(e)})
                print(f"Failed to upload file {file_name}: {str(e)}")

        
        # Run all uploads concurrently
        async def upload_files_concurrently():
            print(f"Starting concurrent file uploads for {len(files)} files.")
            tasks = [upload_file_task(file_itm) for file_itm in files]
            await asyncio.gather(*tasks)
            print("Concurrent file uploads finished.")

        # Synchronous wrapper for async task
        def run_upload_tasks():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(upload_files_concurrently())
            finally:
                loop.close()

        # Run the upload tasks in a separate thread
        print("Starting upload thread...")
        upload_thread = threading.Thread(target=run_upload_tasks, daemon=True)
        upload_thread.start()
        upload_thread.join()

        if all_success:
            message = "All files successfully uploaded"
        elif all_failed:
            message = "All files failed to upload"
        else:
            message = "One or more files failed to upload"
        
        print(f"Upload process complete: {message}")
        
        # Run process_files in the background
        def process_files():
            print('convert idle docx to pdf before upload.....')
            doc_to_pdf_processor.convert_idle_docx_files()

            print("Started background processing of files.")
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(
                asyncio.gather(
                    *[document_seeder_processor.handle_single_document(file_id) for file_id in file_ids]
                )
            )
            print("Background file processing complete.")

        # Start background task
        print("Starting background processing thread.")
        threading.Thread(target=process_files , daemon=True).start()

        return success_response(message, result)
    except Exception as e:
        traceback.print_exc()
        db.session.rollback()
        return error_response(f"Error saving file: {str(e)}", 500)


@app.route('/upload_tender_requirement', methods=['POST'])
def upload_tender_requirement():
    
    if 'file' not in request.files:
        return error_response('No file part')

    files = request.files.getlist('file')

    print(f'received {len(files)} files uploaded...')
    package_id = request.form.get('package_id')
    req_id = request.form.get('request_id', False)
    # is_zip = request.form.get('is_zip')

    if not package_id:
        return 'package is required', 400

    if not files:
        return error_response('No selected files', 400)

    if not all(allowed_file(file.filename) for file in files):
        return error_response('Some files are not allowed', 400)

    if req_id:
        existing_req = Requirement.get_single(req_id)
    else:
        existing_req = None
        req_id = str(uuid.uuid4())
    
    # Step 3: Fetch package details
    try:
        package = Project.get_single(package_id)
    except Exception as e:
        return 'Invalid package_id', 400

     
    folder_path = os.path.join(CHRONOBID_FOLDER, package_id, req_id)
    os.makedirs(folder_path, mode=0o775, exist_ok=True)

    try:
        
        result = []
        file_names_arr = []
        all_success = True
        all_failed = True
        lock = threading.Lock()

        if not existing_req:
            create_kwargs = {
                'id': req_id,
                'name': json.dumps(file_names_arr),
                'project_id': package_id,
                'type': 'cbp',
                'file_type': files[0].filename.rsplit('.', 1)[1].lower(),
                'criteria': ""
            }
            print('creating requirement...')
            Requirement.create(**create_kwargs)

        # Async file upload
        def upload_file_task(file_itm):
            nonlocal all_success, all_failed
            file_name = secure_filename(file_itm.filename)
            file_path = os.path.join(folder_path, file_name)
            file_names_arr.append(file_name)
            try:
                print(f"uploading file: {file_name}")
                file_itm.save(file_path)
                result.append({"success": True, "file": file_name})
                print(f"Successfully uploaded: {file_name}")
            except Exception as e:
                with lock:
                    all_success = False
                result.append({"success": False, "file": file_name, "error": str(e)})
                print(f"Failed to upload file {file_name}: {str(e)}")

        
        # Run all uploads concurrently
        def _upload_files_concurrently():
            print(f"Starting concurrent file uploads for {len(files)} files.")
            # tasks = [upload_file_task(file_itm) for file_itm in files]
            # await asyncio.gather(*tasks)
            pool = eventlet.greenpool.GreenPool()  # Create a GreenPool to manage concurrent tasks
            for file_itm in files:
                pool.spawn(upload_file_task, file_itm)  # Spawn each file upload as a greenlet
            pool.waitall()
            print("Concurrent file uploads finished now processing...")
            # print('Completed embedding data into vec db...')
        
        async def async_upload_files_concurrently():
            await asyncio.to_thread(_upload_files_concurrently)
        
        async def _process_files():
            try:
                print('starting to process files...')
                file_processor = CBPFileProcessor(socket_manager, req_id, instant_file_names=file_names_arr)
                await file_processor.process_files()
            except Exception as e:
                print(f"Error during file processing: {e}")

        # Function to ensure an asyncio event loop exists and run a coroutine
        def ensure_event_loop_and_run(coro):
            try:
                future = asyncio.run_coroutine_threadsafe(coro, main_event_loop)
                return future.result() 
            except RuntimeError as e:
                print(f"Error running coroutine: {e}")
                raise

        # Run the upload tasks with Eventlet's greenlet
        def run_upload_tasks():
            try:
                ensure_event_loop_and_run(async_upload_files_concurrently())
                # Run the asynchronous function safely in the event loop
                ensure_event_loop_and_run(_process_files())
            except Exception as e:
                print(f"Error during file upload processing: {e}")

        print("# Runner initiated....")
        run_upload_tasks()
        
        
        if existing_req:
            existing_req_names = json.loads(existing_req["name"])
            new_names = file_names_arr + existing_req_names
            Requirement.update(req_id, name=json.dumps(new_names))
        else:
            Requirement.update(req_id, name=json.dumps(file_names_arr))
            

        if all_success:
            message = "All files successfully uploaded"
        elif all_failed:
            message = "All files failed to upload"
        else:
            message = "One or more files failed to upload"
        
        print(f"Upload process complete: {message}")
                
        return {'request_id': req_id, 'message' : message, 'result' : result}
    except Exception as e:
        print(e)



@app.route('/upload_project_requirement', methods=['POST'])
def upload_project_requirement():
    
    if 'file' not in request.files:
        return error_response('No file part')

    files = request.files.getlist('file')

    print(f'received {len(files)} files uploaded...')
    project_id = request.form.get('project_id')
    req_id = request.form.get('request_id', False)
    # is_zip = request.form.get('is_zip')

    if not project_id:
        return 'project is required', 400

    if not files:
        return error_response('No selected files', 400)

    if not all(allowed_file(file.filename) for file in files):
        return error_response('Some files are not allowed', 400)

    if req_id:
        existing_req = Requirement.get_single(req_id)
    else:
        existing_req = None
        req_id = str(uuid.uuid4())
    
    # Step 3: Fetch package details
    try:
        project = Project.get_single(project_id)
    except Exception as e:
        return 'Invalid package_id', 400

     
    folder_path = os.path.join(SPECS_FOLDER, project_id, req_id)
    os.makedirs(folder_path, mode=0o775, exist_ok=True)

    try:
        
        result = []
        file_names_arr = []
        all_success = True
        all_failed = True
        lock = threading.Lock()

        if not existing_req:
            create_kwargs = {
                'id': req_id,
                'name': json.dumps(file_names_arr),
                'project_id': project_id,
                'type': 'cbp',
                'file_type': files[0].filename.rsplit('.', 1)[1].lower(),
                'criteria': ""
            }
            print('creating requirement...')
            Requirement.create(**create_kwargs)

        # Async file upload
        def upload_file_task(file_itm):
            nonlocal all_success, all_failed
            file_name = secure_filename(file_itm.filename)
            file_path = os.path.join(folder_path, file_name)
            file_names_arr.append(file_name)
            try:
                print(f"uploading file: {file_name}")
                file_itm.save(file_path)
                result.append({"success": True, "file": file_name})
                print(f"Successfully uploaded: {file_name}")
            except Exception as e:
                with lock:
                    all_success = False
                result.append({"success": False, "file": file_name, "error": str(e)})
                print(f"Failed to upload file {file_name}: {str(e)}")

        
        # Run all uploads concurrently
        def _upload_files_concurrently():
            print(f"Starting concurrent file uploads for {len(files)} files.")
            # tasks = [upload_file_task(file_itm) for file_itm in files]
            # await asyncio.gather(*tasks)
            pool = eventlet.greenpool.GreenPool()  # Create a GreenPool to manage concurrent tasks
            for file_itm in files:
                pool.spawn(upload_file_task, file_itm)  # Spawn each file upload as a greenlet
            pool.waitall()
            print("Concurrent file uploads finished now processing...")
            # print('Completed embedding data into vec db...')
        
        async def async_upload_files_concurrently():
            await asyncio.to_thread(_upload_files_concurrently)
        
        async def _process_files():
            try:
                print('starting to process files...')
                file_processor = SCPFileProcessor(socket_manager, req_id, instant_file_names=file_names_arr)
                await file_processor.process_files()
            except Exception as e:
                print(f"Error during file processing: {e}")

        # Function to ensure an asyncio event loop exists and run a coroutine
        def ensure_event_loop_and_run(coro):
            try:
                future = asyncio.run_coroutine_threadsafe(coro, main_event_loop)
                return future.result() 
            except RuntimeError as e:
                print(f"Error running coroutine: {e}")
                raise

        # Run the upload tasks with Eventlet's greenlet
        def run_upload_tasks():
            try:
                ensure_event_loop_and_run(async_upload_files_concurrently())
                # Run the asynchronous function safely in the event loop
                ensure_event_loop_and_run(_process_files())
            except Exception as e:
                print(f"Error during file upload processing: {e}")

        print("# Runner initiated....")
        run_upload_tasks()
        
        if existing_req:
            existing_req_names = json.loads(existing_req["name"])
            new_names = file_names_arr + existing_req_names
            Requirement.update(req_id, name=json.dumps(new_names))
        else:
            Requirement.update(req_id, name=json.dumps(file_names_arr))
            

        if all_success:
            message = "All files successfully uploaded"
        elif all_failed:
            message = "All files failed to upload"
        else:
            message = "One or more files failed to upload"
        
        print(f"Upload process complete: {message}")
                
        return {'request_id': req_id, 'message' : message, 'result' : result}
    except Exception as e:
        print(e)

@app.route('/search_tender_requirement', methods=['POST'])
@async_route
async def search_tender_requirement():
    try:
        # Get data from JSON instead of form data since we're sending JSON
        data = request.get_json()
        
        request_id = data.get('request_id')
        search_queries = data.get('search_queries')
        k = data.get('k', 5)  # Get k from request or default to 5

        if not search_queries:
            return error_response("Query parameter is required", 400)

        # Initialize FAISS instance
        faiss_instance = FaissEmbedding(request_id=request_id, batch_size=1000)
        if not faiss_instance:
            return error_response("FAISS instance not initialized", 500)

        # Directly await the search method
        # results = await faiss_instance.search(query=query, k=k)
        search_results = await asyncio.gather(*[
            faiss_instance.search(query, k=3) for query in search_queries
        ])

        print('this is search results: ', search_results)
        # Format the results
        formatted_results = []
        for query_result in search_results:
            query_matches = []
            for doc, score in query_result:
                query_matches.append({
                    'content': doc.page_content,
                    'metadata': doc.metadata,
                    'score': float(score)  # Convert numpy float to Python float
                })
            formatted_results.append(query_matches)

        # Return success response with formatted results
        return success_response({
            'request_id': request_id,
            'query_count': len(search_queries),
            'results': formatted_results
        })

    except Exception as e:
        print(f"Error in search_tender_requirement: {str(e)}")
        return error_response(f"Search failed: {str(e)}", 500)

@app.route('/search_project_requirement', methods=['POST'])
@async_route
async def search_project_requirement():
    try:
        # Get data from JSON instead of form data since we're sending JSON
        data = request.get_json()
        
        request_id = data.get('request_id')
        search_queries = data.get('search_queries')
        k = data.get('k', 5)  # Get k from request or default to 5

        if not search_queries:
            return error_response("Query parameter is required", 400)

        # Initialize FAISS instance
        faiss_instance = FaissEmbedding(request_id=request_id, batch_size=1000)
        if not faiss_instance:
            return error_response("FAISS instance not initialized", 500)

        # Directly await the search method
        # results = await faiss_instance.search(query=query, k=k)
        search_results = await asyncio.gather(*[
            faiss_instance.search(query, k=3) for query in search_queries
        ])

        print('this is search results for project: ', search_results)
        # Format the results
        formatted_results = []
        for query_result in search_results:
            query_matches = []
            for doc, score in query_result:
                query_matches.append({
                    'content': doc.page_content,
                    'metadata': doc.metadata,
                    'score': float(score)  # Convert numpy float to Python float
                })
            formatted_results.append(query_matches)

        # Return success response with formatted results
        return success_response({
            'request_id': request_id,
            'query_count': len(search_queries),
            'results': formatted_results
        })

    except Exception as e:
        print(f"Error in search_project_requirement: {str(e)}")
        return error_response(f"Search failed: {str(e)}", 500)



@app.route('/upload', methods=['POST'])
def upload_file():
    project_id = request.form.get('project_id')
    project_type = request.form.get('type')

    if not project_id:
        return error_response("Project ID is required")

    project = Project.get_by(id=project_id)
    # print(project)
    if not project:
        return error_response("Invalid project_id")

    if 'file' not in request.files:
        return error_response("No file part")

    files = request.files.getlist('file')

    if not files:
        return error_response('No selected files', 400)

    if not all(allowed_file(file.filename) for file in files):
        return error_response('Some files are not allowed', 400)

    try:

        dirtry = UPLOAD_FOLDER
        file_dirtry = dirtry
        folder_path = os.path.join(dirtry , project_id)
        os.makedirs(folder_path, mode=0o775, exist_ok=True)
        result = []
        file_ids = []
        file_names = []
        all_success = True
        all_failed = True
        lock = threading.Lock()

        # Convert to synchronous processing
        def process_file(file_itm):
            nonlocal all_success, all_failed
            with app.app_context():
                file_id = str(uuid.uuid4())
                file_name = secure_filename(file_itm.filename)
                file_path = os.path.join(folder_path, file_name)
                try:
                    print(f"Processing file: {file_name}")
                    file_itm.save(file_path)
                    file_extension = file_name.rsplit('.', 1)[-1].lower()

                    pdf_conversion_status = 'done' if file_extension == 'pdf' else 'idle'

                    file_data = {
                        'id': file_id,
                        'name': file_name,
                        'project_id': project_id,
                        'file_dirtry': dirtry,
                        'file_type': file_extension,
                        'pdf_conversion_status': pdf_conversion_status
                    }
                    File.create(**file_data)
                    with lock:
                        all_failed = False
                    result.append({"success": True, "file": file_name})
                    file_ids.append(file_id)
                    file_names.append(file_name)
                    print(f"Successfully uploaded: {file_name}")
                except Exception as e:
                    with lock:
                        all_success = False
                    result.append({"success": False, "file": file_name, "error": str(e)})
                    print(f"Failed to upload file {file_name}: {str(e)}")

        # Process files using a thread pool
        with ThreadPoolExecutor() as executor:
            list(executor.map(process_file, files))

        if all_success:
            message = "All files successfully uploaded"
        elif all_failed:
            message = "All files failed to upload"
        else:
            message = "One or more files failed to upload"
        
        print(f"Upload process complete: {message}")

        print('trying to send files to method...')
        # with app.app_context():
        #     print('now running the task for file upload...')
        #     q = random.choice(queues["file_upload_"])
        #     q.enqueue(upload_files_task, UPLOAD_FOLDER, project_id, file_ids, file_names, retry=Retry(max=2))

        # For file upload tasks with additional arguments
        processor = BackgroundTaskProcessor(task_type="celery", process_type="file_upload")
        processor.process_task(
            upload_folder=UPLOAD_FOLDER,
            project_id=project_id,
            file_ids=file_ids,
            file_names=file_names
        )

        return success_response(message, result)
    except Exception as e:
        traceback.print_exc()
        db.session.rollback()
        return error_response(f"Error saving file: {str(e)}", 500)

# @app.route('/compare_upload', methods=['POST'])
# def compare_upload():
#     project_id = request.form.get('project_id')

#     if not project_id:
#         return error_response('project is required', 400)

#     project = Project.get_single(project_id)
#     if not project:
#         return error_response('Invalid project', 400)

#     if 'file' not in request.files:
#         return error_response('No file part', 400)

#     files = request.files.getlist('file')

#     if not files:
#         return error_response('No selected files', 400)

#     if not all(allowed_file(file.filename) for file in files):
#         return error_response('Some files are not allowed', 400)

#     else:
#         return error_response('Allowed file types are .doc and .docx', 400)
@app.route('/compare_upload_chronobid', methods=['POST'])
def compare_upload_chronobid():
    start_time = time.time()
    print("Operation started at:", start_time)

    exist_bid = request.form.get('exist_bid')
    exist_bid = exist_bid.lower() == "true" if exist_bid else False

    package_id = request.form.get('package_id')
    other_project_ids = request.form.get('other_project_ids')
    criteria_str = request.form.get('criteria')

    if not criteria_str:
        return 'criteria needed', 400
    
    if not package_id:
        return 'package is required', 400

    # Step 3: Fetch package details
    try:
        package = Project.get_single(package_id)
    except Exception as e:
        return 'Invalid package', 400
    
    print(exist_bid and exist_bid == True)
    print('compare above logic exist bid...')

    if exist_bid:
        print('i am in exist bid...')
        bid_id = request.form.get('bid_id')

        try:
            selectedBid = Bids.get_single(bid_id)
        except Exception as e:
            return 'Invalid Bid Selected', 400
        
        request_id = str(uuid.uuid4())
        filenames = []
        bid_files = BidFile.get_by(bid_id=bid_id)
        for bid_file in bid_files:
            filenames.append(bid_file['name'])
            
        create_kwargs = {
            'id': request_id,
            'name': json.dumps(filenames),
            'project_id': package_id,
            'type': type,
            'file_type': 'pdf',
            'criteria': ""
        }
        print('creating requirement...')
        Requirement.create(**create_kwargs)
    else:
        print('i am in not exist bid...')
        request_id = request.form.get('request_id')
        file_names = request.form.get('file_names')

        if not request_id:
            return 'request is required', 400
        
        try:
            requirement = Requirement.get_single(request_id)
        except Exception as e:
            return 'Invalid requirement', 400
        
        if file_names:
            data = json.loads(file_names)
            filenames = [item['file'] for item in data]

    print('this are filenames: ', filenames)
    
    try:

        REDIS_CLIENT.set(f"{request_id}_metadata", json.dumps({'exist_bid': exist_bid, 'bid_id': request.form.get('bid_id', False)}))

        exist_bid_value = 1 if exist_bid == True else 0
        Requirement.update(request_id, criteria=criteria_str, exist_bid=exist_bid_value)
        print(f"Operation completed successfully. Total time: {time.time() - start_time:.4f} seconds")

        # Return the request_id immediately
        return {"success": True, 'message': 'request submitted successfully...', 'request_id': request_id}

    except Exception as e:
        print(f"Error occurred: {str(e)}")
        return {"success": False, "message": "An error occurred while processing the request."}


@app.route('/compare_report_chronobid', methods=['POST'])
def compare_report_chronobid():

    request_id = request.form.get('request_id')

    if not request_id:
        return error_response('request_id is required', 400)

    try:
        requirement = Requirement.get_by(id=request_id)

        if not requirement or len(requirement) == 0:
            return error_response("Invalid Request!")

        requirement = requirement[0]

        if requirement["status"] not in ["done", "response_started"]:
            return success_response("The request is still being processed!", {"status": requirement["status"]})

        report_path = os.path.join(app.config['CHRONOBID_FOLDER'], requirement["project_id"], request_id + ".json")

        with open(report_path, 'r', encoding ='utf8') as json_file:
            ret = json.load(json_file)

        ret["status"] = requirement["status"]
        return success_response(ret)

    except Exception as e:
        print(e)
        return error_response(f"Error Occurred: {str(e)}")

@app.route('/compare_report', methods=['POST'])
def comapre_report():

    request_id = request.form.get('request_id')

    if not request_id:
        return error_response('request_id is required', 400)

    try:
        requirement = Requirement.get_by(id= request_id)[0]

        if not requirement:
            return error_response("Invalid Request!", {"status": requirement["status"]})

        if requirement["status"] != "done":
            return success_response("The request is still being processed!",{"status": requirement["status"]})

        report_path = os.path.join(app.config['SPECS_FOLDER'], requirement["project_id"], request_id + ".json")

        with open(report_path, 'r', encoding ='utf8') as json_file:
            ret = json.load(json_file)

        ret["status"] = requirement["status"]
        return success_response(ret)


    except Exception as e:
        return error_response('Error Occured!', {"error": True})

@app.route('/compare_queue', methods=['GET'])
def comapre_queue():
    try:
        return {"queue": Requirement.get_by()}, 200
    except Exception as e:
        print(e)
        return {"error": True, "message": "Error Occured!"}

@app.route('/compare_report_log', methods=['POST'])
def comapre_report_log():

    request_id = request.json['request_id']

    if not request_id:
        return 'request_id is required', 400

    try:
        requirement = Requirement.get_by(id= request_id)[0]

        if not requirement:
            return {"message": "Invalid Request!", "status": requirement["status"]}

        if requirement["status"] != "done":
            return {"message": "The request is still being processed!", "status": requirement["status"]}

        report_log_path = os.path.join(app.config['SPECS_FOLDER'], requirement["project_id"], request_id + "_log.json")


        with open(report_log_path, 'r', encoding ='utf8') as json_file:
            ret = json.load(json_file)

        ret["status"] = requirement["status"]
        return ret

    except Exception as e:
        print(e)
        return {"error": True, "message": "Error Occured!"}

@app.route('/get_cbp_report_by_request_id', methods=['POST'])
def get_report_by_request_id():
    request_id = request.json.get('request_id')

    if not request_id:
        return 'request_id is required', 400

    try:
        print('request_id:', request_id)
        report_generator = GenerateReportCBP(request_id, 0)
        # Use asyncio.run_coroutine_threadsafe to schedule the coroutine on the main event loop
        asyncio.run_coroutine_threadsafe(report_generator._generate_report(), main_event_loop)
        return success_response("The request is currently processing", {})
  
    except Exception as e:
        print(e)
        error_response(e, 400)

@app.route('/question_answer', methods=['POST'])
def question_answer():
    try:
        # project_id = request.form.get('project_id')
        # question = request.form.get('question')
        project_id = request.json['project_id']
        question = request.json['question']

        if not project_id:
            return 'project_id is required', 400

        try:
            print(project_id, question)
            result = question_answerer.answer_question(project_id, question)
            return result
            # return {"answer": result["answer"], "sources": result["sources"]}
        except Exception as e:
            traceback.print_exc()
            return {"error": True, "message": "Error Occured!"}
    except Exception as e:
        traceback.print_exc()



@app.route('/question_answer_v1_1', methods=['GET'])
def question_answer_v1_response():

    request_id = request.args.get('request_id')

    if not request_id:
        return error_response('request_id is required', 400)

    try:
        requirement = Requirement.get_by(id=request_id)

        if not requirement or len(requirement) == 0:
            return error_response("Invalid Request!")

        requirement = requirement[0]

        if requirement["status"] not in ["done", "response_started"]:
            return success_response("The request is still being processed!", {"status": requirement["status"]})

        report_path = os.path.join(app.config['UPLOAD_FOLDER'], requirement["project_id"], request_id + ".json")

        with open(report_path, 'r', encoding ='utf8') as json_file:
            ret = json.load(json_file)

        ret["status"] = requirement["status"]
        return success_response(ret)

    except Exception as e:
        print(e)
        return error_response(f"Error Occurred: {str(e)}")


@app.route('/question_answer_v1_1', methods=['POST'])
def question_answer_v1_1():

    project_id = request.json['project_id']
    question = request.json['question']
    other_project_ids = request.json['other_project_ids']

    print(project_id, question, other_project_ids)

    if not project_id:
        return 'project_id is required', 400

    try:
        request_id = str(uuid.uuid4())
        create_kwargs = {
            'id': request_id,
            'name': '',
            'project_id': project_id,
            'file_type': '',
            'type' : 'qmp',
            'criteria': question
        }

        Requirement.create(**create_kwargs)
        print('request_id:', request_id)

        processor = BackgroundTaskProcessor(task_type="celery", process_type="evaluate_qmp")
        processor.process_task(
            request_id=request_id,
            other_project_ids=other_project_ids
        )

        # processor = QMPQueryHandler(socket_manager, is_test=False)
        # asyncio.run_coroutine_threadsafe(processor.answer_question_v1_1(request_id, other_project_ids), main_event_loop)

        return {"request_id": request_id}

    except Exception as e:
        print(e)
        traceback.print_exc()
        return {"error": True, "message": "Error Occured!"}

@app.route('/test/question_answer_v1_1', methods=['POST'])
def test_question_answer_v1_1():

    try:
        request_id = '11cc5979-8e23-4372-a671-ef39125dce0e'
        print('request_id:', request_id)
        return {"request_id": request_id}

    except Exception as e:
        print(e)
        traceback.print_exc()
        return {"error": True, "message": "Error Occured!"}


@app.route('/create_project', methods=['POST'])
def create_project():

    project_name = request.form.get('name')
    project_uuid = request.form.get('uuid') if request.form.get('uuid') else str(uuid.uuid4())

    project_data = {
        'id': project_uuid,
        'name': project_name,
        'entity_type': 'project',
    }

    # existing_project = Project.get_one_by(name=project_name)
    # if existing_project:
    #     return error_response("A project with this name already exists", 400)

    try:
        project = Project(**project_data)
        db.session.add(project)
        db.session.commit()

        dirtry = UPLOAD_FOLDER

        project_folder = os.path.join(dirtry, project_data['id'])
        os.makedirs(project_folder, mode=0o777, exist_ok=True)
        # Ensure the directory is writable
        os.chmod(project_folder, 0o777)
        print(project_folder)

        # Check if the directory was created
        if os.path.exists(project_folder) and os.path.isdir(project_folder):
            directory_status = "Directory created successfully"
        else:
            directory_status = "Failed to create directory"

        # if project_type != 'scp':
        #     assistant_sync_manager.create_project_assistant(project_data['id'])

        return success_response("Project created successfully", {'id': project_data['id'], 'directory_status': directory_status} ,201)
    except Exception as e:
        db.session.rollback()
        return error_response(f"Error creating project: {str(e)}", 500)

@app.route('/create_tender_package', methods=['POST'])
def create_tender_package():
    package_name = request.form.get('name')
    package_id = request.form.get('uuid') if request.form.get('uuid') else str(uuid.uuid4())
    if not package_name:
        return error_response('Package name is required', 400)

    existing_package = Project.get_one_by(name=package_name, entity_type="tender")

    # if existing_package:
    #     return error_response('A package with this name already exists', 400)

    try:
        Project.create(id=package_id, name=package_name, entity_type='tender')
        project_folder = os.path.join(CHRONOBID_FOLDER, package_id)
        os.makedirs(project_folder, mode=0o777, exist_ok=True)
        # Ensure the directory is writable
        os.chmod(project_folder, 0o777)

        # Check if the directory was created
        if os.path.exists(project_folder) and os.path.isdir(project_folder):
            directory_status = "Directory created successfully"
        else:
            directory_status = "Failed to create directory"

    except Exception as e:
        db.session.rollback()
        return error_response(f'Error creating tender package: {str(e)}', 500)

    return success_response("Tender Package created successfully", {'id': package_id, 'directory_status': directory_status}, 201)

@app.route('/fetch_tender_packages', methods=['GET'])
def fetch_tender_packages():

    args = request.args.to_dict()
    # print(args)
    packages = Project.get_by(entity_type='tender')

    if not packages:
        return 'No tender packages found', 404

    return {'packages': packages}, 200

@app.route('/fetch_projects', methods=['GET'])
def fetch_projects():

    args = request.args.to_dict()
    projects = Project.get_by(entity_type='project')
    # print('this is projects: ', projects)

    if not projects:
        return 'No projects found', 404

    return {'projects': projects}, 200

@app.route('/fetch_projects_all', methods=['GET'])
def fetch_projects_all():

    args = request.args.to_dict()
    projects = Project.get_by()

    if not projects:
        return 'No projects found', 404

    return {'projects': projects}, 200

@app.route('/search', methods=['GET'])
def search_data():
    try:
        project_id = request.args.get('project_id')
        query = request.args.get('query')
        limit = request.args.get('limit')
        if not project_id:
            return 'project_id is required', 400

        if not query:
            return 'query is required', 400

        if not limit:
            limit = 5
        else:
            limit = int(limit)

        project = Project.get_single(project_id)
        if not project:
            return 'Invalid project_id', 400

        # results = vector_handler.get_data(project_id, [query], limit=limit)
        results = []
        return {'results': results}, 200
    except Exception as e:
        print(e)
        return {"error": True, "message": "Error Occured!"}

@app.route('/search_v2', methods=['GET'])
def search_data_v2():
    try:
        project_id = request.args.get('project_id')
        query = request.args.get('query')
        limit = request.args.get('limit')
        if not project_id:
            return 'project is required', 400

        if not query:
            return 'query is required', 400

        if not limit:
            limit = 5
        else:
            limit = int(limit)

        project = Project.get_single(project_id)
        if not project:
            return 'Invalid project', 400

        results = asyncio.run(data_manager.extract_data(project_id, [query]))

        return {'results': results}, 200
    except Exception as e:
        print(e)
        return {"error": True, "message": "Error Occured!"}

@app.route('/get_files_by_package', methods=['GET'])
def get_files_chronobid():
    try:
        project_id = request.args.get('project_id', None)

        if project_id is None:
            return 'project is required', 400

        pid = Project.get_single(project_id)
        if not pid:
            return 'Invalid project', 400

        # Extracting only the required fields from each file
        files_data = []
        for file_info in pid['files']:
            files_data.append({
                'package_id': pid['id'],
                'file_id': file_info['id'],
                'file_name': file_info['name'],
                'status': file_info['status']
            })

        return {"results": files_data}

    except Exception as e:
        print(e)
        return {"error": True, "message": e.__dict__}

@app.route('/get_all_data', methods=['GET'])
def get_all_vector_data():
    try:
        # project_id = request.args.get('project_id')
        project_id = request.args.get('project_id', None)

        # return {'results': [package_id,project_id]}

        if project_id is None:
            return 'project is required', 400

        pid = Project.get_single(project_id)
        if not pid:
            return 'Invalid project', 400

        return {"results":pid}

    except Exception as e:
        print(e)
        return {"error": True, "message": e.__dict__}

@app.route('/delete_file/<file_id>', methods=['DELETE'])
def delete_file(file_id):
    try:
        # Check if the file exists
        file = File.get_single(file_id)
        if not file:
            return error_response('File not found', 404)

        # Delete the file
        File.delete_by(id=file_id)

        # Optionally, remove the actual file from the filesystem
        file_path = os.path.join(CHRONOBID_FOLDER, file['project_id'], file['name'])
        if os.path.exists(file_path):
            os.remove(file_path)

        return success_response(f"File with id {file_id} successfully deleted")

    except Exception as e:
        return error_response(f"Error deleting file: {str(e)}", 500)


@app.route('/delete_project/<project_id>', methods=['DELETE'])
def delete_project(project_id):
    try:
        # Check if the project exists
        project = Project.get_by(id=project_id)
        if not project or len(project) == 0:
            return error_response('Project not found', 404)

        # Delete all related requirements
        requirements = Requirement.get_by(project_id=project_id)
        if requirements:
            try:
                for req in requirements:
                    Requirement.delete_by(id=req['id'])
            except Exception as e:
                print('Unable to delete project requirements...', str(e))
                return error_response('Error deleting project requirements', 500)

        # Optionally, delete all related files before deleting the project
        related_files = File.get_by(project_id=project_id)
        if related_files:
            try:
                dirtry = CHRONOBID_FOLDER if project[0]['entity_type'] == 'tender' else UPLOAD_FOLDER
                for file in related_files:
                    file_path = os.path.join(dirtry, project_id, file['name'])
                    if os.path.exists(file_path):
                        os.remove(file_path)
                    File.delete_by(id=file['id'])
            except Exception as e:
                print('unable to delete project files...')

        # Delete the project
        Project.delete_by(id=project_id)

        return success_response(f"Project with id {project_id} successfully deleted")

    except Exception as e:
        return error_response(f"Error deleting project: {str(e)}", 500)

# API to return all prompts
@app.route('/prompts', methods=['GET'])
def get_all_prompts():
    try:
        prompts = Prompt.get_by()  # Fetch all prompts
        return success_response("Fetched all prompts", {"prompts": prompts})
    except Exception as e:
        print(e)
        return error_response(f"Error fetching prompts: {str(e)}", 500)


# API to return a prompt by name
@app.route('/prompt/<string:name>', methods=['GET'])
def get_prompt_by_name(name):
    try:
        prompt = Prompt.get_by(name=name)  # Fetch prompt by name
        if not prompt:
            return error_response("Prompt not found", 404)
        return success_response(f"Fetched prompt: {name}", {"prompt": prompt})
    except Exception as e:
        print(e)
        return error_response(f"Error fetching prompt: {str(e)}", 500)


# New API to return all files
@app.route('/files', methods=['GET'])
def get_all_files():
    try:
        files = File.get_by()  # Fetch all files
        return success_response("Fetched all files", {"files": files})
    except Exception as e:
        print(e)
        return error_response(f"Error fetching files: {str(e)}", 500)


# New API to return a file by ID
@app.route('/file/<string:file_id>', methods=['GET'])
def get_file_by_id(file_id):
    try:
        file = File.get_by(id=file_id)  # Fetch file by ID
        if not file:
            return error_response("File not found", 404)

        # Construct the full path to the file
        file_name = file[0]['name']  # File name
        base_dir = os.path.abspath(os.getcwd())
        file_directory = os.path.join(base_dir, file[0]['file_dirtry'], file[0]['project_id'], file_name)  # Directory containing the file
        return send_file(file_directory, as_attachment=False)  # as_attachment=False for browser viewing
    except Exception as e:
        print(e)
        return error_response(f"Error fetching file: {str(e)}", 500)

# Route to create a report
@app.route('/reports', methods=['POST'])
def create_report():
    try:
        client = request.form.get('client')
        intro_template = request.form.get('intro_template')
        body_template = request.form.get('body_template')
        conclusion_template = request.form.get('conclusion_template')

        new_report = reports.create(client, intro_template, body_template, conclusion_template)
        return jsonify(new_report), 201
    except Exception as e:
        return jsonify({"error": str(e)}), 400

# Route to update a report
@app.route('/reports/<id>', methods=['PUT'])
def update_report(id):
    try:
        intro_template = request.form.get('intro_template')
        body_template = request.form.get('body_template')
        conclusion_template = request.form.get('conclusion_template')

        updated_report = reports.update(id, intro_template, body_template, conclusion_template)
        if updated_report:
            return jsonify(updated_report), 200
        return jsonify({"error": "Report not found"}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 400

# Route to delete a report
@app.route('/reports/<id>', methods=['DELETE'])
def delete_report(id):
    try:
        result = reports.delete(id)
        if result:
            return jsonify({"message": "Report deleted successfully"}), 200
        return jsonify({"error": "Report not found"}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 400

# Route to list all reports
@app.route('/reports', methods=['GET'])
def list_reports():
    try:
        filters = request.args.to_dict()  # Get filters from query params
        reports_list = reports.list(**filters)
        return jsonify(reports_list), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 400

# Route to view a single report by ID
@app.route('/reports/<id>', methods=['GET'])
def view_report(id):
    try:
        report = reports.view(id)
        if report:
            return jsonify(report), 200
        return jsonify({"error": "Report not found"}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 400

# Route to create a document numbering template
@app.route('/document_numbering', methods=['POST'])
def create_document_numbering():
    try:
        client = request.form.get('client')
        template = request.form.get('template')

        new_template = document_numbering.create(client, template)
        return jsonify(new_template), 201
    except Exception as e:
        return jsonify({"error": str(e)}), 400

# Route to update a document numbering template
@app.route('/document_numbering/<id>', methods=['PUT'])
def update_document_numbering(id):
    try:
        template = request.form.get('template')

        updated_template = document_numbering.update(id, template)
        if updated_template:
            return jsonify(updated_template), 200
        return jsonify({"error": "Template not found"}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 400

# Route to delete a document numbering template
@app.route('/document_numbering/<id>', methods=['DELETE'])
def delete_document_numbering(id):
    try:
        result = document_numbering.delete(id)
        if result:
            return jsonify({"message": "Template deleted successfully"}), 200
        return jsonify({"error": "Template not found"}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 400

# Route to list all document numbering templates
@app.route('/document_numbering', methods=['GET'])
def list_document_numbering():
    try:
        filters = request.args.to_dict()  # Get filters from query params
        templates_list = document_numbering.list(**filters)
        return jsonify(templates_list), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 400

# Route to view a single document numbering template by ID
@app.route('/document_numbering/<id>', methods=['GET'])
def view_document_numbering(id):
    try:
        template = document_numbering.view(id)
        if template:
            return jsonify(template), 200
        return jsonify({"error": "Template not found"}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 400


# @app.route('/test_scp', methods=['GET'])
# def tester():
#     with app.app_context():
#         q.enqueue(evaluate_files_scp_task, 'd11cf7c3-504f-44bc-a6f8-57877aef4005', retry=Retry(max=2))

#     return jsonify({"message": "Processing started"}), 202


@app.route('/create_user', methods=['POST'])
def create_user():
    try:
        data = request.get_json()
        name = data.get('name')
        status = data.get('status')
        role = data.get('role')
        id = data.get('id')

        if not name or not status or not role:
            return error_response('Name, status, and role are required')

        user_data = {
            'name': name,
            'status': status,
            'id': id,
            'role': role
        }
        user = Users.create(**user_data)

        if user:
            return success_response('User created successfully', user)
        else:
            return error_response('Failed to create user')

    except Exception as e:
        return error_response(f"Error creating user: {str(e)}", 500)

@app.route('/update_user/<user_id>', methods=['PUT'])
def update_user(user_id):
    try:
        name = request.form.get('name')
        status = request.form.get('status')
        role = request.form.get('role')

        if not name and not status and not role:
            return error_response('At least one field is required to update')

        user = Users.update(user_id, name=name, status=status, role=role)

        if user:
            return success_response('User updated successfully', user)
        else:
            return error_response('Failed to update user')

    except Exception as e:
        return error_response(f"Error updating user: {str(e)}", 500)


@app.route('/delete_user/<user_id>', methods=['DELETE'])
def delete_user(user_id):
    try:
        user = Users.delete_by(id=user_id)

        if user:
            return success_response('User deleted successfully', user)
        else:
            return error_response('Failed to delete user')

    except Exception as e:
        return error_response(f"Error deleting user: {str(e)}", 500)

def run_async_task(task, *args, **kwargs):
    """
    Runs an async function in the existing event loop running in a separate thread.
    """
    future = asyncio.run_coroutine_threadsafe(task(*args, **kwargs), main_event_loop)
    return future.result()


@app.route('/process_document_numbering', methods=['POST'])
def extract_document_numbering():
    try:
        files = request.files.getlist('file')

        if not files:
            return error_response('No selected files', 400)

        # Ensure the target folder exists
        folder_path = os.path.join(UPLOAD_FOLDER, 'lab')
        os.makedirs(folder_path, mode=0o775, exist_ok=True)

        # Process the first file (assuming only one file is uploaded)
        file = files[0]
        filename = secure_filename(file.filename)
        file_path = os.path.join(folder_path, filename)
        file.save(file_path)  # Save file

        # Determine file type
        file_type = os.path.splitext(file_path)[-1].lower().lstrip('.')

        # Process file based on type
        if file_type == 'pdf':
            text_single_string, chunks = run_async_task(open_ai_processor.extract_text_from_pdf, file_path)
            result = chunks
        elif file_type in ["docx", "doc"]:
            chunks = run_async_task(open_ai_processor.extract_text_from_docx, file_path)
            result = chunks[1]
        elif file_type in ["csv", "xls", "xlsx"]:
            chunks = run_async_task(open_ai_processor.extract_text_from_excel_or_csv_batch, file_path)
            result = chunks[1]
        else:
            raise ValueError(f"Unsupported file type: {file_type}")

        # Load disciplines (consider caching this)
        specs_dir_path = os.path.join(
            engr_document_number_agent.parent_dir,
            engr_document_number_agent.env_data.get("SPECS_DIR", ""),
            'Aienergy.discipline.json'
        )

        with open(specs_dir_path, 'r', encoding='utf-8') as file:
            disciplines = json.load(file)

        # Extract document info
        document_info = run_async_task(
            engr_document_number_agent.Agent_document_info_extraction,
            result[:3], disciplines
        )

        print('Document info processed successfully...')
        return success_response('Document Processed successfully', {
            "filename": filename,  # Return filename
            "document_info": document_info
        })


    except Exception as e:
        return error_response(f"Error Processing Document: {str(e)}", 500)


@app.route('/test_document_extraction', methods=['POST'])
def test_document_extraction():
    try:
        files = request.files.getlist('file')

        if not files:
            return error_response('No selected files', 400)

        # Ensure the target folder exists
        folder_path = os.path.join(UPLOAD_FOLDER, 'lab')
        os.makedirs(folder_path, mode=0o775, exist_ok=True)

        # Process the first file (assuming only one file is uploaded)
        file = files[0]
        filename = secure_filename(file.filename)
        file_path = os.path.join(folder_path, filename)
        file.save(file_path)  # Save file

        # Determine file type
        file_type = os.path.splitext(file_path)[-1].lower().lstrip('.')

        # Process file based on type
        if file_type == 'pdf':
            text_single_string, chunks = run_async_task(open_ai_processor.extract_text_from_pdf, file_path)
            extracted_content = text_single_string
        elif file_type in ["docx", "doc"]:
            chunks = run_async_task(open_ai_processor.extract_text_from_docx, file_path)
            extracted_content = chunks[1]  # Extract full document content
        elif file_type in ["csv", "xls", "xlsx"]:
            chunks = run_async_task(open_ai_processor.extract_text_from_excel_or_csv_batch, file_path)
            extracted_content = chunks[1]  # Extract full document content
        else:
            raise ValueError(f"Unsupported file type: {file_type}")

        print('Document extraction test successful...')
        return success_response('Document extracted successfully', {
            "filename": filename,  
            "extracted_content": extracted_content
        })

    except Exception as e:
        return error_response(f"Error extracting document content: {str(e)}", 500)


if __name__ == '__main__':
    socketio.run(app, port=5130, host='0.0.0.0', debug=True)
    # app.run(port=5130, host='0.0.0.0', debug=True)