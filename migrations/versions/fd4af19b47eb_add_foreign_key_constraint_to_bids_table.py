"""Add foreign key constraint to bids table

Revision ID: fd4af19b47eb
Revises: d1752768e2ce
Create Date: 2025-03-21 16:33:00.133283

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'fd4af19b47eb'
down_revision = 'd1752768e2ce'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('bids', schema=None) as batch_op:
        batch_op.alter_column('package_id',
               existing_type=mysql.VARCHAR(length=255),
               type_=sa.String(length=36),
               nullable=True)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('bids', schema=None) as batch_op:
        batch_op.alter_column('package_id',
               existing_type=sa.String(length=36),
               type_=mysql.VARCHAR(length=255),
               nullable=True)

    # ### end Alembic commands ###
