import os
import json
import nest_asyncio
from llama_parse import LlamaParse

# Apply nest_asyncio to enable nested event loops
nest_asyncio.apply()

class LLMAPARSER:
    def __init__(self):
        """
        Initializes the PDFLoader, loading API key from env.json in the parent directory
        and setting up the parser for PDF processing.

        :param pdf_path: Path to the PDF document to be loaded.
        """
        # Get the path to the parent directory
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

        # Load environment variables from env.json file in the parent directory
        env_file_path = os.path.join(parent_dir, 'env.json')
        with open(env_file_path, 'r') as f:
            env_data = json.load(f)

        # Set up LlamaParse with the API key from the environment file
        self.parser = LlamaParse(
            api_key=env_data.get('LLAMA_CLOUD_API_KEY', ''),
            result_type="markdown",
            num_workers=4,
            verbose=True,
            language="en"
        )


    def load_pdf(self, pdf_path):
        """
        Loads and parses the PDF document using LlamaParse.

        :return: List of parsed documents.
        """
        try:
            documents = self.parser.load_data(pdf_path)
            return documents
        except Exception as e:
            print(f"Error loading PDF: {e}")
            return None