import json
import os

class PromptSaver:
    def __init__(self):
        self.file_path = "prompt_data.json"

        # # Check if file exists; if not, create it with an empty list
        # if not os.path.exists(self.file_path):
        #     with open(self.file_path, 'w') as f:
        #         json.dump([], f)

    def save_prompt(self, prompt):
        try:
            # Load existing data
            with open(self.file_path, 'r') as f:
                data = json.load(f)

            # Append new prompt
            data.append(prompt)

            # Write updated data back to file
            with open(self.file_path, 'w') as f:
                json.dump(data, f, indent=4)

            print(f"Prompt saved successfully to {self.file_path}")
        except Exception as e:
            print(f"Error saving prompt: {e}")

# # Example usage
# if __name__ == "__main__":
#     # Create the PromptSaver instance
    # prompt_saver = PromptSaver("prompt_data.json")
    # # Save the prompt
    # prompt_saver.save_prompt(new_prompt)
