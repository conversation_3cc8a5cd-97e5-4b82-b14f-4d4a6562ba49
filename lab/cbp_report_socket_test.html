<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>WebSocket Test</title>
  <script src="https://cdn.socket.io/4.5.4/socket.io.min.js"></script>
  <style>
    .log-entry {
      margin: 4px 0;
      padding: 4px;
      border-bottom: 1px solid #eee;
    }
    .error { color: red; }
    .success { color: green; }
    .info { color: blue; }
    #controls {
      margin: 20px 0;
      padding: 10px;
      background: #f5f5f5;
      border-radius: 5px;
    }
    #output {
      height: 400px;
      overflow-y: auto;
      border: 1px solid #ccc;
      padding: 10px;
      background: #f9f9f9;
    }
  </style>
</head>
<body>
  <h1>WebSocket Test</h1>
  
  <div id="controls">
    <div>
      <label for="serverUrlInput">Server URL:</label>
      <input type="text" id="serverUrlInput" value="http://localhost:5130" />
    </div>
    <div>
      <label for="namespaceInput">Namespace:</label>
      <input type="text" id="namespaceInput" value="/cbp_report" />
    </div>
    <div>
      <label for="requestIdInput">Request ID:</label>
      <input type="text" id="requestIdInput" placeholder="Enter Request ID" value="test123" />
    </div>
    <div>
      <label for="processTypeInput">Process Type:</label>
      <select id="processTypeInput">
        <option value="evaluate_scp">evaluate_scp</option>
        <option value="evaluate_cbp">evaluate_cbp</option>
        <option value="evaluate_qmp">evaluate_qmp</option>
        <option value="file_upload">file_upload</option>
        <option value="evaluate_cbp_multibid">evaluate_cbp_multibid</option>
        <option selected value="cbp_report">cbp_report</option>
      </select>
    </div>
    <div>
      <button id="connect">Connect Only</button>
      <button id="join">Join Room</button>
      <button id="disconnect">Disconnect</button>
    </div>
    <div style="margin-top: 10px;">
      <button id="sendProgress">Send Progress</button>
      <button id="sendMessage">Send Message</button>
      <button id="clearLog">Clear Log</button>
    </div>
  </div>
  
  <div id="output"></div>

  <script>
    let socket;
    let attemptNumber = 0;
    let isConnected = false;
    const output = document.getElementById('output');

    function logEntry(message, type = 'info') {
      const timestamp = new Date().toLocaleTimeString();
      const entry = document.createElement('div');
      entry.className = `log-entry ${type}`;
      entry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
      output.appendChild(entry);
      output.scrollTop = output.scrollHeight;
      console.log(`[${timestamp}][${type}] ${message}`);
    }

    document.getElementById('clearLog').addEventListener('click', () => {
      output.innerHTML = '';
      logEntry('Log cleared', 'info');
    });

    document.getElementById('connect').addEventListener('click', () => {
      attemptNumber++;
      const currentAttempt = attemptNumber;
      
      if (socket && socket.connected) {
        logEntry('Already connected! Disconnect first.', 'error');
        return;
      }
      
      const serverUrl = document.getElementById('serverUrlInput').value.trim();
      const namespace = document.getElementById('namespaceInput').value.trim();
      
      logEntry(`Connection attempt #${currentAttempt}: Connecting to ${serverUrl}${namespace}`, 'info');
      
      // Connect with query parameters instead of later join
      const requestId = document.getElementById('requestIdInput').value.trim();
      const processType = document.getElementById('processTypeInput').value;
      
      socket = io(serverUrl + namespace, {
        transports: ["websocket"],
        reconnection: true,
        reconnectionAttempts: Infinity,
        query: {
          "request_id": requestId,
          "process_type": processType
        }
      });
      
      socket.on("connect", () => {
        isConnected = true;
        logEntry(`Connection attempt #${currentAttempt}: Connected successfully to namespace ${namespace}`, 'success');
        logEntry(`Connected with query params: request_id=${requestId}, process_type=${processType}`, 'info');
      });

      socket.on("progress_message", (data) => {
        logEntry(`Received progress: ${JSON.stringify(data)}`, 'success');
      });

      socket.on("completed_event", (data) => {
        logEntry(`Received completion: ${JSON.stringify(data)}`, 'success');
      });

      socket.on("error", (error) => {
        logEntry(`WebSocket Error: ${JSON.stringify(error)}`, 'error');
      });

      socket.on("connect_error", (error) => {
        logEntry(`Connection Error: ${error.message}`, 'error');
      });

      socket.on("disconnect", (reason) => {
        isConnected = false;
        logEntry(`WebSocket disconnected: ${reason}`, 'info');
      });
    });

    document.getElementById('join').addEventListener('click', () => {
      if (socket && socket.connected) {
        const requestId = document.getElementById('requestIdInput').value.trim();
        const processType = document.getElementById('processTypeInput').value;
        
        if (!requestId) {
          logEntry("Warning: Request ID is empty!", 'error');
        }
        
        const joinData = { 
          request_id: requestId,
          process_type: processType
        };
        
        logEntry(`Joining with data: ${JSON.stringify(joinData)}`, 'info');
        socket.emit("join", joinData);
      } else {
        logEntry("Socket not connected! Connect first.", 'error');
      }
    });

    document.getElementById('disconnect').addEventListener('click', () => {
      if (socket) {
        socket.disconnect();
        logEntry("Manually disconnected", 'info');
        isConnected = false;
      } else {
        logEntry("No socket to disconnect!", 'error');
      } 
    });

    document.getElementById('sendProgress').addEventListener('click', () => {
      if (socket && socket.connected) {
        const messageData = { message: "Progress update from client!" };
        socket.emit("progress_message", messageData);
        logEntry(`Sent progress message: ${JSON.stringify(messageData)}`, 'info');
      } else {
        logEntry("Socket not connected!", 'error');
      }
    });

    document.getElementById('sendMessage').addEventListener('click', () => {
      if (socket && socket.connected) {
        const messageData = { message: "Hello from client!" };
        socket.emit("message", messageData);
        logEntry(`Sent message: ${JSON.stringify(messageData)}`, 'info');
      } else {
        logEntry("Socket not connected!", 'error');
      }
    });
  </script>
</body>
</html>