from enum import Enum
from typing import Optional, Dict, List, Any, Tuple
import anthropic
import os
import json
import logging
from pydantic import BaseModel
from ...utils.logger import logger

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def log_token_usage(response: anthropic.types.Message) -> None:
    """Log token usage information from the API response."""
    if hasattr(response, 'usage'):
        logger.info(f"Token Usage - Input: {response.usage.input_tokens}, Output: {response.usage.output_tokens}, Total: {response.usage.input_tokens + response.usage.output_tokens}")

class ClaudeModel(Enum):
    """Enumeration of available Claude models."""
    CLAUDE_3_5_SONNET_OLD = "claude-3-5-sonnet-20240620"
    CLAUDE_3_5_SONNET = "claude-3-5-sonnet-20241022"
    CLAUDE_3_OPUS = "claude-3-opus-20240229"
    CLAUDE_3_SONNET = "claude-3-sonnet-20240229"
    CLAUDE_3_HAIKU = "claude-3-haiku-20240307"
    CLAUDE_3_5_HAIKU = "claude-3-5-haiku-20241022"
    CLAUDE_3_7_SONNET = "claude-3-7-sonnet-20250219"

class AnthropicConfig:
    """Configuration class for Anthropic API settings."""
    MODEL_DEPLOYMENTS: Dict[str, str] = {
        "Claude 3.5 Sonnet Old": ClaudeModel.CLAUDE_3_5_SONNET_OLD.value,
        "Claude 3.5 Sonnet": ClaudeModel.CLAUDE_3_5_SONNET.value,
        "Claude 3 Opus": ClaudeModel.CLAUDE_3_OPUS.value,
        "Claude 3 Sonnet": ClaudeModel.CLAUDE_3_SONNET.value,
        "Claude 3 Haiku": ClaudeModel.CLAUDE_3_HAIKU.value,
        "Claude 3.5 Haiku": ClaudeModel.CLAUDE_3_5_HAIKU.value,
        "Claude 3.7 Sonnet": ClaudeModel.CLAUDE_3_7_SONNET.value
    }
    DEFAULT_TEMPERATURE: float = 0.01
    DEFAULT_MAX_TOKENS: int = 4096
    DEFAULT_TOP_K: int = 3
    MAX_RETRIES: int = 3

class AnthropicService:
    """Service class for interacting with Anthropic's Claude AI models."""
    
    def __init__(self) -> None:
        """Initialize the Anthropic service with API configuration."""
        self.config = AnthropicConfig()
        self.client = self._initialize_client()
        self.default_model = self.config.MODEL_DEPLOYMENTS["Claude 3 Sonnet"]

    def _initialize_client(self) -> anthropic.Anthropic:
        """Initialize the Anthropic client with API key from environment."""
        try:
            root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
            env_file_path = os.path.join(root_dir, 'env.json')
            
            with open(env_file_path, 'r') as f:
                env_data = json.load(f)
            
            api_key = env_data.get('CLAUDE_API_KEY')
            if not api_key:
                raise ValueError("CLAUDE_API_KEY not found in environment configuration")
                
            return anthropic.Anthropic(api_key=api_key)
        except Exception as e:
            logger.error(f"Failed to initialize Anthropic client: {str(e)}")
            raise

    async def _generate_with_fallback(self, operation_func, retry_count: int = 0) -> Tuple[Optional[Any], bool]:
        """Helper method to handle retries and fallback logic."""
        try:
            result = await operation_func()
            return result, True
        except Exception as e:
            logger.error(f"Error in generation attempt {retry_count + 1}: {str(e)}")
            if retry_count < self.config.MAX_RETRIES - 1:
                return await self._generate_with_fallback(operation_func, retry_count + 1)
            return None, False

    def _generate_with_fallback_sync(self, operation_func, retry_count: int = 0) -> Tuple[Optional[Any], bool]:
        """Synchronous helper method to handle retries and fallback logic."""
        try:
            result = operation_func()
            return result, True
        except Exception as e:
            logger.error(f"Error in generation attempt {retry_count + 1}: {str(e)}")
            if retry_count < self.config.MAX_RETRIES - 1:
                return self._generate_with_fallback_sync(operation_func, retry_count + 1)
            return None, False
    

    async def generate(
        self,
        messages: List[Dict[str, str]],
        temperature: float = AnthropicConfig.DEFAULT_TEMPERATURE,
        model_name: str = "Claude 3.5 Sonnet",
        max_tokens: int = AnthropicConfig.DEFAULT_MAX_TOKENS,
        use_fallback: bool = False,
        **kwargs
    ) -> Tuple[Optional[Any], bool]:
        """
        Generate a response using the specified Claude model with optional fallback.
        
        Args:
            messages: List of message dictionaries
            temperature: Sampling temperature
            model_name: Name of the Claude model to use
            max_tokens: Maximum tokens in the response
            use_fallback: Whether to try other models if the specified model fails
            **kwargs: Additional arguments to pass to the client
            
        Returns:
            Tuple[Optional[Any], bool]: (Generated response, Success status)
        """
        def _generate():
            if not use_fallback:
                model_id = self.config.MODEL_DEPLOYMENTS.get(model_name)
                if not model_id:
                    raise ValueError(f"Invalid model name: {model_name}")
                
                response = self.client.messages.create(
                    model=model_id,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    messages=messages,
                    top_k=self.config.DEFAULT_TOP_K,
                    **kwargs
                )
                log_token_usage(response)
                return response
            else:
                # Try the specified model first
                try:
                    model_id = self.config.MODEL_DEPLOYMENTS.get(model_name)
                    if model_id:
                        response = self.client.messages.create(
                            model=model_id,
                            temperature=temperature,
                            max_tokens=max_tokens,
                            messages=messages,
                            top_k=self.config.DEFAULT_TOP_K,
                            **kwargs
                        )
                        log_token_usage(response)
                        return response
                except Exception:
                    pass
                
                # Try other models if the specified one fails
                for fallback_name, fallback_id in self.config.MODEL_DEPLOYMENTS.items():
                    if fallback_name != model_name:
                        try:
                            logger.info(f"Attempting fallback with model: {fallback_name}")
                            response = self.client.messages.create(
                                model=fallback_id,
                                temperature=temperature,
                                max_tokens=max_tokens,
                                messages=messages,
                                top_k=self.config.DEFAULT_TOP_K,
                                **kwargs
                            )
                            log_token_usage(response)
                            return response
                        except Exception as e:
                            logger.warning(f"Failed to generate with {fallback_name}: {str(e)}")
                            continue
                
                raise Exception("All models failed to generate a response")

        return self._generate_with_fallback_sync(_generate)

    async def generate_structured(
        self,
        schema: BaseModel,
        messages: List[Dict[str, str]],
        temperature: float = AnthropicConfig.DEFAULT_TEMPERATURE,
        model_name: str = "Claude 3.5 Sonnet",
        max_tokens: int = AnthropicConfig.DEFAULT_MAX_TOKENS,
        use_fallback: bool = False,
        **kwargs
    ) -> Tuple[Optional[Any], bool]:
        """
        Generate a structured response using a Pydantic schema with optional fallback.
        
        Args:
            schema: Pydantic model defining the response structure
            messages: List of message dictionaries
            temperature: Sampling temperature
            model_name: Name of the Claude model to use
            max_tokens: Maximum tokens in the response
            use_fallback: Whether to try other models if the specified model fails
            **kwargs: Additional arguments to pass to the client
            
        Returns:
            Tuple[Optional[Any], bool]: (Structured response, Success status)
        """
        def _generate_structured():
            analysis_schema = schema.model_json_schema()
            tools = [{
                "name": "build_analysis_result",
                "description": "build the analysis object",
                "input_schema": analysis_schema
            }]

            if not use_fallback:
                model_id = self.config.MODEL_DEPLOYMENTS.get(model_name)
                if not model_id:
                    raise ValueError(f"Invalid model name: {model_name}")
                
                response = self.client.messages.create(
                    model=model_id,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    messages=messages,
                    tools=tools,
                    tool_choice={"type": "tool", "name": "build_analysis_result"},
                    top_k=self.config.DEFAULT_TOP_K,
                    **kwargs
                )
                return response.content[0].input
            else:
                # Try the specified model first
                try:
                    model_id = self.config.MODEL_DEPLOYMENTS.get(model_name)
                    if model_id:
                        response = self.client.messages.create(
                            model=model_id,
                            temperature=temperature,
                            max_tokens=max_tokens,
                            messages=messages,
                            tools=tools,
                            tool_choice={"type": "tool", "name": "build_analysis_result"},
                            top_k=self.config.DEFAULT_TOP_K,
                            **kwargs
                        )
                        return response.content[0].input
                except Exception:
                    pass
                
                # Try other models if the specified one fails
                for fallback_name, fallback_id in self.config.MODEL_DEPLOYMENTS.items():
                    if fallback_name != model_name:
                        try:
                            logger.info(f"Attempting fallback with model: {fallback_name}")
                            response = self.client.messages.create(
                                model=fallback_id,
                                temperature=temperature,
                                max_tokens=max_tokens,
                                messages=messages,
                                tools=tools,
                                tool_choice={"type": "tool", "name": "build_analysis_result"},
                                top_k=self.config.DEFAULT_TOP_K,
                                **kwargs
                            )
                            return response.content[0].input
                        except Exception as e:
                            logger.warning(f"Failed to generate with {fallback_name}: {str(e)}")
                            continue
                
                raise Exception("All models failed to generate a response")

        return self._generate_with_fallback_sync(_generate_structured)

    def generate_sync(
        self,
        messages: List[Dict[str, str]],
        temperature: float = AnthropicConfig.DEFAULT_TEMPERATURE,
        model_name: str = "Claude 3.5 Sonnet",
        max_tokens: int = AnthropicConfig.DEFAULT_MAX_TOKENS,
        use_fallback: bool = False,
        **kwargs
    ) -> Tuple[Optional[Any], bool]:
        """
        Synchronous version of generate function.
        
        Args:
            messages: List of message dictionaries
            temperature: Sampling temperature
            model_name: Name of the Claude model to use
            max_tokens: Maximum tokens in the response
            use_fallback: Whether to try other models if the specified model fails
            **kwargs: Additional arguments to pass to the client
            
        Returns:
            Tuple[Optional[Any], bool]: (Generated response, Success status)
        """
        def _generate():
            if not use_fallback:
                model_id = self.config.MODEL_DEPLOYMENTS.get(model_name)
                if not model_id:
                    raise ValueError(f"Invalid model name: {model_name}")
                
                response = self.client.messages.create(
                    model=model_id,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    messages=messages,
                    top_k=self.config.DEFAULT_TOP_K,
                    **kwargs
                )
                log_token_usage(response)
                return response
            else:
                # Try the specified model first
                try:
                    model_id = self.config.MODEL_DEPLOYMENTS.get(model_name)
                    if model_id:
                        response = self.client.messages.create(
                            model=model_id,
                            temperature=temperature,
                            max_tokens=max_tokens,
                            messages=messages,
                            top_k=self.config.DEFAULT_TOP_K,
                            **kwargs
                        )
                        log_token_usage(response)
                        return response
                except Exception:
                    pass
                
                # Try other models if the specified one fails
                for fallback_name, fallback_id in self.config.MODEL_DEPLOYMENTS.items():
                    if fallback_name != model_name:
                        try:
                            logger.info(f"Attempting fallback with model: {fallback_name}")
                            response = self.client.messages.create(
                                model=fallback_id,
                                temperature=temperature,
                                max_tokens=max_tokens,
                                messages=messages,
                                top_k=self.config.DEFAULT_TOP_K,
                                **kwargs
                            )
                            log_token_usage(response)
                            return response
                        except Exception as e:
                            logger.warning(f"Failed to generate with {fallback_name}: {str(e)}")
                            continue
                
                raise Exception("All models failed to generate a response")

        return self._generate_with_fallback_sync(_generate)

    def generate_structured_sync(
        self,
        schema: BaseModel,
        messages: List[Dict[str, str]],
        temperature: float = AnthropicConfig.DEFAULT_TEMPERATURE,
        model_name: str = "Claude 3.5 Sonnet",
        max_tokens: int = AnthropicConfig.DEFAULT_MAX_TOKENS,
        use_fallback: bool = False,
        **kwargs
    ) -> Tuple[Optional[Any], bool]:
        """
        Synchronous version of generate_structured function.
        
        Args:
            schema: Pydantic model defining the response structure
            messages: List of message dictionaries
            temperature: Sampling temperature
            model_name: Name of the Claude model to use
            max_tokens: Maximum tokens in the response
            use_fallback: Whether to try other models if the specified model fails
            **kwargs: Additional arguments to pass to the client
            
        Returns:
            Tuple[Optional[Any], bool]: (Structured response, Success status)
        """
        def _generate_structured():
            analysis_schema = schema.model_json_schema()
            tools = [{
                "name": "build_analysis_result",
                "description": "build the analysis object",
                "input_schema": analysis_schema
            }]

            if not use_fallback:
                model_id = self.config.MODEL_DEPLOYMENTS.get(model_name)
                if not model_id:
                    raise ValueError(f"Invalid model name: {model_name}")
                
                response = self.client.messages.create(
                    model=model_id,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    messages=messages,
                    tools=tools,
                    tool_choice={"type": "tool", "name": "build_analysis_result"},
                    top_k=self.config.DEFAULT_TOP_K,
                    **kwargs
                )
                return response.content[0].input
            else:
                # Try the specified model first
                try:
                    model_id = self.config.MODEL_DEPLOYMENTS.get(model_name)
                    if model_id:
                        response = self.client.messages.create(
                            model=model_id,
                            temperature=temperature,
                            max_tokens=max_tokens,
                            messages=messages,
                            tools=tools,
                            tool_choice={"type": "tool", "name": "build_analysis_result"},
                            top_k=self.config.DEFAULT_TOP_K,
                            **kwargs
                        )
                        return response.content[0].input
                except Exception:
                    pass
                
                # Try other models if the specified one fails
                for fallback_name, fallback_id in self.config.MODEL_DEPLOYMENTS.items():
                    if fallback_name != model_name:
                        try:
                            logger.info(f"Attempting fallback with model: {fallback_name}")
                            response = self.client.messages.create(
                                model=fallback_id,
                                temperature=temperature,
                                max_tokens=max_tokens,
                                messages=messages,
                                tools=tools,
                                tool_choice={"type": "tool", "name": "build_analysis_result"},
                                top_k=self.config.DEFAULT_TOP_K,
                                **kwargs
                            )
                            return response.content[0].input
                        except Exception as e:
                            logger.warning(f"Failed to generate with {fallback_name}: {str(e)}")
                            continue
                
                raise Exception("All models failed to generate a response")

        return self._generate_with_fallback_sync(_generate_structured)


if __name__ == "__main__":
    import asyncio
    from pydantic import BaseModel, Field
    
    class AnalysisResult(BaseModel):
        """Example schema for structured responses."""
        sentiment: str = Field(..., description="The sentiment of the text (positive/negative/neutral)")
        confidence: float = Field(..., description="Confidence score between 0 and 1")
        key_points: list[str] = Field(..., description="List of key points from the text")

    def run_sync_examples():
        """Run synchronous example usage scenarios of the AnthropicService."""
        # Initialize the service
        service = AnthropicService()
        
        # Example 1: Basic sync generation with fallback
        messages = [
            {"role": "user", "content": "What are the three laws of robotics?"}
        ]
        
        response, success = service.generate_sync(
            messages=messages,
            model_name="Invalid Model",  # This will trigger fallback
            use_fallback=True
        )
        logger.info(f"Basic Sync Response with Fallback - Success: {success}")
        if success:
            logger.info(f"Response: {response.content[0].text}")

        # Example 2: Basic sync generation without fallback
        response, success = service.generate_sync(
            messages=messages,
            model_name="Claude 3.5 Sonnet"
        )
        logger.info(f"Basic Sync Response without Fallback - Success: {success}")
        if success:
            logger.info(f"Response: {response.content[0].text}")

        # Example 3: Structured sync response with schema and fallback
        analysis_messages = [
            {"role": "user", "content": "Analyze this text: The new product launch exceeded all expectations, with customer satisfaction rates hitting 95%."}
        ]
        structured_response, success = service.generate_structured_sync(
            schema=AnalysisResult,
            messages=analysis_messages,
            model_name="Invalid Model",  # This will trigger fallback
            use_fallback=True
        )
        logger.info(f"Structured Sync Response with Fallback - Success: {success}")
        if success:
            logger.info(f"Response: {structured_response}")

        # Example 4: Structured sync response without fallback
        structured_response, success = service.generate_structured_sync(
            schema=AnalysisResult,
            messages=analysis_messages
        )
        logger.info(f"Structured Sync Response without Fallback - Success: {success}")
        if success:
            logger.info(f"Response: {structured_response}")

    async def run_async_examples():
        """Run asynchronous example usage scenarios of the AnthropicService."""
        # Initialize the service
        service = AnthropicService()
        
        # Example 1: Basic async generation with fallback
        messages = [
            {"role": "user", "content": "What are the three laws of robotics?"}
        ]
        response, success = await service.generate(
            messages=messages,
            model_name="Invalid Model",  # This will trigger fallback
            use_fallback=True
        )
        logger.info(f"Basic Async Response with Fallback - Success: {success}")
        if success:
            logger.info(f"Response: {response.content[0].text}")

        # Example 2: Basic async generation without fallback
        response, success = await service.generate(
            messages=messages,
            model_name="Claude 3.5 Sonnet"
        )
        logger.info(f"Basic Async Response without Fallback - Success: {success}")
        if success:
            logger.info(f"Response: {response.content[0].text}")

        # Example 3: Structured async response with schema and fallback
        analysis_messages = [
            {"role": "user", "content": "Analyze this text: The new product launch exceeded all expectations, with customer satisfaction rates hitting 95%."}
        ]
        structured_response, success = await service.generate_structured(
            schema=AnalysisResult,
            messages=analysis_messages,
            model_name="Invalid Model",  # This will trigger fallback
            use_fallback=True
        )


        logger.info(f"Structured Async Response with Fallback - Success: {success}")
        if success:
            logger.info(f"Response: {structured_response}")

        # Example 4: Structured async response without fallback
        structured_response, success = await service.generate_structured(
            schema=AnalysisResult,
            messages=analysis_messages
        )
        logger.info(f"Structured Async Response without Fallback - Success: {success}")
        if success:
            logger.info(f"Response: {structured_response}")

    # Run sync examples
    print("\n=== Running Synchronous Examples ===\n")
    run_sync_examples()
    
    # Run async examples
    print("\n=== Running Asynchronous Examples ===\n")
    asyncio.run(run_async_examples())