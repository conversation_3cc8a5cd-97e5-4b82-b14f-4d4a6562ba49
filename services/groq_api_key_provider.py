import time, os, json, random
from threading import Timer

class GroqAPIKeyProvider:
    def __init__(self, refill_time=60):
        """Initializes the CerebrasAPIKeyProvider class.

        Args:
            refill_time (int): Time in seconds after which API usage is reset.
        """
        # Get the path to the parent directory
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.env_file_path = os.path.join(parent_dir, 'groq_keys.json')

        # Load environment variables from env.json file
        with open(self.env_file_path, 'r') as f:
            self.env_data = json.load(f)

        # Filter out entries with empty api_keys and get all valid keys
        self.api_keys = [
            key for key in self.env_data.keys() 
            if self.env_data[key].get('api_key')
        ]

        # Initialize API key usage from the loaded data
        self.api_key_usage = {
            key: {
                "usage": self.env_data[key].get("usage", 0),
                "total_usage": self.env_data[key].get("total_usage", 0)
            }
            for key in self.api_keys
        }
        self.refill_time = refill_time
        self.timers = {}

    def _save_to_env_file(self):
        """
        Save the current state of API key usage to the env.json file.
        """
        for key in self.api_keys:
            if key in self.env_data:
                # Retain the original data and update only the usage and total_usage
                self.env_data[key].update({
                    "usage": self.api_key_usage[key]["usage"],
                    "total_usage": self.api_key_usage[key]["total_usage"]
                })

        return
        with open(self.env_file_path, 'w') as f:
            json.dump(self.env_data, f, indent=4)

    def _reset_usage(self, api_key):
        """
        Reset the usage for an API key after the refill time.

        Args:
            api_key (str): The API key to reset.
        """
        return
        self.api_key_usage[api_key]["usage"] = 0
        self._save_to_env_file()

    def _schedule_reset(self, api_key):
        """
        Schedule the reset of an API key's usage after the refill time.

        Args:
            api_key (str): The API key to schedule for reset.
        """
        Timer(self.refill_time, self._reset_usage, [api_key]).start()
        timer = Timer(self.refill_time, self._reset_usage, [api_key])
        self.timers[api_key] = timer
        timer.start()
    
    def get_ready_api_key(self):
        """
        Get a random available API key that has not exceeded its usage quota.
        """
        # Filter API keys with usage less than 30
        available_keys = [
            api_key for api_key, usage_info in self.api_key_usage.items()
        ]
        
        if not available_keys:
            raise RuntimeError("No API keys are available. All have exceeded their usage limit.")
        
        # Pick a random API key from the available keys
        selected_key = random.choice(available_keys)
        
        # Increment usage and total_usage for the selected key
        self.api_key_usage[selected_key]["usage"] += 1
        self.api_key_usage[selected_key]["total_usage"] += 1
        
        # Save changes and schedule reset
        # self._save_to_env_file()
        # self._schedule_reset(selected_key)
        
        # Return the actual API key value
        return self.env_data[selected_key]['api_key']
