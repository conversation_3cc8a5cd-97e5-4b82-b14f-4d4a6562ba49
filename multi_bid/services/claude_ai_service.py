import anthropic
import os,sys
import json
import random
import asyncio
from pydantic import BaseModel


class ClaudeService:
    def __init__(self):
        # Get the path to the parent directory
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # Load environment variables from env.json file in the parent directory
        env_file_path = os.path.join(parent_dir, 'env.json')

        claude_keys = ['CLAUDE_API_KEY']
        
        # Select a random API key
        selected_api_key = random.choice(claude_keys)
        
        # Load environment variables from env.json file
        with open(env_file_path, 'r') as f:
            env_data = json.load(f)
        
        # Set up ClaudeAI client
        self.client = anthropic.Anthropic(api_key=env_data.get(selected_api_key, ''))

        # Map deployments to models
        self.deployments_map = {
            "Claude 3.5 Sonnet Old": "claude-3-5-sonnet-20240620",
            "Claude 3.5 Sonnet": "claude-3-5-sonnet-20241022",
            "Claude 3 Opus": "claude-3-opus-20240229",
            "Claude 3 Sonnet": "claude-3-sonnet-20240229",
            "Claude 3 Haiku": "claude-3-haiku-20240307",
            "Claude 3.5 Haiku": "claude-3-5-haiku-20241022",
            "Claude 3.7 Sonnet": "claude-3-7-sonnet-20250219"
        }
        self.model_ids = ["claude-3-5-sonnet-20240620", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-3-7-sonnet-20250219"]

        # Select a random model
        # self.selected_model = random.choice(list(self.deployments_map.values()))
        self.selected_model = self.deployments_map["Claude 3 Sonnet"]


    async def  generate_message_2(self, messages,temperature=0.01, model="Claude 3.5 Sonnet", max_tokens=4096):
        try:
            model="Claude 3.5 Sonnet"
            message = self.client.messages.create(
                model=self.deployments_map[model],
                temperature=temperature,
                max_tokens=max_tokens,
                messages=messages
            )
            return message
        except Exception as e:
            print(f"Error generating message from generate_message_2: {e}")
            return None
        
    async def generate_message(self, messages, temperature=0.01, model="Claude 3.5 Sonnet", max_tokens=4096):
        for model_name, model_id in self.deployments_map.items():
            try:
                print(f"Trying model: {model_name}")
                message = self.client.messages.create(
                    model=model_id,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    messages=messages
                )
                return message
            except Exception as e:
                print(f"Error generating message with model {model_name}: {e}")
        
        # If all models fail, return None or handle it appropriately
        print("All models failed to generate a message.")
        return None
    
    async def generate_message_agent_sonnet(self, messages, temperature=0.01, max_tokens=4096):
        # Use Claude 3.5 Sonnet
        model_id = self.deployments_map["Claude 3.5 Sonnet Old"]
        try:
            print(f"Using model: Claude 3.5 Sonnet Old")
            message = self.client.messages.create(
                model=model_id,
                temperature=temperature,
                max_tokens=max_tokens,
                messages=messages,
                top_k=3
            )
            return message
        except Exception as e:
            print(f"Error generating message with Claude 3.5 Sonnet Old: {e}")
            return None
    
    async def generate_message_agent_sonnet_new(self, messages, temperature=0.01, max_tokens=4096):
        # Use Claude 3.5 Sonnet
        model_id = self.deployments_map["Claude 3.5 Sonnet"]
        try:
            print(f"Using model: Claude 3.5 Sonnet")
            message = self.client.messages.create(
                model=model_id,
                temperature=temperature,
                max_tokens=max_tokens,
                messages=messages,
                top_k=3
            )
            return message
        except Exception as e:
            print(f"Error generating message with Claude 3.5 Sonnet: {e}")
            return None
        
    
    async def generate_message_agent_sonnet_v2(self, schema: BaseModel, messages, temperature=0.01, max_tokens=4096):
        # Use Claude 3.5 Sonnet
        model_id = self.deployments_map["Claude 3.5 Sonnet"]
        try:
            print(f"Using model: Claude 3.5 Sonnet")
            _analysis_schema = schema.model_json_schema()
 
            tools = [
                {
                    "name": "build_analysis_result",
                    "description": "build the analysis object",
                    "input_schema": _analysis_schema
                }
            ]
 
            message = self.client.messages.create(
                model=model_id,
                temperature=temperature,
                max_tokens=max_tokens,
                messages=messages,
                tools=tools,
                tool_choice={"type": "tool", "name": "build_analysis_result"},
                top_k=3
            )
            return message
        except Exception as e:
            print(f"Error generating message with Claude 3.5 Sonnet: {e}")
            return None
    
    async def generate_message_agent_haiku(self, messages, temperature=0.01, max_tokens=4096):
        # Use Claude 3.5 Sonnet
        model_id = self.deployments_map["Claude 3.5 Haiku"]
        try:
            print(f"Using model: Claude 3.5 Haiku")
            message = self.client.messages.create(
                model=model_id,
                temperature=temperature,
                max_tokens=max_tokens,
                messages=messages,
                top_k=3
            )
            return message
        except Exception as e:
            print(f"Error generating message with Claude 3.5 Haiku: {e}")
            return None
    
    async def generate_message_agent_opus(self, messages, temperature=0.01, max_tokens=4096):
        # Use Claude 3 Opus
        model_id = self.deployments_map["Claude 3 Haiku"]
        try:
            print(f"Using model: Claude 3 Opus")
            message = self.client.messages.create(
                model=model_id,
                temperature=temperature,
                max_tokens=max_tokens,
                messages=messages
            )
            return message
        except Exception as e:
            print(f"Error generating message with Claude 3 Opus: {e}")
            return None
    
    async def generate_message_agent_master(self, messages, temperature=0.01, max_tokens=4096):
        # Use Claude 3 Haiku
        model_id = self.deployments_map["Claude 3 Opus"]
        try:
            print(f"Using model: Claude 3 Haiku")
            message = self.client.messages.create(
                model=model_id,
                temperature=temperature,
                max_tokens=max_tokens,
                messages=messages
            )
            return message
        except Exception as e:
            print(f"Error generating message with Claude 3 Haiku: {e}")
            return None
        
    def generate_message_sync2(self, messages, system, temperature=0.01, model="Claude 3 Sonnet", max_tokens=4096):
        try:
            message = self.client.messages.create(
                model=self.deployments_map[model],
                temperature=temperature,
                max_tokens=max_tokens,
                system = system,
                messages=messages
            )
            return message.content[0].text
        except Exception as e:
            print(f"Error generating message from generate_message_sync2: {e}")
            return None
    
    def generate_message_sync(self, messages, system, temperature=0.01, model="Claude 3 Sonnet", max_tokens=4096):
        for model_name, model_id in self.deployments_map.items():
            try:
                print(f"Trying model: {model_name}")
                message = self.client.messages.create(
                    model=model_id,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    system=system,
                    messages=messages
                )
                return message.content[0].text
            except Exception as e:
                print(f"Error generating message with model {model_name}: {e}")
    
    def generate_message_sync_haiku(self, messages, system="", temperature=0.01, max_tokens=4096):
        model_id = self.deployments_map["Claude 3.5 Haiku"]
        try:
            message = self.client.messages.create(
                model=model_id,
                temperature=temperature,
                max_tokens=max_tokens,
                system=system,
                messages=messages
            )
            return message.content[0].text
        except Exception as e:
            print(f"Error generating message with model Haiku 3.5: {e}")
            return None


# if __name__ == "__main__":
#     client = ClaudeService()
#     response = client.generate_message_sync([{"role": "user", "content": "Sources:TITLE_1_4\n5. Scope of Work (SoW)Objective:To supply, deliver, and install valves as per the specifications and BoQ.Deliverables:Supply of valvesDelivery to 123 Oilfield Road, Houston, TX, 77001Installation and commissioningSpecial Requirements:On-site training for maintenance staff\nTITLE_1_6\n7. Supplier Qualification RequirementsEligibility Criteria:Minimum 5 years of experience in valve manufacturingISO 9001 certificationRequired Documents:Company ProfileFinancial Statements (Last 3 years)Client References\nTITLE_1_3\nBill of Quantities (BoQ)<table><tr><th>Item</th><th>Description</th><th>Quantity</th><th>Unit</th></tr><tr><td>1</td><td>Ball Valve, 2 inch</td><td>100</td><td>Each</td></tr><tr><td>2</td><td>Gate Valve, 4 inch</td><td>50</td><td>Each</td></tr><tr><td>3</td><td>Check Valve, 6 inch</td><td>30</td><td>Each</td></tr></table>\nTITLE_1_1\n1. Invitation to Tender (ITT)Subject: Invitation to Tender for Supply of ValvesDear Supplier,We invite you to submit a tender for the supply of valves as per the attached specifications. The tender number is TND-2024-001, and the deadline for submission is August 31, 2024. Please find the detailed tender package below.For any inquiries, contact John Doe at <EMAIL> or +1-234-567-8900.Best regards,RobertProject ManagerOil and Gas Company\nQuestion:\nCould you please provide the manufacturing standards for each of the three types of valves? "}],
#                                         "You are an assistant to answer the question from the user. Use the provided sources on user messages to make decisions. You can ignore any of the sources if not relevant. When you are answering, use the source and cite them with the answer. You have to answer the question and establish the validity of the answer using the provided sources. Cite the sources by their name and preceding section number on the title if needed. The answer should be very detailed and convincing. The answer should be in one or several paragraphs, avoid using numeric points, bullet points, etc. Don't use any other source to answer other than the provided sources. Include the relevant details as much as possible.  If you have multiple values, answer the query, and mention all of them. For example, if a query demands a singular value, and in the documents multiple values exist, answer with multiple values. Do not include any apologizing. Present information in a confident manner.", 0.1, "Claude 3 Sonnet", 4096)
#     print(response)