import json
import re
from bs4 import BeautifulSoup
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def extract_criteria_and_reasons(evaluation_data):
    try:
        # List to store filtered results
        filtered_results = []
        
        # Process each bid
        for bid in evaluation_data:
            if not isinstance(bid, dict):
                logger.warning(f"Skipping invalid bid format: {bid}")
                continue
                
            if 'evaluate_summary' not in bid:
                logger.warning(f"No evaluation summary found for bid: {bid.get('bidder_name', 'Unknown')}")
                continue
                
            bidder_name = bid.get('bidder_name', 'Unknown')
            
            # Parse the HTML content
            soup = BeautifulSoup(bid['evaluate_summary'], 'html.parser')
            
            # Find all criteria sections
            criteria_sections = soup.find_all('div', recursive=False)
            
            for section in criteria_sections:
                # Extract criteria name
                criteria_header = section.find('h3')
                if not criteria_header:
                    continue
                    
                criteria_name = criteria_header.text.strip().split(':')[1].strip()
                
                # Only process if criteria name is "Technical Criteria"
                if "Technical Criteria" in criteria_name:
                    # Extract reason
                    reason_element = section.find('h5', class_='reason')
                    if not reason_element:
                        logger.warning(f"No reason found for criteria '{criteria_name}' for bidder: {bidder_name}")
                        continue
                        
                    reason = reason_element.text.strip()
                    
                    filtered_results.append({
                        "criteria_name": criteria_name,
                        "name": bidder_name,
                        "evaluation": reason
                    })
                    
                    logger.info(f"Processed Technical Criteria for bidder: {bidder_name}")
        
        return filtered_results
        
    except Exception as e:
        logger.error(f"Unexpected error while processing evaluation data: {str(e)}")
        raise

def main():
    # Read the evaluation data from file
    from test_keys import v
    
    # Extract criteria and reasons
    results = extract_criteria_and_reasons(v)
    
    # Print results
    print("\nFiltered Results:")
    print("-" * 50)
    for result in results:
        print(f"\nBidder: {result['name']}")
        print(f"Criteria: {result['criteria_name']}")
        print(f"Evaluation: {result['evaluation']}")
        print("-" * 50)

if __name__ == "__main__":
    main()
