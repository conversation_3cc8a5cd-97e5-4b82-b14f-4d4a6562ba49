import re
from typing import Dict, Any, List, Optional, Union
from models import Chunks
class ClaudeTools:    
    
    """
    A collection of tools that can be used with Claude API.    Each tool includes its definition and execution logic.
    """    
    @staticmethod    
    def get_calculator_tool() -> Dict[str, Any]:
        """        
        Returns the calculator tool definition for Claude API
        """        
        return {
            "name": "calculator",            
            "description": "A simple calculator that performs basic arithmetic operations.",
            "input_schema": {                
                "type": "object",
                "properties": {                    
                    "expression": {
                        "type": "string",                        
                        "description": "The mathematical expression to evaluate (e.g., '2 + 3 * 4')."
                    }                },
                "required": ["expression"]            }
        }    
    @staticmethod    
    def execute_calculator(expression: str) -> str:
        """        
        Executes the calculator tool with the given expression
        Args:
            expression: The mathematical expression to evaluate            
        Returns:            The result as a string, or an error message
        """        
        # Remove any non-digit or non-operator characters from the expression
        expression = re.sub(r'[^0-9+\-*/().]', '', expression)        
        try:            
            # Evaluate the expression using the built-in eval() function
            result = eval(expression)  
            print(f'eval result is {result}')          
            return {"result":  f'{result}'}
        except (SyntaxError, ZeroDivisionError, NameError, TypeError, OverflowError):            
            return "Error: Invalid expression"
    
    @staticmethod
    def get_sum_tool() -> Dict[str, Any]:        
        """
        Returns the sum tool definition for Claude API        
        """
        return {            
            "name": "sum_numbers",
            "description": "Add up a list of numbers and return the total sum",            
            "input_schema": {
                "type": "object",                
                "properties": {
                "numbers": {                        
                    "type": "array",
                    "items": {"type": "number"},                        
                    "description": "Array of numbers to sum"
                    }                
                },
                "required": ["numbers"]            
            }
        }    
        
    @staticmethod    
    def execute_sum_tool(numbers: List[Union[int, float]]) -> Dict[str, Any]:
        """       
        Executes the sum tool with the given numbers
                
        Args:
            numbers: List of numbers to sum            
        Returns:            Dictionary with the sum result and input details
        """        
        try:
            total = sum(numbers)    
            print(f'total is {total}')
            return {
                "result": f'{total}',
                "input_numbers": numbers,
                "count": len(numbers),                
                "success": True
            }       
        except Exception as e:
            return {                
                "error": str(e),
                "success": False            
            }
            
    @staticmethod
    def get_document_source_page_tool() -> Dict[str, Any]:
        return {
            "name": "fetch_document_source_page",
            "description": "Fetch the full content of a specific page from a document in storage to provide additional context when the available document information is insufficient. This tool retrieves the complete content of a specified page from documents that are available in the system. IMPORTANT: This tool should only be used when the existing document context is genuinely insufficient to answer the user's question. Limit usage to a maximum of 3 calls per conversation to ensure efficient resource utilization. Only fetch pages that are directly relevant to answering the user's specific query.",
            "input_schema": {
                "type": "object",
                "properties": {
                "source": {
                    "type": "string",
                    "description": "The document name or source including its extension as it appears in the context as (source). Must exactly match one of the available document identifiers that have been provided in the conversation context."
                },
                "page_number": {
                    "type": "integer",
                    "description": "The specific page number to retrieve from the document. Page numbers should be positive integers starting from 1.",
                    "minimum": 1
                },
                "reason": {
                    "type": "string",
                    "description": "Brief explanation of why this specific page needs to be fetched and how it will help answer the user's question. This helps ensure the tool is used appropriately and efficiently."
                }
                },
                "required": ["source", "page_number", "reason"]
            }
        }

    
    @staticmethod
    def execute_document_source_page_tool(source: str, page_number: int, reason: str) -> Dict[str, Any]:
        try:
            # Fetch the page content from the document source
            print(f"Executing document source page tool with source: {source}, page_number: {page_number}, reason: {reason}")            
            page = Chunks.get_by(source=source, page_number=page_number)
            page_content = page[0]['content'] if page else ""
            return {
                "source": source,
                "page_number": page_number,
                "reason": reason,
                "page_content": page_content,
                "success": True,
                "result": f"Fetched page {page_number} from document {source} with reason: {reason} and content: {page_content}"
            }
        except Exception as e:
            return {
                "error": f"Error fetching page content: {str(e)}",
                "success": False,
                "result": "Could not fetch page content from the document source."
            }

    @staticmethod
    def process_tool_call(tool_name: str, tool_input: Dict[str, Any]) -> Union[str, Dict[str, Any]]:        
        """
        Process a tool call based on the tool name and input        
        Args:            tool_name: The name of the tool to execute
            tool_input: The input parameters for the tool            
        Returns:            The result of the tool execution
        """        
        if tool_name == "calculator":
            return ClaudeTools.execute_calculator(tool_input["expression"])        
        elif tool_name == "sum_numbers":
            return ClaudeTools.execute_sum_tool(tool_input["numbers"])
        elif tool_name == "fetch_document_source_page":
            return ClaudeTools.execute_document_source_page_tool(tool_input["source"], tool_input["page_number"], tool_input["reason"])
        else:
            return {"error": f"Unknown tool: {tool_name}", "success": False}

























































