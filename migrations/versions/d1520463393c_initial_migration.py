"""Initial Migration

Revision ID: d1520463393c
Revises:
Create Date: 2024-08-03 03:19:05.212659

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd1520463393c'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    if not conn.dialect.has_table(conn, 'chunks'):
        op.create_table('chunks',
        sa.Column('id', sa.String(length=36), nullable=False),
        sa.Column('title', sa.String(length=255), nullable=False),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('source', sa.String(length=255), nullable=True),
        sa.Column('summary', sa.Text(), nullable=True),
        sa.Column('project_id', sa.String(length=36), nullable=False),
        sa.Column('file_id', sa.String(length=36), nullable=False),
        sa.PrimaryKeyConstraint('id')
        )
    if not conn.dialect.has_table(conn, 'project'):
        op.create_table('project',
        sa.Column('id', sa.String(length=36), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('entity_type', sa.String(length=255), nullable=True),
        sa.Column('assistant_id', sa.String(length=255), nullable=True),
        sa.Column('has_deleted_file', sa.Boolean(), nullable=False),
        sa.Column('discipline_code', sa.String(length=255), nullable=True),
        sa.PrimaryKeyConstraint('id')
        )
    if not conn.dialect.has_table(conn, 'tender'):
        op.create_table('tender',
        sa.Column('id', sa.String(length=36), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('project_id', sa.String(length=36), nullable=False),
        sa.Column('file_type', sa.String(length=255), nullable=False),
        sa.Column('status', sa.String(length=255), nullable=True),
        sa.Column('tried', sa.Integer(), nullable=True),
        sa.Column('score', sa.Integer(), nullable=True),
        sa.Column('criteria', sa.Text(), nullable=True),
        sa.PrimaryKeyConstraint('id')
        )
    if not conn.dialect.has_table(conn, 'file'):
        op.create_table('file',
        sa.Column('id', sa.String(length=36), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('project_id', sa.String(length=36), nullable=False),
        sa.Column('file_type', sa.String(length=255), nullable=False),
        sa.Column('status', sa.String(length=255), nullable=True),
        sa.Column('merge_status', sa.String(length=255), nullable=True),
        sa.Column('tried', sa.Integer(), nullable=True),
        sa.Column('category', sa.String(length=255), nullable=True),
        sa.Column('file_dirtry', sa.String(length=255), nullable=False),
        sa.ForeignKeyConstraint(['project_id'], ['project.id'], ),
        sa.PrimaryKeyConstraint('id')
        )
    if not conn.dialect.has_table(conn, 'merged_file'):
        op.create_table('merged_file',
        sa.Column('id', sa.String(length=36), nullable=False),
        sa.Column('project_id', sa.String(length=36), nullable=False),
        sa.Column('assistant_file_id', sa.String(length=255), nullable=True),
        sa.Column('status', sa.String(length=255), nullable=True),
        sa.ForeignKeyConstraint(['project_id'], ['project.id'], ),
        sa.PrimaryKeyConstraint('id')
        )
    if not conn.dialect.has_table(conn, 'requirement'):
        op.create_table('requirement',
        sa.Column('id', sa.String(length=36), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('project_id', sa.String(length=36), nullable=False),
        sa.Column('file_type', sa.String(length=255), nullable=False),
        sa.Column('category', sa.String(length=255), nullable=True),
        sa.Column('status', sa.String(length=255), nullable=True),
        sa.Column('tried', sa.Integer(), nullable=True),
        sa.Column('score', sa.Integer(), nullable=True),
        sa.Column('criteria', sa.String(length=255), nullable=True),
        sa.ForeignKeyConstraint(['project_id'], ['project.id'], ),
        sa.PrimaryKeyConstraint('id')
        )
    if not conn.dialect.has_table(conn, 'tags'):
        op.create_table('tags',
        sa.Column('id', sa.String(length=36), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('chunk_id', sa.String(length=36), nullable=False),
        sa.ForeignKeyConstraint(['chunk_id'], ['chunks.id'], ),
        sa.PrimaryKeyConstraint('id')
        )
    if not conn.dialect.has_table(conn, 'file_merged_file_association'):
        op.create_table('file_merged_file_association',
        sa.Column('id', sa.String(length=36), nullable=False),
        sa.Column('file_id', sa.String(length=36), nullable=False),
        sa.Column('merged_file_id', sa.String(length=36), nullable=False),
        sa.Column('association_type', sa.String(length=255), nullable=False),
        sa.Column('status', sa.String(length=255), nullable=False),
        sa.ForeignKeyConstraint(['file_id'], ['file.id'], ),
        sa.ForeignKeyConstraint(['merged_file_id'], ['merged_file.id'], ),
        sa.PrimaryKeyConstraint('id')
        )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('file_merged_file_association')
    op.drop_table('tags')
    op.drop_table('requirement')
    op.drop_table('merged_file')
    op.drop_table('file')
    op.drop_table('tender')
    op.drop_table('project')
    op.drop_table('chunks')