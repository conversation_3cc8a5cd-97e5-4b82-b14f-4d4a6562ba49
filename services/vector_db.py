import os
import uuid
import json
import chromadb
from chromadb import PersistentClient
from chromadb.utils import embedding_functions

class ChromaDBHandler:
    def __init__(self):
        # Get the path to the parent directory
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # Load environment variables from env.json file in the parent directory
        env_file_path = os.path.join(parent_dir, 'env.json')
        with open(env_file_path, 'r') as f:
            self.env_data = json.load(f)

        self.default_ef = embedding_functions.DefaultEmbeddingFunction()
        self.project_base_path = self.env_data.get("WORKSPACE_DIR")

    def get_metadata(self, data):
        return [{"title": x["title"], "content": x["content"], "source": x["source"]} for x in data]

    def save_to_db(self, project_id, data):
        client = PersistentClient(path=f'{self.project_base_path}/chromadb/{project_id}')
        collection = client.get_or_create_collection(name=project_id, embedding_function=self.default_ef, metadata={"hnsw:space": "cosine"})
        metadatas = self.get_metadata(data)
        documents = [x['summary'] for x in data]
        ids = [str(uuid.uuid4()) for x in range(0, len(data))]
        collection.add(documents=documents, ids=ids, metadatas=metadatas)
        return ids

    def count_entries(self, project_id):
        client = PersistentClient(path=f'{self.project_base_path}/chromadb/{project_id}')
        collection = client.get_or_create_collection(name=project_id, embedding_function=self.default_ef)
        return collection.count()

    def get_data(self, project_id, queries, limit=3):
        client = PersistentClient(path=f'{self.project_base_path}/chromadb/{project_id}')
        collection = client.get_or_create_collection(name=project_id, embedding_function=self.default_ef)
        results = collection.query(
            query_texts=queries,
            n_results=limit
        )
        return results

    def get_all_data(self, project_id):
        client = PersistentClient(path=f'{self.project_base_path}/chromadb/{project_id}')
        collection = client.get_or_create_collection(name=project_id, embedding_function=self.default_ef)
        results = collection.query(
            query_texts=[""],
            n_results=collection.count()
        )
        return results

