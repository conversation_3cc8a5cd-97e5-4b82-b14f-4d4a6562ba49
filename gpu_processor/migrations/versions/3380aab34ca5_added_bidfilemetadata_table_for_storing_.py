"""Added BidFileMetadata table for storing bid file metadata as JSON

Revision ID: 3380aab34ca5
Revises: 64e5bd5161df
Create Date: 2025-02-27 23:20:42.672132

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3380aab34ca5'
down_revision = '64e5bd5161df'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('bid_file_metadata',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('bid_id', sa.String(length=36), nullable=False),
    sa.Column('bid_metadata', sa.Text(), nullable=False),
    sa.Column('status', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['bid_id'], ['bids.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('bid_file_metadata', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_bid_file_metadata_bid_id'), ['bid_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_bid_file_metadata_status'), ['status'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('bid_file_metadata', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_bid_file_metadata_status'))
        batch_op.drop_index(batch_op.f('ix_bid_file_metadata_bid_id'))

    op.drop_table('bid_file_metadata')
    # ### end Alembic commands ###
