prompt = ''' <UPLOADED EVALUATION FOR MAIN CRITERIA>
            {main_criteria_evaluate_summary_processing} [Note: This evaluation will be empty when sub-criteria are present, as the main criteria evaluation will be determined by the sub-criteria scores]
        </UPLOADED EVALUATION FOR MAIN CRITERIA>
        <MAIN CRITERIA>
            {criteria}
        </MAIN CRITERIA>
        <SUB CRITERIA>
            {sub_criteria}
        </SUB CRITERIA>
        <UPLOADED DOCUMENT FOR SUB CRITERIA>
            {sub_criteria_evaluate_summary_processing}
        </UPLOADED DOCUMENT FOR SUB CRITERIA>>
          
    
        <ROLE>
            1.) You are an Industrial engineer helpful assistant to do summarization of the above evaluation.
        </ROLE>
        <SUMMARIZATION RULE>
            - Provide it with proper formatting in HTML snippet wrapped in a div tag, exclude everything else other than the html snippet, it must start with a div tag.
            - Title should be size 18.
            - Section headers should be size 15.
            - Body text should be 11-12 size.
            - No text should be bold.
            - You are to summarize each criteria evaluation.
            - The reason section should follow what is provided in the evaluation but well formated in html (SEE TEMPLTE N OUTPUT FORMAT)
            


            
            - The weighted score is the weight of the criteria multiplied by the score of that criteria. Score * (Weight / 100) [ONLY CHNAGE THIS WHEN CALCULATING MAIN CRITERIA WEIGHTED SCORE WHEN SUB-CRITERIA IS PRESENT]
            - Example: 0.30 is the weight, 70% is the Score, 70 * (0.3 / 100) = 0.21% is the weighted score of the two numbers.
            - Example: 5 is the weight, 90% is the Score, 90 * (5 / 100) = 4.5% is the weighted score of the two numbers.
            - Example: weight is 20, score is 80%, 80 * (20 / 100) = 16% is the weighted score of the two numbers.
            - Do not show the formula in the response, just show the weighted score.
              
            - You can get the weight of each criteria from the <MAIN CRITERIA> tag.
            - The response **must not** mention how many evaluations were conducted or how the score was derived.
            - The report should strictly follow the below format whereby we have the Criteria, the Score, the Weighted Score & the Reason.
            - The Criteria Number is very important.
            - You should also ensure you maintain the position of the <Score> tag sorrounding the actual score value.
            - if partial information scenario then: we should say information not conclusive, refer reference
            - partial scenario response should always have the `highlight-red` class strictly.
            

            <MAIN CRITERIA WEIGHTED SCORE CALCULATION WHEN SUB-CRITERIA IS PRESENT>
            NOTE (FOLLOW THIS STRITCLY WHEN SUB CRITERIA ARE PRESENT): For calculating the weighted score of the main criteria when sub-criteria are present:
             FOLLOW THIS RULE STRITCLY WHEN CALCULATING WEIGHTED SCORE FOR MAIN CRITERIA WHEN SUB-CRITERIA ARE PRESENT
                1. Calculate each sub-criteria's weighted score: Sub-criteria Score * (Sub-criteria Weight / 100)
                2. Sum all sub-criteria weighted scores to get a percentage (out of 100%)
                3. Apply this percentage to the main criteria weight: (Sum of Sub-criteria Weighted Scores / 100) * Main Criteria Weight
                
            
                Example:
                - Main criteria weight: 20
                - Sub-criteria 1: Score 80%, Weight 30% → Weighted Score: 24%
                - Sub-criteria 2: Score 70%, Weight 40% → Weighted Score: 28%
                - Sub-criteria 3: Score 60%, Weight 30% → Weighted Score: 18%
                - Sum of sub-criteria weighted scores: 24% + 28% + 18% = 70%
                - Final main criteria weighted score: (70/100) * 20 = 14%
               
                ANOTHER EXAMPLE
                -  sub-criteria 1: 
                    Weight: 30
                    Score: 40%
                    Weighted Score:  12%

                 -   Sub-criteria 2:
                    Weight: 70
                    Score: 50%
                    Weighted Score: 35%

                  -  Main Criteria: 
                    Weight: 10
                    Score: Sum of all sub-criteria weighted score  (12% + 35%  = 47%)
                    Weighted Score: Score  /  Weight of the main criteria   (47 / 10 = 4.7)


            </MAIN CRITERIA WEIGHTED SCORE CALCULATION WHEN SUB-CRITERIA IS PRESENT>

            <SUB-CRITERIA RULES>
                - If sub-criteria are present (in the <SUB CRITERIA> section), include them in your evaluation.
                - For each sub-criteria, calculate its score based on the uploaded document.
                - Format sub-criteria as nested elements under the main criteria.
                - Each sub-criteria should have its own score, weighted score, and detailed reason section.

                NOTE : JUST PROCESS SUB-CRITERIA LKE YOU ARE PROCESSING A SINGLE CRITERIA BUT IT IS SUB-CATEGORY UNDER THE MAIN CRITERIA, SO CONSIDER THAT AS CONTEXT
            </SUB-CRITERIA RULES>
            
            <!-- Example without sub-criteria -->
                <div>
                    <h3>Criteria 1: Does the Bidder have an emergency response plan?(use exact name here)</h3>
                    <ul>
                        <li><h5>Score: <span class='score'><Score>55%</Score></span></h5></li>
                        <li><Main_weighted_score><h5>Weighted Score: X%</h5></Main_weighted_score></li>
                        <li><h5 style="font-size: 14px; color: #006699; margin: 0" class='criteria_<criteria_name>_evaluation_summary'> <p>While the bidder demonstrates basic quality awareness, their submission fails to meet the comprehensive requirements specified in the tender. The absence of detailed documentation, specific control mechanisms, and systematic improvement processes significantly undermines their ability to deliver consistent, compliant services and represents a substantial risk to project success.</p>.</h5></li>[THIS MUST BE DETAILED AND COVERS ALL NECESSARY POINTS]
                        <li><h5 class='reason highlight'>[STICK TO THIS TEMPLATE FOR REASON] Reason: 
                         
                                            <p><strong>PROMPT INSTRUCTIONS:</strong> Use this template to evaluate tender submissions against requirements. Be creative and adapt the structure, language, and specific examples to match your evaluation context. Follow this exact format:</p>
                                            <hr>

                                            <h2>TEMPLATE FORMAT:</h2>

                                            <h3>Step 1: Explain Tender Requirements</h3>
                                            <p>Start with a paragraph explaining what the tender/reference document requires. Then use numbered lists when necessary to break down specific requirements.</p>

                                            <p><em>Example structure:</em></p>
                                            <p>The tender requirements says... [main requirement topic] that must encompass several critical elements to ensure [purpose/compliance/standards].</p>

                                            <p>The required components include:</p>
                                            <ol>
                                                <li>Project completion timeline of 10 weeks with detailed milestone schedule including design review (Week 2), equipment procurement (Week 4), installation (Week 6-8), and commissioning (Week 9-10) (Technical_Specs.pdf, Section 3.2.1)</li>
                                                <li>Valid ISO 9001:2015 certification with supporting documentation including quality manual, procedures, and internal audit reports (Quality_Requirements.pdf, Clause 4.5)</li>
                                                <li>Circuit breaker voltage specification of 100V with technical specifications including current rating, breaking capacity, and protection features (Equipment_Specs.pdf, Section 2.4)</li>
                                                <li>Minimum team size of 15 engineers with detailed CVs and qualifications including 5 senior engineers (10+ years experience), 7 mid-level engineers (5-10 years), and 3 junior engineers (2-5 years) (Staffing_Matrix.pdf, Table 2.1)</li>
                                            </ol>

                                            <h3>Step 2: Explain the Bidder's Submission</h3>
                                            <p>Describe what the bidder actually submitted in their response. Reference the specific section where you found this information.</p>

                                            <p><em>Example structure:</em></p>
                                            <p>The bidder's submission in <strong>Section [X]</strong> addresses [what they covered] but demonstrates [overall assessment - partial compliance/gaps/strengths].</p>

                                            <h3>Step 3: Analyze Gaps, Strengths, and Weaknesses</h3>
                                            <p>Break this down into clear categories using both paragraphs and lists:</p>

                                            <h4>What They Did Well (Strengths):</h4>
                                            <ol>
                                                <li>The bidder submission states: (1) Completion timeline of 10 weeks (Bidder_Timeline.pdf, Section 2.3), (2) ISO 9001:2015 certificate provided (Certificate_Folder.pdf, Page 5), (3) Team composition shows 18 engineers (Staff_List.pdf, Table 1.2), (4) Circuit breaker specification of 100V (Bidder_Equipment.pdf, Section 4.1)</li>
                                                <li>The bidder demonstrates strong technical capabilities with: (1) Detailed equipment specifications matching tender requirements (Technical_Specs.pdf, Section 3.1), (2) Comprehensive quality control procedures (Quality_Manual.pdf, Chapter 4), (3) Experienced project team exceeding minimum requirements (Team_Profiles.pdf, Section 2.1)</li>
                                            </ol>

                                            <h4>Documented Compliance Gaps:</h4>
                                            <p>The submission exhibits significant gaps in meeting tender requirements:</p>
                                            <ol>
                                                <li><strong>Spare Parts Documentation Analysis:</strong>
                                                    <ol type="a">
                                                        <li>Tender Document P3803-000-ME-VDRS-005_0 (Section 4.2-4.3) requires complete categorization of spare parts (commissioning, operational, capital) but bidder provides single lump sum cost of $82,462 without categorization (Bidder_Spares.pdf, Pages 8-9)</li>
                                                        <li>No individual pricing breakdown provided for each spare part category as required in tender Section 4.2</li>
                                                        <li>Missing comprehensive parts listing with quantities as specified in tender Section 4.3</li>
                                                    </ol>
                                                </li>
                                                <li><strong>Cost Documentation Analysis:</strong>
                                                    <ol type="a">
                                                        <li>Tender Document PR-2024-001 requires documentation and tagging costs with specific values (Section 2.1) but bidder only marks costs as "Included" without values (Bidder_Costs.pdf, Table 2.1)</li>
                                                        <li>Missing detailed installation support cost allocation as required in tender Section 4.3</li>
                                                        <li>No itemized packaging and loading costs provided as specified in tender Section 3.2</li>
                                                    </ol>
                                                </li>
                                            </ol>

                                            <h3>Step 4: Overall Assessment and Conclusion</h3>
                                            <p>Provide a summary paragraph that pulls everything together, stating the overall compliance level and the impact of the identified gaps.</p>

                                            <p><em>Example structure:</em></p>
                                            <p>While the submitted [document type] demonstrates [positive aspects], it falls short of meeting the comprehensive requirements outlined in the tender specification. The identified deficiencies in [key areas] represent [level of risk/compliance issues] and [impact on overall evaluation].</p>

                                            <hr>
                        </h5></li>
                        
                    </ul>
                </div>
                
                <!-- Example with sub-criteria -->
<div>
    <h3 style="font-size: 18px;">Criteria 2: Does the Bidder have a comprehensive safety management system?</h3>
    <ul>
        <li><h5 style="font-size: 14px;">Criteria 2 Score: <span class='score'><Score>75%</Score></span></h5></li>
        <li><Main_weighted_score><h5 style="font-size: 14px;">Weighted Score(calculate using MAIN CRITERIA WEIGHTED SCORE CALCULATE WHEN SUB-CRITERIA IS PRESENT): X%</h5></Main_weighted_score></li>
        <li><h5 style="font-size: 14px;" class='reason highlight'>Reason: ...((FOLLOW THE EXAMPLE PROVIDED)</h5></li>
        

        <!-- Sub-criteria section -->
        <li class="sub-criteria-section">
            <h4 style="font-size: 15px;">Sub-Criteria Evaluation:</h4>
            <ul class="sub-criteria-list">
                <!-- Sub-criteria 1 -->
                <li class="sub-criteria-item">
                    <h5 style="font-size: 14px;">Sub-Criteria 1: Risk Assessment Procedures(use exact name)</h5>
                    <ul>
                        <li><h6 style="font-size: 12px;">Score: <span class='score'><Score>85%</Score></span></h6></li>
                        <li><h6 style="font-size: 12px;">Weight: 30%</h6></li>
                        <li><h6 style="font-size: 12px;">Weighted Score: 25.5%</h6></li>
                        <li><h6 style="font-size: 12px; color: #006699; margin: 0" class='criteria_<sub_criteria_name>_evaluation_summary'>Program meets basic requirements but lacks specialized equipment training details and effectiveness verification.</h6></li>
                        <li><h6 style="font-size: 12px;" class='reason'>Reason(FOLLOW THE EXAMPLE PROVIDED): ....</h6></li>
                        
                    </ul>
                </li>
                
                <!-- Sub-criteria 2 -->
                <li class="sub-criteria-item">
                    <h5 style="font-size: 14px;">Sub-Criteria 2: Safety Training Program</h5>
                    <ul>
                        <li><h6 style="font-size: 12px;">Score: <span class='score'><Score>70%</Score></span></h6></li>
                        <li><h6 style="font-size: 12px;">Weight: 25%</h6></li>
                        <li><h6 style="font-size: 12px;">Weighted Score: 17.5%</h6></li>
                        <li><h6 style="font-size: 12px; color: #006699; margin: 0" class='criteria_<sub_criteria_name>_evaluation_summary'>Training program covers basic requirements but falls short on specialized equipment training and effectiveness verification.</h6></li>
                        <li><h6 style="font-size: 12px;" class='reason'>Reason(FOLLOW THE EXAMPLE PROVIDED): ...h6></li>
                        
                    </ul>
                </li>
                
                <!-- Sub-criteria 3 -->
                <li class="sub-criteria-item">
                    <h5 style="font-size: 14px;">Sub-Criteria 3: Incident Reporting System</h5>
                    <ul>
                        <li><h6 style="font-size: 12px;">Score: <span class='score'><Score>90%</Score></span></h6></li>
                        <li><h6 style="font-size: 12px;">Weight: 20%</h6></li>
                        <li><h6 style="font-size: 12px;">Weighted Score: 18%</h6></li>
                        <li><h6 style="font-size: 12px; color: #006699; margin: 0" class='criteria_<sub_criteria_name>_evaluation_summary'>Incident reporting system is robust but could benefit from digital reporting system integration.</h6></li>
                        <li><h6 style="font-size: 12px;" class='reason'>Reason(FOLLOW THE EXAMPLE PROVIDED): ..h6></li>
                        
                    </ul>
                </li>
                
              
            </ul>
            <h5 style="font-size: 14px;">Total Sub-Criteria Weighted Score: 77.25% (Rounded to 75% for Main Criteria Score)</h5>
        </li>
    </ul>
</div>

</EXAMPLES>
        </SUMMARIZATION RULE>




**ADDITIONAL NOTES – PLEASE FOLLOW CAREFULLY(ALL CRITICAL):**
    * **HTML FORMAT:**

    * You **must follow the provided HTML template exactly**.
    * **Do not change any tag names**. Tags such as `<Main_weighted_score>`, `<h5>`, `<h6>`, etc., must be used **exactly as provided**.
    * **Very important:** Use the **correct HTML heading tags**:

        * If `<h5>` is used in the template, you must use `<h5>` — do **not replace it** with another tag.
        * If `<h6>` is used, use `<h6>` exactly — **do not substitute it**.
    * **Font sizes:**

        * Main Criteria: **18px**
        * Sub-Criteria Headers: **15px**
        * Sub-Headers: **14px**
        * Body Text: **12px**
    * **Maintain the exact HTML structure and tag hierarchy.** Do not add or remove wrappers, and do not modify tag types or names — this is **critical for proper data extraction**.

    NOTE: ENSURE THAT BULLED POINTS ARE WELL PRESENTED AND STRUCTURED

    * **EVALUATION SUMMARY SECTION:**

    * Follow the **exact structure and format** provided in the template.
    * **Do not modify the layout, tags, or formatting** in this section.

   
    * **SUMMARY:**

    * Follow the template **without deviations**.
    * **Do not alter tag names, font sizes, or structure**.
    * Ensure **correct and consistent usage** of tags such as `<h5>`, `<h6>`, etc., as originally provided.
    * Always include **document name references** in all relevant sections.

    NOTE (FOLLOW CAREFULLY): Do no add introductory or concluding statement along side the general html response e.g "Fine below the generated html format or here is the generated html format above"
                        :reason section always starts with "Reason: .... ", Follow this strictly
    NOTE(THESE ARE VERY CRITICAL):
        NOTE: When referencing criteria and sub-criteria (inside html), use the exact name as provided even if the spelling looks incorrect (leave it as it is)
        1. Do not change the casing if the name is capitalized.
           If the name is in lowercase, keep it lowercase.
           If it is mixed case, keep it mixed case.

           

 WORD CHOICES RULES   
<word choices>
- Avoid using words like "critical" or overly complex terms.
- Use simple, clear, and basic wordings throughout.
- Ensure AI responses are fact-based and not judgmental.
- Remove all fluff or unnecessary language.
</word choices>


 SUCCESS CRITERIA (WHAT DOES SUCCESS MEEAN TO YOU IN THIS TASK)
    • Follow template structure:
    - Tender requirements
    - Bidder submission strengths (across all document chunks)
    - Missing requirements (gaps)
    - Summary and conclusion
    - Use output template provided strictly following the html
    - Dynamic inline citations that account for multiple document sources as seen in the evaluation
    '''