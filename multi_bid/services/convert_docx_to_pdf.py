from docx import Document
from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas
import os



class DocxToPdf:

    def __init__(self):
        pass

    def docx_to_text(self, docx_path):
        """
        Extract text from a DOCX file.

        Parameters:
        docx_path (str): The path to the DOCX file.

        Returns:
        str: The extracted text.
        """
        print('docx_path:', docx_path)
        doc = Document(docx_path)
        text = "\n".join([p.text for p in doc.paragraphs])
        return text

    def text_to_pdf(self, text, pdf_path):
        """
        Convert text to a PDF file.

        Parameters:
        text (str): The text to convert.
        pdf_path (str): The path to save the PDF file.
        """
        c = canvas.Canvas(pdf_path, pagesize=letter)
        width, height = letter
        text_object = c.beginText(40, height - 40)
        text_object.setFont("Helvetica", 12)
        text_object.setTextOrigin(40, height - 40)
        text_object.textLines(text)
        c.drawText(text_object)
        c.showPage()
        c.save()

    def convert_docx_to_pdf(self, docx_path, output_dir=None):
        """
        Convert a DOCX file to a PDF file.

        Parameters:
        docx_path (str): The path to the DOCX file.
        output_dir (str): The directory where the PDF will be saved. If None, save in the same directory as the DOCX file.

        Returns:
        str: The path to the generated PDF file.
        """
        if output_dir is None:
            output_dir = os.path.dirname(docx_path)

        # Ensure output directory exists
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # Convert DOCX to text
        text = self.docx_to_text(docx_path)

        # Convert text to PDF
        pdf_path = os.path.join(output_dir, os.path.splitext(os.path.basename(docx_path))[0] + '.pdf')
        self.text_to_pdf(text, pdf_path)

        return pdf_path

    def convert_docx_to_pdf_file_to_file(self, file, temp_file_path, output_dir=None):
        """
        Convert a DOCX file to a PDF file.

        Parameters:
        file (str): The path to the DOCX file.
        output_dir (str): The directory where the PDF will be saved. If None, save in the same directory as the DOCX file.

        Returns:
        str: The path to the generated PDF file.
        """
        file.save(temp_file_path)
        
        text = self.docx_to_text(temp_file_path)
        self.text_to_pdf(text, output_dir)
        
        
        
        if output_dir != temp_file_path:
            os.remove(temp_file_path)
            
        return output_dir
# Example usage
# docx_path = os.path.join('chronobid', '3913fc17-ee29-4182-b0d0-334270918624', '5.Scope_of_Work.docx')
# output_dir = 'services'
# pdf_path = convert_docx_to_pdf(docx_path, output_dir)
# print(f'PDF saved at: {pdf_path}')
