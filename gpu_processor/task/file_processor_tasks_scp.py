# /home/<USER>/file_processor_tasks.py
from extensions import celery
from socket_instance import socket_instance
from models import Requirement
from services.scp_file_processor import SCPFileProcessor
import json

@celery.task(bind=True, max_retries=1, name="task.file_processor_tasks_scp.process_files_scp_task")
def process_files_scp_task(self, req_id, file_names_arr, existing_req):
    try:
        socket_manager = socket_instance.get_instance()

        print(req_id, file_names_arr, existing_req)

        print('checkpoint 12 scp task...')
        # Test emit
        socket_manager.emit_to_client(
            req_id,
            'test_event',
            {'message': 'Test from SCP Celery'},
            namespace='/processing_files'
        )
        if existing_req:
            existing_req_names = json.loads(existing_req["name"])
            new_names = file_names_arr + existing_req_names
            Requirement.update(req_id, name=json.dumps(new_names))
        else:
            Requirement.update(req_id, name=json.dumps(file_names_arr))

        try:
            print('starting to process scp files...')
            file_processor = SCPFileProcessor(socket_manager, req_id, existing_req, instant_file_names=file_names_arr)
            file_processor.process_files()
        except Exception as e:
            print(f"Error during file processing: {e}")

        return "SCP Files processing completed successfully."
    except Exception as e:
        print(f"Error during file processing: {e}")
        return str(e)