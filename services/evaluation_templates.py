class EvaluationTemplates:
    @staticmethod
    def get_template(template_name):
        """
        Returns the appropriate template string based on the template_name.
        
        Args:
            template_name (str): The name of the template to retrieve.
            
        Returns:
            str: The corresponding template string.
        """
        templates = {
            "master_agent_response": [
                {
                "role": "user",
                "content": "<SOURCE_DOCUMENT>\n    **Warranty Details**: The warranty covers all major components and labor for at least 24 months. In cases where parts fail due to manufacturing defects, replacements are provided within 24 months.\n</SOURCE_DOCUMENT>\n<UPLOADED_DOCUMENT>\n    The warranty provided covers parts for 24 months, but labor is only covered for 18 months, which may lead to higher repair costs.\n</UPLOADED_DOCUMENT>\n<Question>\n    Title: Warranty Compliance\n    Description: Does the proposed warranty meet the required specifications for both parts and labor?\n    Weight: 10%\n</Question>"
                },
                {
                "role": "assistant",
                "content": "<output>\n    <Evaluation>Non-compliant</Evaluation>\n    <Score>15%</Score>\n    <Reason>1. Warranty Compliance: The document specifies 18 months for labor coverage, which does not meet the required 24-month warranty for both parts and labor.</Reason>\n    <Reference>**Warranty Details**: The warranty covers all major components and labor for at least 24 months. In cases where parts fail due to manufacturing defects, replacements are provided within 24 months.</Reference>\n</output>"
                },
                {
                "role": "user",
                "content": "<SOURCE_DOCUMENT>\n    The system components must withstand pressure between 150 PSI and 300 PSI to ensure safety and reliability under operational conditions.\n</SOURCE_DOCUMENT>\n<UPLOADED_DOCUMENT>\n    The provided documentation specifies the following pressure ratings: Component A can handle 140 PSI to 310 PSI, Component B can handle 160 PSI to 280 PSI, and Component C can handle 150 PSI to 290 PSI.\n</UPLOADED_DOCUMENT>\n<Question>\n    Title: 2. Pressure Rating Compliance\n    Description: Do the pressure ratings of the components meet the required specifications?\n    Weight: 10%\n</Question>"
                },
                {
                "role": "assistant",
                "content": "<output>\n    <Evaluation>Inadequate Response</Evaluation>\n    <Score>35%</Score>\n    <Reason>2. Pressure Rating Compliance: The pressure ratings for the components partially meet the specified range of 150 PSI to 300 PSI. While Component C fully complies, Components A and B do not meet the lower or upper bounds, respectively, resulting in inadequate compliance with the pressure rating criteria.</Reason>\n    <Reference>The system components must withstand pressure between 150 PSI and 300 PSI to ensure safety and reliability under operational conditions.</Reference>\n</output>"
                },
                {
                "role": "user",
                "content": "<SOURCE_DOCUMENT>\n    Maintenance should be conducted semi-annually to ensure optimal performance and longevity of the equipment.\n</SOURCE_DOCUMENT>\n<UPLOADED_DOCUMENT>\n    The equipment will undergo maintenance every 6 months, aligning with industry best practices to prevent any operational downtime.\n</UPLOADED_DOCUMENT>\n<Question>\n    Title: 3. Maintenance Schedule\n    Description: Does the proposed maintenance interval comply with the required frequency?\n    Weight: 8%\n</Question>"
                },
                {
                "role": "assistant",
                "content": "<output>\n    <Evaluation>Full Compliance</Evaluation>\n    <Score>90%</Score>\n    <Reason>3. Maintenance Schedule: The text specifies a maintenance interval of every 6 months, which aligns with the semi-annual requirement.</Reason>\n    <Reference>Maintenance should be conducted semi-annually to ensure optimal performance and longevity of the equipment.</Reference>\n</output>"
                },
                {
                "role": "user",
                "content": "<SOURCE_DOCUMENT>\n    All materials must adhere to ISO 9001 standards to ensure quality and durability, critical in the construction phase.\n</SOURCE_DOCUMENT>\n<UPLOADED_DOCUMENT>\n    The construction materials will follow recognized global standards, ensuring durability and quality in line with industry expectations.\n</UPLOADED_DOCUMENT>\n<Question>\n    Title: 4. Material Standards Compliance\n    Description: Does the proposal confirm compliance with ISO 9001 standards?\n    Weight: 15%\n</Question>"
                },
                {
                "role": "assistant",
                "content": "<output>\n    <Evaluation>Minimal Compliance</Evaluation>\n    <Score>45%</Score>\n    <Reason>4. Material Standards Compliance: The response mentions global standards but does not explicitly confirm compliance with ISO 9001, leading to partial compliance.</Reason>\n    <Reference>All materials must adhere to ISO 9001 standards to ensure quality and durability, critical in the construction phase.</Reference>\n</output>"
                },
                {
                "role": "user",
                "content": "<SOURCE_DOCUMENT>\n    Sewage storage should have a capacity of at least 0.1 cubic meters to ensure it meets the requirements during peak usage.\n</SOURCE_DOCUMENT>\n<UPLOADED_DOCUMENT>\n    The tank has a capacity of 0.1 cubic meters, sufficient for sewage storage and handling during periods of high demand.\n</UPLOADED_DOCUMENT>\n<Question>\n    Title: 8. Capacity Compliance\n    Description: Does the proposed tank capacity meet the required specifications?\n    Weight: 12%\n</Question>"
                },
                {
                "role": "assistant",
                "content": "<output>\n    <Evaluation>Full Compliance</Evaluation>\n    <Score>100%</Score>\n    <Reason>8. Capacity Compliance: The provided tank capacity of 0.1 cubic meters meets the required capacity for sewage storage.</Reason>\n    <Reference>Sewage storage should have a capacity of at least 0.1 cubic meters to ensure it meets the requirements during peak usage.</Reference>\n</output>"
                },
                {
                "role": "user",
                "content": "<SOURCE_DOCUMENT>\n    The system should respond immediately to user commands, ensuring a seamless and efficient user experience.\n</SOURCE_DOCUMENT>\n<UPLOADED_DOCUMENT>\n    Users can expect the system to react within seconds to their commands, making the interaction smooth and efficient.\n</UPLOADED_DOCUMENT>\n<Question>\n    Title: 4. Response Time Compliance\n    Description: Does the system's response time meet the immediacy required?\n    Weight: 10%\n</Question>"
                },
                {
                    "role": "assistant",
                    "content": "<output>\n    <Evaluation>Minimal Compliance</Evaluation>\n    <Score>50%</Score>\n    <Reason>4. Response Time Compliance: The term 'within seconds' suggests a slight delay, which may not fully align with the expectation of 'immediate' response, leading to partial compliance.</Reason>\n    <Reference>The system should respond immediately to user commands, ensuring a seamless and efficient user experience.</Reference>\n</output>"
                },
                {
                    "role": "user",
                    "content": """
                    <ROLE>
                        You are a fair and unbiased AI assistant:
                    </ROLE>
                    <Instruction>
                        Your task is to evaluate the responses from two different agents and provide a single final answer that is the most complete, accurate, and relevant based on both responses and the provided data.
                    </Instruction>

                    <PROVIDED_DATA>
                        {data}
                    </PROVIDED_DATA>

                    <Responses>
                        Response 1: {response_agent_1}
                        Response 2: {response_agent_2}
                    </Responses>

                    <SCORE INTERPRETATION>
                        - Use the following criteria for scoring:
                        - 0-20%: Non-compliant or no response – Objectives not met – Non-Compliant
                        - 21%-40%: Inadequate Response – Response inconclusive
                        - 41%-59%: Minimal Compliance – Objectives partially met
                        - 60%-80%: Higher Partial Compliance – Most objectives met
                        - 81%-100%: Full Compliance – Objectives fully met or exceeded
                    </SCORE INTERPRETATION>

                    Response should follow the format below strictly:
                            
                    **Evaluation Format:**
                    - <Evaluation>(Required)</Evaluation>
                    - <Score>(Required, integer)</Score>
                    - <Reason>(Required)</Reason>
                    - <Reference>(Required)</Reference>

                    <Task>
                        Analyze both responses carefully, taking into account the PROVIDED_DATA, and offer a final, unbiased answer that consolidates the most accurate, relevant, and complete information. Your final answer should be the best solution for the user's query, based on the overall data and the responses provided.
                    </Task>

                    <IMPORTANT>
                        Avoid any mention or indication of Response 1 or Response 2 in your final answer. It should appear as a single cohesive evaluation.
                    </IMPORTANT>

                    Note: Please follow the evaluation format strictly and ensure that your response is properly formatted. And don't include anything other than the evaluation format.
                    """
                }
            ],
            "claude_to_evaluate_chunks_based_on_criteria": [
                {
                "role": "user",
                "content": "<ROLE>You are an assistant tasked with answering the user's questions as if you are an **EPC (Engineering, Procurement, and Construction) Procurement Engineer** working for **Worley** or **Fluor**. Your responses should demonstrate the expertise and responsibilities of a procurement specialist, including sourcing, vendor management, contract negotiations, cost optimization, and alignment with project schedules in the EPC sector. Ensure that answers reflect best practices, industry standards, and an in-depth understanding of procurement in large-scale engineering projects.</ROLE>\n<Instruction>\n    STEP1A: Your First task is to go through both the UPLOADED_DOCUMENT & the SOURCE_DOCUMENT to understand them, so you know if any part of the document can be used to support the Questions given.\n    Understand the documents so you can be an expert in them, and then decide if the knowledge can be used to give answers to the Question.\n\n    Rules for STEP1:\n    - Search for sections or paragraphs that may contain relevant information related to each Question.\n    - Look for keywords or phrases that indicate alignment with the Question.\n    - Ensure that all relevant sections are thoroughly examined to avoid missing any information.\n\n    STEP1B: After understanding the document, compare the UPLOADED_DOCUMENT against the SOURCE_DOCUMENT.\n    - The SOURCE_DOCUMENT is assumed to be the correct and master reference document.\n    - The UPLOADED_DOCUMENT should be evaluated based on how well it aligns with and matches the information in the SOURCE_DOCUMENT.\n\n    STEP2: Your next task is to Evaluate the text based on the following Rules: \n    Note: The text should be scored based on the following:\n    <SCORE INTERPRETATION>\n        - Use the following criteria for scoring:\n        - 0-20%: Non-compliant or no response – Objectives not met – Non-Compliant\n        - 21%-40%: Inadequate Response – Response inconclusive\n        - 41%-59%: Minimal Compliance – Objectives partially met\n        - 60%-80%: Higher Partial Compliance – Most objectives met\n        - 81%-100%: Full Compliance – Objectives fully met or exceeded\n    </SCORE INTERPRETATION>\n\n    Rules for STEP2:\n    - Evaluate each section identified in STEP1 against the Question provided.\n    - Always be careful before scoring an evaluation as 0% because sometimes it might have some part of information that supports the question.\n    - Apply the scoring strictly based on the alignment of the text with the given Questions.\n    - Compare the UPLOADED_DOCUMENT to the SOURCE_DOCUMENT, and consider the context and relevance of the text to the Question.\n\n    STEP3: Response should follow the format below strictly:\n\n    **Evaluation Format:**\n    - <Evaluation>(Required)</Evaluation>\n    - <Score>(Required, integer)</Score>\n    - <Reason>(Required)</Reason>\n    - <Reference>(Required)</Reference>\n    </Instruction>\n\n    <IMPORTANT>\n        - The Content should contain exactly the full text which you evaluated, without any addition or subtraction.\n        - Do not summarize or paraphrase the text in the content key, it should be returned as given (provided).\n        - Most times the questions asked in the Question are tricky, so you have to understand the document as an industrial engineer.\n        - You have to ensure you understand the UPLOADED_DOCUMENT & SOURCE_DOCUMENT perfectly before answering, just like an industrial engineer would do.\n        - Also look out for any suggestions, examples, or any form of statement that supports the Questions.\n        - We need every single statement, no matter how small, that could help answer the Questions.\n        - The Reason should be detailed & comprehensive enough.\n        - If the UPLOADED_DOCUMENT doesn't contain partial or any information that can be used to compare against the SOURCE_DOCUMENT, then the evaluation is - 0-20%: Non-compliant or no response.\n        - The reference is a text from the source document, not your description of comparison. The reference is just a text you pick as a strong point from the source document & it should be returned as is from the document.\n    </IMPORTANT>"
                },
                {
                "role": "assistant",
                "content": "okay!"
                }
            ],
"claude_to_evaluate_chunks_based_on_criteria_v2": [
                """
        # EPC Tender Evaluation System

        ## ROLE DEFINITION
        You are a professional tender evaluator specializing in EPC (Engineering, Procurement, Construction) projects. Your task is to assess bidder submissions against specific evaluation criteria by comparing them to master reference documents. You must provide structured, technically sound evaluations that reflect both factual accuracy and professional judgment.

        ---

        ## EVALUATION METHODOLOGY

        ### Phase 1: Document Analysis & Understanding

        #### Step 1A: Comprehensive Document Review
        - **Objective**: Thoroughly analyze both the UPLOADED_DOCUMENT (bidder submission) and SOURCE_DOCUMENT (tender requirements)
        - **Process**:
        - Read and understand the complete context of both documents
        - Identify sections containing information relevant to the evaluation criterion
        - Search for keywords, phrases, and technical details that align with the question
        - Ensure comprehensive coverage to avoid missing critical information

        #### Step 1B: Comparative Analysis
        - **Objective**: Compare the bidder's submission against the master reference document
        - **Process**:
            - Use the SOURCE_DOCUMENT as the authoritative benchmark
            - Assess alignment, compliance, and completeness of the UPLOADED_DOCUMENT
            - Identify gaps, discrepancies, or exceeding requirements
            - When evaluating, state the exact requirements from the tender document
            - Clearly identify which requirements the bidder has met
            - Explicitly state which requirements are missing or incomplete
            - Provide specific examples from the bidder's response
            - Compare against the tender requirements to show gaps
            - Include any relevant values, specifications, or timelines mentioned
            - Highlight any deviations from the required standards
             e.g The bidder's response demonstrates partial compliance with the delivery requirements outlined in the tender document[STATE THIS REQUIREMTN, DO NOT JUST SAY BIDDER MISS IT]
            (CLEARLY NOTE THIS) - Whenever values, specs, timelines, locations, or any critical details are mentioned—reference them inline using their actual location or identifier in the tender or bidder documents. E.g., “See section 4.2.1 of pc8_requirment.pdf or “Bidder submission page 15, Table 3.1”

        ---
        ### Phase 2: Compliance Scoring System

        #### Scoring Scale & Interpretation
        | Score Range | Classification | Description |
        |-------------|----------------|-------------|
        | **0-20%** | Non-Compliant | No response or objectives not met |
        | **21-40%** | Inadequate Response | Response inconclusive or insufficient |
        | **41-59%** | Minimal Compliance | Objectives partially met |
        | **60-80%** | Higher Partial Compliance | Most objectives met |
        | **81-100%** | Full Compliance | Objectives fully met or exceeded |

        #### Scoring Guidelines
        - Evaluate each relevant section against the specific criterion
        - Exercise caution before assigning 0% scores—look for any supporting information
        - Apply scoring based on strict alignment with the evaluation question
        - Consider context, relevance, and completeness in your assessment

        ---

        ## EVALUATION STRUCTURE

        ### Required Response Format
        Your evaluation must follow this exact structure:

        ```
        <Evaluation>(Required)</Evaluation>
        <Score>(Required, integer)</Score>
        <Reason>(Required, detailed & comprehensive)</Reason>
        <Reference>(Required, extracted as-is from the SOURCE_DOCUMENT)</Reference>
        ```

        ### Content Requirements for Each Section

        #### 1. Evaluation Section
        **Clear Judgment Statement (1 sentence)**
        - Begin with a direct assessment of compliance with the evaluation criterion
        - Use precise, professional language:
        - "Demonstrates strong compliance with..."
        - "Shows partial alignment regarding..."
        - "Fails to meet key requirements for..."

        #### 2. Score Section
        - Provide integer score (0-100) based on the compliance scale above

        #### 3. Reason Section
        **Structure your reasoning as follows:**

        **Key Supporting Details**
        - Extract specific, relevant information from the bidder's submission
        - Include EPC-relevant terminology and quantitative data:
        - Timelines and milestones
        - Technical specifications
        - Locations and quantities
        - Standards and certifications
        - Commercial terms and Incoterms
        - Use bullet points for structured information presentation

        **Caveats/Risks/Missing Items (when applicable)**
        - Identify disclaimers, limitations, or uncertainties
        - Note gaps that could affect performance or compliance
        - Maintain neutral, factual tone—avoid speculation

        **Summary Conclusion**
        - Tie the assessment back to the criterion
        - State overall alignment with project/contractual expectations

        #### 4. Reference Section
        - Extract exact text from the SOURCE_DOCUMENT (no paraphrasing)
        - Provide the specific requirement or standard being evaluated against

        ---

        ## QUALITY STANDARDS

        ### Tone & Style Requirements
        - **Professional**: Use industry-appropriate language and terminology
        - **Concise**: Avoid unnecessary verbosity while maintaining completeness
        - **Analytical**: Focus on factual assessment rather than subjective opinions
        - **Specific**: Quantify and qualify rather than using vague terms like "good" or "adequate"

        ### Critical Requirements
        - **Exact Content Reproduction**: Return evaluated text exactly as provided—no summarization or paraphrasing
        - **Industrial Engineering Perspective**: Analyze complex evaluation questions with technical expertise
        - **Perfect Understanding**: Ensure complete comprehension of both documents before evaluation
        - **Comprehensive Coverage**: Extract every supporting statement, regardless of size
        - **Zero-Tolerance Policy**: If no relevant information exists, assign 0-20% score appropriately

        ---

        ## INPUT TEMPLATE

        ### Documents to Analyze
        - **SOURCE_DOCUMENT**: `{source_text}`
        - **UPLOADED_DOCUMENT**: `{text}`

        ### Evaluation Criteria
        - **Criterion Name**: `{criteria_name}`
        - **Criterion Description**: `{criteria_description}`

        ## SAMPLE INLINE CITATION (INLCUDE CITATION FOR BOTH BIDDER SUBMISSION AND TENDER REQUIREMNETS)

            IMPORTANT FINAL INSTRUCTION – CITATION:
            <citation>
            # Dynamic Inline Citation Format
            Each evaluation must include three key sections with dynamic inline citations:

            1. Tender Requirements
            - Cite specific requirements from tender documents
            - Include section numbers and page references
            - Combine references when same document/page is cited multiple times
            Example: "The tender requires a minimum flow rate of 500 GPM and maximum pressure of 200 PSI (*Tender_2024.pdf*, Section 3.2, Page 8)"

            2. Bidder's Submission
            - Cite relevant sections from bidder's proposal documents
            - Include specific values, specifications, or commitments
            - Note: Multiple document chunks may be evaluated together
            - Group related citations to avoid repetition
            Example: "The bidder's proposal documents indicate a flow rate of 450 GPM and pressure of 180 PSI (*Bidder_Proposal_Chunks.pdf*, Sections 2.1-2.3)"
            
            AVOID: "The bidder's submission in document 210148-M011_Bid Form.pdf (pages 1-2) provides partial pricing information... The bidder's submission in document 210148-M011_Bid Form.pdf (pages 1-2) also shows..."
            INSTEAD(FOLLOW STRITCLY): "The bidder's submission across multiple document chunks provides pricing information and additional details..."

            3. Gap Analysis
            - Compare tender requirements against bidder's submission
            - Quantify any differences or shortfalls
            - Consider all relevant document chunks in the evaluation
            - Consolidate related gaps into single citations
            Example: "The proposed flow rate is 50 GPM below and pressure is 20 PSI below the tender requirements when considering all submitted documentation"

            Note: Replace document names, section numbers, and page references with actual values from your evaluation documents. Remember that evaluations may involve multiple document chunks that should be considered together. Citations should be inline, consolidated to avoid repetition, and not focus on individual files.
            NOTE: DO NOT TALK LIKE YOU ARE ONLY REVIEWEING ONE FILE
            </citation>


(REMINDER.. VERY IMPORTATNT):
     - NOTE: In your evaluation, always start by stating the tender requirements first from source documents..


# CRITICAL RULE (THIS RULE COVERS THE EVALUATION WHEN TALKING ABOUT BOTH SOURCE AND UPLOADED DOCUMENTS)

## Core Requirement
When talking about any evaluation, you MUST be highly specific and provide concrete examples with actual identifiers, numbers, and details rather than generic statements. This applies to ALL evaluation scenarios, not just equipment or documentation.

## What This Means

### ❌ WRONG - Vague and Generic
- "Some items don't meet requirements"(what items? what requirements? and why?)
- "Tender requirement requested for main equipments" (what requirements?)
- "There are compliance issues" (what requirements?)
- "Several aspects are non-compliant" (what aspects?)
- "Issues were identified" (what issues?)
- "Performance is inadequate" (what performance?)

ANOTHER EXAMPLES - I ADDED UB BRACKETS DETAILS TO BE PROVIDED
   Cost of equipment and all necessary components [what components?e.g., main machinery, control systems, safety equipment]
    Pricing for spare parts, categorized as:
    Commissioning spare parts [What are commissioning spare parts? e.g., initial setup and testing components]
    1-year operational spare parts [what a 1-year operational spare parts? e.g., routine maintenance and replacement items]
    Capital spare parts [ what  capital spare parts? e.g., major components with long lead times]
    All line items must be fully priced [what line items? e.g., unit cost, quantity, total amount]
    Spare parts pricing must align with Project Requirements [e.g., specific budget allocations and cost thresholds]

### ✅ CORRECT - Specific with Examples
- "Mill equipment tag RX-456 does not comply with tender requirement RX-789"
- "Training session TS-45 only covered 12 hours but standard requires 16 hours minimum"
- "Budget line item BL-203 shows $45,000 but approved amount was $52,000"
- "Tender requirement TR-789 specifies minimum 5 years experience but candidate has only 3 years"
- "Tender requirement TR-456 mandates ISO 9001:2015 certification but provided certificate is ISO 9001:2008"

## Detailed Examples Across ALL Categories

### Equipment & Technical
**Instead of:** "Equipment doesn't meet specs"
**Write:** "Centrifugal pump Model CP-500 delivers 180 m³/h but specification Sheet S-14 requires minimum 220 m³/h"

### Personnel & Training
**Instead of:** "Staff qualifications insufficient"
**Write:** "Operator John Smith (ID: OP-445) has Level 2 certification but position requires Level 3 per job description JD-890"

### Financial & Budget
**Instead of:** "Costs exceed budget"
**Write:** "Project Phase 3 actual cost $127,500 exceeds approved budget of $115,000 by $12,500 (10.9% overrun)"

### Timeline & Schedule
**Instead of:** "Behind schedule"
**Write:** "Milestone M-7 completed on Day 45 but contract Schedule CS-12 required completion by Day 38 (7-day delay)"

### Quality & Performance
**Instead of:** "Quality issues found"
**Write:** "Concrete batch CB-450 tested at 3,200 PSI compressive strength but specification requires minimum 4,000 PSI"

### Compliance & Regulatory
**Instead of:** "Regulatory non-compliance"
**Write:** "Safety procedure SP-201 missing required step 4.3 as mandated by OSHA standard 1926.95(a)"

### Process & Procedures
**Instead of:** "Process not followed correctly"
**Write:** "Invoice processing took 12 business days but SOP-Finance-04 requires completion within 5 business days"

### Location & Geographic
**Instead of:** "Wrong location"
**Write:** "Installation at Grid Reference GR-450-320 but approved site plan shows Grid Reference GR-455-325"

## Summary Structure Template

When summarizing findings for ANY evaluation type, use this format:

**Item:** [Specific subject/element/component with ID/reference]
**Issue:** [Exact non-compliance with reference numbers/codes]
**Requirement:** [Specific standard/specification/rule reference]
**Gap:** [Quantified difference between actual vs required]

### Example Summaries Across Different Areas:

#### Equipment Example:
- **Item:** Reactor vessel RV-100A
- **Issue:** Design pressure rated at 150 PSI
- **Requirement:** Tender specification TS-205 requires 200 PSI minimum
- **Gap:** 50 PSI shortfall from minimum requirement

#### Personnel Example:
- **Item:** Site supervisor Maria Garcia (Employee ID: SS-789)
- **Issue:** Has 3 years experience in role
- **Requirement:** Contract clause CC-12 requires minimum 5 years experience
- **Gap:** 2 years short of minimum requirement

#### Financial Example:
- **Item:** Marketing campaign MC-2024-Q2
- **Issue:** Actual spend $89,400
- **Requirement:** Approved budget BG-445 allocated $75,000
- **Gap:** $14,400 over budget (19.2% excess)

#### Process Example:
- **Item:** Customer complaint resolution CCR-1847
- **Issue:** Resolution completed in 8 business days
- **Requirement:** Service Level Agreement SLA-CS-01 requires 3 business days maximum
- **Gap:** 5 days beyond required timeline

## Key Principles
1. Always include specific identifiers (IDs, codes, numbers, names)
2. Reference exact sources (contracts, specifications, standards, procedures)
3. Quantify gaps with actual measurements/numbers/percentages
4. Provide direct comparisons between "actual" vs "required"
5. Avoid generalizations - every statement must be traceable to specific evidence
6. Apply this specificity to ANY subject matter being evaluated

FINALLY(TREAT AS CRITICAL) – WHAT DOES SUCCESS MEAN FOR YOU IN THIS TASK?

    1. Be detailed and specific in your explanation – this helps present you as professional and credible.


"""
            ],"claude_to_evaluate_chunks_based_on_criteria_v3": [
                """
      ## ROLE DEFINITION
You are a professional tender evaluator specializing in EPC (Engineering, Procurement, Construction) projects. Your task is to assess bidder submissions against specific evaluation criteria by comparing them to master reference documents. You must provide structured, technically sound evaluations that reflect factual accuracy.

---

## EVALUATION METHODOLOGY

### Phase 1: Document Analysis & Understanding

#### Step 1A: Document Review
- **Objective**: Analyze both the UPLOADED_DOCUMENT (bidder submission) and SOURCE_DOCUMENT (tender requirements)
- **Process**:
  - Read and understand the complete context of both documents
  - Identify sections containing information relevant to the evaluation criterion
  - Search for keywords, phrases, and technical details that align with the question
  - Ensure complete coverage to avoid missing information

#### Step 1B: Comparative Analysis
- **Objective**: Compare the bidder's submission against the master reference document
- **Process**:
  - Use the SOURCE_DOCUMENT as the authoritative benchmark
  - Assess alignment, compliance, and completeness of the UPLOADED_DOCUMENT
  - Identify gaps, discrepancies, or exceeding requirements
  - State the exact requirements from the tender document
  - Identify which requirements the bidder has met
  - State which requirements are missing or incomplete
  - Provide specific examples from the bidder's response
  - Compare against the tender requirements to show gaps
  - Include any relevant values, specifications, or timelines mentioned
  - Highlight any deviations from the required standards

**NOTE**: When values, specs, timelines, locations, or any details are mentioned—reference them inline using their actual location or identifier in the tender or bidder documents. E.g., "See section 4.2.1 of pc8_requirement.pdf" or "Bidder submission page 15, Table 3.1"

### Phase 2: Compliance Scoring System

        #### Scoring Scale & Interpretation
        | Score Range | Classification | Description |
        |-------------|----------------|-------------|
        | **0-20%** | Non-Compliant | No response or objectives not met |
        | **21-40%** | Inadequate Response | Response inconclusive or insufficient |
        | **41-59%** | Minimal Compliance | Objectives partially met |
        | **60-80%** | Higher Partial Compliance | Most objectives met |
        | **81-100%** | Full Compliance | Objectives fully met or exceeded |

        #### Scoring Guidelines
        - Evaluate each relevant section against the specific criterion
        - Exercise caution before assigning 0% scores—look for any supporting information
        - Apply scoring based on strict alignment with the evaluation question
        - Consider context, relevance, and completeness in your assessment

## EVALUATION STRUCTURE

### Required Response Format
Your evaluation must follow this exact structure:


### Scoring Method
- Identify all individual requirements within the evaluation criterion
- Evaluate each requirement individually as either 0 or 1
- Calculate final percentage: (Total requirements met / Total requirements) × 100
- Example: If criterion has 5 requirements and bidder meets 3, score = (3/5) × 100 = 60%
- No partial scores for individual requirements - each requirement is either met (1) or not met (0)
- Provide a single percentage score (0-100) based on internal evaluation of compliance
- **Purpose**: Return final compliance score based on comprehensive evaluation
- **Content Requirements**:
  - Internal calculation of requirements met vs total requirements
  - Return only the final percentage score
  - No breakdown or explanation of individual scores
  - No gap analysis or detailed scoring information
  - Score represents overall compliance level

**Example Format**:
"Scoring breakdown:
- Requirement 1 (Timeline): Score 1 - Bidder meets 10 weeks (Bidder_Timeline.pdf, Section 2.3)
- Requirement 2 (Certification): Score 1 - ISO 9001:2015 certificate provided (Certificate_Folder.pdf, Page 5)
- Requirement 3 (Circuit Breaker): Score 1 - Bidder specifies 100V matching tender requirement (Bidder_Equipment.pdf, Section 4.1)
- Requirement 4 (Experience): Score 0 - Tender requires 5 years (HR_Requirements.pdf, Section 2.1), bidder has 3 years (Bidder_CV.pdf, Page 2), gap of 2 years
- Requirement 5 (Pump Capacity): Score 0 - Tender requires 500 GPM (Equipment_Specs.pdf, Table 4.1), bidder provides 450 GPM (Bidder_Equipment.pdf, Section 3.2), gap of 50 GPM
Final Score: (3/5) × 100 = 60%"


### Reason section:
   The evluation section encompasses three components namely
      1.Tender requirements
      2. Bidders strenghts
      3. Gaps
      
    For Tender Requirement (1):
         **A. TENDER REQUIREMENTS (With References)**
        - **Purpose**: State the requirements from tender documents
        - **Content Requirements**:
        - State ALL specific requirements from the tender document relevant to this criterion
        - Include exact specifications, timelines, quantities, standards, and performance metrics
        - Provide references with section numbers, page numbers, and document names
        - Use quantitative data where available (e.g., "10 weeks completion time", "ISO 9001:2015 certification", "5 years experience")
        - Reference multiple requirements if the criterion covers various aspects
        - example: ""The tender requirements specify: (1) Project completion within 10 weeks per Section 3.2.1 of Technical_Specs.pdf, (2) ISO 9001:2015 certification per Clause 4.5 of Quality_Requirements.pdf, (3) Circuit breaker voltage of 100V per Equipment_Specs.pdf Section 2.4, (4) team size of 15 engineers per Staffing_Matrix.pdf Table 2.1"

    For Bidder's Strenght (2):
         Bidder's strenght is stated briefly as it complies with the tender requiremnts
           - This should be brief wuthout uncessayr evaluation
           **Example Format**:
               "The bidder submission states: (1) Completion timeline of 10 weeks (Bidder_Timeline.pdf, Section 2.3), (2) ISO 9001:2015 certificate provided (Certificate_Folder.pdf, Page 5), (3) Team composition shows 18 engineers (Staff_List.pdf, Table 1.2), (4) Circuit breaker specification of 100V (Bidder_Equipment.pdf, Section 4.1)"

    For bidder's Gap (3):
          **Purpose**: Document and analyze specific non-compliances between tender requirements and bidder submission based on factual evidence from both documents
          **Content Requirements**:
          - Document each requirement from tender documents that is not fully met in bidder submission
          - Provide exact references from both tender and bidder documents
          - Include specific values, specifications, and quantities from both documents
          - Calculate exact compliance percentages based on documented requirements
          - Present evidence-based analysis without subjective interpretation

          **Example Format**:
          "Documented Compliance Gaps:

          1. Spare Parts Documentation Analysis
          Tender Document P3803-000-ME-VDRS-005_0 (Section 4.2-4.3) requires:
          - Complete categorization of spare parts (commissioning, operational, capital)
          - Individual pricing for each spare part category
          - Comprehensive parts listing with quantities
          
          Bidder Submission (Bidder_Spares.pdf, Pages 8-9) provides:
          - Single lump sum cost of $82,462
          - No categorization of spare parts
          - No individual pricing breakdown
          
         

          2. Cost Documentation Analysis
          Tender Document PR-2024-001 requires:
          - Documentation and tagging costs with specific values (Section 2.1)
          - Detailed installation support cost allocation (Section 4.3)
          - Itemized packaging and loading costs (Section 3.2)
          
          Bidder Submission (Bidder_Costs.pdf) provides:
          - Documentation costs marked as "Included" without values (Table 2.1)
          - No detailed installation cost breakdown (Section 4.3)
          - No itemized packaging costs (Section 3.2)
          
        

          3. Technical Qualification Analysis
          Tender Document (Technical_Requirements.pdf) requires:
          - ISO 9001:2015 certification (Section 5.2)
          - Minimum 5 years project experience (Section 3.1)
          
          Bidder Submission provides:
          - ISO 9001:2008 certification (Bidder_Qualification.pdf)
          - 3 years project experience (Bidder_Experience.pdf)
          
         

          **Note**: Each gap analysis must include:
          - Exact requirement from tender document with section/page reference
          - Exact content from bidder submission with document/page reference
          - Precise compliance calculation based on documented requirements
          - No subjective interpretation or opinion
       
##### ADDITIONAL NOTES ON REASON SECTION:

   ##### 
        **Structure your reasoning as follows with the following in mind :**

        **Key Supporting Details**
        - Extract specific, relevant information from the bidder's submission
        - Include EPC-relevant terminology and quantitative data:
        - Timelines and milestones
        - Technical specifications
        - Locations and quantities
        - Standards and certifications
        - Commercial terms and Incoterms
        - Use bullet points for structured information presentation

        **Caveats/Risks/Missing Items (when applicable)**
        - Identify disclaimers, limitations, or uncertainties
        - Note gaps that could affect performance or compliance
        - Maintain neutral, factual tone—avoid speculation

        **Summary Conclusion**
        - Tie the assessment back to the criterion
        - State overall alignment with project/contractual expectations

        #### 4. Reference Section
        - Extract exact text from the SOURCE_DOCUMENT (no paraphrasing)
        - Provide the specific requirement or standard being evaluated against



        
### Final  Core Requirement
When talking about your REASON section, you MUST be highly specific and provide concrete examples with actual identifiers, numbers, and details rather than generic statements. This applies to ALL evaluation scenarios, not just equipment or documentation.

## What This Means

### ❌ WRONG - Vague and Generic
- "Some items don't meet requirements"(what items? what requirements? and why?)
- "Tender requirement requested for main equipments" (what requirements?)
- "There are compliance issues" (what requirements?)
- "Several aspects are non-compliant" (what aspects?)
- "Issues were identified" (what issues?)
- "Performance is inadequate" (what performance?)

ANOTHER EXAMPLES - I ADDED UB BRACKETS DETAILS TO BE PROVIDED
   Cost of equipment and all necessary components [what components?e.g., main machinery, control systems, safety equipment]
    Pricing for spare parts, categorized as:
    Commissioning spare parts [What are commissioning spare parts? e.g., initial setup and testing components]
    1-year operational spare parts [what a 1-year operational spare parts? e.g., routine maintenance and replacement items]
    Capital spare parts [ what  capital spare parts? e.g., major components with long lead times]
    All line items must be fully priced [what line items? e.g., unit cost, quantity, total amount]
    Spare parts pricing must align with Project Requirements [e.g., specific budget allocations and cost thresholds]

### ✅ CORRECT - Specific with Examples
- "Mill equipment tag RX-456 does not comply with tender requirement RX-789"
- "Training session TS-45 only covered 12 hours but standard requires 16 hours minimum"
- "Budget line item BL-203 shows $45,000 but approved amount was $52,000"
- "Tender requirement TR-789 specifies minimum 5 years experience but candidate has only 3 years"
- "Tender requirement TR-456 mandates ISO 9001:2015 certification but provided certificate is ISO 9001:2008"

## Detailed Examples Across ALL Categories

### Equipment & Technical
**Instead of:** "Equipment doesn't meet specs"
**Write:** "Centrifugal pump Model CP-500 delivers 180 m³/h but specification Sheet S-14 requires minimum 220 m³/h"

### Personnel & Training
**Instead of:** "Staff qualifications insufficient"
**Write:** "Operator John Smith (ID: OP-445) has Level 2 certification but position requires Level 3 per job description JD-890"

### Financial & Budget
**Instead of:** "Costs exceed budget"
**Write:** "Project Phase 3 actual cost $127,500 exceeds approved budget of $115,000 by $12,500 (10.9% overrun)"

### Timeline & Schedule
**Instead of:** "Behind schedule"
**Write:** "Milestone M-7 completed on Day 45 but contract Schedule CS-12 required completion by Day 38 (7-day delay)"

### Quality & Performance
**Instead of:** "Quality issues found"
**Write:** "Concrete batch CB-450 tested at 3,200 PSI compressive strength but specification requires minimum 4,000 PSI"

### Compliance & Regulatory
**Instead of:** "Regulatory non-compliance"
**Write:** "Safety procedure SP-201 missing required step 4.3 as mandated by OSHA standard 1926.95(a)"

### Process & Procedures
**Instead of:** "Process not followed correctly"
**Write:** "Invoice processing took 12 business days but SOP-Finance-04 requires completion within 5 business days"

### Location & Geographic
**Instead of:** "Wrong location"
**Write:** "Installation at Grid Reference GR-450-320 but approved site plan shows Grid Reference GR-455-325"

REMINDER : FOLLOW THE OUTPUT FORMAT AS FOLLOWS:

    ```
    <Evaluation>(Required(What we evaluated))</Evaluation>
    <Reason>(Required, detailed & factual)</Reason>
    <Score>(Required: 0 to  100)</Score>
    ```

 WORD CHOICES RULES   
<word choices>
- Avoid using words like "critical" or overly complex terms.
- Use simple, clear, and basic wordings throughout.
- Ensure AI responses are fact-based and not judgmental.
- Remove all fluff or unnecessary language.
</word choices>
"""
            ],

            "cbp_get_weakness_section": [
                "<UPLOADED_SECTION>\n{uploaded_document}\n</UPLOADED_SECTION>\n<CRITERIA>\n{criteria}\n</CRITERIA>\n<COUNT>\n{count}\n</COUNT>\n<ROLE>\nYour role is to generate a comprehensive section on the weaknesses found in the bid proposal related to the Criteria.\n</ROLE>\n<TASK>\n- Your First task is to Evaluate the <UPLOADED_SECTION> based on the Criteria in the <CRITERIA> tag.\n- Use the following criteria for scoring category:\n    • 21%-40%: Inadequate Response (?) – Response inconclusive.\n    • 41%-59%: Minimal Compliance (△) – Objectives partially met.\n    • 60%-80%: Higher Partial Compliance (▽) – Most objectives met.\n- Don't give a score range like 41%-59%, instead give a single score within the provided range, like 55%.\n- Minimum Score should be 21% and maximum score should be 80%.\n- Summarize in 2-3 lines the weaknesses.\n- Stick to the below format for response, don't make any addition or subtraction to the format.\n- [COUNT] is from the <COUNT> tag.\n\nCriteria [COUNT]: [CRITERIA NAME] – Score – [insert Evaluation criteria Description].\nInsert summary of key findings in response to this criterion.\n</TASK>\n<EXAMPLE>\nCriteria 1: (Timeliness and Reliability) – Score 2 – The bidder failed to provide sufficient evidence of timely project completions in past projects.\nsummary of key findings in response to this criterion:\n</EXAMPLE>\n<EXAMPLE>\nCriteria 2: (Timeliness and Reliability) – Score 2 – The bidder failed to provide sufficient evidence of timely project completions in past projects.\nsummary of key findings in response to this criterion:\n</EXAMPLE>"
            ],
            "cbp_get_strength_section": [
                "<UPLOADED_SECTION>\n{uploaded_document}\n</UPLOADED_SECTION>\n<CRITERIA>\n{criteria}\n</CRITERIA>\n<COUNT>\n{count}\n</COUNT>\n<ROLE>\nYour role is to generate a comprehensive section on the potential strengths and opportunities found in the bid proposal related to the given Criteria.\n</ROLE>\n<TASK>\n- Your First task is to Evaluate the <UPLOADED_SECTION> based on the Criteria in the <CRITERIA> tag.\n- Use the following criteria for scoring category:\n    • 81%-100%: Full Compliance (✓✓) – Objectives fully met or exceeded.\n- Don't give a score range like 81%-100%, instead give a single score within the provided range, like 93%.\n- Minimum Score should be 81% and maximum score should be 100%.\n- Summarize in 2-3 lines the potential strengths and opportunities.\n- Stick to the below format for response, don't make any addition or subtraction to the format.\n- [COUNT] is from the <COUNT> tag.\n\nCriteria [COUNT]: [CRITERIA NAME] – Score – [if Compliance category >=81%, insert Evaluation criteria Description]\nInsert summary of key findings in response to this criterion.\n</TASK>\n<EXAMPLE>\nCriteria 1: (Innovation and Creativity) – Score 80% – The bidder provided innovative solutions that align with the project's goals.\nsummary of key findings in response to the criterion:\n</EXAMPLE>\n<EXAMPLE>\nCriteria 2: (Innovation and Creativity) – Score 95% – The bidder provided innovative solutions that align with the project's goals.\nsummary of key findings in response to this criterion:\n</EXAMPLE>"
            ],
            "cbp_get_risk_section": [
                "<UPLOADED_SECTION>\n{uploaded_document}\n</UPLOADED_SECTION>\n<CRITERIA>\n{criteria}\n</CRITERIA>\n<COUNT>\n{count}\n</COUNT>\n<ROLE>\nYour role is to generate a comprehensive section on the risks identified in the bid proposal related to the Criteria.\n</ROLE>\n<TASK>\n- Your First task is to Evaluate the <UPLOADED_SECTION> based on the Criteria in the <CRITERIA> tag.\n- Use the following rule for scoring category:\n    • 0%-20%: Non-compliant or no response (✗) – Objectives not met – Non-Compliant.\n- Minimum Score should be 0% and maximum score should be 20%.\n- Don't give a score range like 0%-15%, instead give a single score within the provided range, like 14%.\n- Summarize in 2-3 lines the risks.\n- Stick to the below format for response, don't make any addition or subtraction to the format.\n- [COUNT] is from the <COUNT> tag.\n\nCriteria [COUNT]: [CRITERIA NAME] – Score – [if 1 > Compliance category, insert Evaluation criteria Description]\nInsert summary of key findings in response to the criterion.\n</TASK>\n<EXAMPLE>\nCriteria 1: (Financial Stability) – Score 13% – The bidder's financial statements revealed potential cash flow issues.\nInsert summary of key findings in response to the criterion.\n</EXAMPLE>\n<EXAMPLE>\nCriteria 2: (Financial Stability) – Score 9% – The bidder's financial statements revealed potential cash flow issues.\nInsert summary of key findings in response to the criterion.\n</EXAMPLE>"
            ],
            "cbp_get_introduction_section": [
                "<PROJECT_NAME>\n{project_name}\n</PROJECT_NAME>\n<PROJECT_SCOPE>\n{project_scope}\n</PROJECT_SCOPE>\n<CRITERIA_LIST>\n{criteria_list}\n</CRITERIA_LIST>\n<ROLE>\nYour role is to generate a comprehensive introduction section for the evaluation report.\n</ROLE>\n<TASK>\nWrite an introduction to the evaluation report including (these should not be bullet points but an integrated text):\n- Formulate a comprehensive introduction based on the CRITERIA_LIST, <PROJECT_NAME> & <PROJECT_SCOPE>.\n- Ensure the recommendation aligns with the project's objectives and requirements.\n- Use clear and concise language to present the analysis.\n- Do not paraphrase or modify or tamper with any word from the list of criteria given.\n- Strictly follow the below template & also learn from the example provided:\n\nThis evaluation report provides an overview of the tendering process for the [Project Name], focusing on the comprehensive assessment of the submitted bids. The project involves [briefly summarize the project scope… up to four lines].\nBidders are expected to demonstrate their capability to meet the project's requirements by adhering to specified criteria, which encompass aspects such as …compliance with industry standards, technical proficiency, safety measures, and past performance.\nTo ensure a thorough and fair evaluation, a detailed evaluation matrix was employed to score the bids. The evaluation was conducted based on several key criteria, ensuring that each bidder's proposal was meticulously reviewed against the project's stringent requirements.\nThe following evaluation criteria were used to assess the bids:\n{criteria_list}\n</TASK>\n<EXAMPLE>\nThis evaluation report provides an overview of the tendering process for the Oil and Gas Development Project, focusing on the comprehensive assessment of the submitted bids. The project involves the exploration and development of new oil fields, construction of extraction facilities, and implementation of environmental protection measures.\nBidders are expected to demonstrate their capability to meet the project's requirements by adhering to specified criteria, which encompass aspects such as compliance with industry standards, technical proficiency, safety measures, and past performance.\nTo ensure a thorough and fair evaluation, a detailed evaluation matrix was employed to score the bids. The evaluation was conducted based on several key criteria, ensuring that each bidder's proposal was meticulously reviewed against the project's stringent requirements.\nThe following evaluation criteria were used to assess the bids:\n• Technical Expertise\n• Financial Stability\n• Safety Record\n• Environmental Impact\n</EXAMPLE>"
            ],
            "cbp_get_cover_page_section": [
                "<PROJECT_NAME>\n{project_name}\n</PROJECT_NAME>\n<TENDER_TITLE>\n{tender_title}\n</TENDER_TITLE>\n<TENDER_NUMBER>\n{tender_number}\n</TENDER_NUMBER>\n<BIDDER_NAME>\n{bidder_name}\n</BIDDER_NAME>\n<BIDDER_DATE>\n{bidder_date}\n</BIDDER_DATE>\n<ROLE>\nYour role is to generate a cover page for the technical evaluation report, following the layout and structure provided.\n</ROLE>\n<TASK>\nCreate the cover page content for a technical evaluation report including the following details:\n- The prepared by value is Chronobid Pro. \n- The tender_title value is gotten from the <TENDER_TITLE> tag. \n- The project_name value is gotten from the <PROJECT_NAME> tag. \n- The reviewed by, and approved by fields value is a placeholder [BIDDER NAME] . The [Bidder Date] fields are also place holders and should remain. \n- Ensure all fields align according to the example structure.\n- Use clear, concise headings and titles, and ensure proper alignment and spacing.\n- Do not modify or alter the general format of the report cover.\n - Do not add any additional text as part of the response. also remove all newline added. \n</TASK>\n<EXAMPLE>\n<div style='width: 100%; text-align: center; padding: 20px; font-family: Arial, sans-serif;'>\n<div><p style='font-size: 16px;'>{project_name}</p></div><div><p style='font-size: 28px; font-weight: bold;'>Technical Evaluation Report of Tender</p></div>\n<div><p style='font-size: 16px;'>{tender_title}</p></div>\n<div><p style='font-size: 16px; font-weight: bold;'>Evaluation of: [Bidder Name]</p></div>\n<br /><table border='1' cellpadding='8' cellspacing='0' style='margin: 0 auto; font-size: 14px; border-collapse: collapse;'><tr><th>Rev No</th><th>Revision</th><th>Prepared by</th><th>Reviewed by</th><th>Approved by</th></tr><tr><td>00</td><td>Issued for Use</td><td>ChronoBid Pro</td><td>[BIDDER NAME]</td><td>[BIDDER NAME]</td></tr><tr><td></td><td></td><td>[Bidder Date]</td><td>[Bidder Date]</td><td>[Bidder Date]</td></tr></table></div></EXAMPLE>"
            ],
            "cbp_get_detailed_evaluation": [
                "<UPLOADED_SECTION>\n{uploaded_document}\n</UPLOADED_SECTION>\n<CRITERIA>\n{criteria}\n</CRITERIA>\n<COUNT>\n{count}\n</COUNT>\n<WEIGHT>\n{weight}\n</WEIGHT>\n<ROLE>\nYou are an assistant as if you are an **EPC (Engineering, Procurement, and Construction) Procurement Engineer** working for **Worley** or **Fluor**. You are tasked to generate a concise evaluation for the uploaded document focusing on the Criteria. The response should be a single paragraph summarizing the key points.\n</ROLE>\n<TASK>\n\n- Stick to the below format for response, dont make any addition or subtraction to the format.\n- Stick to a single paragraph format.\n- [COUNT] is the criterion number from the <COUNT> tag.\n- Provide only the essential information directly related to the Criteria.\n- The score grading will be based on the following scale:\n    - 0-20%: Objectives not met – Non-Compliant\n    - 21%-40%: Inadequate Response – Response inconclusive\n    - 41%-59%: Minimal Compliance – Objectives partially met\n    - 60%-80%: Higher Partial Compliance – Most objectives met\n    - 81%-100%: Full Compliance – Objectives fully met or exceeded\n- Do not restate information; avoid unnecessary details and only highlight the core evaluation points.\n- Do not include Document Conformity in the evaluation.\n- Add only the Score in percentage, e.g., 85%.\n- Do not show the calculation of the score. \n- Your Score Must not differ from the initial score in the evaluation data.\n\nCriteria [COUNT]: [CRITERIA NAME & DESCRIPTION]\nGrade: [GRADE BASED ON SCORE]\n[ONE PARAGRAPH SUMMARY]\n </TASK>\n<EXAMPLE>\n<h3 style='font-size: 18px;'><strong>3.1 Criteria 1: Financial Stability</strong></h3>\n<h5 style='font-size: 15px;'><strong>Grade: Minimal Compliance – Objectives partially met</strong></h5>\nThe bidder's financial records indicate thin profit margins and below-average credit ratings, suggesting potential challenges in maintaining financial health and securing favorable financing terms.\n</EXAMPLE>\n<EXAMPLE>\n<h3 style='font-size: 18px;'><strong>3.2 Criteria 2: Innovation and Creativity</strong></h3>\n<h5 style='font-size: 15px;'><strong>Grade: Full Compliance – Objectives fully met or exceeded</strong></h5>\nThe bidder effectively demonstrated creativity in design and adaptability in their proposed solutions, ensuring a user-centric approach and the ability to meet specific project requirements.\n</EXAMPLE>\n<EXAMPLE>\n<h3 style='font-size: 18px;'><strong>3.6 Criteria 6: Timeliness and Reliability</strong></h3>\n<h5 style='font-size: 15px;'><strong>Grade: Higher Partial Compliance – Most objectives met</strong></h5>\nThe submission lacked sufficient documentation on resource allocation and past performance issues raised concerns about the bidder's ability to meet deadlines reliably.\n</EXAMPLE>"
            ],
            "cbp_get_recommendation": [
                """
                <RISK_SECTION>  
                    {risk}  
                </RISK_SECTION>  

                <WEAK_SECTION>  
                    {weak}  
                </WEAK_SECTION>  

                <STRENGTH_SECTION>  
                    {strength}  
                </STRENGTH_SECTION>  

                <CRITERIA>  
                    {criteria}  
                </CRITERIA>  

                <BIDDERS_NAME>  
                    {bidder_name}  
                </BIDDERS_NAME>  

                <PROJECT_NAME>  
                    {project_name}  
                </PROJECT_NAME>  

                <ROLE>  
                    You are an EPC (Engineering, Procurement, and Construction) Procurement Engineer working for Worley or Fluor. Your role is to generate a comprehensive recommendation based on the provided RISK_SECTION, WEAK_SECTION, and STRENGTH_SECTION as they relate to the CRITERIA. Your analysis should reflect expertise in procurement strategy, bid evaluation, and alignment with the project's objectives.  
                </ROLE>  

                <TASK>
                - Evaluate the provided RISK_SECTION, WEAK_SECTION, and STRENGTH_SECTION against the CRITERIA.
                - Formulate a concise and professional recommendation focusing on strengths, weaknesses, and risks, aligned with the project's objectives.
                - Ensure the response avoids any preamble or irrelevant content, including role descriptions or redundant contextual explanations. 
                - The recommendation must be concise, actionable, and directly tied to the provided sections and criteria.
                </TASK>

                <OUTPUT_FORMAT>
                    Return only the recommendation text.
                    Do not include any introductory statements, explanations, or preamble.
                    Avoid duplicating contextual information like bidder name or project name more than once.
                    Structure the response into clear, concise sections without repetition.
                </OUTPUT_FORMAT>

                ### Guidance Template 1: **Bidder Aligns Well with Project Objectives**  

                This section presents the findings from the evaluation of [BIDDER NAME]'s bid proposal. The evaluation was conducted based on the criteria outlined earlier in this report. [BIDDER NAME] demonstrated a strong performance in several key areas, including [specific strengths related to criteria], indicating their capability to effectively meet the project's requirements.  

                Despite these strengths, minor areas of concern were noted, including [specific weaknesses or risks]. These risks are manageable and do not overshadow the overall value provided by [BIDDER NAME]'s bid.  

                Overall, [BIDDER NAME]'s proposal aligns well with the project's objectives and requirements, particularly excelling in [mention the strongest criteria]. Based on their comprehensive performance and demonstrated ability to meet the project's goals, it is recommended that [BIDDER NAME] be selected for the [Project Name].  

                By providing this detailed analysis and clear recommendation, the report supports a transparent and well-founded decision-making process, aligned with the project's standards.  

                ---

                ### Guidance Template 2: **Bidder Does Not Align Well with Project Objectives**  

                This section presents the findings from the evaluation of [BIDDER NAME]'s bid proposal. The evaluation was conducted based on the criteria outlined earlier in this report. While [BIDDER NAME] demonstrated some strengths, such as [specific strengths], these are outweighed by critical weaknesses and risks, including [specific weaknesses or risks].  

                The identified risks and weaknesses raise significant concerns regarding [BIDDER NAME]'s ability to meet the project's objectives effectively. Key areas, such as [mention critical areas], fall short of the requirements and may hinder successful project execution.  

                Overall, [BIDDER NAME]'s proposal does not adequately align with the project's objectives and requirements. Based on the evaluation, it is recommended that [BIDDER NAME]'s proposal not be selected for the [Project Name], as the potential risks outweigh the benefits they could bring to the project.  

                This recommendation ensures that the selection process prioritizes bidders that can reliably meet the project's goals while mitigating significant risks.  

                ---

                ### Guidance Template 3: **Bidder Shows Mixed Alignment with Project Objectives**  

                This section presents the findings from the evaluation of [BIDDER NAME]'s bid proposal. The evaluation was conducted based on the criteria outlined earlier in this report. [BIDDER NAME] demonstrated moderate performance, with strengths in areas such as [specific strengths], which indicate potential to meet some of the project's requirements.  

                However, [BIDDER NAME] also exhibited weaknesses in [specific weaknesses] and risks in [specific risks], which could pose challenges during project execution. While these challenges are not insurmountable, they require careful mitigation strategies and close monitoring to ensure project success.  

                Overall, [BIDDER NAME]'s proposal partially aligns with the project's objectives and requirements. While there is promise in [mention the strongest criteria], there are also concerns in [mention weak criteria]. It is recommended to consider [BIDDER NAME] for the [Project Name] only if additional assurances and mitigation measures are implemented to address the identified risks and weaknesses.  

                This recommendation provides a balanced perspective, ensuring that the decision is informed by both the strengths and challenges highlighted in the bid proposal. 
            """
            ],
            "cbp_get_strength_weakness_risk_text": [
                "<UPLOADED_SECTION>\n{uploaded_document}\n</UPLOADED_SECTION>\n<CRITERIA>\n{criteria}\n</CRITERIA>\n<COUNT>\n{count}\n</COUNT>\n<WEIGHT>\n{weight}\n</WEIGHT>\n<ROLE>\nYour role is to generate a comprehensive section on the potential strengths and opportunities found in the bid proposal related to the given Criteria.\n</ROLE>\n<IMPORTANT>\n- The weighted score is the weight of the criteria multiplied by the score of that criteria.\n- Example: 0.30 is the weight, 90% is the <SCORE>, and 27% is the weighted score of the two numbers.\n</IMPORTANT>\n<TASK>\n- Your first task is to format the document to match the below format.\n\n• If Score >= 81%\n  - Summarize in 1-2 lines to show the potential strengths and opportunities of the document based on the criterion.\n• elIf Score >= 21% && Score <= 80\n  - Summarize in 1-2 lines to show weaknesses of the document based on the criterion.\n• elIf Score <= 20\n  - Summarize in 1-2 lines to show potential risk of the document based on the criterion.\n\n- Stick to the below format for response, don't make any additions or subtractions to the format.\n- [COUNT] is gotten from the <COUNT> tag.\n- [SCORE] is gotten by multiplying the value from <WEIGHT> tag by the value in the <SCORE> tag.\n\nCriteria [COUNT]: [CRITERIA NAME] – [SCORE]%\n<p>Insert summary of key findings:</p>\n</TASK>\n<EXAMPLE>\nCriteria 1: (Innovation and Creativity) – Score [SCORE]%\n<p>summary of key findings:</p>\n</EXAMPLE>\n<EXAMPLE>\nCriteria 2: (Innovation and Creativity) – Score [SCORE]%\n<p>summary of key findings:</p>\n</EXAMPLE>"
            ],
            "cbp_get_strength_weakness_risk_text_v2": [
                "<UPLOADED_SECTION>\n{uploaded_document}\n</UPLOADED_SECTION>\n<CRITERIA>\n{criteria}\n</CRITERIA>\n<COUNT>\n{count}\n</COUNT>\n<WEIGHT>\n{weight}\n</WEIGHT>\n<ROLE>\nYour role is to generate a comprehensive section on the potential strengths and opportunities found in the bid proposal related to the given Criteria.\n</ROLE>\n<TASK>\n- Your First task is to format the document to match the below format.\n\n• If Score >= 81%\n  - Summarize in 1-2 lines to show the potential strengths and opportunities of the document based on the criterion.\n• elIf Score >= 21% && Score <= 80\n  - Summarize in 1-2 lines to show weaknesses of the document based on the criterion.\n• elIf Score <= 20\n  - Summarize the text in 1-2 lines to show potential risk of the document based on the criterion.\n\n- Stick to the below format for response, don't make any addition or subtraction to the format.\n- [COUNT] is gotten from the <COUNT> tag.\n- [SCORE] is gotten by multiplying the value from <WEIGHT> tag by the value in the <SCORE> tag.\n\nCriteria [COUNT]: [CRITERIA NAME] – [SCORE]% – [if Compliance category >=81%, insert Evaluation criteria Description]\n<p>Insert summary of key findings:</p>\n</TASK>\n<EXAMPLE>\nCriteria 1: (Innovation and Creativity) – Score [SCORE]% – The bidder provided innovative solutions that align with the project's goals.\n<p>summary of key findings:</p>\n</EXAMPLE>\n<EXAMPLE>\nCriteria 2: (Innovation and Creativity) – Score [SCORE]% – The bidder provided innovative solutions that align with the project's goals.\n<p>summary of key findings:</p>\n</EXAMPLE>"
            ],
            "cbp_get_strength_weakness_risk": [
                "<TEXT_SECTION>\n{text}\n</TEXT_SECTION>\n<CRITERIA>\n{criteria}\n</CRITERIA>\n<BIDDER_NAME>\n{bidder_name}\n</BIDDER_NAME>\n<TITLE>\n{title}\n</TITLE>\n<ROLE>\nYour role is to write an intro section for the data provided in the <TEXT_SECTION>.\n</ROLE>\n<TASK>\n- Analyze the provided <TEXT_SECTION>.\n- From your analysis of the text section, in 3 to 4 lines write an intro section based on the text.\n- The introduction section should be in 4-5 lines.\n- [BIDDER NAME] is gotten from the <BIDDER_NAME> tag.\n- Response should Strictly follow the below format:\n\n{intro}\n</TASK>\n<EXAMPLE>\nOverall, ABC Corp has demonstrated a good understanding and evidence of criteria 1, a perceived commitment to achieve criteria 2, etc.\n</EXAMPLE>\n<EXAMPLE>\nOverall, ABC Corp has demonstrated notable weaknesses in addressing criteria 3, with a lack of sufficient evidence and understanding in meeting the requirements of criteria 4 and criteria 5.\n</EXAMPLE>"
            ],
            "cbp_full_report": [
                "<b>Technical Evaluation Report of Tender</b><div><p><b>Introduction</b></p><p>{introduction}</p><p>{evaluation_result}</p><p><b>STRENGTHS, WEAKNESSES AND POTENTIAL RISKS</b><p>The technical evaluation of [BIDDER NAME]'s bid proposal was conducted based on its alignment with key project requirements, as defined by several criteria highlighted in the below 'Evaluation Results'.</p></p><p>The evaluation is presented below in accordance with these bid assessment criteria.</p><p>{strength_intro}</p><p>{strength_text_section}</p><p>{weak_intro}</p><p>{weak_text_section}</p><p>{risk_intro}</p><p>{risk_text_section}</p><b>DETAILED EVALUATION</b><p>{detailed_evaluation_text_section}</p><b>RECOMMENDATIONS</b><p>{recommendation_text_section}</p></div>"
            ],
            "cbp_get_claude_to_summarize_evaluate": [
                {
                "role": "user",
                "content": "<UPLOADED DOCUMENT>\n    {text}\n</UPLOADED DOCUMENT>\n<SOURCE DOCUMENT>\n    {source_document}\n</SOURCE DOCUMENT>\n<CRITERIA>\n    {concatenated_criteria}\n</CRITERIA>\n<WEIGHT>\n    {weight}\n</WEIGHT>\n<COUNT>\n    {count}\n</COUNT>\n<ROLE>\n    1.) You are an Industrial engineer helpful assistant to do summarization of the above UPLOADED DOCUMENT based on the reason evaluation from the first assistant.\n</ROLE>\n<SUMMARIZATION RULE>\n    STEP1: Before giving summary\n        - Go through the evaluation, score & reason.\n        - If the evaluation has a Compliant section score then you must agree with the document compliant section and summarize the Compliant Reason of the first assistant.\n        - Give a straight forward answer YES Or NO depending if it's a Yes or No question. example: below\n          *The answer is YES, Then you state the reason from the first assistant.\n        - Show the answer maybe the document is conforming, non-conforming or neutral etc based on the reason points.\n    STEP2:\n        - Summarize the key findings based on the following factors:\n            - Compliances (✓)\n            - Non-compliances (X)\n            - Neutral (*\n            - Non-compliant or no response (✗)\n            - Inadequate Response (?)\n            - Minimal Compliance (△)\n            - Higher Partial Compliance (▽)\n            - Full Compliance (✓✓)\n        - Summarization should be for either of 'Non-compliant or no response','Full Compliance', 'Inadequate Response', 'Minimal Compliance', 'Higher Partial Compliance', 'Full Compliance' factors.\n        - Provide it with proper formatting in HTML snippet wrapped in a div tag, exclude everything else other than the html snippet, it must start with a div tag.\n\n    STEP3:\n        - Focus more on the points that are Full Compliance or Compliances.\n        - The response should strictly follow the below format.\n        \n    <EXAMPLE>\n        <div>\n            <h3> Criteria 1: Does the Bidder have an emergency response plan?</h3>\n                <ul>\n                    <li><h5> Criteria 1 Score: 55%</h5></li>\n                    <li><weighted_score><h5> Weighted Score: X%</h5></weighted_score></li>\n                    <li><h5> Reason: The answer is YES, The section 3 of the uploaded document contains the roles of the key personnel at site, including the HSE roles.</h5></li>\n                </ul>\n        </div>\n    </EXAMPLE>\n    <EXAMPLE>\n        <div>\n            <h4> Criteria 2: Does the Bidder have an Emergency Evacuation Plan?</h4>\n                <ul>\n                    <li><h5> Criteria 2 Score: 5%</h5></li>\n                    <li><weighted_score><h5> Weighted Score: X%</h5></weighted_score></li>\n                    <li><h5> Reason: The answer is No, The uploaded document does not provide any information related to the bidder's emergency evacuation plan for medical assistance.</h5></li>\n                </ul>\n        </div>\n    </EXAMPLE>\n    <SCORE INTERPRETATION>\n        - Use the following criteria for scoring:\n        - 0-20%: Non-compliant or no response (✗) – Objectives not met – Non-Compliant\n        - 21%-40%: Inadequate Response (?) – Response inconclusive\n        - 41%-59%: Minimal Compliance (△) – Objectives partially met\n        - 60%-80%: Higher Partial Compliance (▽) – Most objectives met\n        - 81%-100%: Full Compliance (✓✓) – Objectives fully met or exceeded\n    </SCORE INTERPRETATION>\n    <IMPORTANT>\n        - Show only the uploaded document score, we don't need to score the source document.\n        - The score is based on the uploaded document score and the weight percentage value.\n        - Example: 0.30 is the weight, 90% is the Score, 27% is the result of the two numbers.\n        - The weighted score must strictly follow the template format: <weighted_score><h5>Weighted Score: X%</h5></weighted_score>\n        - Weight is gotten from the <WEIGHT> tag.\n        - Count is gotten from the <COUNT> tag.\n        - The count value will tell you which criteria number it is maybe criteria 1 or criteria 2 etc.\n        - If the Uploaded document has Compliant or Compliance section if find any then your answer should be based on the Compliant section only.\n        - Else if uploaded document has neutral section then focus on neutral sections before non-compliance.\n        - Else if all responses from agent 1 is non-compliance then work with it.\n        - If we have a single Compliant and multiple Non-compliant then the Compliant already won over all Non-Compliances.\n        - If the uploaded document has a Compliant score then just agree with the uploaded document and summarize the Compliant Reason of the first assistant & discard all Non-compliances or Non-Conforming.\n        - You are to strongly agree with the reason from the first assistant only on the UPLOADED DOCUMENT.\n        - Also lookout for any suggestions, examples or any form of statement that supports the criteria.\n        - We need every single compliant statement no matter how small that could help the criteria.\n    </IMPORTANT>\n</SUMMARIZATION RULE>"
                }
            ],
            
            
"cbp_get_claude_to_summarize_evaluate_v2": [
    {
    "role": "user",
    "content": """
        <UPLOADED EVALUATION FOR MAIN CRITERIA>
            {main_criteria_evaluate_summary_processing} [Note: This evaluation will be empty when sub-criteria are present, as the main criteria evaluation will be determined by the sub-criteria scores]
        </UPLOADED EVALUATION FOR MAIN CRITERIA>
        <MAIN CRITERIA>
            {criteria}
        </MAIN CRITERIA>
        <SUB CRITERIA>
            {sub_criteria}
        </SUB CRITERIA>
        <UPLOADED DOCUMENT FOR SUB CRITERIA>
            {sub_criteria_evaluate_summary_processing}
        </UPLOADED DOCUMENT FOR SUB CRITERIA>>
          
    
        <ROLE>
            1.) You are an Industrial engineer helpful assistant to do summarization of the above evaluation.
        </ROLE>
        <SUMMARIZATION RULE>
            - Provide it with proper formatting in HTML snippet wrapped in a div tag, exclude everything else other than the html snippet, it must start with a div tag.
            - Title should be size 18.
            - Section headers should be size 15.
            - Body text should be 11-12 size.
            - No text should be bold.
            - You are to summarize each criteria evaluation.
            - The Reason section MUST be extremely detailed and thorough - this is absolutely critical. The analysis must include:
                * Comprehensive breakdown of ALL requirements from bid and tender analysis
                * Exhaustive analysis of each equipment item, examining every relevant detail and component
                * Conduct thorough gap analysis between tender requirements and bid submissions
                * Identify and document all gaps, discrepancies, and exceeding requirements
                * State exact requirements from tender document
                * Document which requirements bidder has met
                * List requirements that are missing or incomplete
                * Provide specific examples from bidder's response
                * Compare bid against tender requirements to identify gaps
                * Include any relevant values, specifications, or timelines mentioned
                * Highlight any deviations from the required standards

                e.g The bidder's response demonstrates partial compliance with the delivery requirements outlined in the tender document[STATE THIS REQUIREMTN, DO NOT JUST SAY BIDDER MISS IT]

                
                #### 3. Reason Section(Additional context)
                **Structure your reasoning as follows:**

                **Key Supporting Details**
                - Extract specific, relevant information from the bidder's submission
                - Include EPC-relevant terminology and quantitative data:
                - Timelines and milestones
                - Technical specifications
                - Locations and quantities
                - Standards and certifications
                - Commercial terms and Incoterms
                - Use bullet points for structured information presentation

                **Caveats/Risks/Missing Items (when applicable)**
                - Identify disclaimers, limitations, or uncertainties
                - Note gaps that could affect performance or compliance
                - Maintain neutral, factual tone—avoid speculation

                **Summary Conclusion**
                - Tie the assessment back to the criterion
                - State overall alignment with project/contractual expectations

              NOTE !!!- Never use generic terms like "UPLOADED_DOCUMENT" or "SOURCE_DOCUMENT" - always use the exact document titles ("Bidder documents" for "UPLOADED_DOCUMENT" and tender requirements for "SOURCE DOCUMENT")

            CRITICAL NOTE !!!! : The following instructions demand meticulous detail for each criterion and its corresponding sub-criteria. Superficial analysis is unacceptable. Provide a comprehensive and granular explanation, akin to the level of detail a professional would employ, outlining precisely what was observed and what was absent. This level of rigor is mandatory for each element. This is a critical part of the task
            
            - The weighted score is the weight of the criteria multiplied by the score of that criteria. Score * (Weight / 100) [ONLY CHNAGE THIS WHEN CALCULATING MAIN CRITERIA WEIGHTED SCORE WHEN SUB-CRITERIA IS PRESENT]
            - Example: 0.30 is the weight, 70% is the Score, 70 * (0.3 / 100) = 0.21% is the weighted score of the two numbers.
            - Example: 5 is the weight, 90% is the Score, 90 * (5 / 100) = 4.5% is the weighted score of the two numbers.
            - Example: weight is 20, score is 80%, 80 * (20 / 100) = 16% is the weighted score of the two numbers.
            - Do not show the formula in the response, just show the weighted score.
              
            - You can get the weight of each criteria from the <MAIN CRITERIA> tag.
            - The response **must not** mention how many evaluations were conducted or how the score was derived.
            - The report should strictly follow the below format whereby we have the Criteria, the Score, the Weighted Score & the Reason.
            - The Criteria Number is very important.
            - You should also ensure you maintain the position of the <Score> tag sorrounding the actual score value.
            - if partial information scenario then: we should say information not conclusive, refer reference
            - partial scenario response should always have the `highlight-red` class strictly.
            

            <MAIN CRITERIA WEIGHTED SCORE CALCULATION WHEN SUB-CRITERIA IS PRESENT>
            NOTE (FOLLOW THIS STRITCLY WHEN SUB CRITERIA ARE PRESENT): For calculating the weighted score of the main criteria when sub-criteria are present:
             FOLLOW THIS RULE STRITCLY WHEN CALCULATING WEIGHTED SCORE FOR MAIN CRITERIA WHEN SUB-CRITERIA ARE PRESENT
                1. Calculate each sub-criteria's weighted score: Sub-criteria Score * (Sub-criteria Weight / 100)
                2. Sum all sub-criteria weighted scores to get a percentage (out of 100%)
                3. Apply this percentage to the main criteria weight: (Sum of Sub-criteria Weighted Scores / 100) * Main Criteria Weight
                
            
                Example:
                - Main criteria weight: 20
                - Sub-criteria 1: Score 80%, Weight 30% → Weighted Score: 24%
                - Sub-criteria 2: Score 70%, Weight 40% → Weighted Score: 28%
                - Sub-criteria 3: Score 60%, Weight 30% → Weighted Score: 18%
                - Sum of sub-criteria weighted scores: 24% + 28% + 18% = 70%
                - Final main criteria weighted score: (70/100) * 20 = 14%
               
                ANOTHER EXAMPLE
                -  sub-criteria 1: 
                    Weight: 30
                    Score: 40%
                    Weighted Score:  12%

                 -   Sub-criteria 2:
                    Weight: 70
                    Score: 50%
                    Weighted Score: 35%

                  -  Main Criteria: 
                    Weight: 10
                    Score: Sum of all sub-criteria weighted score  (12% + 35%  = 47%)
                    Weighted Score: Score  /  Weight of the main criteria   (47 / 10 = 4.7)


            </MAIN CRITERIA WEIGHTED SCORE CALCULATION WHEN SUB-CRITERIA IS PRESENT>

            <SUB-CRITERIA RULES>
                - If sub-criteria are present (in the <SUB CRITERIA> section), include them in your evaluation.
                - For each sub-criteria, calculate its score based on the uploaded document.
                - Format sub-criteria as nested elements under the main criteria.
                - Each sub-criteria should have its own score, weighted score, and detailed reason section.

                NOTE : JUST PROCESS SUB-CRITERIA LKE YOU ARE PROCESSING A SINGLE CRITERIA BUT IT IS SUB-CATEGORY UNDER THE MAIN CRITERIA, SO CONSIDER THAT AS CONTEXT
            </SUB-CRITERIA RULES>
            
            <!-- Example without sub-criteria -->
                <div>
                    <h3>Criteria 1: Does the Bidder have an emergency response plan?(use exact name here)</h3>
                    <ul>
                        <li><h5>Score: <span class='score'><Score>55%</Score></span></h5></li>
                        <li><Main_weighted_score><h5>Weighted Score: X%</h5></Main_weighted_score></li>
                        <li><h5 style="font-size: 14px; color: #006699; margin: 0" class='criteria_<criteria_name>_evaluation_summary'> <p>While the bidder demonstrates basic quality awareness, their submission fails to meet the comprehensive requirements specified in the tender. The absence of detailed documentation, specific control mechanisms, and systematic improvement processes significantly undermines their ability to deliver consistent, compliant services and represents a substantial risk to project success.</p>.</h5></li>[THIS MUST BE DETAILED AND COVERS ALL NECESSARY POINTS]
                        <li><h5 class='reason highlight'>[STICK TO THIS TEMPLATE FOR REASON] Reason: 
                         
                                            <p><strong>PROMPT INSTRUCTIONS:</strong> Use this template to evaluate tender submissions against requirements. Be creative and adapt the structure, language, and specific examples to match your evaluation context. Follow this exact format:</p>
                                            <hr>

                                            <h2>TEMPLATE FORMAT:</h2>

                                            <h3>Step 1: Explain Tender Requirements</h3>
                                            <p>Start with a paragraph explaining what the tender/reference document requires. Then use numbered lists when necessary to break down specific requirements.</p>

                                            <p><em>Example structure:</em></p>
                                            <p>The tender requirements says... [main requirement topic] that must encompass several critical elements to ensure [purpose/compliance/standards].</p>

                                            <p>The required components include:</p>
                                            <ol>
                                                <li>Requirement 1 - Brief description( as seen in document name xyz.pdf)</li>
                                                <li>Requirement 2 - Brief description( as seen in document name xyz.pdf)</li>
                                                <li>Requirement 3 - Brief description( as seen in document name xyz.pdf)</li>
                                                <li>Additional requirements as needed( as seen in document name xyz.pdf)</li>
                                            </ol>

                                            <h3>Step 2: Explain the Bidder's Submission</h3>
                                            <p>Describe what the bidder actually submitted in their response. Reference the specific section where you found this information.</p>

                                            <p><em>Example structure:</em></p>
                                            <p>The bidder's submission in <strong>Section [X]</strong> addresses [what they covered] but demonstrates [overall assessment - partial compliance/gaps/strengths].</p>

                                            <h3>Step 3: Analyze Gaps, Strengths, and Weaknesses</h3>
                                            <p>Break this down into clear categories using both paragraphs and lists:</p>

                                            <h4>What They Did Well (Strengths):</h4>
                                            <ol>
                                                <li>Strength 1 with explanation( as seen in document name xyz.pdf)</li>
                                                <li>Strength 2 with explanation( as seen in document name xyz.pdf)</li>
                                            </ol>

                                            <h4>Critical Gaps and Missing Elements:</h4>
                                            <p>However, the submission fails to provide several essential elements:</p>
                                            <ol>
                                                <li><strong>Gap Category 1:</strong>
                                                    <ol type="a">
                                                        <li>Specific missing item 1</li>
                                                        <li>Specific missing item 2</li>
                                                        <li>Specific missing item 3</li>
                                                    </ol>
                                                </li>
                                                <li><strong>Gap Category 2:</strong>
                                                    <ol type="a">
                                                        <li>Specific missing item 1</li>
                                                        <li>Specific missing item 2</li>
                                                    </ol>
                                                </li>
                                            </ol>

                                            <h3>Step 4: Overall Assessment and Conclusion</h3>
                                            <p>Provide a summary paragraph that pulls everything together, stating the overall compliance level and the impact of the identified gaps.</p>

                                            <p><em>Example structure:</em></p>
                                            <p>While the submitted [document type] demonstrates [positive aspects], it falls short of meeting the comprehensive requirements outlined in the tender specification. The identified deficiencies in [key areas] represent [level of risk/compliance issues] and [impact on overall evaluation].</p>

                                            <hr>

                                            <h2>SAMPLE IMPLEMENTATION:</h2>

                                            <p>The tender specification requires a comprehensive quality management system that must demonstrate full compliance with ISO 9001:2015 standards and include robust process controls to ensure consistent service delivery.</p>

                                            <p>The mandatory requirements include:</p>
                                            <ol>
                                                <li><strong>Documented quality management system</strong> - Complete QMS manual and procedures( as seen in document name xyz.pdf)</li>
                                                <li><strong>Process control mechanisms</strong> - Monitoring and measurement systems( as seen in document name xyz.pdf)</li>
                                                <li><strong>Continuous improvement framework</strong> - Regular review and enhancement processes( as seen in document name xyz.pdf)</li>
                                                <li><strong>Customer satisfaction monitoring</strong> - Feedback collection and analysis systems( as seen in document name xyz.pdf)</li>
                                                <li><strong>Internal audit program</strong> - Scheduled audits and corrective actions( as seen in document name xyz.pdf)</li>
                                            </ol>

                                            <p>The bidder's submission in <strong>Section 4.2</strong> provides a basic overview of their quality approach but lacks the depth and specificity required for full tender compliance.</p>

                                            <h3>Identified Strengths:</h3>
                                            <ol>
                                                <li>The submission acknowledges ISO 9001:2015 as their quality standard and references their certification status.( as seen in document name xyz.pdf)</li>
                                                <li>Basic quality control measures are mentioned, including inspection protocols for key deliverables.( as seen in document name xyz.pdf)</li>
                                                <li>The response demonstrates awareness of customer satisfaction importance( as seen in document name xyz.pdf).</li>
                                            </ol>

                                            <h3>Critical Deficiencies:</h3>
                                            <p>Despite these positive elements, the submission exhibits significant gaps:</p>

                                            <ol>
                                                <li><strong>Documentation Gaps:</strong>
                                                    <ol type="a">
                                                        <li>No detailed QMS manual or procedure documentation provided</li>
                                                        <li>Missing process flowcharts and control point identification</li>
                                                        <li>Absence of document control and revision management procedures</li>
                                                    </ol>
                                                </li>
                                                
                                                <li><strong>Monitoring and Measurement:</strong>
                                                    <ol type="a">
                                                        <li>No specific key performance indicators (KPIs) defined</li>
                                                        <li>Missing measurement tools and frequency specifications</li>
                                                        <li>Lack of corrective action protocols</li>
                                                    </ol>
                                                </li>
                                                
                                                <li><strong>Continuous Improvement:</strong>
                                                    <ol type="a">
                                                        <li>No evidence of systematic improvement processes</li>
                                                        <li>Missing management review procedures</li>
                                                        <li>Absence of lessons learned integration mechanisms</li>
                                                    </ol>
                                                </li>
                                            </ol>

                                            <hr>
        
                        </h5></li>
                        
                    </ul>
                </div>
                
                <!-- Example with sub-criteria -->
<div>
    <h3 style="font-size: 18px;">Criteria 2: Does the Bidder have a comprehensive safety management system?</h3>
    <ul>
        <li><h5 style="font-size: 14px;">Criteria 2 Score: <span class='score'><Score>75%</Score></span></h5></li>
        <li><Main_weighted_score><h5 style="font-size: 14px;">Weighted Score(calculate using MAIN CRITERIA WEIGHTED SCORE CALCULATE WHEN SUB-CRITERIA IS PRESENT): X%</h5></Main_weighted_score></li>
        <li><h5 style="font-size: 14px;" class='reason highlight'>Reason: The reference document "Safety Requirements Specification v3.0" requires a comprehensive safety management system addressing various components including risk assessment, training protocols, incident reporting, and continuous improvement mechanisms. The "Vendor Safety Manual v2.1" demonstrates strong compliance in many areas but has some gaps. The score represents a weighted calculation based on the sub-criteria evaluation below.</h5></li>
        

        <!-- Sub-criteria section -->
        <li class="sub-criteria-section">
            <h4 style="font-size: 15px;">Sub-Criteria Evaluation:</h4>
            <ul class="sub-criteria-list">
                <!-- Sub-criteria 1 -->
                <li class="sub-criteria-item">
                    <h5 style="font-size: 14px;">Sub-Criteria 1: Risk Assessment Procedures(use exact name)</h5>
                    <ul>
                        <li><h6 style="font-size: 12px;">Score: <span class='score'><Score>85%</Score></span></h6></li>
                        <li><h6 style="font-size: 12px;">Weight: 30%</h6></li>
                        <li><h6 style="font-size: 12px;">Weighted Score: 25.5%</h6></li>
                        <li><h6 style="font-size: 12px;" class='reason'>Reason: ....</h6></li>
                        <li><h6 style="font-size: 12px; color: #006699; margin: 0" class='criteria_<sub_criteria_name>_evaluation_summary'>Program meets basic requirements but lacks specialized equipment training details and effectiveness verification.</h6></li>
                    </ul>
                </li>
                
                <!-- Sub-criteria 2 -->
                <li class="sub-criteria-item">
                    <h5 style="font-size: 14px;">Sub-Criteria 2: Safety Training Program</h5>
                    <ul>
                        <li><h6 style="font-size: 12px;">Score: <span class='score'><Score>70%</Score></span></h6></li>
                        <li><h6 style="font-size: 12px;">Weight: 25%</h6></li>
                        <li><h6 style="font-size: 12px;">Weighted Score: 17.5%</h6></li>
                        <li><h6 style="font-size: 12px;" class='reason'>Reason: ...h6></li>
                        <li><h6 style="font-size: 12px; color: #006699; margin: 0" class='criteria_<sub_criteria_name>_evaluation_summary'>Training program covers basic requirements but falls short on specialized equipment training and effectiveness verification.</h6></li>
                    </ul>
                </li>
                
                <!-- Sub-criteria 3 -->
                <li class="sub-criteria-item">
                    <h5 style="font-size: 14px;">Sub-Criteria 3: Incident Reporting System</h5>
                    <ul>
                        <li><h6 style="font-size: 12px;">Score: <span class='score'><Score>90%</Score></span></h6></li>
                        <li><h6 style="font-size: 12px;">Weight: 20%</h6></li>
                        <li><h6 style="font-size: 12px;">Weighted Score: 18%</h6></li>
                        <li><h6 style="font-size: 12px;" class='reason'>Reason: ..h6></li>
                        <li><h6 style="font-size: 12px; color: #006699; margin: 0" class='criteria_<sub_criteria_name>_evaluation_summary'>Incident reporting system is robust but could benefit from digital reporting system integration.</h6></li>
                    </ul>
                </li>
                
              
            </ul>
            <h5 style="font-size: 14px;">Total Sub-Criteria Weighted Score: 77.25% (Rounded to 75% for Main Criteria Score)</h5>
        </li>
    </ul>
</div>

</EXAMPLES>
        </SUMMARIZATION RULE>

<REFRENCING RULES>

 (CLEARLY NOTE THIS) - Whenever values, specs, timelines, locations, or any critical details are mentioned—reference them inline using their actual location or identifier in the tender or bidder documents. E.g., “See section 4.2.1 of pc8_requirment.pdf or “Bidder submission page 15, Table 3.1”

 </REFERECNING RULES>


**ADDITIONAL NOTES – PLEASE FOLLOW CAREFULLY(ALL CRITICAL):**
    * **HTML FORMAT:**

    * You **must follow the provided HTML template exactly**.
    * **Do not change any tag names**. Tags such as `<Main_weighted_score>`, `<h5>`, `<h6>`, etc., must be used **exactly as provided**.
    * **Very important:** Use the **correct HTML heading tags**:

        * If `<h5>` is used in the template, you must use `<h5>` — do **not replace it** with another tag.
        * If `<h6>` is used, use `<h6>` exactly — **do not substitute it**.
    * **Font sizes:**

        * Main Criteria: **18px**
        * Sub-Criteria Headers: **15px**
        * Sub-Headers: **14px**
        * Body Text: **12px**
    * **Maintain the exact HTML structure and tag hierarchy.** Do not add or remove wrappers, and do not modify tag types or names — this is **critical for proper data extraction**.

    NOTE: ENSURE THAT BULLED POINTS ARE WELL PRESENTED AND STRUCTURED

    * **EVALUATION SUMMARY SECTION:**

    * Follow the **exact structure and format** provided in the template.
    * **Do not modify the layout, tags, or formatting** in this section.

   
    * **SUMMARY:**

    * Follow the template **without deviations**.
    * **Do not alter tag names, font sizes, or structure**.
    * Ensure **correct and consistent usage** of tags such as `<h5>`, `<h6>`, etc., as originally provided.
    * Always include **document name references** in all relevant sections.

    NOTE (FOLLOW CAREFULLY): Do no add introductory or concluding statement along side the general html response e.g "Fine below the generated html format or here is the generated html format above"
                        :reason section always starts with "Reason: .... ", Follow this strictly
    NOTE(THESE ARE VERY CRITICAL):
        NOTE: When referencing criteria and sub-criteria (inside html), use the exact name as provided even if the spelling looks incorrect (leave it as it is)
        1. Do not change the casing if the name is capitalized.
           If the name is in lowercase, keep it lowercase.
           If it is mixed case, keep it mixed case.

   


# CRITICAL RULE (THIS RULE COVERS THE EVALUATION WHEN TALKING ABOUT BOTH SOURCE AND UPLOADED DOCUMENTS)

## Core Requirement
When talking about any evaluation, you MUST be highly specific and provide concrete examples with actual identifiers, numbers, and details rather than generic statements. This applies to ALL evaluation scenarios, not just equipment or documentation.

## What This Means

### ❌ WRONG - Vague and Generic
- "Some items don't meet requirements"(what items? what requirements? and why?)
- "Tender requirement requested for main equipments" (what requirements?)
- "There are compliance issues" (what requirements?)
- "Several aspects are non-compliant" (what aspects?)
- "Issues were identified" (what issues?)
- "Performance is inadequate" (what performance?)

ANOTHER EXAMPLES - I ADDED UB BRACKETS DETAILS TO BE PROVIDED
   Cost of equipment and all necessary components [what components?e.g., main machinery, control systems, safety equipment]
    Pricing for spare parts, categorized as:
    Commissioning spare parts [What are commissioning spare parts? e.g., initial setup and testing components]
    1-year operational spare parts [what a 1-year operational spare parts? e.g., routine maintenance and replacement items]
    Capital spare parts [ what  capital spare parts? e.g., major components with long lead times]
    All line items must be fully priced [what line items? e.g., unit cost, quantity, total amount]
    Spare parts pricing must align with Project Requirements [e.g., specific budget allocations and cost thresholds]

### ✅ CORRECT - Specific with Examples
- "Mill equipment tag RX-456 does not comply with tender requirement RX-789"
- "Training session TS-45 only covered 12 hours but standard requires 16 hours minimum"
- "Budget line item BL-203 shows $45,000 but approved amount was $52,000"
- "Tender requirement TR-789 specifies minimum 5 years experience but candidate has only 3 years"
- "Tender requirement TR-456 mandates ISO 9001:2015 certification but provided certificate is ISO 9001:2008"

## Detailed Examples Across ALL Categories

### Equipment & Technical
**Instead of:** "Equipment doesn't meet specs"
**Write:** "Centrifugal pump Model CP-500 delivers 180 m³/h but specification Sheet S-14 requires minimum 220 m³/h"

### Personnel & Training
**Instead of:** "Staff qualifications insufficient"
**Write:** "Operator John Smith (ID: OP-445) has Level 2 certification but position requires Level 3 per job description JD-890"

### Financial & Budget
**Instead of:** "Costs exceed budget"
**Write:** "Project Phase 3 actual cost $127,500 exceeds approved budget of $115,000 by $12,500 (10.9% overrun)"

### Timeline & Schedule
**Instead of:** "Behind schedule"
**Write:** "Milestone M-7 completed on Day 45 but contract Schedule CS-12 required completion by Day 38 (7-day delay)"

### Quality & Performance
**Instead of:** "Quality issues found"
**Write:** "Concrete batch CB-450 tested at 3,200 PSI compressive strength but specification requires minimum 4,000 PSI"

### Compliance & Regulatory
**Instead of:** "Regulatory non-compliance"
**Write:** "Safety procedure SP-201 missing required step 4.3 as mandated by OSHA standard 1926.95(a)"

### Process & Procedures
**Instead of:** "Process not followed correctly"
**Write:** "Invoice processing took 12 business days but SOP-Finance-04 requires completion within 5 business days"

### Location & Geographic
**Instead of:** "Wrong location"
**Write:** "Installation at Grid Reference GR-450-320 but approved site plan shows Grid Reference GR-455-325"

## Summary Structure Template

When summarizing findings for ANY evaluation type, use this format:

**Item:** [Specific subject/element/component with ID/reference]
**Issue:** [Exact non-compliance with reference numbers/codes]
**Requirement:** [Specific standard/specification/rule reference]
**Gap:** [Quantified difference between actual vs required]

### Example Summaries Across Different Areas:

#### Equipment Example:
- **Item:** Reactor vessel RV-100A
- **Issue:** Design pressure rated at 150 PSI
- **Requirement:** Tender specification TS-205 requires 200 PSI minimum
- **Gap:** 50 PSI shortfall from minimum requirement

#### Personnel Example:
- **Item:** Site supervisor Maria Garcia (Employee ID: SS-789)
- **Issue:** Has 3 years experience in role
- **Requirement:** Contract clause CC-12 requires minimum 5 years experience
- **Gap:** 2 years short of minimum requirement

#### Financial Example:
- **Item:** Marketing campaign MC-2024-Q2
- **Issue:** Actual spend $89,400
- **Requirement:** Approved budget BG-445 allocated $75,000
- **Gap:** $14,400 over budget (19.2% excess)

#### Process Example:
- **Item:** Customer complaint resolution CCR-1847
- **Issue:** Resolution completed in 8 business days
- **Requirement:** Service Level Agreement SLA-CS-01 requires 3 business days maximum
- **Gap:** 5 days beyond required timeline

## Key Principles
1. Always include specific identifiers (IDs, codes, numbers, names)
2. Reference exact sources (contracts, specifications, standards, procedures)
3. Quantify gaps with actual measurements/numbers/percentages
4. Provide direct comparisons between "actual" vs "required"
5. Avoid generalizations - every statement must be traceable to specific evidence
6. Apply this specificity to ANY subject matter being evaluated


CRITICAL REMINDER:
       - Enusure the summary part for both criteria and sub-criteria is not missing, detailed and captured the entire evaluation (mention values where necessary e.g prices, timelines, specifications  etc as this will be used for comparison) and follow the naming format specific strictly
       -  Remember to add %  symbol to the scores , all scores are in percentage
 NOTE: I have provided a template demonstrating how the reason section should be formatted. Please follow this format carefully and ensure proper structure. The reason section should include a balanced mix of paragraphs, sections, and bulleted lists to provide comprehensive and well-detailed evaluation points.
IMPORTANT FINAL INSTRUCTION – CITATION:
    <citation>
    # Dynamic Inline Citation Format
    Each evaluation must include three key sections with dynamic inline citations:

    1. Tender Requirements
    - Cite specific requirements from tender documents
    - Include section numbers and page references
    - Combine references when same document/page is cited multiple times
    Example: "The tender requires a minimum flow rate of 500 GPM and maximum pressure of 200 PSI (*Tender_2024.pdf*, Section 3.2, Page 8)"

    2. Bidder's Submission
    - Cite relevant sections from bidder's proposal documents
    - Include specific values, specifications, or commitments
    - Note: Multiple document chunks may be evaluated together
    - Group related citations to avoid repetition
    Example: "The bidder's proposal documents indicate a flow rate of 450 GPM and pressure of 180 PSI (*Bidder_Proposal_Chunks.pdf*, Sections 2.1-2.3)"
    
    AVOID: "The bidder's submission in document 210148-M011_Bid Form.pdf (pages 1-2) provides partial pricing information... The bidder's submission in document 210148-M011_Bid Form.pdf (pages 1-2) also shows..."
    INSTEAD(FOLLOW STRITCLY): "The bidder's submission across multiple document chunks provides pricing information and additional details..."

    3. Gap Analysis
    - Compare tender requirements against bidder's submission
    - Quantify any differences or shortfalls
    - Consider all relevant document chunks in the evaluation
    - Consolidate related gaps into single citations
    Example: "The proposed flow rate is 50 GPM below and pressure is 20 PSI below the tender requirements when considering all submitted documentation"

    Note: Replace document names, section numbers, and page references with actual values from your evaluation documents. Remember that evaluations may involve multiple document chunks that should be considered together. Citations should be inline, consolidated to avoid repetition, and not focus on individual files.
    NOTE: DO NOT TALK LIKE YOU ARE ONLY REVIEWEING ONE FILE
    </citation>

 WORD CHOICES RULES   
<word choices>
- Avoid using words like "critical" or overly complex terms.
- Use simple, clear, and basic wordings throughout.
- Ensure AI responses are fact-based and not judgmental.
- Remove all fluff or unnecessary language.
</word choices>


 SUCCESS CRITERIA (WHAT DOES SUCCESS MEEAN TO YOU IN THIS TASK)
    • Detailed points demonstrate professionalism and credibility
    • Follow template structure:
    - Tender requirements
    - Bidder submission strengths (across all document chunks)
    - Missing requirements (gaps)
    - Summary and conclusion
    - All sections must be detailed
    - Use output template provided strictly following the html
    - Dynamic inline citations that account for multiple document sources

                """
                }
            ],"cbp_get_claude_to_summarize_evaluate_v3": [
    {
    "role": "user",
    "content": """
 <UPLOADED EVALUATION FOR MAIN CRITERIA>
            {main_criteria_evaluate_summary_processing} [Note: This evaluation will be empty when sub-criteria are present, as the main criteria evaluation will be determined by the sub-criteria scores]
        </UPLOADED EVALUATION FOR MAIN CRITERIA>
        <MAIN CRITERIA>
            {criteria}
        </MAIN CRITERIA>
        <SUB CRITERIA>
            {sub_criteria}
        </SUB CRITERIA>
        <UPLOADED DOCUMENT FOR SUB CRITERIA>
            {sub_criteria_evaluate_summary_processing}
        </UPLOADED DOCUMENT FOR SUB CRITERIA>>
        

        <ROLE>
            1.) You are an Industrial engineer helpful assistant to do summarization of the above evaluation.
        </ROLE>
        <SUMMARIZATION RULE>
            - Provide it with proper formatting in HTML snippet wrapped in a div tag, exclude everything else other than the html snippet, it must start with a div tag.
            - Title should be size 18.
            - Section headers should be size 15.
            - Body text should be 11-12 size.
            - No text should be bold.
            - You are to summarize each criteria evaluation.
            - The reason section should follow what is provided in the evaluation but well formated in html (SEE TEMPLTE N OUTPUT FORMAT)
            


            
            - The weighted score is the weight of the criteria multiplied by the score of that criteria. Score * (Weight / 100) [ONLY CHNAGE THIS WHEN CALCULATING MAIN CRITERIA WEIGHTED SCORE WHEN SUB-CRITERIA IS PRESENT]
            - Example: 0.30 is the weight, 70% is the Score, 70 * (0.3 / 100) = 0.21% is the weighted score of the two numbers.
            - Example: 5 is the weight, 90% is the Score, 90 * (5 / 100) = 4.5% is the weighted score of the two numbers.
            - Example: weight is 20, score is 80%, 80 * (20 / 100) = 16% is the weighted score of the two numbers.
            - Do not show the formula in the response, just show the weighted score.
              
            - You can get the weight of each criteria from the <MAIN CRITERIA> tag.
            - The response **must not** mention how many evaluations were conducted or how the score was derived.
            - The report should strictly follow the below format whereby we have the Criteria, the Score, the Weighted Score & the Reason.
            - The Criteria Number is very important.
            - You should also ensure you maintain the position of the <Score> tag sorrounding the actual score value.
            - if partial information scenario then: we should say information not conclusive, refer reference
            - partial scenario response should always have the `highlight-red` class strictly.
            

            <MAIN CRITERIA WEIGHTED SCORE CALCULATION WHEN SUB-CRITERIA IS PRESENT>
            NOTE (FOLLOW THIS STRITCLY WHEN SUB CRITERIA ARE PRESENT): For calculating the weighted score of the main criteria when sub-criteria are present:
             FOLLOW THIS RULE STRITCLY WHEN CALCULATING WEIGHTED SCORE FOR MAIN CRITERIA WHEN SUB-CRITERIA ARE PRESENT
                1. Calculate each sub-criteria's weighted score: Sub-criteria Score * (Sub-criteria Weight / 100)
                2. Sum all sub-criteria weighted scores to get a percentage (out of 100%)
                3. Apply this percentage to the main criteria weight: (Sum of Sub-criteria Weighted Scores / 100) * Main Criteria Weight
                
            
                Example:
                - Main criteria weight: 20
                - Sub-criteria 1: Score 80%, Weight 30% → Weighted Score: 24%
                - Sub-criteria 2: Score 70%, Weight 40% → Weighted Score: 28%
                - Sub-criteria 3: Score 60%, Weight 30% → Weighted Score: 18%
                - Sum of sub-criteria weighted scores: 24% + 28% + 18% = 70%
                - Final main criteria weighted score: (70/100) * 20 = 14%
               
                ANOTHER EXAMPLE
                -  sub-criteria 1: 
                    Weight: 30
                    Score: 40%
                    Weighted Score:  12%

                 -   Sub-criteria 2:
                    Weight: 70
                    Score: 50%
                    Weighted Score: 35%

                  -  Main Criteria: 
                    Weight: 10
                    Score: Sum of all sub-criteria weighted score  (12% + 35%  = 47%)
                    Weighted Score: Score  /  Weight of the main criteria   (47 / 10 = 4.7)


            </MAIN CRITERIA WEIGHTED SCORE CALCULATION WHEN SUB-CRITERIA IS PRESENT>

            <SUB-CRITERIA RULES>
                - If sub-criteria are present (in the <SUB CRITERIA> section), include them in your evaluation.
                - For each sub-criteria, calculate its score based on the uploaded document.
                - Format sub-criteria as nested elements under the main criteria.
                - Each sub-criteria should have its own score, weighted score, and detailed reason section.

                NOTE : JUST PROCESS SUB-CRITERIA LKE YOU ARE PROCESSING A SINGLE CRITERIA BUT IT IS SUB-CATEGORY UNDER THE MAIN CRITERIA, SO CONSIDER THAT AS CONTEXT
            </SUB-CRITERIA RULES>
            
            <!-- Example without sub-criteria -->
                <div>
                    <h3>Criteria 1: Does the Bidder have an emergency response plan?(use exact name here)</h3>
                    <ul>
                        <li><h5>Score: <span class='score'><Score>55%</Score></span></h5></li>
                        <li><Main_weighted_score><h5>Weighted Score: X%</h5></Main_weighted_score></li>
                        <li><h5 style="font-size: 14px; color: #006699; margin: 0" class='criteria_<criteria_name>_evaluation_summary'> <p>While the bidder demonstrates basic quality awareness, their submission fails to meet the comprehensive requirements specified in the tender. The absence of detailed documentation, specific control mechanisms, and systematic improvement processes significantly undermines their ability to deliver consistent, compliant services and represents a substantial risk to project success.</p>.</h5></li>[THIS MUST BE DETAILED AND COVERS ALL NECESSARY POINTS]
                        <li><h5 class='reason highlight'>[STICK TO THIS TEMPLATE FOR REASON<REASON TEMPLATE>] Reason: 
                         
                                            <p><strong>PROMPT INSTRUCTIONS:</strong> Use this template to evaluate tender submissions against requirements. Be creative and adapt the structure, language, and specific examples to match your evaluation context. Follow this exact format:</p>
                                            <hr>

                                            <h2>TEMPLATE FORMAT:</h2>

                                            <p>Start with a paragraph explaining what the tender/reference document requires. Then use numbered lists when necessary to break down specific requirements.</p>

                                            <p><em>Example structure:</em></p>
                                            <p>The tender requirements says... [main requirement topic] that must encompass several critical elements to ensure [purpose/compliance/standards].</p>

                                            <p>The required components include:</p>
                                            <ol>
                                                <li>Project completion timeline of 10 weeks with detailed milestone schedule including design review (Week 2), equipment procurement (Week 4), installation (Week 6-8), and commissioning (Week 9-10) (Technical_Specs.pdf, Section 3.2.1)</li>
                                                <li>Valid ISO 9001:2015 certification with supporting documentation including quality manual, procedures, and internal audit reports (Quality_Requirements.pdf, Clause 4.5)</li>
                                                <li>Circuit breaker voltage specification of 100V with technical specifications including current rating, breaking capacity, and protection features (Equipment_Specs.pdf, Section 2.4)</li>
                                                <li>Minimum team size of 15 engineers with detailed CVs and qualifications including 5 senior engineers (10+ years experience), 7 mid-level engineers (5-10 years), and 3 junior engineers (2-5 years) (Staffing_Matrix.pdf, Table 2.1)</li>
                                            </ol>

                                         
                                            
                                            <h4>Bidder's Strengths):</h4>
                                            <ol>
                                                <li>The bidder submission states: (1) Completion timeline of 10 weeks (Bidder_Timeline.pdf, Section 2.3), (2) ISO 9001:2015 certificate provided (Certificate_Folder.pdf, Page 5), (3) Team composition shows 18 engineers (Staff_List.pdf, Table 1.2), (4) Circuit breaker specification of 100V (Bidder_Equipment.pdf, Section 4.1)</li>
                                                <li>The bidder demonstrates strong technical capabilities with: (1) Detailed equipment specifications matching tender requirements (Technical_Specs.pdf, Section 3.1), (2) Comprehensive quality control procedures (Quality_Manual.pdf, Chapter 4), (3) Experienced project team exceeding minimum requirements (Team_Profiles.pdf, Section 2.1)</li>
                                            </ol>

                                            <h4>Documented Compliance Gaps:</h4>
                                            <p>The submission exhibits significant gaps in meeting tender requirements:</p>
                                            <ol>
                                                <li><strong>Spare Parts Documentation Analysis:</strong>
                                                    <ol type="a">
                                                        <li>Tender Document P3803-000-ME-VDRS-005_0 (Section 4.2-4.3, Pages 12-15) requires detailed categorization of spare parts into three distinct categories:
                                                            <ul>
                                                                <li>Commissioning spares (valued at $25,000) for initial setup and testing as specified in Technical_Specs_2024.pdf (Page 8)</li>
                                                                <li>Operational spares (valued at $35,000) for routine maintenance as detailed in Maintenance_Requirements.pdf (Pages 4-6)</li>
                                                                <li>Capital spares (valued at $22,462) for major components as outlined in Equipment_Lifecycle.pdf (Page 12)</li>
                                                            </ul>
                                                            However, bidder only provides a single lump sum cost of $82,462 without proper categorization (Bidder_Spares.pdf, Pages 8-9)</li>
                                                        <li>No individual pricing breakdown provided for each spare part category as required in tender Section 4.2 (P3803-000-ME-VDRS-005_0, Pages 13-14), specifically:
                                                            <ul>
                                                                <li>Missing unit costs for individual components (Cost_Breakdown_2024.pdf, Page 3)</li>
                                                                <li>No quantity specifications for each spare part (Inventory_Requirements.pdf, Pages 2-4)</li>
                                                                <li>Lack of total cost calculations per category (Financial_Requirements.pdf, Page 7)</li>
                                                            </ul>
                                                        </li>
                                                        <li>Missing comprehensive parts listing with quantities as specified in tender Section 4.3 (P3803-000-ME-VDRS-005_0, Page 15), including:
                                                            <ul>
                                                                <li>No detailed inventory of spare parts (Inventory_Matrix.pdf, Pages 5-8)</li>
                                                                <li>Missing minimum stock levels for critical components (Stock_Levels_2024.pdf, Page 4)</li>
                                                                <li>No reorder point specifications (Inventory_Management.pdf, Page 6)</li>
                                                            </ul>
                                                        </li>
                                                    </ol>
                                                </li>
                                                <li><strong>Cost Documentation Analysis:</strong>
                                                    <ol type="a">
                                                        <li>Tender Document PR-2024-001 requires detailed documentation and tagging costs with specific values (Section 2.1, Pages 4-6), including:
                                                            <ul>
                                                                <li>Equipment tagging costs ($2,500 per unit) as specified in Tagging_Specs.pdf (Page 3)</li>
                                                                <li>Documentation preparation ($1,800 per document set) as detailed in Documentation_Requirements.pdf (Page 5)</li>
                                                                <li>Quality control verification ($950 per batch) as outlined in QC_Procedures.pdf (Page 8)</li>
                                                            </ul>
                                                            However, bidder only marks costs as "Included" without specific values (Bidder_Costs.pdf, Table 2.1, Page 4)</li>
                                                        <li>Missing detailed installation support cost allocation as required in tender Section 4.3 (PR-2024-001, Pages 12-14), specifically:
                                                            <ul>
                                                                <li>No breakdown of on-site technical support costs (Support_Costs_2024.pdf, Pages 2-3)</li>
                                                                <li>Missing travel and accommodation expenses (Travel_Policy.pdf, Page 4)</li>
                                                                <li>No specification of daily rates for support personnel (Rates_Schedule.pdf, Page 6)</li>
                                                            </ul>
                                                        </li>
                                                        <li>No itemized packaging and loading costs provided as specified in tender Section 3.2 (PR-2024-001, Pages 8-10), including:
                                                            <ul>
                                                                <li>Missing crating and packaging material costs (Packaging_Specs.pdf, Pages 3-4)</li>
                                                                <li>No loading and handling equipment costs (Handling_Requirements.pdf, Page 5)</li>
                                                                <li>Lack of transportation preparation expenses (Transport_Guide.pdf, Page 7)</li>
                                                            </ul>
                                                        </li>
                                                    </ol>
                                                </li>
                                            </ol>

                                            <h3>Overall Assessment and Conclusion</h3>
                                            <p>Provide a summary paragraph that pulls everything together, stating the overall compliance level and the impact of the identified gaps.</p>

                                            <p><em>Example structure:</em></p>
                                            <p>While the submitted [document type] demonstrates [positive aspects], it falls short of meeting the comprehensive requirements outlined in the tender specification. The identified deficiencies in [key areas] represent [level of risk/compliance issues] and [impact on overall evaluation].</p>

                                            <hr>
                        </h5></li>
                        
                    </ul>
                </div>
                
                <!-- Example with sub-criteria -->
<div>
    <h3 style="font-size: 18px;">Criteria 2: Does the Bidder have a comprehensive safety management system?</h3>
    <ul>
        <li><h5 style="font-size: 14px;">Criteria 2 Score: <span class='score'><Score>75%</Score></span></h5></li>
        <li><Main_weighted_score><h5 style="font-size: 14px;">Weighted Score(calculate using MAIN CRITERIA WEIGHTED SCORE CALCULATE WHEN SUB-CRITERIA IS PRESENT): X%</h5></Main_weighted_score></li>
        <li><h5 style="font-size: 14px;" class='reason highlight'>Reason: ...((FOLLOW THE EXAMPLE PROVIDED)</h5></li>
        

        <!-- Sub-criteria section -->
        <li class="sub-criteria-section">
            <h4 style="font-size: 15px;">Sub-Criteria Evaluation:</h4>
            <ul class="sub-criteria-list">
                <!-- Sub-criteria 1 -->
                <li class="sub-criteria-item">
                    <h5 style="font-size: 14px;">Sub-Criteria 1: Risk Assessment Procedures(use exact name)</h5>
                    <ul>
                        <li><h6 style="font-size: 12px;">Score: <span class='score'><Score>85%</Score></span></h6></li>
                        <li><h6 style="font-size: 12px;">Weight: 30%</h6></li>
                        <li><h6 style="font-size: 12px;">Weighted Score: 25.5%</h6></li>
                        <li><h6 style="font-size: 12px; color: #006699; margin: 0" class='criteria_<sub_criteria_name>_evaluation_summary'>Program meets basic requirements but lacks specialized equipment training details and effectiveness verification.</h6></li>
                        <li><h6 style="font-size: 12px;" class='reason'>Reason(FOLLOW THE EXAMPLE PROVIDED): ....</h6></li>
                        
                    </ul>
                </li>
                
                <!-- Sub-criteria 2 -->
                <li class="sub-criteria-item">
                    <h5 style="font-size: 14px;">Sub-Criteria 2: Safety Training Program</h5>
                    <ul>
                        <li><h6 style="font-size: 12px;">Score: <span class='score'><Score>70%</Score></span></h6></li>
                        <li><h6 style="font-size: 12px;">Weight: 25%</h6></li>
                        <li><h6 style="font-size: 12px;">Weighted Score: 17.5%</h6></li>
                        <li><h6 style="font-size: 12px; color: #006699; margin: 0" class='criteria_<sub_criteria_name>_evaluation_summary'>Training program covers basic requirements but falls short on specialized equipment training and effectiveness verification.</h6></li>
                        <li><h6 style="font-size: 12px;" class='reason'>Reason(FOLLOW THE EXAMPLE PROVIDED): ...h6></li>
                        
                    </ul>
                </li>
                
                <!-- Sub-criteria 3 -->
                <li class="sub-criteria-item">
                    <h5 style="font-size: 14px;">Sub-Criteria 3: Incident Reporting System</h5>
                    <ul>
                        <li><h6 style="font-size: 12px;">Score: <span class='score'><Score>90%</Score></span></h6></li>
                        <li><h6 style="font-size: 12px;">Weight: 20%</h6></li>
                        <li><h6 style="font-size: 12px;">Weighted Score: 18%</h6></li>
                        <li><h6 style="font-size: 12px; color: #006699; margin: 0" class='criteria_<sub_criteria_name>_evaluation_summary'>Incident reporting system is robust but could benefit from digital reporting system integration.</h6></li>
                        <li><h6 style="font-size: 12px;" class='reason'>Reason(FOLLOW THE EXAMPLE PROVIDED): ..h6></li>
                        
                    </ul>
                </li>
                
              
            </ul>
            <h5 style="font-size: 14px;">Total Sub-Criteria Weighted Score: 77.25% (Rounded to 75% for Main Criteria Score)</h5>
        </li>
    </ul>
</div>

</EXAMPLES>
        </SUMMARIZATION RULE>




**ADDITIONAL NOTES – PLEASE FOLLOW CAREFULLY(ALL CRITICAL):**
    * **HTML FORMAT:**

    * You **must follow the provided HTML template exactly**.
    * **Do not change any tag names**. Tags such as `<Main_weighted_score>`, `<h5>`, `<h6>`, etc., must be used **exactly as provided**.
    * **Very important:** Use the **correct HTML heading tags**:

        * If `<h5>` is used in the template, you must use `<h5>` — do **not replace it** with another tag.
        * If `<h6>` is used, use `<h6>` exactly — **do not substitute it**.
    * **Font sizes:**

        * Main Criteria: **18px**
        * Sub-Criteria Headers: **15px**
        * Sub-Headers: **14px**
        * Body Text: **12px**
    * **Maintain the exact HTML structure and tag hierarchy.** Do not add or remove wrappers, and do not modify tag types or names — this is **critical for proper data extraction**.

    NOTE: ENSURE THAT BULLED POINTS ARE WELL PRESENTED AND STRUCTURED

    * **EVALUATION SUMMARY SECTION:**

    * Follow the **exact structure and format** provided in the template.
    * **Do not modify the layout, tags, or formatting** in this section.

   
    * **SUMMARY:**

    * Follow the template **without deviations**.
    * **Do not alter tag names, font sizes, or structure**.
    * Ensure **correct and consistent usage** of tags such as `<h5>`, `<h6>`, etc., as originally provided.
    * Always include **document name references** in all relevant sections.

    NOTE (FOLLOW CAREFULLY): Do no add introductory or concluding statement along side the general html response e.g "Fine below the generated html format or here is the generated html format above"
                        :reason section always starts with "Reason: .... ", Follow this strictly
    NOTE(THESE ARE VERY CRITICAL):
        NOTE: When referencing criteria and sub-criteria (inside html), use the exact name as provided even if the spelling looks incorrect (leave it as it is)
        1. Do not change the casing if the name is capitalized.
           If the name is in lowercase, keep it lowercase.
           If it is mixed case, keep it mixed case.

           

WORD CHOICES RULES

<word choices>
    - Use clear, direct language without unnecessary complexity
    - Focus on factual statements based on available evidence
    - Present information objectively without subjective interpretation
    - Keep language simple and straightforward
    - Avoid making assumptions about future actions or intentions
    - Use neutral, descriptive language

Examples of phrases to avoid:
    - "The complete absence of" (assumes finality)
    - "The lack of" (implies deficiency)
    - "Failed to" (implies failure)
    - "Only" (implies limitation)
    - "Merely" (implies insufficiency)
    - "Just" (implies inadequacy)
    - "Simply" (implies oversimplification)
    - "Clearly" (implies obviousness)
    - "Obviously" (implies certainty)
    - "Significantly" (implies judgment)
</word choices>



# Additional Core Requirement (Specificity)
When talking about any evaluation, you MUST be highly specific and provide concrete examples with actual identifiers, numbers, and details rather than generic statements. This applies to ALL evaluation scenarios, not just equipment or documentation.

## What This Means

### ❌ WRONG - Vague and Generic
- "Some items don't meet requirements"(what items? what requirements? and why?)
- "Tender requirement requested for main equipments" (what requirements?)
- "There are compliance issues" (what requirements?)
- "Several aspects are non-compliant" (what aspects?)
- "Issues were identified" (what issues?)
- "Performance is inadequate" (what performance?)

ANOTHER EXAMPLES - I ADDED UB BRACKETS DETAILS TO BE PROVIDED
   Cost of equipment and all necessary components [what components?e.g., main machinery, control systems, safety equipment]
    Pricing for spare parts, categorized as:
    Commissioning spare parts [What are commissioning spare parts? e.g., initial setup and testing components]
    1-year operational spare parts [what a 1-year operational spare parts? e.g., routine maintenance and replacement items]
    Capital spare parts [ what  capital spare parts? e.g., major components with long lead times]
    All line items must be fully priced [what line items? e.g., unit cost, quantity, total amount]
    Spare parts pricing must align with Project Requirements [e.g., specific budget allocations and cost thresholds]

### ✅ CORRECT - Specific with Examples
- "Mill equipment tag RX-456 does not comply with tender requirement RX-789"
- "Training session TS-45 only covered 12 hours but standard requires 16 hours minimum"
- "Budget line item BL-203 shows $45,000 but approved amount was $52,000"
- "Tender requirement TR-789 specifies minimum 5 years experience but candidate has only 3 years"
- "Tender requirement TR-456 mandates ISO 9001:2015 certification but provided certificate is ISO 9001:2008"

## Detailed Examples Across ALL Categories (ADAPT THIS TO ALL SCENARIOS)


**Instead of:** "Equipment doesn't meet specs"
**Write:** "Centrifugal pump Model CP-500 delivers 180 m³/h but specification Sheet S-14 requires minimum 220 m³/h"


**Instead of:** "Staff qualifications insufficient"
**Write:** "Operator John Smith (ID: OP-445) has Level 2 certification but position requires Level 3 per job description JD-890"


**Instead of:** "Costs exceed budget"
**Write:** "Project Phase 3 actual cost $127,500 exceeds approved budget of $115,000 by $12,500 (10.9% overrun)"


**Instead of:** "Behind schedule"
**Write:** "Milestone M-7 completed on Day 45 but contract Schedule CS-12 required completion by Day 38 (7-day delay)"


**Instead of:** "Bidder provided Individual component pricing for main equipment systems"
**Write:** "Bidder provided detailed pricing breakdown for main equipment systems including primary crusher ($450,000), secondary crusher ($380,000), and ball mill ($1.2M) as specified in pricing schedule PS-2024-03"


**Write:** "Invoice processing took 12 business days but SOP-Finance-04 requires completion within 5 business days"
 SUCCESS CRITERIA (WHAT DOES SUCCESS MEEAN TO YOU IN THIS TASK)
    • Follow template structure:
    - Tender requirements
    - Bidder submission strengths (across all document chunks)
    - Missing requirements (gaps)
    - Summary and conclusion
    - Use output template provided strictly following the html
    - Dynamic inline citations that account for multiple document sources as seen in the evaluation
    - FOLLOW THE EXAMPLES IN <REASON TEMPLATE> YOUR REASON SECTION SOULD LOOK LIKE THAT PLEASE

NOTE: In the Reason section, the bidder's gaps are the most important and must be detailed and well presented. Avoid using terms like "partial compliance" - requirements are either compliant or non-compliant. If not fully compliant, list the specific gaps.
                """
                }
            ],
                       "cbp_analyse_equipment_items_using_bid":[
{"role":"user",
 "content":"""
    <EQUIPMENT_NAME>
        {equipment_name}
    </EQUIPMENT_NAME>
    <EQUIPMENT_ITEMS>
        {formatted_items}
    </EQUIPMENT_ITEMS>
    
    <BID_DOCUMENTS>
        {bid_documents}
    </BID_DOCUMENTS>
    
    <ROLE>
        You are an EPC (Engineering, Procurement, and Construction) Procurement Engineer. Your task is to analyze bid documents and extract information about specific attributes/items of the equipment named in EQUIPMENT_NAME.
    </ROLE>
    
    <TASK>
        - Analyze the provided BID_DOCUMENTS to extract information about each attribute/item listed in EQUIPMENT_ITEMS for the equipment specified in EQUIPMENT_NAME.
        - Understand that EQUIPMENT_NAME (e.g., "BALL MILL") is the main equipment, and EQUIPMENT_ITEMS are specific attributes or components of that equipment.
        - For each attribute/item of the equipment, extract ANY information found including specifications, dimensions, materials, etc.
        - Maintain strict adherence to factual information present in the documents without inference or assumptions.
        - Document the exact reference sources where information was found.
    </TASK>
    
    <INSTRUCTIONS>
        1. For each numbered attribute/item of the equipment, extract the following:
           - Name: The exact attribute/item name as listed
           - Information: ALL details found about this specific attribute/item of the equipment
           - Analysis: Brief factual analysis of what the documents say about this attribute/item
           - Reference Documents: Specific document names, page numbers, and sections where information was found
           
        2. Critical Rules:
           - ONLY extract information explicitly stated in the BID_DOCUMENTS
           - Do NOT infer, assume, or generate information not present in the documents
           - If NO information is found for an attribute/item, state "N/A"
           - Use ONLY the specified format - nothing before or after
           - FOLLOW THE FORMAT STRICTLY - no additional text, explanations, or summaries
           
        3. Format your response EXACTLY as specified below, with no additional text.
    </INSTRUCTIONS>
    

    
    <RESPONSE_FORMAT>
        <item_1>
        name: [exact attribute/item name from EQUIPMENT_ITEMS list]
        information: [ALL information found about this attribute/item or "N/A"]
        analysis_from_documents: [factual analysis based only on document content or "N/A"]
        reference_documents: [specific document names, page numbers, and sections or "N/A"]
        </item_1>
        
        <item_2>
        name: [exact attribute/item name from EQUIPMENT_ITEMS list]
        information: [ALL information found about this attribute/item or "N/A"]
        analysis_from_documents: [factual analysis based only on document content or "N/A"]
        reference_documents: [specific document names, page numbers, and sections or "N/A"]
        </item_2>
        
        [Continue for all attributes/items in EQUIPMENT_ITEMS]
    </RESPONSE_FORMAT>
    
    <IMPORTANT>
        - FOLLOW THE FORMAT STRICTLY
        - NO text before or after the item entries
        - NO explanations or summaries outside the specified format
        - Use "N/A" for any information not found
        - Remember that you are analyzing attributes/items OF the equipment, not separate equipment
        - The tag should be <item_1>, <item_2>, etc. (not equip_1, equip_2)
    NOTE: Make you sure to include the source document name in the reference documents section., it is very important, Texts/sections in BID DOCUMENTS are already prefixed with the source document name, so just copy paste it as is.
    </IMPORTANT>
 """
}
            ],
                       "cbp_analyse_equipment_items_using_bid":[
{"role":"user",
 "content":"""
    <EQUIPMENT_NAME>
        {equipment_name}
    </EQUIPMENT_NAME>
    <EQUIPMENT_ITEMS>
        {formatted_items}
    </EQUIPMENT_ITEMS>
    
    <BID_DOCUMENTS>
        {bid_documents}
    </BID_DOCUMENTS>
    
    <ROLE>
        You are an EPC (Engineering, Procurement, and Construction) Procurement Engineer. Your task is to analyze bid documents and extract information about specific attributes/items of the equipment named in EQUIPMENT_NAME.
    </ROLE>
    
    <TASK>
        - Analyze the provided BID_DOCUMENTS to extract information about each attribute/item listed in EQUIPMENT_ITEMS for the equipment specified in EQUIPMENT_NAME.
        - Understand that EQUIPMENT_NAME (e.g., "BALL MILL") is the main equipment, and EQUIPMENT_ITEMS are specific attributes or components of that equipment.
        - For each attribute/item of the equipment, extract ANY information found including specifications, dimensions, materials, etc.
        - Maintain strict adherence to factual information present in the documents without inference or assumptions.
        - Document the exact reference sources where information was found.
    </TASK>
    
    <INSTRUCTIONS>
        1. For each numbered attribute/item of the equipment, extract the following:
           - Name: The exact attribute/item name as listed
           - Information: ALL details found about this specific attribute/item of the equipment
           - Analysis: Brief factual analysis of what the documents say about this attribute/item
           - Reference Documents: Specific document names, page numbers, and sections where information was found
           
        2. Critical Rules:
           - ONLY extract information explicitly stated in the BID_DOCUMENTS
           - Do NOT infer, assume, or generate information not present in the documents
           - If NO information is found for an attribute/item, state "N/A"
           - Use ONLY the specified format - nothing before or after
           - FOLLOW THE FORMAT STRICTLY - no additional text, explanations, or summaries
           
        3. Format your response EXACTLY as specified below, with no additional text.
    </INSTRUCTIONS>
    

    
    <RESPONSE_FORMAT>
        <item_1>
        name: [exact attribute/item name from EQUIPMENT_ITEMS list]
        information: [ALL information found about this attribute/item or "N/A"]
        analysis_from_documents: [factual analysis based only on document content or "N/A"]
        reference_documents: [specific document names, page numbers, and sections or "N/A"]
        </item_1>
        
        <item_2>
        name: [exact attribute/item name from EQUIPMENT_ITEMS list]
        information: [ALL information found about this attribute/item or "N/A"]
        analysis_from_documents: [factual analysis based only on document content or "N/A"]
        reference_documents: [specific document names, page numbers, and sections or "N/A"]
        </item_2>
        
        [Continue for all attributes/items in EQUIPMENT_ITEMS]
    </RESPONSE_FORMAT>
    
    <IMPORTANT>
        - FOLLOW THE FORMAT STRICTLY
        - NO text before or after the item entries
        - NO explanations or summaries outside the specified format
        - Use "N/A" for any information not found
        - Remember that you are analyzing attributes/items OF the equipment, not separate equipment
        - The tag should be <item_1>, <item_2>, etc. (not equip_1, equip_2)
    NOTE: Make you sure to include the source document name in the reference documents section., it is very important, Texts/sections in BID DOCUMENTS are already prefixed with the source document name, so just copy paste it as is.
    </IMPORTANT>
 """
}
            ],
            

                                               "cbp_technical_equipment_evaluation": [
    {
    "role": "user",
    "content": """
    <COMPLETE_EQUIPMENT_ANALYSIS>
        {complete_equipment_analysis}
    </COMPLETE_EQUIPMENT_ANALYSIS>

    <TECHNICAL CRITERIA WEIGHT>
        {technical_criteria_weight}
    </TECHNICAL CRITERIA WEIGHT>

 <EQUIPMENT_NAME_WEIGHTS>
        {equipment_name_weights}
    </EQUIPMENT_NAME_WEIGHTS>
<criteria_index>
    {criteria_index}
</criteria_index>
    
    <ROLE>
        1.) You are an Industrial engineer helpful assistant to do summarization of the above evaluation.
    </ROLE>
    
    <EVALUATION_RULES>
        1. DOCUMENT STRUCTURE:
           - The document (COMPLETE_EQUIPMENT_ANALYSIS) contains detailed information for multiple pieces of equipment
           - Each equipment consists of multiple items/components with technical specifications
           
        2. COMPLIANCE METHODOLOGY:
           - For each equipment item, compare the BID ANALYSIS against the TENDER ANALYSIS
           - BID ANALYSIS: What the bidder has provided/offered for each equipment item FROM their documents
           - TENDER ANALYSIS: The required technical specifications set by the procuring entity
           - IF TENDER REQUIREMENTS ARE NOT SPECIFIED FOR AN ITEM, SCORE BY IF ITEM IS PROPERLY SPECIFIED IN THE BID OR NOT OR MISSING
           
        3. CALCULATION METHODOLOGY:
           - ITEM WEIGHTS: Each item within an equipment has equal weight
              * Item weight = 100 / (total number of items in that equipment)
              * Example: If Equipment A has 5 items, each item has weight of 20%
              
              
           - EQUIPMENT (SUB-CRITERIA) SCORE: 
              * Calculate the weighted average of all item scores within that equipment
              * Formula: Sum of (Item Score × Item Weight) for all items in the equipment
              * To get weighted score of the equipment,ypu need
                1. sum_of_weighted_scores = sum_of_all_item_weighted_scores
                2. equipment weighted score  = sum_of_weighted_scores / 100  * equipment(sub-criteria) weight

                Each equipment weight  has been  provided in the tag <EQUIPMENT_NAME_WEIGHTS> with its weight, use this accordingly
                If you say the score of a  sub-criteria(equipment) is 80% and the weight is 50%, then the weighted score is 80% * (50/100) = 40%
                (NEVER MISCALCULATE THE WEIGHTED SCORE OF THE EQUIPMENT, FOLLOW THIS RULE)
              
           - TECHNICAL CRITERIA SCORE:
              * Sum of all equipment (sub-criteria) scores
              * This produces a score out of 100%
              
           - WEIGHTED TECHNICAL CRITERIA SCORE:
              * Formula: Technical Criteria Score × (Technical Criteria Weight / 100)
              * Example: If Technical Criteria Score is 85% and weight is 30, then Weighted Score = 85% × (30/100) = 25.5%
    

    </EVALUATION_RULES>
    
    <SUMMARIZATION RULE>
        - Provide it with proper formatting in HTML snippet wrapped in a div tag, exclude everything else other than the html snippet, it must start with a div tag.
        - Title should be size 18.
        - Section headers should be size 15.
        - Body text should be 11-12 size.
        - No text should be bold.
        - You are to summarize each criteria evaluation.
        - The Reason section MUST be extremely detailed and thorough - this is absolutely critical. The analysis must include:
                * Comprehensive breakdown of ALL requirements from bid and tender analysis
                * Exhaustive analysis of each equipment item, examining every relevant detail and component
                * Rigorous gap analysis comparing tender requirements vs bid submissions, with precise identification of any misalignments
                * Meticulous explanation of scoring rationale, supported by concrete examples
                * Extensive evidence and direct quotes from both tender and bid analysis
                * Detailed examples demonstrating compliance or non-compliance
                * Exhaustive analysis of the evaluation, examining every relevant detail and component
                * Meticulous explanation of scoring rationale, supported by concrete examples
                * In-depth assessment of how any gaps impact project success
                * Specific recommendations for addressing deficiencies
                * Analysis of both direct and indirect consequences of any shortcomings

        
                
                #### 3. Reason Section(Additional context)
                **Structure your reasoning as follows:**

                **Key Supporting Details**
                - Extract specific, relevant information from the bidder's submission
                - Include EPC-relevant terminology and quantitative data:
                - Timelines and milestones
                - Technical specifications
                - Locations and quantities
                - Standards and certifications
                - Commercial terms and Incoterms
                - Use bullet points for structured information presentation

                **Caveats/Risks/Missing Items (when applicable)**
                - Identify disclaimers, limitations, or uncertainties
                - Note gaps that could affect performance or compliance
                - Maintain neutral, factual tone—avoid speculation

                **Summary Conclusion**
                - Tie the assessment back to the criterion
                - State overall alignment with project/contractual expectations

              NOTE !!!- Never use generic terms like "UPLOADED_DOCUMENT" or "SOURCE_DOCUMENT" - always use the exact document titles ("Bidder documents" for "UPLOADED_DOCUMENT" and tender requirements for "SOURCE DOCUMENT")

            CRITICAL NOTE !!!! : The following instructions demand meticulous detail for each criterion and its corresponding sub-criteria. Superficial analysis is unacceptable. Provide a comprehensive and granular explanation, akin to the level of detail a professional would employ, outlining precisely what was observed and what was absent. This level of rigor is mandatory for each element. This is a critical part of the task

            
        
        - The weighted score is the weight of the criteria multiplied by the score of that criteria. Score * (Weight / 100)
        - Example: 0.30 is the weight, 70% is the Score, 70 * (0.3 / 100) = 0.21% is the weighted score of the two numbers.
        - Example: 5 is the weight, 90% is the Score, 90 * (5 / 100) = 4.5% is the weighted score of the two numbers.
        - Example: weight is 20, score is 80%, 80 * (20 / 100) = 16% is the weighted score of the two numbers.
        - Do not show the formula in the response, just show the weighted score.
        - The response **must not** mention how many evaluations were conducted or how the score was derived.
        - The report should strictly follow the below format whereby we have the Criteria, the Score, the Weighted Score & the Reason.
        - You should also ensure you maintain the position of the <Score> tag surrounding the actual score value.
        - The weighted_score tag must surround the weighted score section.

    FOR SCENRAIOS WHERE TENDER AND BID ANALYSIS DOCUMENTS FOR EQUIIPMENT ITEMS ARE NOT AVAILABLE , KINDLY SAY THE EQUIPMENT DOES NOT HAVE ANY ITEMS AVAILABLE IN THE DATABASE FOR PROPER ANALYSIS
    </SUMMARIZATION RULE>
    
    <RESPONSE_FORMAT>
        Provide your evaluation in HTML format wrapped in a div tag with the following structure:
        
        <div>
            <h3 style="font-size: 18px;">Criteria {criteria_index}: Technical Criteria</h3>
            <ul>
                <li><h5 style="font-size: 14px;">Criteria 1 Score: <span class='score'><Score>X%</Score></span></h5></li>
                <li><Main_weighted_score><h5 style="font-size: 14px;">Weighted Score: X%</h5></Main_weighted_score></li>
                <li><h5 style="font-size: 14px;">Reason: [Detailed technical assessment summarizing overall equipment compliance]</h5></li>
                <li><h5 style="font-size: 14px; color: #006699; margin: 0" class='criteria_Technical Criteria_evaluation_summary'>[Summary of overall technical compliance, highlighting key points and areas for improvement]</h5></li>
            </ul>
            
            <h4 style="font-size: 15px;">Sub-Criteria Analysis:</h4>
            
            [For each equipment as sub-criteria:]
            <div>
                <h4 style="font-size: 15px;">Sub-Criteria X: [Equipment Name(USE EXACT SAME NAME, DO NOT CHANGE THE CASE)]</h4>
                <ul>
                    <li><h5 style="font-size: 14px;">Sub-Criteria X Score: <span class='score'><Score>X%</Score></span></h5></li>
                    <li><weighted_score><h5 style="font-size: 14px;">Weighted Score: X%</h5></weighted_score></li>
                    <h6 style="font-size: 12px; color: #006699; margin: 0" class='criteria_<(EQUIPMENT NAME)>_evaluation_summary'>[Summary of this equipment's compliance, highlighting key points and areas for improvement]</h6>
                    <li><h5 class='reason' style="font-size: 14px; margin-bottom: 15px;">Reason: 
                                   [USE THIS AS TEMPLATE BUT BE CREATIVE]     <p>
                                            The reference document requires a comprehensive emergency response plan that covers the following key components:
                                        </p>
                                        
                                        <ol>
                                            <li>Detailed emergency procedures</li>
                                            <li>Clearly defined roles and responsibilities</li>
                                            <li>Effective communication protocols</li>
                                            <li>Emergency contact information</li>
                                        </ol>
                                        
                                        <p>
                                            The bidder's submission in <strong>Section 3</strong> addresses some of these requirements but falls short in several critical areas.
                                        </p>
                                        
                                        <ol>
                                            <li>
                                            It outlines the roles of key personnel, including those related to Health, Safety, and Environment (HSE).
                                            </li>
                                            <li>
                                            However, it does not provide:
                                            <ol type="a">
                                                <li>Detailed emergency response procedures</li>
                                                <li>Specific communication protocols for various emergency scenarios</li>
                                                <li>Clearly marked evacuation routes</li>
                                                <li>Designated assembly points</li>
                                                <li>Locations of emergency equipment</li>
                                            </ol>
                                            </li>
                                        </ol>
                                        
                                        <p>
                                            Although the plan shows a basic understanding of emergency response requirements, it lacks the depth and specificity needed to fully comply with project safety standards. The omission of essential elements weakens the overall quality of the response plan.
                                        </p>
                        </h5>
                       
                    </li>
                </ul>
            </div>
            [Repeat for all equipment]
        </div>
    </RESPONSE_FORMAT>
    
    <IMPORTANT>
        - Be extremely detailed and thorough in your analysis
        - The <Score> tag must surround the actual score value
        - The weighted_score tag must surround the weighted score section
        - Provide specific comparisons between tender requirements and bid specifications
        - Clearly show all calculations for transparency
        - Include detailed reasoning about compliance for each item without showing individual item scores
        - Include the weights used for calculating the final technical criteria score
        - Highlight specific compliance issues and strengths
        - Never use generic terms like "TENDER_ANALYSIS" or "BID_ANALYSIS" in your response
        - Use percentage values for all scores (e.g., 85%)
        - Round all calculations to one decimal place
        - NOTE: FOLLOW THE HTML FORMAT STRICTLY, DO NOT CHANGE THE TEMPLATE (USE THE EXACT PIXEL SIZES SPECIFIED: 18PX FOR MAIN CRITERIA, 15PX FOR SUB-CRITERIA HEADERS, 14PX FOR SUB-HEADERS, AND 12PX FOR BODY TEXT)\
        - FOR THE EVALUATION SUMMARY TAG USE THE EXACT SAME FORMAT TOO , DO NOT CHNAGE PLEASE 
        NOTE - VERY VERY CIRITCAL WHEN MAKE SURE TO REFERENCE THE EXACT DOCUMENTS NAMES FROM WHEREEVER TEXT YOU ARE REFERERRING TOO (EVALUATION, REASON AND REFERENCE ALL HAVE DOCUMENT NAMES IN THEIR CONTENT), DOCUMENT NAME REFERENCING IS VERY IMPORTANT
    </IMPORTANT>


    
    **ADDITIONAL NOTES – PLEASE FOLLOW CAREFULLY(ALL CRITICAL):**
    * **HTML FORMAT:**

    * You **must follow the provided HTML template exactly**.
    * **Do not change any tag names**. Tags such as `<Main_weighted_score>`, `<h5>`, `<h6>`, etc., must be used **exactly as provided**.
    * **Very important:** Use the **correct HTML heading tags**:

        * If `<h5>` is used in the template, you must use `<h5>` — do **not replace it** with another tag.
        * If `<h6>` is used, use `<h6>` exactly — **do not substitute it**.

    * **Font sizes:**

        * Main Criteria: **18px**
        * Sub-Criteria Headers: **15px**
        * Sub-Headers: **14px**
        * Body Text: **12px**
    * **Maintain the exact HTML structure and tag hierarchy.** Do not add or remove wrappers, and do not modify tag types or names — this is **critical for proper data extraction**.

    * **EVALUATION SUMMARY SECTION:**

    * Follow the **exact structure and format** provided in the template.
    * **Do not modify the layout, tags, or formatting** in this section.
    "Ensure there is no extra space (no margin or padding) between the reason and summary parts."

    * **DOCUMENT NAME REFERENCING (VERY CRITICAL):**
    * For **every evaluation point, reason, and reference**, reference the name of the document when necessary but do not necessayr make it like that is the only file you are checking since there are lot of files

    * **SUMMARY:**

    * Follow the template **without deviations**.
    * **Do not alter tag names, font sizes, or structure**.
    * Ensure **correct and consistent usage** of tags such as `<h5>`, `<h6>`, etc., as originally provided.
    * Always include **document name references** in all relevant sections.

    CRITICAL INSTRUCTION - EQUIPMENT NAME FORMATTING:
    When referencing equipment names or sub-criteria in the evaluation:
    1. Use the EXACT name as provided - no modifications allowed
    2. Preserve the original casing:
       - If capitalized (e.g., "BALL MILL"), keep it capitalized
       - If lowercase (e.g., "ball mill"), keep it lowercase
       - If mixed case (e.g., "Ball Mill"), maintain the mixed case
    3. Preserve ALL spaces exactly as provided
       - If there are leading spaces (e.g., " Cyclone cluster"), keep them
       - If there are trailing spaces (e.g., "Cyclone cluster "), keep them
       - If there are multiple spaces between words (e.g., "Cyclone  cluster"), keep them
    4. Do not add or remove any characters, including spaces
    5. Do not standardize or normalize the names in any way

    This is CRITICAL for maintaining data integrity and ensuring accurate evaluation tracking.

    Always preserve the original formatting of the name when mentioning it in the sub-criteria.
NOTE (FOLLOW CAREFULLY): Do no add introductory or concluding statement along side the general html response e.g "Fine below the generated html format or here is the generated html format above"
 CRITICAL REMINDER:
       Enusure the summary part for both criteria and sub-criteria is not missing, detailed and captured the entire evaluation (mention values where necessayr) and follow the naming format specific strictly

NOTE: I have provided a template demonstrating how the reason section should be formatted. Please follow this format carefully and ensure proper structure. The reason section should include a balanced mix of paragraphs, sections, and bulleted lists to provide comprehensive and well-detailed evaluation points.
FOLLOW THE OUTPUT FORMAT STRICTLY
"""
}
],
                           "cbp_analyse_equipment_items_using_tender":[
{"role":"user",
 "content":"""
    <EQUIPMENT_NAME>
        {equipment_name}
    </EQUIPMENT_NAME>
    <EQUIPMENT_ITEMS>
        {formatted_items}
    </EQUIPMENT_ITEMS>
    
    <TENDER_DOCUMENTS>
        {tender_document}
    </TENDER_DOCUMENTS>
    
    <ROLE>
        You are an EPC (Engineering, Procurement, and Construction) Technical Engineer. Your task is to analyze tender documents and extract technical specifications or standards for specific attributes/items of the equipment named in EQUIPMENT_NAME.
    </ROLE>
    
    <TASK>
        - Analyze the provided TENDER_DOCUMENTS to extract technical specifications, standards, or requirements or any useful information specified about about each attribute/item listed in EQUIPMENT_ITEMS for the equipment specified in EQUIPMENT_NAME.
        - Understand that EQUIPMENT_NAME (e.g., "BALL MILL") is the main equipment, and EQUIPMENT_ITEMS are specific attributes or components of that equipment.
        - For each attribute/item of the equipment, extract ANY technical requirements including specifications, dimensions, materials, standards, or anything useful that could be genralized for all of them etc.
        - Maintain strict adherence to factual information present in the documents without inference or assumptions.
        - Document the exact reference sources where information was found.
    </TASK>
    
    <INSTRUCTIONS>
        1. For each numbered attribute/item of the equipment, extract the following:
           - Name: The exact attribute/item name as listed
           - Technical Specification: ALL technical requirements, specifications, or standards found about this specific attribute/item
           - Reference Documents: Specific document names, page numbers, and sections where information was found
           
        2. Critical Rules:
           - ONLY extract information explicitly stated in the TENDER_DOCUMENTS
           - Do NOT infer, assume, or generate information not present in the documents
           - If NO information is found for an attribute/item, state "N/A"
           - Use ONLY the specified format - nothing before or after
           - FOLLOW THE FORMAT STRICTLY - no additional text, explanations, or summaries
           
        3. Format your response EXACTLY as specified below, with no additional text.
    NOTE: IF you fnd any standard, requirement or information pointing to the equipment to the equipment itself and indirectly to the attribute/item, please include it in the response. as a spec for the items
    </INSTRUCTIONS>
    
    <RESPONSE_FORMAT>
        <item_1>
        name: [exact attribute/item name from EQUIPMENT_ITEMS list]
        technical_specification/requirement: [ALL technical requirements found about this attribute/item or "N/A"]
        reference_documents: [specific document names, page numbers, and sections or "N/A"]
        </item_1>
        
        <item_2>
        name: [exact attribute/item name from EQUIPMENT_ITEMS list]
        technical_specification/requirement: [ALL technical requirements found about this attribute/item or "N/A"]
        reference_documents: [specific document names, page numbers, and sections or "N/A"]
        </item_2>
        
        [Continue for all attributes/items in EQUIPMENT_ITEMS]
    </RESPONSE_FORMAT>
    
    <IMPORTANT>
        - FOLLOW THE FORMAT STRICTLY
        - NO text before or after the item entries
        - NO explanations or summaries outside the specified format
        - Use "N/A" for any information not found
        - Remember that you are extracting technical requirements FOR the equipment, not general information
        - The tag should be <item_1>, <item_2>, etc. (not equip_1, equip_2)
    </IMPORTANT>

    NOTE: Make you sure to include the source document name in the reference documents section., it is very important, Texts/sections in TENDER DOCUMENTS are already prefixed with the source document name, so just copy paste it as is for references purposes

 """}],

            "cbp_summarize_evaluation_result": [
                {
                "role": "system",
                "content": "As an AI parser assistant, your task is to review the document and focus on which part of the document is focused mainly on the criteria given.\n\nSTEP1: You should give an Evaluation Result based on both documents by comparing the uploaded document against the source document.\n\nExample Below: \nCompliant: The answer is YES. It's in section 7.2 of this document.\nSTEP2: Your results should be for the number of criteria you have.\nExample if you have Criteria1 & Criteria 2 then Evaluation Summary has to be done for each of the criteria.\n\n**Important:**\n- Also lookout for any suggestions, examples or any form of statement that supports the criteria.\n- We need every single statement no matter how small that could help the criteria.\n- Also point to which part of the document it is.\n\nNote: The text should be scored based on the following:\n- 'Non-Conforming': Score -1 if it does not answer the Question.\n- 'Compliant': Score 1 if it clearly answers the Question.\n- 'Neutral': Score 0 if the document satisfies the Question but does not strongly align with it."
                },
                {
                "role": "user",
                "content": "SOURCE DOCUMENT: \n    {source_document}  \nUPLOADED DOCUMENT: \n    {text} \nCRITERIA:\n    {concatenated_criteria}\n\n**Important:**\n- Also lookout for any suggestions, examples or any form of statement that supports the criteria.\n- We need every single statement no matter how small that could help the criteria.\n- Also point to which part of the document it is."
                }
            ],
            "scp_get_claude_to_summarize_evaluate_v2": [
                {
                "role": "user",
                "content": "<UPLOADED DOCUMENT>\n    {evaluate_summary_processing}\n    </UPLOADED DOCUMENT>\n    <PROJECT_TITLE>\n    {project_title}\n    </PROJECT_TITLE>\n    <DOCUMENT_TITLE>\n    {document_title}\n    </DOCUMENT_TITLE>\n    <DOCUMENT_NUMBER>\n    {document_number}\n    </DOCUMENT_NUMBER>\n    <ROLE>\n        1.) You are an Industrial engineer helpful assistant to do summarization of the above evaluation\n    </ROLE>\n    <SUMMARIZATION_RULE>\n        - Provide it with proper formatting in HTML snippet wrapped in a div tag, exclude everything else other than the html snippet, it must start with a div tag.\n        - Title should be size 18\n        - Section headers should be size 15\n        - Body text should be 11-12 size\n        - No text should be bold\n        - Do not mention the conformity score i.e., either of non-compliance, Minimal Compliance, full compliance, etc.\n        - The report should strictly follow the below format.\n        <SCORE INTERPRETATION>\n            - Use the following score criteria for evaluation:\n            code 1: Approved (95%- 100% score)\n            code 2: Accepted with comments (between 80-95%)\n            code 3: Reviewed with comments (between 60-79%)\n            code 4: Rejected (score < 59%)\n        </SCORE INTERPRETATION>\n        <EXAMPLE>\n            <div>\n                <h2 style=\"font-size: 18px;\">Project Title: Pump Replacement Project</h2>\n                <h3 style=\"font-size: 18px;\">Document Title: Specifications for API 610 Centrifugal Pump</h3>\n                <h4 style=\"font-size: 18px;\">Document Number and Revision: 34-06-12345-Specifications API Pump - rev0</h4>\n                <h4 style=\"font-size: 18px;\">code 3</h4>\n                <p>Summary of Document Review: The Specifications for API 610 Centrifugal Pump Document no. 34-06-12345-Specifications API Pump -rev0 was reviewed against the project document specifications document reference 34-06409-1092 and industry standards, including API Standard 610: Centrifugal Pumps for Petroleum, Petrochemical, and Natural Gas Industries and ASME B73.1: Specification for Horizontal End Suction Centrifugal Pumps for Chemical Process codes and other applicable regulations. While the document demonstrates a commendable level of detail, several critical non-compliance findings were identified during the analysis, necessitating corrective actions to ensure compliance and performance. Refer to the details below for further references. The document revision cannot be accepted and should be returned as Code 3 \"Rejected\". Contractor is kindly requested to address the identified issues and prepare a revised version of the document for resubmission at the earliest convenience. The revised document should incorporate all necessary corrections and updates to ensure compliance with the original project document specifications and any additional feedback provided.</p>\n            </div>\n        </EXAMPLE>\n        <EXAMPLE>\n            <div>\n                <h2 style=\"font-size: 18px;\">Project Title: Quality Improvement Plan</h2>\n                <h3 style=\"font-size: 18px;\">Document Title: Process Optimization Strategy</h3>\n                <h4 style=\"font-size: 18px;\">Document Number and Revision: 21-04-56789-Optimization Strategy - rev1</h4>\n                <h4 style=\"font-size: 18px;\">code 1</h4>\n                <p>Summary of Document Review: The Process Optimization Strategy document no. 21-04-56789-Optimization Strategy -rev1 was thoroughly evaluated against the project specifications and industry best practices. The document provides a comprehensive and detailed approach to process optimization, meeting all necessary criteria and standards. It is well-organized and clearly articulated, with no major issues identified. The document has been reviewed and found to be fully compliant with the project requirements and industry standards. The document is approved for implementation without any further revisions.</p>\n            </div>\n        </EXAMPLE>\n        <EXAMPLE>\n            <div>\n                <h2 style=\"font-size: 18px;\">Project Title: Equipment Calibration</h2>\n                <h3 style=\"font-size: 18px;\">Document Title: Calibration Procedures for Measuring Instruments</h3>\n                <h4 style=\"font-size: 18px;\">Document Number and Revision: 18-02-34567-Calibration Procedures - rev2</h4>\n                <h4 style=\"font-size: 18px;\">code 4</h4>\n                <p>Summary of Document Review: The Calibration Procedures document no. 18-02-34567-Calibration Procedures -rev2 was reviewed against the project specifications and relevant standards. The document fails to meet the necessary criteria and standards due to several significant deficiencies, including incomplete procedures and lack of critical calibration details. These issues impact the reliability and accuracy of the calibration processes outlined in the document. As a result, the document is not acceptable and is classified as Code 4 \"Rejected\". The contractor is requested to address the identified issues thoroughly and submit a revised version that fully complies with the required standards and specifications.</p>\n            </div>\n        </EXAMPLE>\n    </SUMMARIZATION_RULE>"
                }
            ],
            "scp_get_claude_to_give_non_compliance_comment": [
                {
                "role": "user",
                "content": "<REPORT>\n    <Count>{count}</Count>\n    <Criteria>{criteria}</Criteria>\n    {text}\n    <PROJECT_TITLE>\n        {project_title}\n    </PROJECT_TITLE>\n    <DOCUMENT_TITLE>\n        {document_title}\n    </DOCUMENT_TITLE>\n    <DOCUMENT_NUMBER>\n        {document_number}\n    </DOCUMENT_NUMBER>\n</REPORT>\n<ROLE>\n    1.) You are an Industrial engineer helpful assistant to do detailed document review of the above report based on the reason & the reference.\n</ROLE>\n<RULE>\n    - Provide it with proper formatting in HTML snippet wrapped in a div tag, exclude everything else other than the html snippet, it must start with a div tag.\n    - Title should be size 18\n    - Section headers should be size 15\n    - Body text should be 11-12 size\n    - No text should be bold\n    - The detailed review should be just one detailed paragraph.\n    - The reference should also be just one paragraph same as the actual reference given.\n    - Do not use the term UPLOADED_DOCUMENT instead use the document title itself.\n    - Do not use the term SOURCE_DOCUMENT instead use the Project title.\n    - The report should strictly follow the below format whereby we have the detailed review wrapped in DETAILED_REVIEW tag & reference wrapped between REFERENCE tag:\n        <DETAILED_REVIEW>\n        </DETAILED_REVIEW>\n        <REFERENCE>\n        </REFERENCE>\n    - The title is gotten from the count & criteria example the below is gotten from <Count>1</Count><Criteria>section 4.2 page 16 design life</Criteria>\n</RULE>\n<EXAMPLE>\n    <DETAILED_REVIEW>\n        1. section 4.2 page 16 design life: The submitted centrifugal pump design specification proposes a service life of 25 years, which falls short of the specified minimum design service life requirement of thirty (30) years outlined in the project specification. Additionally, the minimum continuous operation period without the need for shutdown is stated as three (3) years, rather than the required minimum of five (5) years. These deviations from the specified requirements raise concerns regarding the longevity and reliability of the equipment under operational conditions. Further documentation or justification is necessary to ensure compliance with the specified minimum design service life and continuous operation period requirements.\n    </DETAILED_REVIEW>\n    <REFERENCE>\n        Section 4.2 Page 16 - Design Life\n        Within the scope of our engineering and manufacturing excellence, the centrifugal pump has been engineered with a keen focus on durability and operational longevity, achieving a service life of 25 years. This specification underscores our commitment to providing high-quality, durable solutions that support the long-term objectives of our clients. Recognizing the challenges and demands of the operational environments these pumps are destined for, the design incorporates advanced materials and engineering techniques aimed at reducing wear and tear, thereby extending the operational life of the equipment. Additionally, the design facilitates a minimum continuous operation period of three (3) years, a feature meticulously developed to ensure that our clients benefit from reduced maintenance requirements and uninterrupted service. This operational characteristic is vital for ensuring that workflow efficiencies are maximized, and operational costs are minimized, reflecting our dedication to enhancing the value and reliability of our products within their intended application environments.\n    </REFERENCE>\n</EXAMPLE>\n<EXAMPLE>\n    <DETAILED_REVIEW>\n        2 Attachment 2 page 34 Environmental Data: The proposed material specifications do not align with the environmental data provided for Canada, especially concerning minimum temperature requirements. For instance, the specified environmental parameters for onshore engineering projects in Canada include a minimum recorded temperature of -35°C, while the proposed material specifications overlook this critical threshold, proposing a minimum temperature of 0°C. This discrepancy raises significant concerns regarding the materials' ability to withstand extreme conditions and maintain performance and integrity. It is imperative to revise the material specifications to align with the provided environmental data, ensuring suitability and durability for the intended application in Canada.\n    </DETAILED_REVIEW>\n    <REFERENCE>\n        Attachment 2 page 34 - Material Specifications for Canadian Climate Conditions\n        Environmental Compatibility and Performance\n        Our centrifugal pump design has been meticulously engineered to excel in various operational environments, with a specific focus on the challenging climate conditions found in Canada. Understanding the critical nature of environmental adaptability, our specifications are tailored to ensure unparalleled durability and performance, even in extreme weather conditions.\n    </REFERENCE>\n</EXAMPLE>\n<EXAMPLE>\n    <DETAILED_REVIEW>\n        3. Transport and lifting drawings: the submitted documentation lacks the required section covering Transport and Lifting Drawings, as stipulated in the code. Specifically, the final transportation and lifting drawings, load test certificates for lifting equipment, spreader bars, slings, davits, and shackles, along with weights and \"centre of gravity\" diagrams are absent. Additionally, the submission fails to include one copy each of the installation and operation manual and the preservation instruction book for each equipment/package, as mandated. This non-conformity poses a risk to the efficient and safe handling, installation, and operation of the equipment. Immediate action is necessary to rectify this deficiency and ensure compliance with the specified requirements.\n    </DETAILED_REVIEW>\n    <REFERENCE>\n        Minimum Temperature Compliance:\n        Specified Environmental Parameters: In anticipation of the demanding Canadian winters, our engineering approach has integrated material specifications designed to withstand significant temperature variations. Specifically, we have identified a minimum operational temperature threshold of 0°C, based on our comprehensive analysis of material resilience and performance metrics.\n        Rationale and Engineering Insights: This specification is underpinned by our commitment to ensuring operational integrity and reliability under a wide range of environmental conditions. Our selection of materials and engineering techniques are the results of rigorous testing and evaluation, aiming to balance cost-efficiency with the highest standards of environmental compatibility.\n        Section 6.2.9: Performance Test Specifications\n        Adherence to Industry Standards and Project Requirements\n        Our centrifugal pumps are designed to meet and exceed the operational demands of our clients, with performance testing being a cornerstone of our quality assurance process. We adhere strictly to industry standards, ensuring that our equipment delivers reliable and efficient service throughout its lifespan.\n    </REFERENCE>\n</EXAMPLE>"
                }
            ],
            "generate_synonym": [
                "\n<WORD>\n    {word}\n</WORD>\n\n- Provide a list of 3 synonyms for the given word or phrase.\n- Consider both single words and phrases that have similar meanings.\n- Include variations that might be used within the field of Engineering, Procurement, Construction, or that convey similar concepts.\n- Ensure that the synonyms cover a range of formal and informal language.\n- The synonyms should help improve the accuracy of search results by allowing for indirect or varied phrasings.\n- Aim for a broad range of terms to support a more flexible and robust search.\n- Strictly follow the below format\n\n<SYNONYMS>\n    Name:\n    Description:\n</SYNONYMS>\n\n<EXAMPLE>\n    <SYNONYMS>\n        Name: Technical Details\n        Description: Specific details related to the technical aspects of a project or product.\n\n        Name: Product Requirements\n        Description: The needs or criteria that a product must meet as specified for development or acquisition.\n    </SYNONYMS>\n</EXAMPLE>\n<EXAMPLE>\n    <SYNONYMS>\n        Name: Cost Estimate\n        Description: An approximation of the expenses required for a project or task.\n\n        Name: Quantity Survey\n        Description: The process of measuring and evaluating quantities of materials and labor needed for a project.\n    </SYNONYMS>\n</EXAMPLE>\n<EXAMPLE>\n    <SYNONYMS>\n        Name: Emergency Evacuation Procedure\n        Description: A detailed procedure for safely evacuating individuals in an emergency situation.\n\n        Name: Rescue Plan\n        Description: A strategy for rescuing individuals in distress or in need of assistance.\n    </SYNONYMS>\n</EXAMPLE>\n"
            ],
            "detect_special_criteria": [
                """
<CRITERIA_NAME>
    {criteria_name}
</CRITERIA_NAME>

<TASK>
    Analyze the given criteria name and classify it into exactly ONE of these categories:
    - Price: For criteria related to costs, pricing, financial aspects, budgets, or monetary considerations
    - Delivery: For criteria related to schedules, timelines, deadlines, timeframes, or project duration
    - Other: For any criteria that doesn't clearly fit into the above categories
</TASK>

<OUTPUT_FORMAT>
    Return ONLY one of these three words without any explanation or additional text:
    - Price
    - Delivery
    - Other

NOTE: VERY CRITICAL - DO NOT include any explanations, reasoning, or additional text. Return ONLY the value in the exact format shown in the examples.Do not add any explanations, reasoning, or additional text. Return ONLY the value in the exact format shown in the examples.only return the category

EXAMPLES :
  BAD RESPONSE:  'Based on the provided criteria name, I can determine that it falls under the "Price" category.'
  GOOD RESPONSE: Price

</OUTPUT_FORMAT>
                """
            ],
            "extract_special_criteria_value": [
                """
<CRITERIA_NAME>
    {criteria_name}
</CRITERIA_NAME>

<ANALYSIS>
    {analysis}
</ANALYSIS>

<TASK>
    First, categorize the criteria based on the name and analysis:
    1. Price: If related to costs, pricing, financial aspects, budgets, or monetary considerations
    2. Delivery: If related to schedules, timelines, deadlines, timeframes, or project duration
    3. Other: If it doesn't fit into the above categories
    
    Then, extract the specific value based on the category:
    - For Price criteria: Extract the total price value with currency in parentheses, e.g., 345000(USD)
    - For Delivery criteria: Convert all time periods to weeks and extract as a number with WEEKS in parentheses, e.g., 12(WEEKS)
      * Convert months to weeks (1 month = 4 weeks)
      * Convert years to weeks (1 year = 52 weeks)
    - For Other criteria: Return "N/A"
</TASK>

<OUTPUT_FORMAT>
    Return ONLY the extracted value in one of these formats without any explanation:
    - For Price: [number](USD) or [number](EUR) or [number](other currency)
    - For Delivery: [number](WEEKS)
    - For Other: N/A
    
    Examples:
    - 1000000(USD)
    - 12(WEEKS)
    - 500000(EUR)
    - N/A

NOTE: VERY CRITICAL - DO NOT include any explanations, reasoning, or additional text. Return ONLY the value in the exact format shown in the examples. Do not include commas in numbers. Do not include currency symbols. Do not include spaces.
The criteria name has been passed in the <CRITERIA_NAME>  tag
EXAMPLES : 
  BAD RESPONSE:  'Based on the provided text, I extracted the value for the "Price" criteria:\n\n22,710,906(USD)\n\nThis value meets the format requirement of [number](USD).
  GOOD RESPONSE: 22,710,906(USD)
</OUTPUT_FORMAT>
                """
            ],
           "generate_comparison_summary_on_comparison_table": [
    """
<TASK>
Generate a detailed and insightful comparison summary for a specific evaluation criterion across multiple bidders.
</TASK>

<CRITERIA_INFO>
    Criterion Name: {criteria_name}
    Is Sub-Criterion: {is_sub_criteria}
    Parent Criterion: {parent_criteria_name}
</CRITERIA_INFO>

<BIDDER_INFORMATION>
{bidder_info}
</BIDDER_INFORMATION>

<ADDITIONAL_SCORING_INFO>
{scores_explanation_formula}
</ADDITIONAL_SCORING_INFO>

NOTE: Additional scoring info shows how the score was calculated for special criteria that uses a formula to calculate the criteria. If the criteria does not have a formula, the value will be empty

<INSTRUCTIONS>
For each bidder:
1. Analyze their performance specifically for the given criterion
2. Highlight strengths and weaknesses based on their evaluation summary
3. Compare their performance to other bidders:
   - Identify where they rank overall (highest, middle, lowest)
   - Note significant score differences (e.g., "10% higher than nearest competitor")
   - Highlight unique advantages or disadvantages compared to others
4. Format each summary as a concise, professional paragraph (3-5 sentences)
5. Focus on factual comparison rather than subjective judgment
6. Include specific metrics from the data (scores, percentages) to support observations

Important guidelines:
- Be concise and precise - focus on meaningful distinctions
- Avoid generic language or repetitive phrasing across bidders
- Ensure a balanced treatment of all bidders
- The comparison should enhance understanding beyond individual summaries
</INSTRUCTIONS>

<OUTPUT_FORMAT>
  FOLLOW STRICTLY THE FORMAT BELOW
    Suppose the bidders are: bidder_1, bidder_2, bidder_3  
    Return the summaries in **valid JSON format** exactly like this:

    
        "bidder_1_summary": "<HTML summary of bidder_1 vs bidder_2 and bidder_3>",
        "bidder_2_summary": "<HTML summary of bidder_2 vs bidder_1 and bidder_3>",
        "bidder_3_summary": "<HTML summary of bidder_3 vs bidder_1 and bidder_2>"
    
     For example if the bidder name is John Mike then the key should be "John Mike_summary"

     IF Bid information as regards score/summary evluation is "N/A" then return the  same format but make the summary as "N/A" too ("John Mike_summary": "N/A")

    Follow this format strictly. Use the bidder names as keys ending in `_summary`.
    FOLLOW THE JSON OUTPUT FORMAT DO NOT PUT ```json` before or after the json output. and no explanation before or after the json output.
    </OUTPUT_FORMAT>
NOTE: When comparing bidders:
- Avoid explicitly stating scores (e.g. "Bidder A scores 80%")
- Focus on concrete metrics and specifications and ensure to mention them:
  * Prices and cost breakdowns
  * Delivery timelines and schedules
  * Technical specifications and requirements
  * Performance metrics and measurements
  * Quantifiable differences between bidders
  * Specific values with units where applicable
    """
]
,
            "criteria_query_expansion":[
                 """# Query Variation Generator for Document Search

## Task Description
You are a specialized system that generates search query variations based on criteria names and descriptions for a document retrieval system. Your goal is to create a diverse set of semantically related queries that will help find all relevant information in a vector database about a specific search criterion.

## Instructions
1. Analyze the provided criteria name and description carefully to understand the core information need.
2. Generate 2-3 query variations that capture different ways the information might be expressed in documents.
3. Include:
   - The original criteria name and description (separately and combined)
   - Variations using synonyms for key terms
   - Domain-specific terminology related to the criteria
   - Alternative phrasings that express the same information need
   - Specific aspects or components mentioned in the description
   - Contextual queries that incorporate related concepts
   - Questions that would be answered by relevant content
   

## Input Format


  "criteria": "The name or title of the criteria",
  "description": "The detailed description of what information needs to be found"


## Output Format
Return a JSON array of strings, each representing a query variation:

  "query_variations": [
    "variation 1",
    "variation 2",
    "variation 3",
    ...
  ]


## Examples

### Example 1: Price Criteria

**Input:**

  "criteria": "Price",
  "description": "Bidder must provide all pricing information"


**Output:**
  "query_variations": [
    "Price",
    "Bidder must provide all pricing information",
    "Price Bidder must provide all pricing information",
    "pricing information",
    "price details",
    "cost information",
  ]


Now, please generate query variations for this input:
{criteria_input}

NOTE: FOLLOW THE JSON OUTPUT FORTMAT, DO NOT PUT ANYTHING BEFORE OR AFTER AND DO NOT ADD ```json before or after, no explanation
"""
            ],
            "_generate_synonym": [
                "\n<WORD>\n    {word}\n</WORD>\n\n- Provide a list of 3 synonyms for the given word or phrase.\n- Consider both single words and phrases that have similar meanings.\n- Include variations that might be used in different contexts or that convey similar concepts.\n- Ensure that the synonyms cover a range of formal and informal language.\n- The synonyms should help improve the accuracy of search results by allowing for indirect or varied phrasings.\n- Aim for a broad range of terms to support a more flexible and robust search.\n- Strictly follow the below format\n\n<SYNONYMS>\n    Name:\n    Description:\n</SYNONYMS>\n\n<EXAMPLE>\n    <SYNONYMS>\n        Name: Technical Details\n        Description: Specific details related to the technical aspects of a project or product.\n\n        Name: Product Requirements\n        Description: The needs or criteria that a product must meet as specified for development or acquisition.\n    </SYNONYMS>\n</EXAMPLE>\n<EXAMPLE>\n    <SYNONYMS>\n        Name: Cost Estimate\n        Description: An approximation of the expenses required for a project or task.\n\n        Name: Quantity Survey\n        Description: The process of measuring and evaluating quantities of materials and labor needed for a project.\n    </SYNONYMS>\n</EXAMPLE>\n<EXAMPLE>\n    <SYNONYMS>\n        Name: Emergency Evacuation Procedure\n        Description: A detailed procedure for safely evacuating individuals in an emergency situation.\n\n        Name: Rescue Plan\n        Description: A strategy for rescuing individuals in distress or in need of assistance.\n    </SYNONYMS>\n</EXAMPLE>\n"
            ]
,
            "detect_section_heading": [
                 """
                    <ROLE>
                    You are an AI assistant. Your task is to find the heading (title) for a specific piece of text called <TARGET_TEXT_SECTION>. You will also be given up to five pieces of text that came before it, called <PRECEDING_SECTION_1> through <PRECEDING_SECTION_5>. Some preceding sections might be empty or marked as not provided; ignore those.
                    </ROLE>

                    <TEXT_TO_ANALYZE>
                        <PRECEDING_SECTION_1>
                        {preceding_section_1_content}
                        </PRECEDING_SECTION_1>

                        <PRECEDING_SECTION_2>
                        {preceding_section_2_content}
                        </PRECEDING_SECTION_2>

                        <PRECEDING_SECTION_3>
                        {preceding_section_3_content}
                        </PRECEDING_SECTION_3>

                        <TARGET_TEXT_SECTION>
                        {target_section_content}
                        </TARGET_TEXT_SECTION>
                    </TEXT_TO_ANALYZE>

                    <INSTRUCTIONS>
                    1.  **Your Goal:** Identify the heading for the <TARGET_TEXT_SECTION>.
                    2.  **What is a Heading?** A heading is typically a short line of text that acts as a title for the content that follows it. It might be capitalized, numbered (e.g., "2.1 Findings"), or use keywords like "Section", "Chapter". It's usually distinct from paragraph text.

                    3.  **How to Find the Heading (Follow these steps in order):**
                        a.  **Check <TARGET_TEXT_SECTION> First:**
                            *   Look at the very first line (or first few lines) of <TARGET_TEXT_SECTION>.
                            *   Does it look like a heading for the rest of the <TARGET_TEXT_SECTION> content?
                            *   If YES, this is your heading. Stop here and go to Step 5 (Output).

                        b.  **Check End of <PRECEDING_SECTION_3> (if it has content and no heading found yet):**
                            *   Look at the last line (or last few lines) of <PRECEDING_SECTION_3>.
                            *   Does it look like a heading that introduces <TARGET_TEXT_SECTION>?
                            *   If YES, this is your heading. Stop here and go to Step 5 (Output).

                        c.  **Check End of <PRECEDING_SECTION_2> (if it has content and no heading found yet):**
                            *   Look at the last line (or last few lines) of <PRECEDING_SECTION_2>.
                            *   Does it look like a heading that introduces <TARGET_TEXT_SECTION>?
                            *   If YES, this is your heading. Stop here and go to Step 5 (Output).

                        d.  **Check End of <PRECEDING_SECTION_1> (if it has content and no heading found yet):**
                            *   Look at the last line (or last few lines) of <PRECEDING_SECTION_1>.
                            *   Does it look like a heading that introduces <TARGET_TEXT_SECTION>?
                            *   If YES, this is your heading. Stop here and go to Step 5 (Output).

                        e.  **Check for a Broader, Recent Heading (if no specific heading found yet):**
                            *   Scan through all provided and non-empty <PRECEDING_SECTION_1> to <PRECEDING_SECTION_5>, from earliest to latest.
                            *   What was the most recent line you saw that looked like a general section heading (e.g., "Chapter 3", "Section B: Analysis") before you reached the <TARGET_TEXT_SECTION>?
                            *   If <TARGET_TEXT_SECTION> seems to logically belong under such a heading, and you haven't found a more direct heading in steps 3a-3d, then this general heading is your answer.
                            *   If multiple such general headings exist, pick the one that appeared most recently before <TARGET_TEXT_SECTION>.

                    4.  **If No Heading Found:** If, after checking all the above, you cannot identify a clear heading for <TARGET_TEXT_SECTION>, then the answer is "NO_HEADING_IDENTIFIED".

                    5.  **Output Format:**
                        *   If you identified a heading, output ONLY the heading text.
                            *   Example: If the heading is "Detailed Analysis", your output must be: Detailed Analysis
                        *   If you did not identify a heading, your output must be EXACTLY: NO_HEADING_IDENTIFIED
                        *   **CRITICAL:** Do not include any other words, explanations, or sentences in your response.

                    </INSTRUCTIONS>

                    Question: Based on the <TEXT_TO_ANALYZE> and the <INSTRUCTIONS>, what is the heading for the <TARGET_TEXT_SECTION>?
                    """
            ],
             "auto_criteria_detection": [
                 """
                    <ROLE>
                    You are an AI assistant. Your task is to find the heading (title) for a specific piece of text called <TARGET_TEXT_SECTION>. You will also be given up to five pieces of text that came before it, called <PRECEDING_SECTION_1> through <PRECEDING_SECTION_5>. Some preceding sections might be empty or marked as not provided; ignore those.
                    </ROLE>

                    <TEXT_TO_ANALYZE>
                        <PRECEDING_SECTION_1>
                        {preceding_section_1_content}
                        </PRECEDING_SECTION_1>

                        <PRECEDING_SECTION_2>
                        {preceding_section_2_content}
                        </PRECEDING_SECTION_2>

                        <PRECEDING_SECTION_3>
                        {preceding_section_3_content}
                        </PRECEDING_SECTION_3>

                        <TARGET_TEXT_SECTION>
                        {target_section_content}
                        </TARGET_TEXT_SECTION>
                    </TEXT_TO_ANALYZE>

                    <INSTRUCTIONS>
                    1.  **Your Goal:** Identify the heading for the <TARGET_TEXT_SECTION>.
                    2.  **What is a Heading?** A heading is typically a short line of text that acts as a title for the content that follows it. It might be capitalized, numbered (e.g., "2.1 Findings"), or use keywords like "Section", "Chapter". It's usually distinct from paragraph text.

                    3.  **How to Find the Heading (Follow these steps in order):**
                        a.  **Check <TARGET_TEXT_SECTION> First:**
                            *   Look at the very first line (or first few lines) of <TARGET_TEXT_SECTION>.
                            *   Does it look like a heading for the rest of the <TARGET_TEXT_SECTION> content?
                            *   If YES, this is your heading. Stop here and go to Step 5 (Output).

                        b.  **Check End of <PRECEDING_SECTION_3> (if it has content and no heading found yet):**
                            *   Look at the last line (or last few lines) of <PRECEDING_SECTION_3>.
                            *   Does it look like a heading that introduces <TARGET_TEXT_SECTION>?
                            *   If YES, this is your heading. Stop here and go to Step 5 (Output).

                        c.  **Check End of <PRECEDING_SECTION_2> (if it has content and no heading found yet):**
                            *   Look at the last line (or last few lines) of <PRECEDING_SECTION_2>.
                            *   Does it look like a heading that introduces <TARGET_TEXT_SECTION>?
                            *   If YES, this is your heading. Stop here and go to Step 5 (Output).

                        d.  **Check End of <PRECEDING_SECTION_1> (if it has content and no heading found yet):**
                            *   Look at the last line (or last few lines) of <PRECEDING_SECTION_1>.
                            *   Does it look like a heading that introduces <TARGET_TEXT_SECTION>?
                            *   If YES, this is your heading. Stop here and go to Step 5 (Output).

                        e.  **Check for a Broader, Recent Heading (if no specific heading found yet):**
                            *   Scan through all provided and non-empty <PRECEDING_SECTION_1> to <PRECEDING_SECTION_5>, from earliest to latest.
                            *   What was the most recent line you saw that looked like a general section heading (e.g., "Chapter 3", "Section B: Analysis") before you reached the <TARGET_TEXT_SECTION>?
                            *   If <TARGET_TEXT_SECTION> seems to logically belong under such a heading, and you haven't found a more direct heading in steps 3a-3d, then this general heading is your answer.
                            *   If multiple such general headings exist, pick the one that appeared most recently before <TARGET_TEXT_SECTION>.

                    4.  **If No Heading Found:** If, after checking all the above, you cannot identify a clear heading for <TARGET_TEXT_SECTION>, then the answer is "NO_HEADING_IDENTIFIED".

                    5.  **Output Format:**
                        *   If you identified a heading, output ONLY the heading text.
                            *   Example: If the heading is "Detailed Analysis", your output must be: Detailed Analysis
                        *   If you did not identify a heading, your output must be EXACTLY: NO_HEADING_IDENTIFIED
                        *   **CRITICAL:** Do not include any other words, explanations, or sentences in your response.

                    </INSTRUCTIONS>

                    Question: Based on the <TEXT_TO_ANALYZE> and the <INSTRUCTIONS>, what is the heading for the <TARGET_TEXT_SECTION>?
                    """
            ],
"auto_criteria_detection": [
                 """
                    <ROLE>
                    You are an AI assistant acting as an experienced EPC (Engineering, Procurement, and Construction) Engineer with over 20 years of experience in the oil and gas industry. Your task is to extract and categorize major equipment information from the uploaded project documentation.
                    </ROLE>

                    <TEXT_TO_ANALYZE>
                        <DOCUMENT>
                        {document_content}
                        </DOCUMENT>
                    </TEXT_TO_ANALYZE>

                    <INSTRUCTIONS>
                    1.  **Your Goal:** Identify all major equipment mentioned in the uploaded document.
                    
                    2.  **What is Major Equipment?** Major equipment in oil and gas projects typically includes:
                        - Process vessels (separators, columns, reactors, drums)
                        - Rotating equipment (compressors, pumps, turbines)
                        - Heat transfer equipment (heat exchangers, coolers, heaters)
                        - Storage tanks and pressure vessels
                        - Major piping systems and manifolds
                        - Control systems and instrumentation packages
                        - Power generation equipment
                        - Safety and environmental systems
                    
                    3.  **How to Extract Equipment Information:**
                        a.  **Scan the Document:**
                            * Carefully review all content in the uploaded document.
                            * Look for equipment names, specifications, quantities, and their components.
                            
                        b.  **Identify Equipment Hierarchy:**
                            * Determine main equipment units and their sub-components.
                            * Note relationships between different equipment pieces when indicated.
                            
                        c.  **Collect Technical Specifications:**
                            * Gather capacity, size, material, pressure, temperature ratings when available.
                            * Note any unique design features or requirements.
                            
                        d.  **Extract Installation/Location Information:**
                            * Note any mentioned locations or installation requirements.
                            
                        e.  **Identify Equipment Tags/IDs:**
                            * Record any equipment identification numbers or tagging systems used.

                    4.  **Output Format:**
                        * Structure the output as a JSON array of equipment names.
                        
                        * Sample format:
                        
                      {output_format}
                        

                    5.  **Important Notes:**
                        * Focus ONLY on major equipment items, not consumables or minor components.
                        * If no major equipment is identified, return an empty array: []
                        * Include ONLY equipment clearly mentioned in the provided text.
                        * Do not include general equipment categories without specific references.
                        * If specific details aren't provided, leave those fields as empty objects or omit them.

                    </INSTRUCTIONS>

                    Question: Based on the <TEXT_TO_ANALYZE> and the <INSTRUCTIONS>, extract all major equipment information from the uploaded document and format it as specified.
                    NOTE: Return only json format please, nothing before or after do not put ``` before or after
                    """
            ],
            "summarize_tender_requiremments": [
                """
# Token-Efficient Tender Requirements Extraction

## Input Structure
```
<CRITERIA>
{criteria_name}
</CRITERIA>

<DOCUMENTS>
{document_content}
</DOCUMENTS>
```

## Mission: Maximum Information Density
Extract ALL requirements related to criteria with ZERO information loss while achieving maximum token compression for downstream processing.

## Critical Extraction Rules

### Information Preservation (NEVER OMIT)
- **ALL** mandatory requirements, specifications, thresholds, deadlines
- **ALL** quantitative values (numbers, percentages, timeframes, quantities)
- **ALL** qualitative standards (certifications, approval levels, quality grades)
- **ALL** conditional requirements ("if X then Y", exceptions, alternatives)
- **ALL** penalties, consequences, or enforcement mechanisms
- **ALL** specific processes, procedures, or methodologies mentioned

### Mandatory Source Attribution
- Include document name + section reference for EVERY requirement
- Format: `(filename.pdf, Section X.Y)` or `(filename.pdf, p.15)` or `(filename.pdf, Clause 3.2.1)`
- When requirement spans multiple documents: `(doc1.pdf, doc2.pdf)`
- When specific text/section referenced: `(filename.pdf, "Exact Section Title")`

### Token Optimization Techniques
- Remove filler words: "it is required that" → direct statement
- Use standard abbreviations: minimum→min, maximum→max, required→req'd
- Consolidate similar requirements with semicolons
- Use ranges: "between 5 and 10 years" → "5-10 years"
- Compress timeframes: "within thirty (30) days" → "within 30 days"

## Output Format

### Multiple Requirements:
```
• Min 5 years experience in telecommunications infrastructure (tech_specs.pdf, Section 2.1); relevant certifications req'd (qualifications.pdf, Clause 4.2)
• Project manager: full-time availability, PMP certification, min 3 years similar projects (staffing_reqs.pdf, p.8-9)
• Progress reports: monthly by 5th, include KPIs and risk assessment (reporting.pdf, "Monthly Deliverables")
• Equipment: ISO 9001 certified, warranty min 2 years, local service support within 24hrs (equipment_specs.pdf, Section 3.4, 3.7)
• Performance bond: 10% contract value, valid until completion + 12 months (financial_reqs.pdf, Clause 5.1)
```

### Single Requirement:
```
Minimum 5 years telecommunications experience required; project manager must hold PMP certification and be available full-time (tech_specs.pdf, Section 2.1; staffing.pdf, p.12)
```

### No Requirements:
```
No specific requirements found for this criteria
```

## Quality Assurance Checklist
Before finalizing output, verify:
- [ ] Every number, date, percentage preserved exactly
- [ ] Every "must", "shall", "required" obligation captured
- [ ] Every penalty/consequence included
- [ ] Every document reference includes filename + location
- [ ] No redundant phrasing or filler words
- [ ] All conditional logic ("if/then", "except when") preserved

## Critical Success Factors
1. **Zero Information Loss**: If downstream agent needs it, it must be included
2. **Maximum Compression**: Every word must add value
3. **Perfect Attribution**: Every requirement traceable to source
4. **Actionable Format**: Clear what must be done/provided/met

NOTE: IF requirement references same documents, plese do not repeate the doducment name just mention it once and say those require are from that document
CRITICAL : Make sure you do not omit any informatiom related to the criteria requiremnt
                """ 
            ],

            "cbp_report_overall_recommendation":[
                """

# CBP Report Overall Recommendation Prompt

## Mission
Act as an EPC evaluation engineer to analyze tender evaluation data and provide overall contract award recommendations in raw HTML format based on technical and commercial criteria.
You will be provided with a JSON object containing tender evaluation data. Your task is to analyze this data and provide an overall recommendation for contract award.

<CRITERIA_ANALYSIS>
{criteria_analysis}
</CRITERIA_ANALYSIS>

## Input Analysis
 
- Analyze performance in both technical and commercial dimensions
- Identify strengths and weaknesses for each tenderer

## Output Format Requirements
**CRITICAL: Return ONLY raw HTML - no markdown, no code blocks, no explanations**

## OUTPUT FORMAT TEMPLATE

    <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; background: white; border: 1px solid #ddd;">
        <h2 style="color: #2c5aa0; text-align: left; border-bottom: 2px solid #2c5aa0; padding-bottom: 10px;">
            RECOMMENDATION
        </h2>
        
        <div style="background: #f8f9fa; padding: 20px; margin: 20px 0; border-left: 4px solid #2c5aa0;">
            <p style="font-weight: bold; margin-bottom: 15px;">
                It is recommended that this contract be awarded to one of the following Tenderers, subject to further clarifications, negotiations, and the most favourable outcome of those negotiations which AMM is able to secure for the project.
            </p>
            
            <p style="margin-bottom: 15px;">
                Overall preference (considering all technical and commercial considerations) is sequential as indicated below. Further, Tenderers who are not recommended are not indicated below:
            </p>
            
            <ol style="font-weight: bold; font-size: 16px; line-height: 1.8;">
                <li>[Highest Scoring Tenderer]</li>
                <li>[Second Highest Tenderer]</li>
            </ol>
            
            <div style="margin-top: 25px;">
                <p><strong>[Highest Scoring Tenderer]:</strong> [Summary of technical and commercial performance, key strengths, and areas for improvement]</p>
                
                <p><strong>[Second Highest Tenderer]:</strong> [Summary of technical and commercial performance, key strengths, and areas for improvement]</p>
                
                <!-- Add more tenderer summaries as needed for all recommended tenderers -->
            </div>
        </div>
        
        <div style="margin-top: 30px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7;">
            <h4 style="color: #856404; margin-top: 0;">RECOMMENDATION NOTES</h4>
            <p style="margin-bottom: 0; color: #856404;">
                Final contract award is contingent upon successful completion of clarifications and negotiations with the recommended tenderer(s).
            </p>
        </div>
    </div>


## Calculation Method

1. Sum all criteria scores for each tenderer
2. Rank by total percentage (highest first)
3. Only recommend tenderers with competitive scores
4. Exclude tenderers with significant compliance issues

## Key Requirements
- Raw HTML output only
- Professional EPC industry language
- Balanced evaluation of strengths/weaknesses
- Standard AMM contract award recommendation format
- You do not need tomention their scores, just recommend based on your analysis

                   """
            ],

                        "cbp_report_technical_recommendation":[
                """

# CBP Report Technical Recommendation Prompt

## Mission
Act as an EPC evaluation engineer to analyze tender evaluation data and provide technical recommendations in raw HTML format based on technical criteria evaluation.

You will be provided with a JSON object containing tender evaluation data. Your task is to analyze this data and provide technical recommendations for contract award.

The criteria analysis contains technical criteria analysis alongside equipment analysis under this technical criteria.

<CRITERIA_ANALYSIS>
{criteria_analysis}
</CRITERIA_ANALYSIS>

## Input Analysis
- Analyze performance based on technical criteria
- Identify technically acceptable proposals
- Provide reasoning for technical recommendations
- Assess compliance with technical requirements

## Output Format Requirements
**CRITICAL: Return ONLY raw HTML - no markdown, no code blocks, no explanations**

## OUTPUT FORMAT TEMPLATE


<div style="font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; background: white; border: 1px solid #ddd;">
    <h2 style="color: #2c5aa0; text-align: left; border-bottom: 2px solid #2c5aa0; padding-bottom: 10px;">
        TECHNICAL RECOMMENDATION
    </h2>
    
    <div style="background: #f8f9fa; padding: 20px; margin: 20px 0; border-left: 4px solid #2c5aa0;">
        <p style="font-weight: bold; margin-bottom: 15px; font-size: 16px;">
            Based on the technical evaluation of the proposals, the following recommendations are made regarding technically acceptable tenderers:
        </p>
        
        <div style="margin-top: 25px;">
            <p><strong>[Bidder 1]:</strong> [Detailed technical assessment including strengths, compliance status, and key technical advantages]</p>
            
            <p><strong>[Bidder 2]:</strong> [Detailed technical assessment including strengths, compliance status, and key technical advantages]</p>
            
            <p><strong>[Bidder 3]:</strong> [Detailed technical assessment including strengths, compliance status, and key technical advantages]</p>
        </div>
    </div>
    
    <div style="margin-top: 30px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7;">
        <h4 style="color: #856404; margin-top: 0;">TECHNICAL EVALUATION NOTES</h4>
        <p style="margin-bottom: 0; color: #856404;">
            This technical recommendation is based on evaluation against specified technical criteria. Final recommendations may be subject to technical clarifications and further evaluation.
        </p>
    </div>
</div>

## Evaluation Method

1. Analyze technical criteria compliance for each bidder
2. Assess technical capabilities and proposed solutions
3. Evaluate equipment specifications and technical approach
4. Identify technically acceptable proposals
5. Provide detailed reasoning for technical recommendations

## Key Requirements
- Raw HTML output only
- Professional EPC industry language
- Focus on technical acceptability and compliance
- Clear reasoning for each technically recommended bidder
- Standard technical evaluation format
"""
            ],

    

"cbp_report_discipline_analysis":[
                """

# CBP Report Technical Recommendation Prompt

# EPC Tender Evaluation System - EQUIPMENT - Discipline-Specific Analysis

        ## ROLE DEFINITION
        You are a professional tender evaluator specializing in EPC (Engineering, Procurement, Construction) projects. Your task is to assess bidder submissions against specific evaluation criteria by comparing them to master reference documents with particular focus on the specified engineering discipline in relation to the EQUIPMENT namely : {equipment}. You must provide structured, technically sound evaluations that reflect both factual accuracy and professional judgment within the discipline context.

        ---

        ## EVALUATION METHODOLOGY

        ### Phase 1: Document Analysis & Understanding

        #### Step 1A: Comprehensive Document Review
        - **Objective**: Thoroughly analyze both the UPLOADED_DOCUMENT (bidder submission) and SOURCE_DOCUMENT (tender requirements) with specific focus on the {discipline} discipline
        - **Process**:
        - Read and understand the complete context of both documents
        - Identify sections containing information relevant to the evaluation criterion and the {discipline} discipline 
        - Search for keywords, phrases, and technical details that align with both the question and the discipline requirements
        - Focus on discipline-specific standards, codes, equipment, and technical specifications
        - Ensure comprehensive coverage to avoid missing critical discipline-related information

        #### Step 1B: Summary Analysis
        - **Objective**: Compare the bidder's submission against the master reference document with emphasis on {discipline} discipline requirements
        - **Process**:
            - Use the SOURCE_DOCUMENT as the authoritative benchmark for {discipline} requirements
            - Assess alignment, compliance, and completeness of the UPLOADED_DOCUMENT specifically for {discipline} aspects
            - Identify gaps, discrepancies, or exceeding requirements in the {discipline} domain
            - When evaluating, state the exact {discipline} requirements from the tender document
            - Clearly identify which {discipline} requirements the bidder has met
            - Explicitly state which {discipline} requirements are missing or incomplete
            - Provide specific examples from the bidder's response related to {discipline}
            - Compare against the tender requirements to show gaps in {discipline} compliance
            - Include any relevant values, specifications, or timelines mentioned for {discipline}
            - Highlight any deviations from the required {discipline} standards
             e.g The bidder's response demonstrates partial compliance with the {discipline} delivery requirements outlined in the tender document[STATE THIS REQUIREMENT, DO NOT JUST SAY BIDDER MISS IT]




        #### Scoring Guidelines
        - Evaluate each relevant section against the specific criterion with {discipline} focus
        - Exercise caution before assigning 0% scores—look for any supporting {discipline} information
        - Apply scoring based on strict alignment with the evaluation question and {discipline} requirements
        - Consider context, relevance, and completeness in your {discipline} assessment

        ---

        ## EVALUATION STRUCTURE

        ### Required Response Format
        Your evaluation must follow this exact structure:

        ```
        <Evaluation>(Required)</Evaluation>
        <Score>(Required, integer)</Score>
        <Reason>(Required, detailed & comprehensive with {discipline} focus)</Reason>
        <Reference>(Required, extracted as-is from the SOURCE_DOCUMENT)</Reference>
        ```

        ### Content Requirements for Each Section

        #### 1. Evaluation Section
        **Clear Judgment Statement (1 sentence)**
        - Begin with a direct assessment of compliance with the evaluation criterion for {discipline}
        - Use precise, professional language:
        - "Demonstrates strong compliance with {discipline} requirements..."
        - "Shows partial alignment regarding {discipline} aspects..."
        - "Fails to meet key {discipline} requirements for..."

        #### 2. Score Section
        - Provide integer score (0-100) based on the compliance scale above for {discipline} evaluation

        #### 3. Reason Section
        **Structure your reasoning as follows:**

        **Key Supporting Details**
        - Extract specific, relevant information from the bidder's submission related to {discipline}
        - Include {discipline}-relevant terminology and quantitative data:
        - {discipline} timelines and milestones
        - {discipline} technical specifications and standards
        - {discipline} equipment locations and quantities
        - {discipline} standards and certifications
        - {discipline} commercial terms and requirements
        - Use bullet points for structured {discipline} information presentation

        **Caveats/Risks/Missing Items (when applicable)**
        - Identify {discipline} disclaimers, limitations, or uncertainties
        - Note {discipline} gaps that could affect performance or compliance
        - Maintain neutral, factual tone—avoid speculation about {discipline} aspects

        **Summary Conclusion**
        - Tie the assessment back to the criterion with {discipline} focus
        - State overall alignment with {discipline} project/contractual expectations

        #### 4. Reference Section
        - Extract exact text from the SOURCE_DOCUMENT related to {discipline} (no paraphrasing)
        - Provide the specific {discipline} requirement or standard being evaluated against

        ---

        ## QUALITY STANDARDS

        ### Tone & Style Requirements
        - **Professional**: Use {discipline}-appropriate language and terminology
        - **Concise**: Avoid unnecessary verbosity while maintaining {discipline} completeness
        - **Analytical**: Focus on factual {discipline} assessment rather than subjective opinions
        - **Specific**: Quantify and qualify {discipline} aspects rather than using vague terms like "good" or "adequate"

        ### Critical Requirements
        - **Exact Content Reproduction**: Return evaluated text exactly as provided—no summarization or paraphrasing
        - **{discipline} Engineering Perspective**: Analyze complex evaluation questions with {discipline} technical expertise
        - **Perfect Understanding**: Ensure complete comprehension of both documents before {discipline} evaluation
        - **Comprehensive Coverage**: Extract every supporting {discipline} statement, regardless of size
        

        ---

        ## INPUT TEMPLATE

        ### Documents to Analyze
        - **SOURCE_DOCUMENT**: `{source_text}`
        - **UPLOADED_DOCUMENT**: `{text}`
        - **DISCIPLINE**: `{discipline}`

      

(REMINDER.. VERY IMPORTANT):
     - NOTE: In your evaluation, always start by stating the tender requirements first from source documents with specific focus on {discipline} requirements..
     - Focus your analysis on {discipline} aspects of the euqipment : {equipment}: Mechanical, Electrical, Instrumentation and Control, Structural, Civil, Piping, MechProcess, etc.
     - Ensure all technical assessments are relevant to the specified {discipline} in relation to the equipment : {equipment}

"""
            ],
                                    "cbp_report_technical_recommendation":[
                """

# CBP Report Technical Recommendation Prompt

## Mission
Act as an EPC evaluation engineer to analyze tender evaluation data and provide technical recommendations in raw HTML format based on technical criteria evaluation.

You will be provided with a JSON object containing tender evaluation data. Your task is to analyze this data and provide technical recommendations for contract award.

The criteria analysis contains technical criteria analysis alongside equipment analysis under this technical criteria.

<CRITERIA_ANALYSIS>
{criteria_analysis}
</CRITERIA_ANALYSIS>

## Input Analysis
- Analyze performance based on technical criteria
- Identify technically acceptable proposals
- Provide reasoning for technical recommendations
- Assess compliance with technical requirements

## Output Format Requirements
**CRITICAL: Return ONLY raw HTML - no markdown, no code blocks, no explanations**

## OUTPUT FORMAT TEMPLATE


<div style="font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; background: white; border: 1px solid #ddd;">
    <h2 style="color: #2c5aa0; text-align: left; border-bottom: 2px solid #2c5aa0; padding-bottom: 10px;">
        TECHNICAL RECOMMENDATION
    </h2>
    
    <div style="background: #f8f9fa; padding: 20px; margin: 20px 0; border-left: 4px solid #2c5aa0;">
        <p style="font-weight: bold; margin-bottom: 15px; font-size: 16px;">
            Based on the technical evaluation of the proposals, the following recommendations are made regarding technically acceptable tenderers:
        </p>
        
        <div style="margin-top: 25px;">
            <p><strong>[Bidder 1]:</strong> [Detailed technical assessment including strengths, compliance status, and key technical advantages]</p>
            
            <p><strong>[Bidder 2]:</strong> [Detailed technical assessment including strengths, compliance status, and key technical advantages]</p>
            
            <p><strong>[Bidder 3]:</strong> [Detailed technical assessment including strengths, compliance status, and key technical advantages]</p>
        </div>
    </div>
    
    <div style="margin-top: 30px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7;">
        <h4 style="color: #856404; margin-top: 0;">TECHNICAL EVALUATION NOTES</h4>
        <p style="margin-bottom: 0; color: #856404;">
            This technical recommendation is based on evaluation against specified technical criteria. Final recommendations may be subject to technical clarifications and further evaluation.
        </p>
    </div>
</div>

## Evaluation Method

1. Analyze technical criteria compliance for each bidder
2. Assess technical capabilities and proposed solutions
3. Evaluate equipment specifications and technical approach
4. Identify technically acceptable proposals
5. Provide detailed reasoning for technical recommendations

## Key Requirements
- Raw HTML output only
- Professional EPC industry language
- Focus on technical acceptability and compliance
- Clear reasoning for each technically recommended bidder
- Standard technical evaluation format
"""
            ],

    
"cbp_criteria_formuala_scoring":[
                """
# AI Bidder Evaluation Assistant

You are an AI evaluation assistant specialized in scoring bidders based on specific criteria and formulas. Your task is to perform accurate calculations and provide transparent, justified scoring.

## Input Structure

You will receive three key components:

1. **Criteria Name**: {criteria_name}
2. **Scoring Formula**: {formula}
3. **Bidder Evaluations**: 
   ```
   <EVALUATION_BIDDERS>
   {bidders_evaluation}
   </EVALUATION_BIDDERS>
   ```

## Evaluation Process

### Step 1: Formula Analysis
- Parse the provided formula completely
- Identify all required variables and their relationships
- Note any conditional logic or special cases
- Understand the expected output range and units

### Step 2: Data Extraction and Processing
**Critical**: Perform comprehensive data analysis before calculations:

- **Aggregation Requirements**: Sum up multiple line items when formulas require totals (e.g., total project cost, total duration)
- **Unit Conversions**: Convert time periods (days to weeks, months to years) as needed
- **Derived Calculations**: Calculate intermediate values (e.g., weekly rates from monthly costs, completion percentages)
- **Missing Data Identification**: Flag incomplete information early
- **Data Validation**: Cross-check values for consistency and reasonableness

### Step 3: Score Calculation
- Apply the formula step-by-step for each bidder
- Show intermediate calculations for transparency
- Handle edge cases (division by zero, negative values, etc.)
- Round final scores to 2 decimal places

### Step 4: Quality Assurance
- Verify calculations against source data
- Ensure all bidders are evaluated consistently
- Check for computational errors or logical inconsistencies

## Output Requirements

Return results in this exact JSON structure (no code fencing):


{{
  "explanation": "Clear explanation of the scoring methodology and any important notes about the calculations",
  "scores": [
    {{"bidder_name": "EXACT_BIDDER_NAME_AS_PROVIDED", "score": 85.50}},
    {{"bidder_name": "EXACT_BIDDER_NAME_AS_PROVIDED", "score": 92.75}},
    {{"bidder_name": "EXACT_BIDDER_NAME_AS_PROVIDED", "score": "N/A"}}
  ]
}}


## Critical Rules

### Data Handling
- **Preserve Original Names**: Use bidder names exactly as provided - no corrections, standardization, or modifications
- **Comprehensive Data Mining**: Extract ALL relevant information from evaluation texts
- **Smart Aggregation**: When formulas require totals, sum all applicable components:
  - Total costs = sum of all cost line items
  - Total duration = sum of all time periods or phases
  - Total resources = sum of personnel, equipment, materials, etc.

### Calculation Standards
- **Formula Adherence**: Follow the provided formula precisely
- **Transparent Process**: Show key calculation steps in the explanation
- **Error Handling**: Use "N/A" for scores when essential data is unavailable
- **Precision**: Round to 2 decimal places for final scores only

### Common Calculation Scenarios
Be prepared to handle:
- **Multi-component pricing**: Sum individual service costs, materials, labor, etc.
- **Time calculations**: Convert and aggregate different time units (days, weeks, months)
- **Rate calculations**: Derive hourly/daily/weekly rates from total costs and durations
- **Percentage calculations**: Calculate completion rates, efficiency metrics, etc.
- **Weighted scoring**: Apply different weights to various criteria components

## Quality Indicators

Your evaluation is successful when:
- All mathematical operations are clearly justified
- Data extraction captures complete information from source texts
- Scores reflect accurate application of the provided formula
- Missing data is appropriately handled and documented
- Results are consistent and comparable across all bidders

## Example Output
{{
  "explanation": "Scoring based on cost-effectiveness ratio: (Total Deliverables / Total Project Cost) × 100. Total costs calculated by summing all line items including labor ($50,000), materials ($30,000), and overhead ($20,000). Deliverables counted as discrete milestones completed.",
  "scores": [
    {{"bidder_name": "Tech Solutions Inc.", "score": 85.50}},
    {{"bidder_name": "Digital Innovations LLC", "score": 92.75}},
    {{"bidder_name": "Systems Pro Corp", "score": "N/A"}}
  ]
}}

Important Note(FOLLOW THIS): When uncertain about specific data points, use available information intelligently to make reasonable estimates or approximations. Always document your assumptions, estimation methods, and data sources clearly in the explanation. Only use "N/A" when absolutely no relevant data exists to perform any calculation.
Remember: Accuracy and transparency in calculations are paramount. When in doubt, show your work and explain your reasoning.
"""
            ]
        }


# Example usage:
# template_name = "cbp_get_strength_section"
# print(EvaluationTemplates.get_template(template_name))

        

        
        return templates.get(template_name, "Invalid template name")

# Example usage:
# template_name = "cbp_get_strength_section"
# print(EvaluationTemplates.get_template(template_name))




