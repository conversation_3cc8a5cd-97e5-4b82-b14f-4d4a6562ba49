"""adding tables for scp discipline & category

Revision ID: 3e28f331b3fb
Revises: 2e541b16d040
Create Date: 2024-08-21 23:19:08.054094

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3e28f331b3fb'
down_revision = '2e541b16d040'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('disciplines',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('shortcode', sa.String(length=2), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('shortcode')
    )
    op.create_table('categories',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('feed', sa.Boolean(), nullable=True),
    sa.Column('detail', sa.Boolean(), nullable=True),
    sa.Column('vendor', sa.Boolean(), nullable=True),
    sa.Column('discipline_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['discipline_id'], ['disciplines.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('criteria',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('description', sa.String(length=1024), nullable=False),
    sa.Column('category_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['category_id'], ['categories.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('criteria')
    op.drop_table('categories')
    op.drop_table('disciplines')
    # ### end Alembic commands ###
