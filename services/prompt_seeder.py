import json
import uuid
from datetime import datetime
from models import db, Prompt  # Ensure 'Prompt' is imported correctly

class PromptSeeder:
    def __init__(self, json_file_path):
        """
        Initialize the PromptSeeder with the path to the JSON file.
        """
        self.json_file_path = json_file_path
        self.prompts_data = self._load_json()

    def _load_json(self):
        """
        Private method to load the JSON file containing the prompts.
        """
        try:
            with open(self.json_file_path, 'r') as file:
                return json.load(file)
        except FileNotFoundError:
            print(f"Error: The file {self.json_file_path} was not found.")
            return {}
        except json.JSONDecodeError:
            print(f"Error: The file {self.json_file_path} is not a valid JSON.")
            return {}

    def seed_prompts(self):
        """
        Method to seed the prompts into the database.
        """
        if not self.prompts_data:
            print("No prompts data to seed.")
            return

        processed_count = 0  # Track the number of processed prompts

        for name in self.prompts_data.keys():
            value = self.prompts_data[name]
            if isinstance(value, list) or isinstance(value, dict):
                value = json.dumps(value)
            if name.startswith("cbp_"):
                type_ = "CBP"
            elif name.startswith("scp_"):
                type_ = "SCP"
            else:
                type_ = "general"

            try:
                # Check if the prompt already exists
                existing_prompt = Prompt.query.filter_by(name=name).first()
                if existing_prompt:
                    # Update the existing prompt
                    existing_prompt.value = value
                    existing_prompt.type = type_
                    existing_prompt.date_updated = datetime.utcnow()
                else:
                    # Create a new Prompt object
                    new_prompt = Prompt(
                        id=str(uuid.uuid4()),
                        name=name,
                        value=value,
                        type=type_,
                        date_created=datetime.utcnow(),
                        date_updated=datetime.utcnow()
                    )
                    db.session.add(new_prompt)

                processed_count += 1  # Increment processed count

            except Exception as e:
                print(f"Error processing prompt '{name}': {e}")
                db.session.rollback()  # Rollback the session to prevent failure in subsequent prompts

        # Commit the changes to the database
        db.session.commit()
        print(f"{processed_count} prompts have been processed successfully.")

# Usage example
if __name__ == "__main__":
    seeder = PromptSeeder('claude_prompts.json')
    seeder.seed_prompts()
