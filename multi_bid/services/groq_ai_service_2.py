from pydantic import BaseModel
from groq import Groq
import instructor
import os, json
import time
from threading import Timer
from services.groq_api_key_provider import GroqAPIKeyProvider

class GroqService2:
    def __init__(self, temperature: float = 0.01):
        """
        Initialize the GroqService with API key, model, and default temperature.

        :param api_key: Groq API key for authentication
        :param model: The Groq model to use for completions
        :param temperature: Sampling temperature for response generation
        """

         # Get the path to the parent directory
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        env_file_path = os.path.join(parent_dir, 'env.json')

        self.prefix = ["ftech_", "fyunusa_", "seeker_", "arif_A_", "arif_B_", "arif_C_"]

        self.api_keys = [
            f"{prefix}{key}" for prefix in self.prefix for key in [
                'GROQ_API_KEY_1', 'GROQ_API_KEY_2', 'GROQ_API_KEY_3',
                'GROQ_API_KEY_4', 'GROQ_API_KEY_5', 'GROQ_API_KEY_6',
                'GROQ_API_KEY_7', 'GROQ_API_KEY_8', 'GROQ_API_KEY_9',
                'GROQ_API_KEY_10'
            ]
        ]

        # Load environment variables from env.json file
        with open(env_file_path, 'r') as f:
            self.env_data = json.load(f)

        # Map deployments to models
        self.deployments_map = {
            "Llama 3 8B": "llama3-8b-8192",
            "Llama 3 70B": "llama3-70b-8192",
            "Mixtral 7B": "mixtral-8x7b-32768"
        }

        self.model_ids = ["llama3-8b-8192", "llama3-70b-8192", "mixtral-8x7b-32768"]
        self.temperature = temperature
        self.api_key_provider = GroqAPIKeyProvider()
        print(self.api_key_provider.get_ready_api_key())


    def extract_data(self, schema: BaseModel, messages):
        """
        Extract data using Groq with the specified schema and prompts.

        :param schema: Pydantic model specifying the response schema
        :param system_prompt: The system-level instruction for the Groq model
        :param user_prompt: The user-level input text to extract data from
        :return: An instance of the schema populated with extracted data
        """
        # for api_key in self.groq_keys:
        for model_id in self.model_ids:
            try:
                api_key = self.api_key_provider.get_ready_api_key()
                print("Using API Key", api_key)
                self.client = instructor.from_groq(Groq(api_key=self.env_data.get(api_key, '')), mode=instructor.Mode.JSON)

                response = self.client.chat.completions.create(
                    model=model_id,
                    response_model=schema,
                    messages=messages,
                    temperature=self.temperature,
                )

                start = time.time()

                input_tokens = len(" ".join([msg['content'] for msg in messages]))
                ouput_tokens = len(response.Evaluation) + len(str(response.Score)) + len(response.Reason) + len(response.Reference)
                usage_token = input_tokens + ouput_tokens
                print(usage_token)

                end = time.time()

                print("Total Time Take to Calculate the Token Usage: ", end-start)
                print("Token Usage: ", usage_token)
                print("Input Tokens: ", input_tokens)
                print("Output Tokens: ", ouput_tokens)

                # Update token usage
                token_count = usage_token  # Example token count used for the request
                self.api_key_provider.update_token_usage(api_key, token_count)

                return response
            except Exception as e:
                print("Error with key", api_key)
                self.api_key_provider._flag_key_as_pending(api_key)
                print(f"Error generating completion: {e}")
                print(f"Error with key {api_key}: {e}")

