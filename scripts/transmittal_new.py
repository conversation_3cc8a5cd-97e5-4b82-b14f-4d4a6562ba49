import argparse
import json
import requests
from datetime import datetime
import sys
import os
from cryptography.fernet import Fernet
import urllib.parse

def read_env_file(env_path):
    """Read and parse the environment JSON file"""
    try:
        with open(env_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error reading environment file: {e}")
        sys.exit(1)

def get_session_key(env_config):
    base_url = "https://sa1.doc.ineight.com/tbws/session.asmx/LogonTFAWithApplication"
    url = (
    f"https://sa1.doc.ineight.com/tbws/session.asmx/LogonTFAWithApplication"
    f"?UserID={env_config['userID']}"
    f"&CompanyID={env_config['companyID']}"
    f"&TfaToken={env_config.get('TfaToken', '.')}"
    f"&ProjNo={env_config['ProjectNo']}"
    f"&connectingProduct={env_config.get('connectingProduct', '.')}"
    f"&Password={env_config['Password']}"
    )
    try:
        response = requests.get(url)
        if response.text.strip().startswith('<!DOCTYPE HTML') or '<html' in response.text.lower():
            print(f"Received HTML error response: {response.text[:200]}...")
            return f"ERROR - Server returned HTML error response (status code: {response.status_code})"
        if response.status_code == 200:
            try:
                # Parse the XML response to extract the session key
                import xml.etree.ElementTree as ET
                root = ET.fromstring(response.text)
                session_key = root.text.strip() if root.text else None
                
                # Check if the response contains an error message
                if session_key and "ERROR" in session_key:
                    print(f"Session key contains error: {session_key}")
                    return session_key  # Return the error message as is
                
                if session_key:
                    return session_key
                else:
                    return "ERROR - No session key found in response"
            except ET.ParseError:
                print(f"Failed to parse XML response: {response.text[:200]}...")
                return f"ERROR - Invalid XML response from server"
        else:
            print(f"Failed to get session key. Status code: {response.status_code}")
            print(f"Response: {response.text}")
            return f"ERROR - Authentication failed (status code: {response.status_code})"
    except requests.RequestException as e:
        print(f"Request error getting session key: {e}")
        return f"ERROR - Network error: {str(e)}"
    except Exception as e:
        print(f"Unexpected error getting session key: {e}")
        return f"ERROR - Unexpected error: {str(e)}"

def get_directory_data(session_key, env_config):
    """Get directory data using the session key"""
    directory_url = f"{env_config['base_url']}/TBReportingAPI/Report/05.001"
    headers = {
        'Accept': 'application/json',
        'Authorization': f'basic {session_key}'
    }
    print("directory_url >> ", directory_url)
    print("headers >> ", headers)

    try:
        response = requests.get(directory_url, headers=headers)
        
        # Check for HTML error responses
        if response.text.strip().startswith('<!DOCTYPE HTML') or '<html' in response.text.lower():
            print(f"Received HTML error response: {response.text[:200]}...")
            raise ValueError(f"Server returned HTML error response (status code: {response.status_code})")
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Failed to get directory data. Status code: {response.status_code}")
            print(f"Response: {response.text}")
            raise ValueError(f"Failed to get directory data: {response.text}")
    except requests.RequestException as e:
        print(f"Request error getting directory data: {e}")
        raise ValueError(f"Network error: {str(e)}")
    except json.JSONDecodeError as e:
        print(f"JSON decode error: {e}")
        print(f"Response content: {response.text[:200]}...")
        raise ValueError(f"Invalid JSON response: {str(e)}")
    except Exception as e:
        print(f"Error getting directory data: {e}")
        raise ValueError(f"Unexpected error: {str(e)}")

def save_directory_data(data):
    """Save directory data to JSON file"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"transmittal_report.json"
    
    # Create ineight_info directory if it doesn't exist
    ineight_info_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ineight_info')
    os.makedirs(ineight_info_dir, exist_ok=True)
    
    file_path = os.path.join(ineight_info_dir, filename)
    
    try:
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2)
        print(f"Directory data saved to {file_path}")
    except Exception as e:
        print(f"Error saving directory data: {e}")
        raise ValueError(f"Failed to save directory data: {str(e)}")

def fetch_ineight_projects(fernet_key, Project):
    
    try:
        cipher = Fernet(fernet_key.encode())
        print("cipher >> ")
        all_projects = Project.get_by(is_ineight=1)
        print("all_projects >> ", all_projects)
        
        ineight_projects = [p for p in all_projects if p.get('is_ineight') == 1]
        
        if not ineight_projects:
            print("No iNeight projects found in the database")
            return []
            
        return ineight_projects, cipher
    except Exception as e:
        print(f"Error fetching iNeight projects: {e}")
        return [], None

def process_ineight_projects(fernet_key, Project):
    print("fernet_key >> ", fernet_key)
    projects, cipher = fetch_ineight_projects(fernet_key, Project)
    print("projects >> ", projects)
    results = []
    
    # Create ineight_info directory if it doesn't exist
    ineight_info_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ineight_info')
    os.makedirs(ineight_info_dir, exist_ok=True)
    
    for project in projects:
        try:
            # Decrypt credentials
            def decrypt(val):
                return cipher.decrypt(val.encode()).decode() if val else None
            
            ineight_project_id = decrypt(project.get('ineight_project_id'))
            ineight_user_id = decrypt(project.get('ineight_user_id'))
            ineight_company_id = decrypt(project.get('ineight_company_id'))
            ineight_password = decrypt(project.get('ineight_password'))
            
            # Skip if any credential is missing
            if not all([ineight_project_id, ineight_user_id, ineight_company_id, ineight_password]):
                print(f"Skipping project {project['name']} due to missing credentials")
                results.append({
                    'project_id': project['id'],
                    'project_name': project['name'],
                    'status': 'error',
                    'error': 'Missing credentials'
                })
                continue
                
            # Create environment config for this project
            env_config = {
                'base_url': 'https://sa1.doc.ineight.com',  # Updated to sa1 instead of eu1
                'userID': ineight_user_id,
                'companyID': ineight_company_id,
                'Password': ineight_password,
                'ProjectNo': ineight_project_id,
                'TfaToken': '.',  # Default value
                'connectingProduct': '.',  # Default value
                'Application': 'InEight Document'
            }
            print("env_config >> ", env_config)
            
            try:
                # Get session key for this project
                session_key = get_session_key(env_config)
                
                # Check if session key contains an error message
                if "ERROR" in session_key:
                    raise ValueError(f"Session key error: {session_key}")
                
                # Get directory data using the session key
                directory_data = get_directory_data(session_key.strip('"'), env_config)
                
                # Save data with project ID in filename
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"transmittal_{project['id']}_{timestamp}.json"
                file_path = os.path.join(ineight_info_dir, filename)
                
                with open(file_path, 'w') as f:
                    json.dump(directory_data, f, indent=2)
                
                print(f"Directory data for project {project['name']} saved to {file_path}")
                
                results.append({
                    'project_id': project['id'],
                    'project_name': project['name'],
                    'status': 'success',
                    'filename': filename
                })
            except ValueError as auth_error:
                # Handle authentication errors specifically
                print(f"Authentication error for project {project['name']}: {auth_error}")
                results.append({
                    'project_id': project['id'],
                    'project_name': project['name'],
                    'status': 'error',
                    'error': f'Authentication failed: {str(auth_error)}'
                })
            except Exception as api_error:
                # Handle other API errors
                print(f"API error for project {project['name']}: {api_error}")
                results.append({
                    'project_id': project['id'],
                    'project_name': project['name'],
                    'status': 'error',
                    'error': f'API error: {str(api_error)}'
                })
            
        except Exception as e:
            print(f"Error processing project {project['name']}: {e}")
            results.append({
                'project_id': project['id'],
                'project_name': project['name'],
                'status': 'error',
                'error': str(e)
            })
    
    return results

def transmittal_daily_work(name, email, FERNET_KEY, Project):
    try:
        # Process iNeight projects
        data = process_ineight_projects(FERNET_KEY, Project)
        return data
    except Exception as e:
        print(f"Error in transmittal_daily_work: {e}")
        # Return error information instead of crashing
        return [{'status': 'error', 'error': f'System error: {str(e)}'}]
    
if __name__ == "__main__":
    from init import app
    from models import Project
    with app.app_context():
        transmittal_daily_work(
            'test',
            '<EMAIL>',
            'R8337urkBHZraE-DxfGobPJzTW58f50SAS4hPulD_8s=',
            Project
        )
