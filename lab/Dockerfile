# Use a Python 3.10 base image
FROM python:3.10

# Set the working directory inside the container
WORKDIR /app

# Install dependencies and OpenGL libraries in one step
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    nano \
    cron \
    && apt-get clean

# Copy the current directory contents into the container at /app
COPY . /app

# Upgrade pip and install required Python packages globally
RUN pip install --upgrade pip && \
    pip install -r /app/requirements.txt && \
    pip install torch && \
    pip install gunicorn

# Copy the current directory contents into the container at /app
COPY . /app

# Ensure the logs directory exists
RUN mkdir -p /app/logs

# Give execution rights on the cron job scripts
RUN chmod +x /app/scripts/*.sh

# Copy the cron job file into the cron.d directory
COPY scripts/template.cron /etc/cron.d/template

# Give execution rights on the cron job file
RUN chmod 0644 /etc/cron.d/template

# Apply the cron job
RUN crontab /etc/cron.d/template

# Expose port 5110 to allow external connections
EXPOSE 5110
EXPOSE 5120
EXPOSE 5250

# Command to run when the container starts
CMD ["bash", "/app/scripts/setup.sh"]
