def generate_technical_assessment(data):
    """
    Generate technical assessment HTML based on bidder summary data.
    """
    # Extract technical criteria from bidder summary
    technical_summaries = []
    for item in data["data_aggregrated_report"]["bidder_summary"]:
        if item["criteria"] == "Technical Criteria":
            technical_summaries.append(item)

    # Generate HTML
    html_template = f"""
    <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; background: white; border: 1px solid #ddd;">
        <h2 style="color: #2c5aa0; text-align: left; border-bottom: 2px solid #2c5aa0; padding-bottom: 10px;">
            TECHNICAL ASSESSMENT OF TENDERS
        </h2>
        
        <h3 style="color: #2c5aa0;">Offer Summary</h3>
        <p style="font-weight: bold; margin-bottom: 15px;">
            The following is a summary of each of the options in this adjudication, for more technical details refer to Attachment 1.
        </p>
    """

    # Add bidder summaries
    for summary in technical_summaries:
        # Get all keys that end with _summary
        bidder_summaries = {k: v for k, v in summary.items() if k.endswith('_summary')}
        
        for summary_key, summary_text in bidder_summaries.items():
            # Extract bidder name by removing _summary suffix
            bidder_name = summary_key.replace('_summary', '')
            html_template += f"""
            <div style="margin-top: 25px;">
                <p><strong>{bidder_name}:</strong> {summary_text}</p>
            </div>
            """

    # Close the HTML template
    html_template += """
        <div style="margin-top: 30px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7;">
            <h4 style="color: #856404; margin-top: 0;">ASSESSMENT NOTES</h4>
            <p style="margin-bottom: 0; color: #856404;">
                This technical assessment is based on the evaluation of technical criteria and its comparison with the requirements in Tender documents.
            </p>
        </div>
    </div>
    """

    return html_template

# Example usage:


if __name__ == "__main__":

    data =   {
        "data_aggregrated_report": {
            "comparison_table": [
                {
                    "criteria": "BALL MILL",
                    "Tysen": "<p>Tysen demonstrates strong technical competence in their Ball Mill specifications, providing comprehensive documentation on critical parameters such as dimensions, power requirements, and operating speeds. Their submission includes detailed technical data across multiple reference documents, showcasing a thorough understanding of equipment requirements. Key strengths include precise specifications for shell diameter, grinding length, and critical speed operation. However, the absence of tender requirements prevents a complete compliance assessment. In comparison to Mo group, Tysen's submission appears equally strong, with both bidders receiving identical scores, suggesting comparable technical proficiency in this area.</p>",
                    "Mo group": "<p>Mo group's Ball Mill specifications largely meet the technical requirements, with notable strengths in power capacity, mill dimensions, and liner specifications. Their submission demonstrates good technical compliance overall, but there are areas for improvement, including the need for clarification on the drilling method and more detailed information on certain operational parameters. When compared to Tysen, Mo group's proposal shows similar technical proficiency, as evidenced by their identical scores. However, Mo group's submission appears to have some minor gaps that need addressing for optimal project alignment, which could potentially differentiate them from Tysen in a more detailed assessment.</p>",
                    "Scores": {
                        "Tysen": "32%",
                        "Mo group": "32%"
                    }
                },
                {
                    "criteria": "Technical Criteria",
                    "Tysen": "<p>Tysen's technical submission for the Ball Mill demonstrates good technical competence, providing detailed specifications for critical operational parameters. Their proposal includes comprehensive information on mill dimensions, power requirements, operating conditions, and liner specifications. While the information appears technically sound and consistent with industry standards, the absence of tender specification documents prevents a complete compliance assessment. Compared to Mo group, Tysen's submission is equally strong in terms of technical details, with both bidders receiving the same score. However, Tysen's proposal lacks specific information on drilling methods, which is an area where Mo group provided more clarity.</p>",
                    "Mo group": "<p>Mo group's Ball Mill specifications largely meet the technical requirements, showcasing strong points in power capacity, mill dimensions, and liner specifications. Their proposal demonstrates good technical compliance, aligning well with project needs. Compared to Tysen, Mo group provides more clarity on the drilling method, which is a unique advantage. However, both bidders received the same technical score, indicating a similar overall level of technical competence. Mo group could improve their submission by providing more detailed information on certain operational parameters to further distinguish themselves from competitors like Tysen.</p>",
                    "Scores": {
                        "Tysen": "40%",
                        "Mo group": "40%"
                    }
                }
            ],
            "technical_criteria_names": [
                "BALL MILL",
                "Technical Criteria",
                "BALL MILL",
                "Technical Criteria"
            ],
            "bidder_summary": [
                {
                    "criteria": "technical_evaluation_criteria"
                },
                {
                    "criteria": "Technical Criteria",
                    "Tysen_score": "40%",
                    "Tysen_summary": "The technical submission for the Ball Mill demonstrates good technical competence with detailed specifications for critical operational parameters. The bidder has provided comprehensive information on mill dimensions, power requirements, operating conditions, and liner specifications. While the information appears technically sound and consistent with industry standards, the absence of tender specification documents prevents a complete compliance assessment. The overall technical score reflects the quality and completeness of the provided information rather than direct compliance with tender requirements.",
                    "Mo group_score": "40%",
                    "Mo group_summary": "The Ball Mill specifications largely meet the technical requirements, with strong points in power capacity, mill dimensions, and liner specifications. Areas for improvement include clarification on the drilling method and more detailed information on certain operational parameters. Overall, the equipment demonstrates good technical compliance, but some minor gaps need addressing for optimal project alignment."
                },
                {
                    "criteria": "BALL MILL",
                    "Tysen_score": "32%",
                    "Tysen_summary": "The Ball Mill specifications are well-documented with detailed information on critical parameters including dimensions, power requirements, operating speeds, and liner specifications. The bidder has provided comprehensive technical data in multiple reference documents that demonstrate a thorough understanding of the equipment requirements. Key specifications include a 6.50m inside shell diameter, 9.80m effective grinding length, 75% critical speed operation at 12.6 rpm, and power draw specifications under different operating conditions. While the technical information is comprehensive, the absence of tender requirements prevents a complete compliance assessment.",
                    "Mo group_score": "32%",
                    "Mo group_summary": "The Ball Mill specifications largely meet the technical requirements, with strong points in power capacity, mill dimensions, and liner specifications. Areas for improvement include clarification on the drilling method and more detailed information on certain operational parameters. Overall, the equipment demonstrates good technical compliance, but some minor gaps need addressing for optimal project alignment."
                }
            ]
        }
    }

    technical_assessment_html = generate_technical_assessment(data)
    #save as html
    with open('technical_assessment.html', 'w') as f:
        f.write(technical_assessment_html)



