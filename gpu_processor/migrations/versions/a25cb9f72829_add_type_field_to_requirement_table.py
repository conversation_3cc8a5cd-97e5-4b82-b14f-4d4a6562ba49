"""add type field to requirement table

Revision ID: a25cb9f72829
Revises: 74db0dfc8a22
Create Date: 2024-11-08 17:29:08.566849

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a25cb9f72829'
down_revision = '74db0dfc8a22'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('requirement', schema=None) as batch_op:
        batch_op.add_column(sa.Column('type', sa.String(length=255), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('requirement', schema=None) as batch_op:
        batch_op.drop_column('type')

    # ### end Alembic commands ###
